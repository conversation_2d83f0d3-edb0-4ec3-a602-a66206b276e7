2025-06-17 13:23:01|   0.0|  0.0|INFORM|Current FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|System Time: 20250617052302 UTC
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Workspace was last saved in FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME_HOME is 'C:\Program Files\FME\'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME ESRI ArcGIS Server Edition (floating)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Permanent License.
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Machine host name is: DESKTOP-9BLU554
2025-06-17 13:23:01|   0.0|  0.0|INFORM|OS Locale Name     : zh_CN
2025-06-17 13:23:01|   0.0|  0.0|INFORM|OS Locale Encoding : GBK
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Process Encoding   : UTF-8
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME API version: '4.0 20230426'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: FME_BASE is 'no'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: FME_MF_DIR is 'E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL/'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: FME_MF_NAME is 'cad2gis.fmw'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: FME_PRODUCT_NAME is 'FME(R) 2023.1.0.0'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Operating System: Microsoft Windows 10 64-bit  (Build 19045)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Platform: WIN64
2025-06-17 13:23:01|   0.0|  0.0|INFORM|System Status: 205.32 GB of disk space available in the FME temporary folder (C:\Users\<USER>\AppData\Local\Temp)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|System Status: 15.74 GB of physical memory available
2025-06-17 13:23:01|   0.0|  0.0|INFORM|System Status: 62.96 GB of virtual memory available
2025-06-17 13:23:01|   0.0|  0.0|INFORM|START - ProcessID: 39712, peak process memory usage: 44460 kB, current process memory usage: 44460 kB
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Command line arguments are `C:\Program Files\FME\fme.exe' `E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL\cad2gis.fmw' `--dwg_path' `E:\GeoStream_Integration\frontend\backend\temp\QXOp39yOhfwfKTXYV1noVEK81HJmZ01TGsMJUUKE\zz' `--output_Choice' `['点（Point）', '富点（MultiPoint）', '线（Line）', '面（Area）', '文本（Text）', '富文本（MultiText）', '曲线（Curve）', '圆弧（Arc）', '椭圆（Ellipse）', '其他（Other）']' `--save_path' `E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL\output\task_1750137779291_2j328cvkx'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Connection Storage: 'C:\Users\<USER>\AppData\Roaming\Safe Software\FME\'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Shared folders for formats are : C:\Program Files\FME\datasources;C:\Users\<USER>\Documents\FME\Formats
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Shared folders for transformers are : C:\Users\<USER>\AppData\Roaming\Safe Software\FME\Packages\23619-win64\transformers;C:\Program Files\FME\transformers;C:\Users\<USER>\AppData\Roaming\Safe Software\FME\FME Store\Transformers
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Shared folders for coordinate systems are : C:\Users\<USER>\Documents\FME\CoordinateSystems
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Shared folders for coordinate system exceptions are : C:\Users\<USER>\Documents\FME\CoordinateSystemExceptions
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Shared folders for coordinate system grid overrides are : C:\Users\<USER>\Documents\FME\CoordinateSystemGridOverrides
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Shared folders for CS-MAP transformation exceptions are : C:\Users\<USER>\Documents\FME\CsmapTransformationExceptions
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Shared folders for transformer categories are : C:\Users\<USER>\Documents\FME\TransformerCategories
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Reader Keyword is `MULTI_READER'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Writer Keyword is `MULTI_DEST'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Writer Group Definition Keyword is `MULTI_DEST_DEF'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Reader type is `MULTI_READER'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Writer type is `MULTI_WRITER'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Current working folder is `E:\GeoStream_Integration\frontend\backend'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Temporary folder is `C:\Users\<USER>\AppData\Local\Temp', set from environment variable `TEMP'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Cache folder is 'C:\Users\<USER>\AppData\Local\Temp'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: FME_HOME is `C:\Program Files\FME\'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Start freeing memory when the process exceeds 47.22 GB
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME Configuration: Stop freeing memory when the process is below 35.42 GB
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Creating writer for format: 
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Creating reader for format: 
2025-06-17 13:23:01|   0.0|  0.0|INFORM|MULTI_READER(MULTI_READER): Will fail with first member reader failure
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Using Multi Reader with keyword `MULTI_READER' to read multiple datasets
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Using MultiWriter with keyword `MULTI_DEST' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Loaded module 'Geometry_func' from file 'C:\Program Files\FME\plugins/Geometry_func.dll'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME API version of module 'Geometry_func' matches current internal version (4.0 20230426)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Loaded module 'QueryFactory' from file 'C:\Program Files\FME\plugins/QueryFactory.dll'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME API version of module 'QueryFactory' matches current internal version (4.0 20230426)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Loaded module 'GeometryFilterFactory' from file 'C:\Program Files\FME\plugins/GeometryFilterFactory.dll'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME API version of module 'GeometryFilterFactory' matches current internal version (4.0 20230426)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Loaded module 'GQueryFactory' from file 'C:\Program Files\FME\plugins/GQueryFactory.dll'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME API version of module 'GQueryFactory' matches current internal version (4.0 20230426)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Loaded module 'StatisticsCalculatorFactory' from file 'C:\Program Files\FME\plugins/StatisticsCalculatorFactory.dll'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME API version of module 'StatisticsCalculatorFactory' matches current internal version (4.0 20230426)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'path_windows.sum'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Emptying factory pipeline
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Loaded module 'PATH' from file 'C:\Program Files\FME\plugins/PATH.dll'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|FME API version of module 'PATH' matches current internal version (4.0 20230426)
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Performing query against PATH dataset `E:\GeoStream_Integration\frontend\backend\temp\QXOp39yOhfwfKTXYV1noVEK81HJmZ01TGsMJUUKE\zz'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Path Reader: Opening the PATH Reader on folder 'E:\GeoStream_Integration\frontend\backend\temp\QXOp39yOhfwfKTXYV1noVEK81HJmZ01TGsMJUUKE\zz'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Path Reader: Using Glob Pattern '*'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Path Reader: Allowed Path Type set to 'ANY'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Path Reader: Recurse into subdirectories 'true'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Path Reader: Hidden Files and Folders set to 'INCLUDE'
2025-06-17 13:23:01|   0.0|  0.0|INFORM|Path Reader: Retrieve file properties 'false'
2025-06-17 13:23:02|   0.2|  0.2|INFORM|Creating reader for format: Autodesk AutoCAD DWG/DXF
2025-06-17 13:23:02|   0.2|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `ACAD'
2025-06-17 13:23:02|   0.9|  0.7|INFORM|Loaded module 'ACAD' from file 'C:\Program Files\FME\plugins/acad/ACAD.dll'
2025-06-17 13:23:02|   0.9|  0.0|INFORM|FME API version of module 'acad/ACAD' matches current internal version (4.0 20230426)
2025-06-17 13:23:02|   0.9|  0.0|INFORM|Performing query against ACAD dataset `E:\GeoStream_Integration\frontend\backend\temp\QXOp39yOhfwfKTXYV1noVEK81HJmZ01TGsMJUUKE\zz\222.dwg'
2025-06-17 13:23:02|   0.9|  0.0|INFORM|Creating reader for format: Autodesk AutoCAD DWG/DXF
2025-06-17 13:23:02|   0.9|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `ACAD'
2025-06-17 13:23:02|   0.9|  0.0|INFORM|R_4 Reader: Using rich geometry.
2025-06-17 13:23:02|   0.9|  0.0|INFORM|AutoCAD Reader: Successfully opened the 'Release2007' AutoCAD file 'E:\GeoStream_Integration\frontend\backend\temp\QXOp39yOhfwfKTXYV1noVEK81HJmZ01TGsMJUUKE\zz\222.dwg'
2025-06-17 13:23:02|   0.9|  0.0|WARN  |AutoCAD Reader: Failed to locate or read world file. Geometric attributes will not be transformed
2025-06-17 13:23:08|   6.5|  5.6|STATS |Creator_XML_Creator (CreationFactory): Created 1 features
2025-06-17 13:23:08|   6.5|  0.0|STATS |Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-06-17 13:23:08|   6.5|  0.0|STATS |Creator_CREATED Brancher -1 218 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-06-17 13:23:08|   6.5|  0.0|STATS |_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-06-17 13:23:08|   6.5|  0.0|INFORM|Path Reader: Closing the PATH Reader
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_3 (TestFactory): Tested 1 input feature(s) -- 1 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |AttributeExposer_2 (TeeFactory): Cloned 53223 input feature(s) into 53223 output feature(s)
2025-06-17 13:23:08|   6.5|  0.0|STATS |AttributeExposer_3 (TeeFactory): Cloned 53223 input feature(s) into 53223 output feature(s)
2025-06-17 13:23:08|   6.5|  0.0|STATS |AttributeExposer_3 OUTPUT Splitter (TeeFactory): Cloned 53223 input feature(s) into 106446 output feature(s)
2025-06-17 13:23:08|   6.5|  0.0|STATS |GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_12 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_7 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_8 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_9 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_13 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_10 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_14 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_15 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_16 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_17 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_18 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_19 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_11 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_20 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_21 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_22 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_23 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_24 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_25 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_26 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_27 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_28 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_29 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_30 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_31 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_32 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_33 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_34 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_35 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_36 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_37 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_38 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_39 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_40 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_41 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_42 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_43 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.5|  0.0|STATS |Tester_44 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-17 13:23:08|   6.6|  0.1|INFORM|Creating writer for format: 
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Using MultiWriter with keyword `FeatureWriter_2' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Creating writer for format: Microsoft Excel
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Trying to find a DYNAMIC plugin for writer named `XLSXW'
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Loaded module 'XLSXW' from file 'C:\Program Files\FME\plugins/xlsx.dll'
2025-06-17 13:23:08|   6.6|  0.0|INFORM|FME API version of module 'xlsx' matches current internal version (4.0 20230426)
2025-06-17 13:23:08|   6.6|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Writer `FeatureWriter_2_0' of type `XLSXW' using group definition keyword `FeatureWriter_2_0_DEF'
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Use Attribute Names As Column Positions is set to 'no' for sheet 'Summary' in dataset 'E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL\output\task_1750137779291_2j328cvkx\ceshi.xlsx' 
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Opening dataset 'E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL\output\task_1750137779291_2j328cvkx\ceshi.xlsx'...
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Reading existing workbook formatting. Workbook contains 1 fonts and 1 cell formats
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Sheet 'Summary' has writer mode 'INSERT'
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Sheet 'Summary' has Output field names set to 'yes'
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Sheet 'Summary' will start writing to row '1' and column 'A' relative to its start location. Sheets have a starting location of row 1, column 1, named range starting locations can be specified
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Outputting field names for sheet 'Summary'
2025-06-17 13:23:08|   6.6|  0.0|INFORM|StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Saving changes to dataset 'E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL\output\task_1750137779291_2j328cvkx\ceshi.xlsx'
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Workbook contains 1 fonts and 1 cell formats after writing
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Excel Writer: Closing dataset 'E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL\output\task_1750137779291_2j328cvkx\ceshi.xlsx'...
2025-06-17 13:23:08|   6.6|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Feature output statistics for `XLSXW' writer using keyword `FeatureWriter_2_0':
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |                               Features Written
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |Summary                                                                      1
2025-06-17 13:23:08|   6.6|  0.0|STATS |==============================================================================
2025-06-17 13:23:08|   6.6|  0.0|STATS |Total Features Written                                                       1
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |Destination Feature Type Routing Correlator (RoutingFactory): Tested 0 input feature(s), wrote 0 output feature(s): 0 matched merge filters, 0 were routed to output, 0 could not be routed.
2025-06-17 13:23:08|   6.6|  0.0|STATS |Final Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |                            Features Read Summary
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |==============================================================================
2025-06-17 13:23:08|   6.6|  0.0|STATS |Total Features Read                                                          0
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |                           Features Written Summary
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|STATS |==============================================================================
2025-06-17 13:23:08|   6.6|  0.0|STATS |Total Features Written                                                       0
2025-06-17 13:23:08|   6.6|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-17 13:23:08|   6.6|  0.0|INFORM|Translation was SUCCESSFUL with 1 warning(s) (0 feature(s) output)
2025-06-17 13:23:08|   6.6|  0.0|INFORM|FME Session Duration: 6.6 seconds. (CPU: 6.1s user, 0.5s system)
2025-06-17 13:23:08|   6.6|  0.0|INFORM|END - ProcessID: 39712, peak process memory usage: 181808 kB, current process memory usage: 112560 kB
