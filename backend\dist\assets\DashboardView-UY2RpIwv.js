import{d as y,r as d,o as C,a as B,c as k,e,w as o,f as n,R as f,b as s,A as i,y as g,l as x,S as V,k as G,_ as I}from"./index-Dn7OnccA.js";const D={class:"dashboard"},N={class:"card-header"},S={class:"stats-grid"},T={class:"stat-item"},q={class:"stat-value"},U={class:"number"},z={class:"stat-item"},A={class:"stat-value"},E={class:"number"},M={class:"stat-item"},P={class:"stat-value"},R={class:"number"},j={class:"card-header"},F={class:"server-stats"},H={class:"server-stat-item"},J={class:"server-stat-item"},K={class:"server-stat-item"},L=y({__name:"DashboardView",setup(O){const c=d("--"),u=d("--"),_=d("--"),p=async()=>{try{const a=await f.get("/api/tools/count");a.data.success&&(c.value=a.data.count)}catch(a){console.error("获取工具总数失败:",a)}},v=async()=>{try{const a=await f.get("/api/task/status/count");a.data.success&&(u.value=a.data.data.queue_count,_.value=a.data.data.running_count)}catch(a){console.error("获取任务状态数量失败:",a)}};let l;return C(()=>{p(),v(),l=window.setInterval(()=>{p(),v()},5e3)}),B(()=>{l&&clearInterval(l)}),(a,t)=>{const h=n("el-icon"),b=n("el-button"),m=n("el-card"),w=n("el-tag"),r=n("el-progress");return G(),k("div",D,[e(m,{class:"dashboard-card"},{header:o(()=>[s("div",N,[t[1]||(t[1]=s("span",null,"工具情况",-1)),e(b,{type:"primary",link:""},{default:o(()=>[t[0]||(t[0]=g(" 查看详情 ")),e(h,{class:"el-icon--right"},{default:o(()=>[e(x(V))]),_:1})]),_:1})])]),default:o(()=>[s("div",S,[s("div",T,[t[3]||(t[3]=s("div",{class:"stat-title"},"工具总数",-1)),s("div",q,[s("span",U,i(c.value),1),t[2]||(t[2]=s("span",{class:"unit"},"个",-1))])]),s("div",z,[t[5]||(t[5]=s("div",{class:"stat-title"},"当前队列数量",-1)),s("div",A,[s("span",E,i(u.value),1),t[4]||(t[4]=s("span",{class:"unit"},"个",-1))])]),s("div",M,[t[7]||(t[7]=s("div",{class:"stat-title"},"当前运行数量",-1)),s("div",P,[s("span",R,i(_.value),1),t[6]||(t[6]=s("span",{class:"unit"},"个",-1))])])])]),_:1}),e(m,{class:"dashboard-card"},{header:o(()=>[s("div",j,[t[9]||(t[9]=s("span",null,"服务器信息",-1)),e(w,{size:"small",type:"success",effect:"light"},{default:o(()=>t[8]||(t[8]=[g("正常运行")])),_:1})])]),default:o(()=>[s("div",F,[s("div",H,[t[10]||(t[10]=s("div",{class:"stat-header"},[s("span",{class:"stat-title"},"CPU使用率"),s("span",{class:"stat-value"},"--%")],-1)),e(r,{percentage:0,format:()=>""})]),s("div",J,[t[11]||(t[11]=s("div",{class:"stat-header"},[s("span",{class:"stat-title"},"内存使用情况"),s("span",{class:"stat-value"},"--GB / --GB")],-1)),e(r,{percentage:0,format:()=>""})]),s("div",K,[t[12]||(t[12]=s("div",{class:"stat-header"},[s("span",{class:"stat-title"},"磁盘使用情况"),s("span",{class:"stat-value"},"--GB / --GB")],-1)),e(r,{percentage:0,format:()=>""})])])]),_:1})])}}}),W=I(L,[["__scopeId","data-v-5d118600"]]);export{W as default};
