import{d as xt,r as d,N as Q,u as Tt,O as K,M as X,o as Z,a as St,c as C,b as o,e as a,H as ee,I as te,f as p,n as M,w as l,C as P,i as f,E as u,aa as Vt,F as E,m as v,ai as Me,A as h,B as F,R as Pe,P as Mt,Q as Pt,t as Ie,s as Ne,y as ae,aj as Ee,J as It,ak as De,v as Re,a2 as Nt,z as Et,ab as Dt,al as Rt,T as se,l as g,_ as zt}from"./index-B7WNRWO3.js";const Ut={class:"settings"},Ot={class:"settings-nav"},Lt={class:"nav-list"},qt=["onClick"],Bt={class:"nav-item-text"},Wt={class:"content-wrapper"},Jt={key:0,class:"loading-container"},jt={key:1},At={class:"section-header"},Ft={class:"header-left"},Yt={class:"task-list"},$t={key:0,class:"empty-tasks"},Ht={class:"section-header"},Gt={class:"header-left"},Qt={style:{width:"100%"}},Kt={class:"form-actions"},Xt={class:"section-header"},Zt={class:"header-left"},ea={class:"port-control"},ta={key:0,class:"port-status"},aa={class:"form-actions"},sa={class:"section-header"},la={class:"header-left"},oa={class:"theme-content"},na={class:"theme-grid"},ia=["onClick"],ra={class:"theme-info"},da={class:"theme-name"},ua={class:"theme-desc"},ca={class:"theme-actions"},ma={class:"section-header"},pa={class:"header-left"},ga={key:0,class:"progress-control"},va={class:"form-actions"},fa={class:"section-header"},_a={class:"header-left"},ha={class:"register-control"},ya={class:"status-text"},ba={class:"form-item-tip"},wa={class:"restart-header"},ka=xt({__name:"SettingsView",setup(Ca){const b=d("monitor"),Y=d(!1),I=d(""),ze=e=>({monitor:Me,data:Dt,config:Ie,theme:Ee,ui:De,user:Re})[e]||Rt,Ue=[{key:"monitor",label:"实时任务状态",description:"监控任务状态"},{key:"data",label:"运行记录导出",description:"导出运行记录"},{key:"config",label:"服务器参数配置",description:"服务器参数配置"},{key:"theme",label:"主题配置",description:"自定义界面主题"},{key:"ui",label:"导航菜单配置",description:"导航菜单配置"},{key:"user",label:"用户注册控制",description:"用户账户管理"}],r=Q({limits:1,server_host:"127.0.0.1",server_port:9997,mode:"offline"}),i=Q({visibleItems:{home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},itemModes:{home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},itemProgress:{home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:75,settings:75,about:75}}),Oe=[{key:"home",label:"首页",description:"系统首页和仪表板"},{key:"tools",label:"我的工具",description:"个人工具管理"},{key:"market",label:"工具市场",description:"浏览和申请工具"},{key:"quality",label:"数据质检",description:"数据质量检查平台"},{key:"coordinate",label:"坐标转换",description:"坐标系转换工具"},{key:"cadtogis",label:"CAD转GIS",description:"CAD文件转换工具"},{key:"requirement",label:"需求提交",description:"功能需求申请"},{key:"layerPreview",label:"图层预览",description:"地理数据预览"},{key:"userManagement",label:"用户管理",description:"用户账户管理（管理员）"},{key:"settings",label:"设置",description:"系统设置（管理员）"},{key:"about",label:"关于",description:"系统信息"}],S=d(null),$=d(null),z=d([]),U=d(!1),le=d(!1),L=d(!1),Le=d();d();const q=d(!1),B=d("."),oe=d(!1),D=d(!1),N=d(!1),qe=Tt(),w=d(!1),H=d(!0),ne=d(!0),ie=d(!0),re=d(!0),de=d([]),Be=d({running_count:0,pending_count:0}),ue=d(!1);let O=null;const y=Q({tableName:"task_records_all",dateRange:null}),ce=d(!1),We=[{text:"最近一周",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-3600*1e3*24*7),[t,e]}},{text:"最近一个月",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-3600*1e3*24*30),[t,e]}},{text:"最近三个月",value:()=>{const e=new Date,t=new Date;return t.setTime(t.getTime()-3600*1e3*24*90),[t,e]}}];let W=null,me=null;K(()=>r.server_port,()=>{me&&clearTimeout(me),me=setTimeout(()=>{D.value=!1,N.value=!1},300)},{flush:"post"}),K(()=>b.value,e=>{});const Je=async()=>{ne.value=!0;try{const e=await f.post("/api/settings/get",{},{timeout:1e4});e.data.success?(Object.assign(r,e.data.data),S.value=JSON.parse(JSON.stringify(e.data.data)),r.mode==="online"&&Ce().catch(console.error)):u.error(e.data.message||"获取设置失败")}catch(e){console.error("获取设置失败:",e),u.error("获取设置失败")}finally{ne.value=!1,ve()}},Ce=async()=>{try{const e=await f.post("/api/settings/ips",{},{timeout:5e3});e.data.success&&Array.isArray(e.data.data)?(z.value=e.data.data,r.mode==="online"&&!z.value.includes(r.server_host)&&(r.server_host=z.value[0]||"127.0.0.1")):z.value=["127.0.0.1"]}catch(e){console.error("获取IP列表失败:",e),z.value=["127.0.0.1"]}},je=async e=>{e==="offline"?r.server_host="127.0.0.1":setTimeout(()=>{Ce().catch(console.error)},0)},pe=X(()=>S.value&&r.server_port!==S.value.server_port),xe=X(()=>S.value?r.limits!==S.value.limits||r.server_host!==S.value.server_host||r.server_port!==S.value.server_port||r.mode!==S.value.mode:!1),Ae=X(()=>D.value?N.value?"端口可用":"端口已被占用，请更换其他端口":"请先检测端口占用情况"),Fe=X(()=>xe.value?pe.value?D.value&&N.value&&!U.value:!U.value:!1),Ye=async()=>{if(!xe.value){u.info("未检测到任何更改，无需保存");return}if(pe.value&&(!D.value||!N.value)){u.warning("端口更改后必须检测端口占用，并确保端口可用后才能保存！");return}se.confirm("确定要保存当前设置吗？保存后需重启服务器才能生效。","确认保存",{confirmButtonText:"保存",cancelButtonText:"取消",type:"warning"}).then(async()=>{U.value=!0;try{const e=await f.post("/api/settings/set",{...r});e.data.success?(u.success("设置已保存，将在重启服务器后生效"),S.value=JSON.parse(JSON.stringify(r))):u.error(e.data.message||"保存失败")}catch{u.error("保存失败")}finally{U.value=!1}})},$e=async()=>{se.confirm("确认要重启服务器吗？","确认重启",{confirmButtonText:"重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{L.value=!0;try{const e=await f.post("/api/settings/restart",{});e.data.success?(u.success("服务器重启命令已发送"),q.value=!0,Te(),Se()):e.data.needForce?se.confirm(e.data.message||"当前有任务正在进行，是否强制重启？","警告",{confirmButtonText:"强制重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{L.value=!0;try{const t=await f.post("/api/settings/restart",{force:!0});t.data.success?(u.success("服务器重启命令已发送"),q.value=!0,Te(),Se()):u.error(t.data.message||"重启失败")}finally{L.value=!1}}):u.error(e.data.message||"重启失败")}catch{u.error("重启失败")}finally{L.value=!1}})};function Te(){B.value=".",W&&clearInterval(W),W=setInterval(()=>{B.value=B.value.length<3?B.value+".":"."},500)}function Se(){const e=window.location.protocol;let t=r.server_host;t==="0.0.0.0"&&(t=window.location.hostname||"127.0.0.1");const n=r.server_port;setTimeout(()=>{q.value=!1,qe.logout(),window.location.href=`${e}//${t}:${n}/`},5e3)}const He=async()=>{oe.value=!0;try{const e=await f.post("/api/settings/check_port",{port:r.server_port});await new Promise(t=>setTimeout(t,200)),D.value=!0,e.data.success?N.value=!0:N.value=!1}catch{await new Promise(t=>setTimeout(t,200)),D.value=!0,N.value=!1}finally{oe.value=!1}},Ge=async()=>{ie.value=!0;try{const e=await f.post("/api/settings/get-nav-settings",{},{timeout:8e3});if(e.data.success){const t={...e.data.data.visibleItems,userManagement:!0,settings:!0};Object.assign(i.visibleItems,t),e.data.data.itemModes&&Object.assign(i.itemModes,e.data.data.itemModes),e.data.data.itemProgress&&Object.assign(i.itemProgress,e.data.data.itemProgress),$.value=JSON.parse(JSON.stringify({visibleItems:t,itemModes:i.itemModes,itemProgress:i.itemProgress}))}else{const t={...i.visibleItems,userManagement:!0,settings:!0};$.value=JSON.parse(JSON.stringify({visibleItems:t,itemModes:i.itemModes,itemProgress:i.itemProgress}))}}catch(e){console.error("获取导航栏设置失败:",e);const t={...i.visibleItems,userManagement:!0,settings:!0};$.value=JSON.parse(JSON.stringify({visibleItems:t,itemModes:i.itemModes,itemProgress:i.itemProgress}))}finally{ie.value=!1,ve()}},Qe=async()=>{le.value=!0;try{const e={visibleItems:{...i.visibleItems,userManagement:!0,settings:!0},itemModes:{...i.itemModes,userManagement:"production",settings:"production"},itemProgress:{...i.itemProgress,userManagement:100,settings:100}},t=await f.post("/api/settings/set-nav-settings",e);t.data.success?(u.success("导航栏设置已保存"),i.visibleItems=e.visibleItems,i.itemModes=e.itemModes,i.itemProgress=e.itemProgress,$.value=JSON.parse(JSON.stringify(e)),window.dispatchEvent(new CustomEvent("navSettingsChanged",{detail:e}))):u.error(t.data.message||"保存导航栏设置失败")}catch{u.error("保存导航栏设置失败")}finally{le.value=!1}},Ke=()=>{se.confirm("确定要重置导航栏设置为默认值吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{i.visibleItems={home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},i.itemModes={home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},i.itemProgress={home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:100,settings:100,about:75},u.success("已重置为默认设置")})},Xe=async()=>{re.value=!0;try{const e=await f.post("/api/get_allow_register",{},{timeout:5e3});e.data&&typeof e.data.allow_register=="boolean"?w.value=e.data.allow_register:e.data&&typeof e.data.allow_register=="string"?w.value=e.data.allow_register==="true":w.value=!1}catch(e){console.error("获取注册开关状态失败:",e),w.value=!1}finally{re.value=!1,ve()}},Ze=async e=>{try{await f.post("/api/set_allow_register",{allow_register:e})}catch{w.value=!e}},ge=async()=>{ue.value=!0;try{const e=await f.post("/api/task/current/list",{});e.data.success?(de.value=e.data.data.tasks||[],Be.value={running_count:e.data.data.running_count||0,pending_count:e.data.data.pending_count||0}):u.error(e.data.message||"获取任务列表失败")}catch(e){console.error("获取任务列表失败:",e),u.error("获取任务列表失败")}finally{ue.value=!1}},et=e=>{if(!e)return"";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return e}},tt=()=>{O&&clearInterval(O),ge(),O=setInterval(()=>{ge()},5e3)},at=()=>{O&&(clearInterval(O),O=null)},st=async()=>{var e,t;if(!y.tableName){u.warning("请选择要导出的数据表");return}ce.value=!0;try{const n={table_name:y.tableName,date_range:y.dateRange},x=await f.post("/api/task/export-excel",n,{responseType:"blob"}),_=new Blob([x.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),G=window.URL.createObjectURL(_),m=document.createElement("a");m.href=G;const _e=new Date().toISOString().slice(0,19).replace(/:/g,"-");m.download=`${y.tableName}_${_e}.xlsx`,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(G),u.success("导出成功")}catch(n){console.error("导出失败:",n),(t=(e=n.response)==null?void 0:e.data)!=null&&t.message?u.error(n.response.data.message):u.error("导出失败")}finally{ce.value=!1}},lt=()=>{y.tableName="task_records_all",y.dateRange=null},ve=()=>{H.value=ne.value||ie.value||re.value},ot=async()=>{H.value=!0;const e=[Je().catch(console.error),Ge().catch(console.error),Xe().catch(console.error)];try{await Promise.allSettled(e)}catch(t){console.error("加载数据时发生错误:",t)}setTimeout(()=>{H.value=!1},100)};Z(()=>{ot(),tt()}),St(()=>{at(),W&&clearInterval(W)});const nt=async e=>{Y.value||e===b.value||(Y.value=!0,I.value=b.value,b.value=e,await new Promise(t=>setTimeout(t,300)),I.value="",Y.value=!1)},fe=d([{name:"default",displayName:"默认",description:"经典蓝紫渐变",gradient:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},{name:"blue",displayName:"蓝色",description:"专业商务风格",gradient:"linear-gradient(135deg, #4299e1 0%, #3182ce 100%)"},{name:"purple",displayName:"紫色",description:"优雅现代风格",gradient:"linear-gradient(135deg, #9f7aea 0%, #805ad5 100%)"},{name:"green",displayName:"绿色",description:"清新自然风格",gradient:"linear-gradient(135deg, #48bb78 0%, #38a169 100%)"},{name:"orange",displayName:"橙色",description:"活力温暖风格",gradient:"linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)"},{name:"red",displayName:"红色",description:"热情活力风格",gradient:"linear-gradient(135deg, #f56565 0%, #e53e3e 100%)"},{name:"pink",displayName:"粉色",description:"温柔浪漫风格",gradient:"linear-gradient(135deg, #f687b3 0%, #ed64a6 100%)"},{name:"indigo",displayName:"靛蓝",description:"科技感风格",gradient:"linear-gradient(135deg, #63b3ed 0%, #4299e1 100%)"},{name:"brown",displayName:"棕色",description:"复古怀旧风格",gradient:"linear-gradient(135deg, #795548 0%, #6b463f 100%)"},{name:"gray",displayName:"灰色",description:"中性稳重风格",gradient:"linear-gradient(135deg, #4a5568 0%, #2d3748 100%)"},{name:"dark",displayName:"深色",description:"适合暗光环境",gradient:"linear-gradient(135deg, #1a202c 0%, #2d3748 100%)"},{name:"light",displayName:"浅色",description:"适合明亮环境",gradient:"linear-gradient(135deg, #f3f3f3 0%, #e0e0e0 100%)"}]),k=d(localStorage.getItem("currentTheme")||"default"),it=e=>{k.value=e},J=e=>{const t=document.querySelector("#app");t&&(t.classList.forEach(n=>{n.startsWith("theme-")&&n!=="theme-initialized"&&t.classList.remove(n)}),t.classList.add(`theme-${e}`))},rt=async()=>{try{window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:k.value}})),localStorage.setItem("currentTheme",k.value);const e=await f.post("/api/set_default_theme",{theme:k.value});e.data&&e.data.success?u.success("主题已应用并保存"):u.warning("主题已应用，但保存失败")}catch{u.warning("主题已应用，但保存失败")}},dt=()=>{k.value="default",window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}})),localStorage.setItem("currentTheme","default");const e=document.querySelector("#app");e&&(e.classList.forEach(t=>{t.startsWith("theme-")&&t!=="theme-initialized"&&e.classList.remove(t)}),e.classList.add("theme-default")),u.success("已重置为默认主题")},ut=async()=>{try{const e=localStorage.getItem("currentTheme");e?fe.value.some(x=>x.name===e)?(k.value=e,J(e),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:e}}))):(k.value="default",J("default"),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}}))):(k.value="default",J("default"),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}})));const t=await f.post("/api/get_default_theme");if(t.data&&t.data.success&&t.data.theme){const n=t.data.theme;fe.value.some(_=>_.name===n)&&!e&&(k.value=n,J(n),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:n}})))}}catch(e){console.error("获取默认主题失败:",e),k.value="default",J("default"),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}}))}};Z(()=>{ut()});const R=d(!1),ct=async()=>{try{const e=await f.post("/api/get_fme_visibility");e.data&&typeof e.data.fme_visibility=="boolean"?R.value=e.data.fme_visibility:e.data&&typeof e.data.fme_visibility=="string"?R.value=e.data.fme_visibility==="true":R.value=!1}catch{R.value=!1}},mt=async e=>{try{await f.post("/api/set_fme_visibility",{fme_visibility:e}),u.success("设置已保存，重启后端服务后生效")}catch{u.error("保存失败"),R.value=!e}};Z(()=>{ct()});const pt=(e,t)=>{i.itemProgress[e]=Math.max(0,Math.min(100,t))};K(()=>i.itemModes,e=>{Object.entries(e).forEach(([t,n])=>{n==="production"?i.itemProgress[t]=100:n==="development"&&i.itemProgress[t]===100&&(i.itemProgress[t]=75)})},{deep:!0});const c=Q({data:[],columns:[{prop:"task_id",label:"任务ID",minWidth:120},{prop:"tool_name",label:"工具名称",minWidth:120},{prop:"project",label:"项目",minWidth:100},{prop:"submitter",label:"提交者",minWidth:100},{prop:"submit_time",label:"提交时间",minWidth:160},{prop:"task_name",label:"任务名称",minWidth:120},{prop:"status",label:"状态",minWidth:80},{prop:"time_consuming",label:"运行时间(秒)",minWidth:100},{prop:"file_size",label:"文件大小",minWidth:100},{prop:"file_name",label:"文件名",minWidth:150},{prop:"up_nums",label:"文件数量",minWidth:80}],loading:!1,pagination:{total:0,page:1,page_size:10,total_pages:1}}),j=async()=>{c.loading=!0;try{const e={table_name:y.tableName,date_range:y.dateRange,page:c.pagination.page,page_size:c.pagination.page_size},t=await f.post("/api/task/records",e);if(t.data.success){if(c.data=t.data.data.records,c.pagination=t.data.data.pagination,t.data.data.columns){const n={任务ID:"task_id",工具名称:"tool_name",项目:"project",提交者:"submitter",提交时间:"submit_time",任务名称:"task_name",状态:"status","运行时间(秒)":"time_consuming",文件大小:"file_size",文件名:"file_name",文件数量:"up_nums"};c.columns=t.data.data.columns.map(x=>({label:x,prop:n[x]||x,minWidth:100}))}}else c.data=[],c.pagination.total=0}catch{c.data=[],c.pagination.total=0}finally{c.loading=!1}},gt=e=>{c.pagination.page=e,j()},vt=e=>{c.pagination.page_size=e,c.pagination.page=1,j()},ft=()=>{c.pagination.page=1,j()};return K(()=>y.tableName,()=>{c.pagination.page=1,j()}),Z(()=>{j()}),(e,t)=>{const n=p("el-icon"),x=p("el-skeleton"),_=p("el-button"),G=p("el-empty"),m=p("el-table-column"),_e=p("el-tag"),he=p("el-table"),A=p("el-option"),ye=p("el-select"),T=p("el-form-item"),_t=p("el-date-picker"),ht=p("el-pagination"),be=p("el-form"),we=p("el-input-number"),Ve=p("el-radio"),yt=p("el-radio-group"),bt=p("el-input"),wt=p("el-alert"),ke=p("el-switch"),kt=p("el-dialog"),Ct=Pt("loading");return g(),C("div",Ut,[o("div",Ot,[o("div",Lt,[(g(),C(ee,null,te(Ue,s=>o("div",{key:s.key,class:M(["nav-item",{active:b.value===s.key,disabled:Y.value}]),onClick:V=>nt(s.key)},[a(n,null,{default:l(()=>[(g(),P(Vt(ze(s.key))))]),_:2},1024),o("span",Bt,E(s.label),1)],10,qt)),64))])]),o("div",Wt,[H.value?(g(),C("div",Jt,[a(x,{rows:6,animated:""})])):(g(),C("div",jt,[o("div",{class:M(["settings-section",{active:b.value==="monitor",prev:I.value==="monitor"}])},[o("div",At,[o("div",Ft,[a(n,{class:"section-icon"},{default:l(()=>[a(v(Me))]),_:1}),t[10]||(t[10]=o("span",null,"实时任务状态",-1))]),a(_,{type:"primary",size:"small",onClick:ge,loading:ue.value},{default:l(()=>[a(n,null,{default:l(()=>[a(v(F))]),_:1}),t[11]||(t[11]=h(" 刷新 "))]),_:1},8,["loading"])]),o("div",Yt,[de.value.length===0?(g(),C("div",$t,[a(G,{description:"暂无任务"})])):(g(),P(he,{key:1,data:de.value,border:"",style:{width:"100%"}},{default:l(()=>[a(m,{prop:"task_id",label:"任务ID",width:"180"}),a(m,{label:"状态",width:"100"},{default:l(s=>[a(_e,{type:s.row.status==="running"?"success":"warning",size:"small"},{default:l(()=>[h(E(s.row.status==="running"?"运行中":"等待中"),1)]),_:2},1032,["type"])]),_:1}),a(m,{prop:"tool_name",label:"工具名称","min-width":"120"}),a(m,{prop:"submitter",label:"提交者",width:"100"}),a(m,{prop:"project",label:"项目",width:"120"}),a(m,{prop:"file_name",label:"文件名","min-width":"150","show-overflow-tooltip":""}),a(m,{prop:"up_nums",label:"文件数",width:"80",align:"center"}),a(m,{label:"提交时间",width:"160"},{default:l(s=>[h(E(et(s.row.submit_time)),1)]),_:1})]),_:1},8,["data"]))])],2),o("div",{class:M(["settings-section",{active:b.value==="data",prev:I.value==="data"}])},[o("div",Ht,[o("div",Gt,[a(n,{class:"section-icon"},{default:l(()=>[a(v(Pe))]),_:1}),t[12]||(t[12]=o("span",null,"运行记录导出",-1))])]),a(be,{"label-width":"120px",class:"export-form",style:{width:"100%","max-width":"none",background:"transparent",padding:"0"}},{default:l(()=>[a(T,{label:"选择数据表"},{default:l(()=>[a(ye,{modelValue:y.tableName,"onUpdate:modelValue":t[0]||(t[0]=s=>y.tableName=s),placeholder:"请选择要导出的数据表",style:{"max-width":"240px",width:"100%"}},{default:l(()=>[a(A,{label:"任务记录总表",value:"task_records_all"}),a(A,{label:"当前任务记录",value:"task_records"})]),_:1},8,["modelValue"])]),_:1}),a(T,{label:"日期范围"},{default:l(()=>[a(_t,{modelValue:y.dateRange,"onUpdate:modelValue":t[1]||(t[1]=s=>y.dateRange=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",shortcuts:We,onChange:ft,style:{"max-width":"320px",width:"100%"}},null,8,["modelValue"])]),_:1}),a(T,{"label-width":"0",style:{width:"100%","margin-bottom":"0"}},{default:l(()=>[o("div",Qt,[Mt((g(),P(he,{data:c.data,border:"",style:{width:"100%"},"show-overflow-tooltip":!0},{default:l(()=>[(g(!0),C(ee,null,te(c.columns,s=>(g(),P(m,{key:s.prop,prop:s.prop,label:s.label,"min-width":s.minWidth||100,"show-overflow-tooltip":""},null,8,["prop","label","min-width"]))),128))]),_:1},8,["data"])),[[Ct,c.loading]]),a(ht,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:c.pagination.total,"page-size":c.pagination.page_size,"current-page":c.pagination.page,"page-sizes":[10,20,50,100],onSizeChange:vt,onCurrentChange:gt,style:{"margin-top":"8px"}},null,8,["total","page-size","current-page"])])]),_:1}),a(T,{"label-width":"0"},{default:l(()=>[o("div",Kt,[a(_,{type:"primary",onClick:st,loading:ce.value},{default:l(()=>[a(n,null,{default:l(()=>[a(v(Pe))]),_:1}),t[13]||(t[13]=h(" 导出Excel "))]),_:1},8,["loading"]),a(_,{onClick:lt},{default:l(()=>[a(n,null,{default:l(()=>[a(v(F))]),_:1}),t[14]||(t[14]=h(" 重置 "))]),_:1})])]),_:1})]),_:1})],2),o("div",{class:M(["settings-section",{active:b.value==="config",prev:I.value==="config"}])},[o("div",Xt,[o("div",Zt,[a(n,{class:"section-icon"},{default:l(()=>[a(v(Ie))]),_:1}),t[15]||(t[15]=o("span",null,"服务器参数配置",-1))])]),a(be,{"label-width":"120px",model:r,ref_key:"formRef",ref:Le,class:"settings-form"},{default:l(()=>[a(T,{label:"最大并发任务数"},{default:l(()=>[a(we,{modelValue:r.limits,"onUpdate:modelValue":t[2]||(t[2]=s=>r.limits=s),min:1,max:8},null,8,["modelValue"])]),_:1}),a(T,{label:"运行模式"},{default:l(()=>[a(yt,{modelValue:r.mode,"onUpdate:modelValue":t[3]||(t[3]=s=>r.mode=s),onChange:je},{default:l(()=>[a(Ve,{label:"offline"},{default:l(()=>t[16]||(t[16]=[h("离线（本地127.0.0.1）")])),_:1}),a(Ve,{label:"online"},{default:l(()=>t[17]||(t[17]=[h("在线（局域网/公网）")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(T,{label:"IP"},{default:l(()=>[r.mode==="online"?(g(),P(ye,{key:0,modelValue:r.server_host,"onUpdate:modelValue":t[4]||(t[4]=s=>r.server_host=s),filterable:"",placeholder:"请选择IP"},{default:l(()=>[(g(!0),C(ee,null,te(z.value,s=>(g(),P(A,{key:s,label:s,value:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):(g(),P(bt,{key:1,modelValue:r.server_host,"onUpdate:modelValue":t[5]||(t[5]=s=>r.server_host=s),disabled:""},null,8,["modelValue"]))]),_:1}),a(T,{label:"端口"},{default:l(()=>[o("div",ea,[a(we,{modelValue:r.server_port,"onUpdate:modelValue":t[6]||(t[6]=s=>r.server_port=s),min:1024,max:65535},null,8,["modelValue"]),a(_,{onClick:He,loading:oe.value},{default:l(()=>t[18]||(t[18]=[h("检测端口占用")])),_:1},8,["loading"])]),pe.value?(g(),C("div",ta,[a(wt,{type:D.value?N.value?"success":"error":"warning",title:Ae.value,closable:!1,"show-icon":""},null,8,["type","title"])])):Ne("",!0)]),_:1}),a(T,{label:"后端运行任务是否隐藏进程窗口"},{default:l(()=>[a(ke,{modelValue:R.value,"onUpdate:modelValue":t[7]||(t[7]=s=>R.value=s),"active-text":"隐藏（推荐生产环境）","inactive-text":"显示（推荐调试）",disabled:U.value,onChange:mt},null,8,["modelValue","disabled"]),t[19]||(t[19]=o("div",{class:"form-item-tip"},"更改后需重启后端服务才能生效。",-1))]),_:1}),a(T,null,{default:l(()=>[o("div",aa,[a(_,{type:"primary",onClick:Ye,loading:U.value,disabled:!Fe.value},{default:l(()=>[a(n,null,{default:l(()=>[a(v(ae))]),_:1}),t[20]||(t[20]=h(" 保存设置 "))]),_:1},8,["loading","disabled"]),a(_,{type:"danger",onClick:$e,loading:L.value},{default:l(()=>[a(n,null,{default:l(()=>[a(v(F))]),_:1}),t[21]||(t[21]=h(" 重启服务器 "))]),_:1},8,["loading"])])]),_:1})]),_:1},8,["model"])],2),o("div",{class:M(["settings-section theme-section",{active:b.value==="theme",prev:I.value==="theme"}])},[o("div",sa,[o("div",la,[a(n,{class:"section-icon"},{default:l(()=>[a(v(Ee))]),_:1}),t[22]||(t[22]=o("span",null,"主题配置",-1))])]),o("div",oa,[o("div",na,[(g(!0),C(ee,null,te(fe.value,s=>(g(),C("div",{key:s.name,class:M(["theme-item",{active:k.value===s.name}]),onClick:V=>it(s.name)},[o("div",{class:"theme-preview",style:It({background:s.gradient})},null,4),o("div",ra,[o("div",da,E(s.displayName),1),o("div",ua,E(s.description),1)])],10,ia))),128))]),o("div",ca,[a(_,{type:"primary",onClick:rt},{default:l(()=>[a(n,null,{default:l(()=>[a(v(ae))]),_:1}),t[23]||(t[23]=h(" 应用主题 "))]),_:1}),a(_,{onClick:dt},{default:l(()=>[a(n,null,{default:l(()=>[a(v(F))]),_:1}),t[24]||(t[24]=h(" 重置默认 "))]),_:1})])])],2),o("div",{class:M(["settings-section",{active:b.value==="ui",prev:I.value==="ui"}])},[o("div",ma,[o("div",pa,[a(n,{class:"section-icon"},{default:l(()=>[a(v(De))]),_:1}),t[25]||(t[25]=o("span",null,"导航菜单配置",-1))])]),a(he,{data:Oe,border:"",class:"nav-table"},{default:l(()=>[a(m,{label:"显示",width:"80",align:"center"},{default:l(s=>[a(ke,{modelValue:i.visibleItems[s.row.key],"onUpdate:modelValue":V=>i.visibleItems[s.row.key]=V,disabled:s.row.key==="userManagement"||s.row.key==="settings"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(m,{prop:"label",label:"菜单项","min-width":"120"}),a(m,{prop:"description",label:"说明","min-width":"180"}),a(m,{label:"模式",width:"120"},{default:l(s=>[a(ye,{modelValue:i.itemModes[s.row.key],"onUpdate:modelValue":V=>i.itemModes[s.row.key]=V,size:"small",disabled:s.row.key==="userManagement"||s.row.key==="settings"},{default:l(()=>[a(A,{label:"生产",value:"production"}),a(A,{label:"开发",value:"development"})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(m,{label:"开发进度",width:"180"},{default:l(s=>[i.itemModes[s.row.key]==="development"?(g(),C("div",ga,[a(we,{modelValue:i.itemProgress[s.row.key],"onUpdate:modelValue":V=>i.itemProgress[s.row.key]=V,min:0,max:100,step:5,size:"small",disabled:s.row.key==="userManagement"||s.row.key==="settings",onChange:V=>pt(s.row.key,V)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])])):Ne("",!0)]),_:1})]),_:1}),o("div",va,[a(_,{type:"primary",onClick:Qe,loading:le.value},{default:l(()=>[a(n,null,{default:l(()=>[a(v(ae))]),_:1}),t[26]||(t[26]=h(" 保存设置 "))]),_:1},8,["loading"]),a(_,{onClick:Ke},{default:l(()=>[a(n,null,{default:l(()=>[a(v(F))]),_:1}),t[27]||(t[27]=h(" 重置默认 "))]),_:1})])],2),o("div",{class:M(["settings-section",{active:b.value==="user",prev:I.value==="user"}])},[o("div",fa,[o("div",_a,[a(n,{class:"section-icon"},{default:l(()=>[a(v(Re))]),_:1}),t[28]||(t[28]=o("span",null,"用户注册控制",-1))])]),a(be,{"label-width":"120px",class:"settings-form"},{default:l(()=>[o("div",ha,[a(ke,{modelValue:w.value,"onUpdate:modelValue":t[8]||(t[8]=s=>w.value=s),"active-text":"允许","inactive-text":"禁止",onChange:Ze},null,8,["modelValue"]),o("div",{class:M(["status-indicator",{"is-active":w.value}])},[w.value?(g(),P(n,{key:0},{default:l(()=>[a(v(ae))]),_:1})):(g(),P(n,{key:1},{default:l(()=>[a(v(Nt))]),_:1})),o("span",ya,E(w.value?"当前允许新用户注册":"当前禁止新用户注册"),1)],2)]),o("div",ba,E(w.value?"开放注册后，新用户可以自行注册账号。建议在需要时再开放注册。":"关闭注册后，只有管理员可以创建新用户账号。"),1)]),_:1})],2)]))]),a(kt,{modelValue:q.value,"onUpdate:modelValue":t[9]||(t[9]=s=>q.value=s),"show-close":!1,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"400px",class:"restart-dialog"},{header:l(()=>[o("div",wa,[a(n,{class:"restart-icon"},{default:l(()=>[a(v(Et))]),_:1}),o("span",null,"服务器重启中"+E(B.value),1)])]),default:l(()=>[t[29]||(t[29]=o("div",{class:"restart-content"}," 请勿关闭页面，重启完成后将自动跳转... ",-1))]),_:1},8,["modelValue"])])}}}),Sa=zt(ka,[["__scopeId","data-v-2f2ceab0"]]);export{Sa as default};
