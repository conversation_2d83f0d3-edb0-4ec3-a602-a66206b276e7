2025-05-08 13:36:37|   0.0|  0.0|INFORM|Current FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|System Time: 20250508053637 UTC
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Workspace was last saved in FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME_HOME is 'C:\Program Files\FME\'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME ESRI ArcGIS Server Edition (floating)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Permanent License.
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Machine host name is: DESKTOP-9BLU554
2025-05-08 13:36:37|   0.0|  0.0|INFORM|OS Locale Name     : zh_CN
2025-05-08 13:36:37|   0.0|  0.0|INFORM|OS Locale Encoding : GBK
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Process Encoding   : UTF-8
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME API version: '4.0 20230426'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: FME_BASE is 'no'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: FME_MF_DIR is 'E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\models\KqZ01b7kEGOtJcDGRD24v4bmKSHSDoSr2a5InyYd/'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: FME_MF_NAME is '合并shp.fmw'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: FME_PRODUCT_NAME is 'FME(R) 2023.1.0.0'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Operating System: Microsoft Windows 10 64-bit  (Build 19045)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Platform: WIN64
2025-05-08 13:36:37|   0.0|  0.0|INFORM|System Status: 196.58 GB of disk space available in the FME temporary folder (C:\Users\<USER>\AppData\Local\Temp)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|System Status: 15.74 GB of physical memory available
2025-05-08 13:36:37|   0.0|  0.0|INFORM|System Status: 62.96 GB of virtual memory available
2025-05-08 13:36:37|   0.0|  0.0|INFORM|START - ProcessID: 16380, peak process memory usage: 45032 kB, current process memory usage: 45032 kB
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Command line arguments are `C:\Program Files\FME\fme.exe' `E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\models\KqZ01b7kEGOtJcDGRD24v4bmKSHSDoSr2a5InyYd\合并shp.fmw' `--dir' `E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\temp\29JZUBIc9pHHDAThj9xCCv6Xq5HAHTHu8tGdrHup\待合并 (4)' `--PARAMETER' `1' `--dir_2' `E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\models\KqZ01b7kEGOtJcDGRD24v4bmKSHSDoSr2a5InyYd\output\WmROslnAUUbeO7qLyDyqcam6JnD0uRQoU3MU0VkU'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Connection Storage: 'C:\Users\<USER>\AppData\Roaming\Safe Software\FME\'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Shared folders for formats are : C:\Program Files\FME\datasources;C:\Users\<USER>\Documents\FME\Formats
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Shared folders for transformers are : C:\Users\<USER>\AppData\Roaming\Safe Software\FME\Packages\23619-win64\transformers;C:\Program Files\FME\transformers;C:\Users\<USER>\AppData\Roaming\Safe Software\FME\FME Store\Transformers
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Shared folders for coordinate systems are : C:\Users\<USER>\Documents\FME\CoordinateSystems
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Shared folders for coordinate system exceptions are : C:\Users\<USER>\Documents\FME\CoordinateSystemExceptions
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Shared folders for coordinate system grid overrides are : C:\Users\<USER>\Documents\FME\CoordinateSystemGridOverrides
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Shared folders for CS-MAP transformation exceptions are : C:\Users\<USER>\Documents\FME\CsmapTransformationExceptions
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Shared folders for transformer categories are : C:\Users\<USER>\Documents\FME\TransformerCategories
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Reader Keyword is `MULTI_READER'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Writer Keyword is `MULTI_DEST'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Writer Group Definition Keyword is `MULTI_DEST_DEF'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Reader type is `MULTI_READER'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Writer type is `MULTI_WRITER'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Current working folder is `E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Temporary folder is `C:\Users\<USER>\AppData\Local\Temp', set from environment variable `TEMP'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Cache folder is 'C:\Users\<USER>\AppData\Local\Temp'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: FME_HOME is `C:\Program Files\FME\'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Start freeing memory when the process exceeds 47.22 GB
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Stop freeing memory when the process is below 35.42 GB
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Creating writer for format: 
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Creating reader for format: 
2025-05-08 13:36:37|   0.0|  0.0|INFORM|MULTI_READER(MULTI_READER): Will fail with first member reader failure
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Using Multi Reader with keyword `MULTI_READER' to read multiple datasets
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Using MultiWriter with keyword `MULTI_DEST' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Loaded module 'Geometry_func' from file 'C:\Program Files\FME\plugins/Geometry_func.dll'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME API version of module 'Geometry_func' matches current internal version (4.0 20230426)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Loaded module 'QueryFactory' from file 'C:\Program Files\FME\plugins/QueryFactory.dll'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME API version of module 'QueryFactory' matches current internal version (4.0 20230426)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Loaded module 'GeometryFilterFactory' from file 'C:\Program Files\FME\plugins/GeometryFilterFactory.dll'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME API version of module 'GeometryFilterFactory' matches current internal version (4.0 20230426)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME Configuration: Using FME Reprojection Engine
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Emptying factory pipeline
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Loaded module 'PATH' from file 'C:\Program Files\FME\plugins/PATH.dll'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|FME API version of module 'PATH' matches current internal version (4.0 20230426)
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Performing query against PATH dataset `E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\temp\29JZUBIc9pHHDAThj9xCCv6Xq5HAHTHu8tGdrHup\待合并 (4)'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Path Reader: Opening the PATH Reader on folder 'E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\temp\29JZUBIc9pHHDAThj9xCCv6Xq5HAHTHu8tGdrHup\待合并 (4)'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Path Reader: Using Glob Pattern '*'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Path Reader: Allowed Path Type set to 'ANY'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Path Reader: Recurse into subdirectories 'true'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Path Reader: Hidden Files and Folders set to 'INCLUDE'
2025-05-08 13:36:37|   0.0|  0.0|INFORM|Path Reader: Retrieve file properties 'false'
2025-05-08 13:36:37|   0.1|  0.1|INFORM|Creating reader for format: Esri Shapefile
2025-05-08 13:36:37|   0.1|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `SHAPEFILE'
2025-05-08 13:36:37|   0.1|  0.0|INFORM|Loaded module 'SHAPEFILE' from file 'C:\Program Files\FME\plugins/shapefile_dbase.dll'
2025-05-08 13:36:37|   0.1|  0.0|INFORM|FME API version of module 'shapefile_dbase' matches current internal version (4.0 20230426)
2025-05-08 13:36:37|   0.1|  0.0|INFORM|Performing query against SHAPEFILE dataset `E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\temp\29JZUBIc9pHHDAThj9xCCv6Xq5HAHTHu8tGdrHup\待合并 (4)\GBGD32058210001402022062023年杨舍镇高标准农田改造提升项目.shp'
2025-05-08 13:36:37|   0.1|  0.0|INFORM|Creating reader for format: Esri Shapefile
2025-05-08 13:36:37|   0.1|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `SHAPEFILE'
2025-05-08 13:36:37|   0.1|  0.0|INFORM|SHAPEFILE reader: Opening dataset 'E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\temp\29JZUBIc9pHHDAThj9xCCv6Xq5HAHTHu8tGdrHup\待合并 (4)\GBGD32058210001402022062023年杨舍镇高标准农田改造提升项目.shp'
2025-05-08 13:36:37|   0.4|  0.3|INFORM|The OGC definition of the FME coordinate system '_CGCS2000/GK3d-40_FME_0' is 'PROJCS["CGCS2000_3_Degree_GK_Zone_40",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Transverse_Mercator"],PARAMETER["false_easting",40500000.0],PARAMETER["false_northing",0.0],PARAMETER["central_meridian",120.0],PARAMETER["scale_factor",1.0],PARAMETER["latitude_of_origin",0.0],UNIT["Meter",1.0],AUTHORITY["EPSG",4528]]'
2025-05-08 13:36:37|   0.4|  0.0|INFORM|Shapefile reader: Reading dataset with encoding 'UTF-8' as determined by 'Codepage File'
2025-05-08 13:36:37|   0.4|  0.0|INFORM|FME Configuration: Source coordinate system for reader R_4[SHAPEFILE] set to `_CGCS2000/GK3d-40_FME_0' as read from input data
2025-05-08 13:36:37|   0.4|  0.0|INFORM|Coordinate System `_CGCS2000/GK3d-40_FME_0' parameters: CS_NAME=`_CGCS2000/GK3d-40_FME_0' CS_ORIGINAL_NAME=`CGCS2000/GK3d-40_FME' DESC_NM=`CGCS 2000 / 3-degree Gauss-Kruger zone 40' DT_NAME=`China_2000_FME' ESRI_WKT=`PROJCS["CGCS2000_3_Degree_GK_Zone_40",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Transverse_Mercator"],PARAMETER["false_easting",40500000.0],PARAMETER["false_northing",0.0],PARAMETER["central_meridian",120.0],PARAMETER["scale_factor",1.0],PARAMETER["latitude_of_origin",0.0],UNIT["Meter",1.0],AUTHORITY["EPSG",4528]]' GROUP=`ASIA' MAP_SCL=`1' MAX_LAT=`53.330000000000005' MAX_LNG=`121.5' MIN_LAT=`24.43' MIN_LNG=`118.5' PARM1=`120' PROJ=`TM' QUAD=`1' SCL_RED=`1' SOURCE=`EPSG, V9.2.2, 4528' UNIT=`Meter' X_OFF=`40500000' ZERO_X=`0.0001' ZERO_Y=`0.0001'
2025-05-08 13:36:37|   0.4|  0.0|INFORM|GeometryFilter (GeometryFilterFactory): Splitting bulk features into individual features
2025-05-08 13:36:37|   0.4|  0.0|INFORM|Creating writer for format: 
2025-05-08 13:36:37|   0.4|  0.0|INFORM|Using MultiWriter with keyword `FeatureWriter' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-08 13:36:37|   0.5|  0.1|WARN  |SHAPEFILE Writer: Renamed user attribute 'fme_feature_type' to 'fme_featur' based on format constraints for invalid characters, length, and case
2025-05-08 13:36:37|   0.5|  0.0|WARN  |SHAPEFILE Writer: Renamed user attribute 'fme_geometry' to 'fme_geomet' based on format constraints for invalid characters, length, and case
2025-05-08 13:36:37|   0.5|  0.0|WARN  |SHAPEFILE Writer: Renamed user attribute 'multi_writer_id' to 'multi_writ' based on format constraints for invalid characters, length, and case
2025-05-08 13:36:37|   0.5|  0.0|WARN  |SHAPEFILE Writer: Renamed user attribute 'shapefile_type' to 'shapefile_' based on format constraints for invalid characters, length, and case
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Creating writer for format: Esri Shapefile
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Trying to find a DYNAMIC plugin for writer named `SHAPEFILE'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Writer `FeatureWriter_0' of type `SHAPEFILE' using group definition keyword `FeatureWriter_0_DEF'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|SHAPEFILE writer: Writing files using character encoding 'utf-8'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Shapefile Writer: Feature Type '合并后' set to geometry type 'shapefile_polygon'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|FME Configuration: Destination coordinate system set to input coordinate system `EPSG:4528'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Shapefile Writer: Automatically detected 'polygon_2D' Shape type for feature type '合并后'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Shapefile Writer: Detected coordinate system 'EPSG:4528' on feature type '合并后'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Creating reader for format: Esri Shapefile
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `SHAPEFILE'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Performing query against SHAPEFILE dataset `E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\temp\29JZUBIc9pHHDAThj9xCCv6Xq5HAHTHu8tGdrHup\待合并 (4)\GBZ32058210001402022062023年杨舍镇高标准农田改造提升项目.shp'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Creating reader for format: Esri Shapefile
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `SHAPEFILE'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|SHAPEFILE reader: Opening dataset 'E:\备份\vue3\202405\05081035（引入异步任务管理前的最后一个版本）\frontend\backend\temp\29JZUBIc9pHHDAThj9xCCv6Xq5HAHTHu8tGdrHup\待合并 (4)\GBZ32058210001402022062023年杨舍镇高标准农田改造提升项目.shp'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|The OGC definition of the FME coordinate system '_CGCS2000/GK3d-40_FME_1' is 'PROJCS["CGCS2000_3_Degree_GK_Zone_40",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",40500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",120.0],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]]'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Shapefile reader: Reading dataset with encoding 'UTF-8' as determined by 'Codepage File'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|FME Configuration: Source coordinate system for reader R_6[SHAPEFILE] set to `_CGCS2000/GK3d-40_FME_1' as read from input data
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Coordinate System `_CGCS2000/GK3d-40_FME_1' parameters: CS_NAME=`_CGCS2000/GK3d-40_FME_1' CS_ORIGINAL_NAME=`CGCS2000/GK3d-40_FME' DESC_NM=`CGCS 2000 / 3-degree Gauss-Kruger zone 40' DT_NAME=`China_2000_FME' ESRI_WKT=`PROJCS["CGCS2000_3_Degree_GK_Zone_40",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",40500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",120.0],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]]' GROUP=`ASIA' MAP_SCL=`1' MAX_LAT=`53.330000000000005' MAX_LNG=`121.5' MIN_LAT=`24.43' MIN_LNG=`118.5' PARM1=`120' PROJ=`TM' QUAD=`1' SCL_RED=`1' SOURCE=`EPSG, V9.2.2, 4528' UNIT=`Meter' X_OFF=`40500000' ZERO_X=`0.0001' ZERO_Y=`0.0001'
2025-05-08 13:36:37|   0.5|  0.0|INFORM|GeometryFilter (GeometryFilterFactory): Splitting bulk features into individual features
2025-05-08 13:36:37|   0.5|  0.0|STATS |Creator_XML_Creator (CreationFactory): Created 1 features
2025-05-08 13:36:37|   0.5|  0.0|STATS |Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |Creator_CREATED Brancher -1 4 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-08 13:36:37|   0.5|  0.0|STATS |_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Path Reader: Closing the PATH Reader
2025-05-08 13:36:37|   0.5|  0.0|STATS |Tester (TestFactory): Tested 15 input feature(s) -- 2 feature(s) passed and 13 feature(s) failed
2025-05-08 13:36:37|   0.5|  0.0|STATS |GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |Reprojector (TeeFactory): Cloned 567 input feature(s) into 567 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |Tester_2 (TestFactory): Tested 567 input feature(s) -- 0 feature(s) passed and 567 feature(s) failed
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaCalculator_AreaCalculatorInput (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaCalculator_AreaCalculator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaCalculator_Rejector (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaCalculator_2_AreaCalculatorInput (TeeFactory): Cloned 567 input feature(s) into 567 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaCalculator_2_AreaCalculator (TeeFactory): Cloned 567 input feature(s) into 567 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |AreaCalculator_2_Rejector (TestFactory): Tested 567 input feature(s) -- 0 feature(s) passed and 567 feature(s) failed
2025-05-08 13:36:37|   0.5|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Feature output statistics for `SHAPEFILE' writer using keyword `FeatureWriter_0':
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |                               Features Written
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |合并后                                                                  567
2025-05-08 13:36:37|   0.5|  0.0|STATS |==============================================================================
2025-05-08 13:36:37|   0.5|  0.0|STATS |Total Features Written                                                     567
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |Destination Feature Type Routing Correlator (RoutingFactory): Tested 0 input feature(s), wrote 0 output feature(s): 0 matched merge filters, 0 were routed to output, 0 could not be routed.
2025-05-08 13:36:37|   0.5|  0.0|STATS |Final Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |                            Features Read Summary
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |==============================================================================
2025-05-08 13:36:37|   0.5|  0.0|STATS |Total Features Read                                                          0
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |                           Features Written Summary
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|STATS |==============================================================================
2025-05-08 13:36:37|   0.5|  0.0|STATS |Total Features Written                                                       0
2025-05-08 13:36:37|   0.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-08 13:36:37|   0.5|  0.0|INFORM|Translation was SUCCESSFUL with 4 warning(s) (0 feature(s) output)
2025-05-08 13:36:37|   0.5|  0.0|INFORM|FME Session Duration: 0.6 seconds. (CPU: 0.3s user, 0.2s system)
2025-05-08 13:36:37|   0.5|  0.0|INFORM|END - ProcessID: 16380, peak process memory usage: 106412 kB, current process memory usage: 105320 kB
