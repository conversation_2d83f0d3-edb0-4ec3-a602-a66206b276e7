import{d as ul,u as il,r as _,M as Oe,O as Re,a as ia,o as pa,c as _e,e as t,w as o,f as V,i as P,E as i,Y as da,P as ge,Z as Qe,b as g,m as R,V as Be,A as y,B as ca,C as I,n as je,F as B,s as Ne,$ as fa,a0 as ma,a1 as va,a2 as pl,Q as dl,a3 as _a,R as cl,W as fl,H as ga,I as ya,a4 as Ze,T as ye,a5 as ml,l as A,a6 as ne,_ as vl}from"./index-CdoJMuEX.js";import{f as _l}from"./format-CBpsKyOP.js";const gl={class:"tools-container"},yl={class:"tab-content tab-applications"},hl={class:"search-section"},bl={class:"search-row"},wl={class:"search-buttons"},Cl={class:"table-container"},zl={class:"pagination-container"},kl={class:"tab-content tab-tools"},$l={class:"search-section"},xl={class:"search-row"},Ul={class:"search-buttons"},Vl={class:"table-header"},Tl={class:"table-container"},Al={class:"pagination-container"},Rl={key:0,class:"message-content"},Sl={key:6,class:"color-picker-wrapper"},Dl={class:"color-value"},Pl={key:1,class:"no-params"},Ll={class:"dialog-footer"},Il={class:"dialog-footer"},Ml={class:"dialog-footer"},ql={class:"pagination-container"},El={class:"error-log-content"},Fl={class:"dialog-footer"},Ol=5e3,Bl=ul({__name:"ToolsView",setup(jl){const z=il(),q=_(!1),Se=_([]),De=_([]),he=_(""),Pe=a=>window.location.pathname.startsWith("/gsi/")?`/gsi${a}`:`${P.defaults.baseURL}${a}`,Je=a=>window.location.pathname.startsWith("/gsi/")?`${window.location.protocol}//${window.location.host}/gsi${a}`:`${window.location.protocol}//${window.location.host}${a}`,be=_(""),G=_("applications"),we=_(!1),Ce=_(!1),ue=_(1),ee=_(1),ie=_(1),ze=_(10),ke=_(10),ae=_(0),ha=_(0),E=_(localStorage.getItem("token")),j={PARSE_FMW:"/api/parse_fmw",RUN_TOOL:"/api/run_fme",UPDATE_TOOL:"/api/tools/update",UPLOAD_FILE:"/api/upload",UPLOAD_TOOL:"/api/tools/upload",MY_APPLICATIONS:"/api/tools/my-applications",RUN_RECORDS:"/api/tools/run_records",DELETE_RESULT:"/api/tools/delete_result",DOWNLOAD_RESULT:"/api/tools/download_result",UPDATE_COUNT:"/api/tools/update_usacount",VERIFY_RUN_PERMISSION:"/api/tools/verify_run_permission"},Le=()=>({Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}),Ke=Oe(()=>Se.value.filter(a=>a.fmw_name.toLowerCase().includes(he.value.toLowerCase())||a.project.toLowerCase().includes(he.value.toLowerCase()))),ea=Oe(()=>De.value.filter(a=>{const e=(a.status||"").toLowerCase();return e==="已通过"||e==="approved"?a.fmw_name.toLowerCase().includes(be.value.toLowerCase())||a.project.toLowerCase().includes(be.value.toLowerCase()):!1})),ba=Oe(()=>{const a=(ie.value-1)*ze.value,e=a+ze.value;return Ke.value.slice(a,e)}),wa=Oe(()=>{const a=(ee.value-1)*ke.value,e=a+ke.value;return ea.value.slice(a,e)}),Ye=()=>{ue.value=1},He=()=>{ee.value=1},Ca=()=>{he.value="",ie.value=1},za=()=>{be.value="",ee.value=1},ka=a=>{ee.value=a},Z=async()=>{if(!we.value){we.value=!0,q.value=!0;try{const a=await P.get("/api/tools/list",{params:{username:z.user.username,my_tools:"true"},headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});a.data.success&&(Se.value=a.data.data||[],ha.value=Se.value.length)}catch{i.error("获取工具列表失败，请检查网络连接")}finally{q.value=!1,we.value=!1}}},pe=async()=>{if(!Ce.value){Ce.value=!0,q.value=!0;try{const a=await P.get(j.MY_APPLICATIONS,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username},params:{source:"tools"}});a.data.success&&(De.value=a.data.data||[])}catch{i.error("获取申请列表失败，请检查网络连接")}finally{q.value=!1,Ce.value=!1}}},J=(a,e="yyyy-MM-dd HH:mm")=>{if(!a)return"--";try{return _l(new Date(a),e)}catch{return"--"}};Re(()=>z.user,a=>{a!=null&&a.username?Z():(Se.value=[],De.value=[])},{immediate:!0});const le=_(!1),k=_(null),Q=_([]),m=_({}),F=_(null),$e=_({}),Ie=_(!1),Xe=_(!1),$a=async a=>{var e,u,p,c,d;try{if(!a||!a.fmw_id){i.error("工具信息不完整");return}Ie.value=!1,Xe.value=!0,k.value={...a,isApplyRun:!1,isRun:!0},q.value=!0,console.log("开始解析工具参数:",{fmw_id:a.fmw_id,username:z.user.username});const n=await P.post(j.PARSE_FMW,{fmw_id:a.fmw_id},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});console.log("解析参数响应:",n.data),console.log("后端返回的原始参数:",n.data.data.parameters);const w=(n.data.data.def_params||"N")==="Y",N=[],W=n.data.data.parameters,me=n.data.data.parameters_dict,S={},re={};(!Array.isArray(W)||W.length===0)&&(console.warn("未找到任何参数"),i.warning("该工具没有可配置的参数"));for(const $ of W)try{const v=$.name,r=$.info;if(!v||!r){console.warn("参数格式不正确:",$);continue}if(r.access_mode==="write"){console.log(`跳过write类型参数 ${v}`);continue}console.log(`处理参数 ${v}:`,r);const Y=r.type==="file"||((e=r.prompt)==null?void 0:e.includes("压缩包"));let K={prop:v,label:r.prompt||v,required:r.required,tip:r.prompt||"",type:"text",order:r.order,component:{type:"el-input",props:{placeholder:""}}};r.visibility&&(K.component.visibility=r.visibility);const D=xe(K);let f=w;if(!D){Y&&w&&(re[v]=r.default_value||"",console.log(`参数 ${v} 不显示但保存到hiddenParams:`,r.default_value));continue}r.type==="file"&&console.log(`文件参数 ${v} 的配置:`,{itemsToSelect:r.itemsToSelect,is_folder:r.is_folder,file_types:r.file_types,selectMultiple:r.selectMultiple});let s={prop:v,label:r.prompt||v,required:r.required,tip:r.prompt||"",type:"text",order:r.order,component:{type:"el-input",props:{placeholder:""}}};if(r.required&&(S[v]=[{required:!0,message:`请输入${r.prompt||v}`,trigger:["blur","change"]}]),r.type==="message"||(u=r.prompt)!=null&&u.includes("消息")||(p=r.prompt)!=null&&p.includes("提示"))s.type="message",s.component={type:"div",props:{class:"message-content"},content:r.prompt||v},delete S[v];else if((r.type==="dropdown"||r.type==="listbox")&&r.options&&r.options.length>0){if(s.type="select",s.component={type:"el-select",props:{placeholder:"",multiple:r.type==="listbox",delimiter:r.delimiter||","},options:r.options.map(x=>typeof x=="object"&&x!==null?{label:x.label||x,value:x.value||x.label||x}:{label:x,value:x})},f&&r.default_value)r.type==="listbox"?m.value[v]=r.default_value.split(r.delimiter||","):m.value[v]=r.default_value;else if(r.options.length>0){const x=r.options[0];m.value[v]=typeof x=="object"?x.value:x}}else if(r.type==="color")s.type="color",s.component={type:"el-color-picker",props:{showAlpha:!1,colorFormat:"rgb",showPicker:!0,size:"default",popperClass:"custom-color-picker"}},m.value[v]=f&&r.default_value?r.default_value:"rgb(255, 255, 255)",r.required&&(S[v]=[{required:!0,message:`请选择${r.prompt||v}`,trigger:["change"]}]);else if(r.type==="file"||(c=r.prompt)!=null&&c.includes("压缩包")){s.type="upload";const x=((d=r.file_types)==null?void 0:d.map(M=>M.replace("*","")).filter(M=>r.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(M.toLowerCase())).join(","))||"";s.component={type:"el-upload",props:{action:Pe(j.UPLOAD_FILE),"on-success":M=>aa(v,M),"on-remove":()=>la(v),"before-remove":ta,"before-upload":M=>{if(M.size>10737418240)return i.error("文件大小不能超过10GB"),!1;if(r.file_types&&r.file_types.length>0){const O=M.name.substring(M.name.lastIndexOf(".")).toLowerCase();if(!r.file_types.some(l=>{const h=l.replace("*","").toLowerCase();return(r.is_folder||![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(h))&&O===h})){const l=r.file_types.filter(h=>r.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(h.toLowerCase())).join("、");return i.error(`只能上传${l}格式的文件`),!1}}return!0},multiple:r.selectMultiple||!1,"show-file-list":!0,accept:x,headers:Le()}},r.required&&(S[v]=[{required:!0,message:`请上传${r.prompt||"文件"}`,trigger:["change"]}])}else r.type==="number"?(s.type="number",s.component={type:"el-input-number",props:{min:0,max:999999}},m.value[v]=f&&r.default_value?Number(r.default_value):0):r.type==="datetime"?(s.type="datetime",s.component={type:"el-date-picker",props:{type:"datetime",placeholder:"",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",style:"width: 100%"}},m.value[v]=f&&r.default_value?r.default_value:"",r.required&&(S[v]=[{required:!0,message:`请选择${r.prompt||v}`,trigger:["change"]}])):r.type==="checkbox"||r.type==="boolean"?(s.type="checkbox",s.component={type:"el-checkbox",props:{class:"custom-checkbox"}},m.value[v]=f&&r.default_value?r.default_value:"NO",r.required&&(S[v]=[{required:!0,message:`请选择${r.prompt||v}`,trigger:["change"]}])):(r.type==="text"||!r.type)&&(s.type="text",s.component={type:"el-input",props:{placeholder:""}},m.value[v]=f&&r.default_value?r.default_value:"");r.visibility&&(console.log(`参数 ${v} 的visibility条件:`,r.visibility),s.component.visibility=r.visibility),N.push(s)}catch(v){console.error("处理参数失败:",v),i.error("处理参数失败，请检查网络连接")}Q.value=N.sort(($,v)=>$.order-v.order),console.log("处理后的表单项:",Q.value),$e.value=S,k.value={...a,fmw_path:n.data.data.path,name:n.data.data.name,hiddenParams:re},le.value=!0}catch(n){console.error("运行工具失败:",n);let b="运行工具失败";if(n.response){const w=n.response.data;b=(w==null?void 0:w.error)||(w==null?void 0:w.message)||"服务器错误",console.error("服务器返回错误:",w)}else n.request?(b="网络请求失败，请检查网络连接",console.error("网络请求错误:",n.request)):console.error("请求配置错误:",n.message);i.error(b)}finally{q.value=!1}},aa=(a,e)=>{e.success?m.value[a]=e.data.path:i.error(e.message||"文件上传失败")},la=a=>{m.value[a]=""},ta=a=>!0,Me=_(!1),H=_([]),L=_(null),oa=a=>a.some(e=>{var p;const u=(p=e.status)==null?void 0:p.toLowerCase();return u==="running"||u==="pending"||u==="processing"||u==="运行中"||u==="处理中"||u==="等待中"});Re(Me,a=>{a?xa():(ue.value=1,L.value&&(clearInterval(L.value),L.value=null))});const xa=()=>{L.value&&clearInterval(L.value),L.value=setInterval(async()=>{oa(H.value)?(console.log("定时刷新运行记录..."),await qe(!1)):(console.log("没有运行中的任务，停止定时器"),L.value&&(clearInterval(L.value),L.value=null))},Ol)};ia(()=>{L.value&&(clearInterval(L.value),L.value=null)});const Ua=a=>{const e=(a||"").toLowerCase().replace(/\s/g,"");return["approved","已通过","completed","success","已完成"].some(u=>e.includes(u))?"success":["rejected","已驳回","failed","error","运行失败"].some(u=>e.includes(u))?"danger":["pending","审批中"].some(u=>e.includes(u))?"info":["running","processing","运行中","处理中","等待中"].some(u=>e.includes(u))?"warning":"info"},Va=a=>{switch(a==null?void 0:a.toLowerCase()){case"approved":return"已通过";case"rejected":return"已驳回";case"pending":return"审批中";case"running":case"processing":return"运行中";case"completed":case"success":return"已完成";case"failed":case"error":return"运行失败";case"运行中":return"运行中";case"处理中":return"处理中";case"等待中":return"等待中";case"已完成":return"已完成";case"运行失败":return"运行失败";default:return a||"未知状态"}},Ta=a=>{if(!a)return"-";const e=Math.floor(a/60),u=a%60;return`${e}分${u}秒`},Aa=async a=>{try{if(!a.tool_id||!a.task_id||!a.file_name){console.error("缺少必要参数:",{tool_id:a.tool_id,task_id:a.task_id,file_name:a.file_name}),i.error("下载失败：缺少必要参数");return}const e=`models/${a.tool_id}/output/${a.task_id}/${a.file_name}`,u=document.createElement("iframe");u.style.display="none",document.body.appendChild(u);const p=document.createElement("a");p.style.display="none",document.body.appendChild(p);const c=`${j.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`;p.href=Je(c),p.download=a.file_name,p.click(),setTimeout(()=>{document.body.removeChild(p),document.body.removeChild(u)},100),i.success("开始下载")}catch(e){console.error("下载运行成果失败:",e),i.error("下载失败，请稍后重试")}},Ra=async a=>{try{await ye.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=H.value.findIndex(u=>u.task_id===a.task_id);e!==-1&&(H.value.splice(e,1),ae.value--),P.post("/api/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}}).then(u=>{u.data.success?i.success("删除成功"):(H.value.splice(e,0,a),ae.value++,i.error(u.data.message||"删除失败"))}).catch(u=>{H.value.splice(e,0,a),ae.value++,i.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&i.error("删除失败，请稍后重试")}},qe=async(a=!0)=>{var e,u,p,c;if(!((e=k.value)!=null&&e.fmw_id)){console.error("缺少 fmw_id，当前工具信息:",k.value),i.error("获取运行记录失败：缺少必要参数");return}if(!((u=z.user)!=null&&u.username)){console.error("缺少 username，当前用户信息:",z.user),i.error("获取运行记录失败：用户未登录");return}a&&(q.value=!0);try{const d={fmw_id:k.value.fmw_id,username:z.user.username,page:ue.value,page_size:10};console.log("获取运行记录参数:",d);const n=await P.get(`/api/tools/run_records?fmw_id=${d.fmw_id}&username=${d.username}&page=${d.page}&page_size=${d.page_size}`,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});n.data.success?(H.value=Array.isArray(n.data.data.records)?n.data.data.records:[],ae.value=((p=n.data.data.pagination)==null?void 0:p.total)||0,a||!oa(H.value)&&L.value&&(console.log("没有运行中的任务，停止定时器"),clearInterval(L.value),L.value=null)):(i.error(n.data.message||"获取运行记录失败"),H.value=[],ae.value=0)}catch(d){console.error("获取运行记录失败:",d),d.response?(console.error("错误响应:",d.response.data),a&&i.error(((c=d.response.data)==null?void 0:c.message)||"获取运行记录失败")):a&&i.error("获取运行记录失败"),H.value=[],ae.value=0}finally{a&&(q.value=!1)}},Sa=a=>{ie.value=a},Da=a=>{console.log("页码改变:",a),console.log("当前工具信息:",k.value),ue.value=a,qe()},Pa=a=>{ye.confirm("确定要删除该申请吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await P.post("/api/tools/delete-application",{applicationId:a.id},{headers:{"X-Username":z.user.username}});e.data.success?(i.success("删除成功"),await pe()):i.error(e.data.message||"删除失败")}catch{i.error("删除申请失败，请检查网络连接")}}).catch(()=>{})},La=a=>{ye.confirm("确定要撤回该申请吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await P.post("/api/tools/withdraw-application",{applicationId:a.id},{headers:{"X-Username":z.user.username}});e.data.success?(i.success("撤回成功"),await pe()):i.error(e.data.message||"撤回失败")}catch{i.error("撤回申请失败，请检查网络连接")}}).catch(()=>{})};pa(()=>{Z()}),ia(()=>{});const de=_(!1),ce=_(!1),C=_({fmw_name:"",project:"",tools_class:"",description:"",file:null,use_default_params:!1}),X=_({fmw_id:"",file:null}),Ia={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],tools_class:[{required:!0,message:"请选择工具类型",trigger:"change"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},te=_(null),oe=_(null),Ma=()=>{le.value=!1},qa=()=>{var a;m.value={},Q.value=[],F.value&&F.value.resetFields(),(a=F.value)!=null&&a.$el&&F.value.$el.querySelectorAll(".el-upload").forEach(u=>{var c;const p=(c=u.__vueParentComponent)==null?void 0:c.ctx;p&&typeof p.clearFiles=="function"&&p.clearFiles()})},Ea=()=>{de.value=!1},Fa=()=>{se.value&&se.value.resetFields(),C.value={fmw_name:"",project:"",description:"",file:null,file_path:"",use_default_params:!1},te.value&&typeof te.value.clearFiles=="function"&&te.value.clearFiles()},Oa=()=>{ce.value=!1},Ba=()=>{Ee.value&&Ee.value.resetFields(),X.value={fmw_id:"",file:null,file_path:""},oe.value&&typeof oe.value.clearFiles=="function"&&oe.value.clearFiles()},ja=()=>{de.value=!0},se=_(),Ee=_();Re(()=>m.value,a=>{const e={};Q.value.forEach(u=>{xe(u)&&$e.value[u.prop]&&(e[u.prop]=$e.value[u.prop])}),F.value&&(F.value.clearValidate(),F.value.rules=e)},{deep:!0});const Na=async()=>{var a;if(!Ue.value){Ue.value=!0;try{if(!k.value){i.error("工具信息不完整");return}const e=Q.value.filter(d=>xe(d));console.log("可见的表单项:",e.map(d=>d.prop));for(const d of e)if(d.required&&!m.value[d.prop]){let n="";d.type==="file"||d.type==="upload"?n=`请上传${d.label}`:d.type==="select"||d.type==="dropdown"||d.type==="listbox"?n=`请选择${d.label}`:n=`请填写${d.label}`,i.error(n);return}const p={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:k.value.fmw_id,fmw_name:k.value.fmw_name,fmw_path:k.value.fmw_path,params:{}};for(const d of e){const n=m.value[d.prop];n!=null&&!d.prop.endsWith("_value")&&(d.type==="color"?p.params[d.prop]=m.value[`${d.prop}_value`]:p.params[d.prop]=n)}if(k.value.hiddenParams&&(Object.assign(p.params,k.value.hiddenParams),console.log("合并的hiddenParams:",k.value.hiddenParams)),console.log("提交的请求数据:",p),Ie.value)try{const d=await P.post(j.UPDATE_COUNT,{id:k.value.id,username:z.user.username});if(!d.data.success){i.error(d.data.message||"更新使用次数失败");return}const n=De.value.find(b=>b.id===k.value.id);n&&(n.count=(parseInt(n.count)||0)+1,n.remaining_count=(parseInt(n.usage_count)||0)-n.count)}catch(d){console.error("更新使用次数失败:",d),i.error("更新使用次数失败，请稍后重试");return}const c=await P.post(j.RUN_TOOL,p,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});c.data.success?(i.success("任务提交成功"),m.value={},Q.value=[],F.value&&F.value.resetFields(),(a=F.value)!=null&&a.$el&&F.value.$el.querySelectorAll(".el-upload").forEach(n=>{var w;const b=(w=n.__vueParentComponent)==null?void 0:w.ctx;b&&typeof b.clearFiles=="function"&&b.clearFiles()}),le.value=!1,await qe(),await pe()):i.error(c.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),i.error("提交失败，请稍后重试")}finally{Ue.value=!1}}},Ya=async()=>{if(!Ve.value&&(Ve.value=!0,!!se.value))try{if(await se.value.validate(),!C.value.file_path){i.error("请上传工具文件");return}const a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",u={fmw_id:Array.from({length:40},()=>a[Math.floor(Math.random()*a.length)]).join(""),fmw_name:C.value.fmw_name,project:C.value.project,tools_class:C.value.tools_class,description:C.value.description,fmw_path:C.value.file_path,file_path:C.value.file_path,data:new Date().toISOString(),def_params:C.value.use_default_params?"Y":"N"},p=await P.post(`${j.UPLOAD_TOOL}`,u,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});p.data.success?(i.success("工具上传成功"),de.value=!1,C.value={fmw_name:"",project:"",description:"",file:null,file_path:"",use_default_params:!1},se.value&&se.value.resetFields(),te.value&&typeof te.value.clearFiles=="function"&&te.value.clearFiles(),await Z()):i.error(p.data.message||"上传失败")}catch(a){console.error("上传工具失败:",a),i.error("参数未填写完整")}finally{Ve.value=!1}},Ha=async()=>{if(!fe.value){if(fe.value=!0,!X.value.file_path){i.error("请先上传工具文件"),fe.value=!1;return}try{const a={fmw_id:X.value.fmw_id,file_path:X.value.file_path},e=await P.post(`${j.UPDATE_TOOL}`,a,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});e.data.success?(i.success("工具更新成功"),ce.value=!1,X.value={fmw_id:"",file:null,file_path:""},Ee.value&&Ee.value.resetFields(),oe.value&&typeof oe.value.clearFiles=="function"&&oe.value.clearFiles(),await Z()):i.error(e.data.message||"更新失败")}catch(a){console.error("更新工具失败:",a),i.error("更新失败，请检查网络连接")}finally{fe.value=!1}}},sa=async a=>{try{k.value=a,Me.value=!0,await qe()}catch(e){console.error("获取运行记录失败:",e),i.error("获取运行记录失败，请检查网络连接")}},Xa=a=>{X.value={fmw_id:a.fmw_id,file:null},ce.value=!0},Wa=async a=>{try{if(await ye.confirm("是否确认下载该工具？","下载确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"}),!a.fmw_path){i.error("下载失败：缺少必要参数");return}const e=`/api/tools/download?fmw_path=${encodeURIComponent(a.fmw_path)}`;window.open(Je(e)),i.success("已发起下载")}catch(e){if(e==="cancel")return;i.error("下载失败，请稍后重试")}},Ga=async a=>{try{await ye.confirm("确定要删除该工具吗？删除后无法恢复。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await P.post("/api/tools/delete",{fmw_id:a.fmw_id},{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});e.data.success?(i.success("删除成功"),await Z()):i.error(e.data.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除工具失败:",e),i.error("删除失败，请检查网络连接"))}},Qa=async a=>{try{const e=await P.post("/api/tools/detail",{fmw_id:a.fmw_id},{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});if(e.data.success){const u=e.data.data;ye.alert(`
        <div class="tool-detail">
          <p><strong>工具名称：</strong>${u.fmw_name}</p>
          <p><strong>所属项目：</strong>${u.project}</p>
          <p><strong>上传时间：</strong>${J(u.created_at)}</p>
          <p><strong>运行次数：</strong>${u.run_times}</p>
          <p><strong>工具描述：</strong>${u.description||"暂无描述"}</p>
        </div>
        `,"工具详情",{confirmButtonText:"确定",dangerouslyUseHTMLString:!0,customClass:"tool-detail-dialog"})}else i.error(e.data.message||"获取详情失败")}catch(e){console.error("获取工具详情失败:",e),i.error("获取详情失败，请检查网络连接")}},Za=(a,e)=>{if(!e){m.value[a]="rgb(255, 255, 255)";return}m.value[a]=e;const u=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(u){const[,p,c,d]=u;m.value[`${a}_value`]=`${p},${c},${d}`}else m.value[`${a}_value`]="255,255,255"},xe=a=>{var u;if(!((u=a.component)!=null&&u.visibility))return!0;const e=a.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const p of e.if){const{condition:c,then:d}=p;let n=!1;if(c.allOf)n=c.allOf.every(b=>{if(b.equals){const{parameter:w,value:N}=b.equals;return m.value[w]===N}else if(b.isEnabled){const{parameter:w}=b.isEnabled;return!!m.value[w]}return!1});else if(c.equals){const{parameter:b,value:w}=c.equals;n=m.value[b]===w}else if(c.isEnabled){const{parameter:b}=c.isEnabled;n=!!m.value[b]}if(n)return d==="visibleEnabled"||d==="visibleDisabled"}return!1},Ja=async a=>{var e,u,p,c,d,n,b,w,N,W,me;try{if(!a||!a.fmw_id){i.error("工具信息不完整");return}console.log("handleApplyRun 接收到的 row:",a);try{const D=await P.post(j.VERIFY_RUN_PERMISSION,{id:a.id,username:z.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});if(!D.data.success){i.error(D.data.message||"验证运行权限失败");return}}catch(D){console.error("验证运行权限失败:",D),D.response?i.error(((e=D.response.data)==null?void 0:e.message)||"验证运行权限失败"):i.error("验证运行权限失败，请稍后重试");return}Ie.value=!0,Xe.value=!1,k.value={...a,isApplyRun:!0,isRun:!1},console.log("handleApplyRun 设置后的状态:",{isApplyRun:Ie.value,isRun:Xe.value,currentTool:k.value,id:k.value.id,fmw_id:k.value.fmw_id}),q.value=!0;const S=await P.post(j.PARSE_FMW,{fmw_id:a.fmw_id},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});if(!((u=S.data)!=null&&u.success)||!((c=(p=S.data)==null?void 0:p.data)!=null&&c.parameters)){const D=((n=(d=S.data)==null?void 0:d.data)==null?void 0:n.error)||"解析工具参数失败";i.error(D);return}const $=(S.data.data.def_params||"N")==="Y",v=[],r=S.data.data.parameters,Y={},K={};for(const D of r)try{const f=D.name,s=D.info;if(!f||!s||s.access_mode==="write")continue;const x=s.type==="file"||((b=s.prompt)==null?void 0:b.includes("压缩包"));let M={prop:f,label:s.prompt||f,required:s.required,tip:s.prompt||"",type:"text",order:s.order,component:{type:"el-input",props:{placeholder:""}}};s.visibility&&(M.component.visibility=s.visibility);const ve=xe(M);let O=$;if(!ve){x&&$&&(K[f]=s.default_value||"",console.log(`参数 ${f} 不显示但保存到hiddenParams:`,s.default_value));continue}let U={prop:f,label:s.prompt||f,required:s.required,tip:s.prompt||"",type:"text",order:s.order,component:{type:"el-input",props:{placeholder:""}}};if(s.required&&(Y[f]=[{required:!0,message:`请输入${s.prompt||f}`,trigger:["blur","change"]}]),s.type==="message"||(w=s.prompt)!=null&&w.includes("消息")||(N=s.prompt)!=null&&N.includes("提示"))U.type="message",U.component={type:"div",props:{class:"message-content"},content:s.prompt||f},delete Y[f];else if((s.type==="dropdown"||s.type==="listbox")&&s.options&&s.options.length>0){if(U.type="select",U.component={type:"el-select",props:{placeholder:""},options:s.options.map(l=>typeof l=="object"&&l!==null?{label:l.label||l,value:l.value||l.label||l}:{label:l,value:l})},O&&s.default_value)m.value[f]=s.default_value;else if(s.options.length>0){const l=s.options[0];m.value[f]=typeof l=="object"?l.value:l}}else if(s.type==="color")U.type="color",U.component={type:"el-color-picker",props:{showAlpha:!1,colorFormat:"rgb",showPicker:!0,size:"default",popperClass:"custom-color-picker"}},m.value[f]=O&&s.default_value?s.default_value:"rgb(255, 255, 255)",s.required&&(Y[f]=[{required:!0,message:`请选择${s.prompt||f}`,trigger:["change"]}]);else if(s.type==="file"||(W=s.prompt)!=null&&W.includes("压缩包")){U.type="upload";const l=((me=s.file_types)==null?void 0:me.map(h=>h.replace("*","")).filter(h=>s.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(h.toLowerCase())).join(","))||"";U.component={type:"el-upload",props:{action:Pe(j.UPLOAD_FILE),"on-success":h=>aa(f,h),"on-remove":()=>la(f),"before-remove":ta,"before-upload":h=>{if(h.size>10737418240)return i.error("文件大小不能超过10GB"),!1;if(s.file_types&&s.file_types.length>0){const Te=h.name.substring(h.name.lastIndexOf(".")).toLowerCase();if(!s.file_types.some(Ge=>{const Ae=Ge.replace("*","").toLowerCase();return(s.is_folder||![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(Ae))&&Te===Ae})){const Ge=s.file_types.filter(Ae=>s.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(Ae.toLowerCase())).join("、");return i.error(`只能上传${Ge}格式的文件`),!1}}return!0},multiple:s.selectMultiple||!1,"show-file-list":!0,accept:l,headers:Le()}},s.required&&(Y[f]=[{required:!0,message:`请上传${s.prompt||"文件"}`,trigger:["change"]}])}else s.type==="number"?(U.type="number",U.component={type:"el-input-number",props:{min:0,max:999999}},m.value[f]=O&&s.default_value?Number(s.default_value):0):s.type==="datetime"?(U.type="datetime",U.component={type:"el-date-picker",props:{type:"datetime",placeholder:"",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",style:"width: 100%"}},m.value[f]=O&&s.default_value?s.default_value:"",s.required&&(Y[f]=[{required:!0,message:`请选择${s.prompt||f}`,trigger:["change"]}])):s.type==="checkbox"||s.type==="boolean"?(U.type="checkbox",U.component={type:"el-checkbox",props:{class:"custom-checkbox"}},m.value[f]=O&&s.default_value?s.default_value:"NO",s.required&&(Y[f]=[{required:!0,message:`请选择${s.prompt||f}`,trigger:["change"]}])):(s.type==="text"||!s.type)&&(U.type="text",U.component={type:"el-input",props:{placeholder:""}},m.value[f]=O&&s.default_value?s.default_value:"");s.visibility&&(console.log(`参数 ${f} 的visibility条件:`,s.visibility),U.component.visibility=s.visibility),v.push(U)}catch{console.error("处理参数失败，请检查网络连接")}Q.value=v.sort((D,f)=>D.order-f.order),$e.value=Y,k.value={...a,fmw_path:S.data.data.path,name:S.data.data.name,hiddenParams:K},le.value=!0}catch{i.error("运行工具失败")}finally{q.value=!1}},Fe=_(!1),ra=_(""),Ka=a=>{ra.value=a,Fe.value=!0},el=a=>{ze.value=a,ie.value=1},al=a=>{ke.value=a,ee.value=1},ll=(a,e)=>{a.success&&a.data&&a.data.path?(X.value.file_path=a.data.path,X.value.file=e):(i.error(a.message||"文件上传失败"),X.value.file_path="")},tl=a=>{i.error("文件上传失败，请检查网络连接"),X.value.file_path=""};_(0);const ol=(a,e)=>{a.success&&a.data&&a.data.path?(C.value.file_path=a.data.path,C.value.file=e):(i.error(a.message||"文件上传失败"),C.value.file_path="")},sl=a=>{i.error("文件上传失败，请检查网络连接"),C.value.file_path=""},rl=async a=>{a.props.name!==G.value&&(a.props.name==="applications"&&!Ce.value?await pe():a.props.name==="tools"&&!we.value&&await Z())},na=_(!1);Re(G,(a,e)=>{a!==e&&(na.value=!0,setTimeout(()=>{na.value=!1},400))});const ua=ml(),nl=a=>{const e=a.row||a;return ua.query.highlightId&&String(e.id)===String(ua.query.highlightId)?"highlight-row":""},Ue=_(!1),Ve=_(!1),fe=_(!1);return pa(async()=>{G.value==="applications"?await pe():G.value==="tools"&&await Z()}),Re(G,async a=>{a==="applications"&&!Ce.value?await pe():a==="tools"&&!we.value&&await Z()}),(a,e)=>{var O,U;const u=V("el-input"),p=V("el-icon"),c=V("el-button"),d=V("el-card"),n=V("el-table-column"),b=V("el-tooltip"),w=V("el-button-group"),N=V("el-table"),W=V("el-pagination"),me=V("el-tab-pane"),S=V("el-tabs"),re=V("el-upload"),$=V("el-option"),v=V("el-select"),r=V("el-input-number"),Y=V("el-date-picker"),K=V("el-checkbox"),D=V("el-color-picker"),f=V("el-form-item"),s=V("el-form"),x=V("el-dialog"),M=V("el-tag"),ve=dl("loading");return A(),_e("div",gl,[t(S,{modelValue:G.value,"onUpdate:modelValue":e[6]||(e[6]=l=>G.value=l),class:"tools-tabs",onTabClick:rl},{default:o(()=>[t(me,{label:"我的申请",name:"applications"},{default:o(()=>[t(da,{name:"tab-slide",mode:"out-in"},{default:o(()=>[ge(g("div",yl,[t(d,{class:"search-card"},{default:o(()=>[g("div",hl,[g("div",bl,[t(u,{modelValue:be.value,"onUpdate:modelValue":e[0]||(e[0]=l=>be.value=l),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":R(Be),onClear:He,onInput:He},null,8,["modelValue","prefix-icon"]),g("div",wl,[t(c,{type:"primary",onClick:He},{default:o(()=>[t(p,null,{default:o(()=>[t(R(Be))]),_:1}),e[22]||(e[22]=y(" 搜索 "))]),_:1}),t(c,{onClick:za},{default:o(()=>[t(p,null,{default:o(()=>[t(R(ca))]),_:1}),e[23]||(e[23]=y(" 重置 "))]),_:1})])])])]),_:1}),t(d,{class:"table-card"},{header:o(()=>e[24]||(e[24]=[g("div",{class:"table-header"},[g("span",{class:"title"},"我的申请")],-1)])),default:o(()=>[g("div",Cl,[ge((A(),I(N,{data:wa.value,style:{width:"100%"},"row-class-name":nl},{default:o(()=>[t(n,{type:"index",label:"序号",width:"80",align:"center"}),t(n,{prop:"fmw_name",label:"工具名称","min-width":"200","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"project",label:"所属项目","min-width":"150","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"usage_count",label:"申请运行次数",width:"120",align:"center"},{default:o(({row:l})=>[g("span",{class:je({highlight:l.usage_count>0})},B(l.usage_count),3)]),_:1}),t(n,{label:"已运行次数",width:"120",align:"center"},{default:o(({row:l})=>[g("span",{class:je({highlight:l.count>0})},B(l.count),3)]),_:1}),t(n,{prop:"end_date",label:"使用截止日期",width:"180",align:"center"},{default:o(({row:l})=>[t(b,{content:J(l.end_date,"yyyy-MM-dd"),placement:"top"},{default:o(()=>[g("span",null,B(J(l.end_date,"yyyy-MM-dd")),1)]),_:2},1032,["content"])]),_:1}),t(n,{prop:"created_at",label:"申请时间",width:"180",align:"center"},{default:o(({row:l})=>[t(b,{content:J(l.created_at,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:o(()=>[g("span",null,B(J(l.created_at,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(n,{label:"操作",width:"300",align:"center",fixed:"right"},{default:o(({row:l})=>[t(w,null,{default:o(()=>[t(c,{type:"primary",size:"small",onClick:h=>Ja(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(fa))]),_:1}),e[25]||(e[25]=y(" 运行 "))]),_:2},1032,["onClick"]),t(c,{type:"success",size:"small",onClick:h=>sa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(ma))]),_:1}),e[26]||(e[26]=y(" 运行成果 "))]),_:2},1032,["onClick"]),l.status==="已通过"?(A(),I(c,{key:0,type:"danger",size:"small",onClick:h=>Pa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(va))]),_:1}),e[27]||(e[27]=y(" 删除 "))]),_:2},1032,["onClick"])):Ne("",!0),l.status==="审批中"?(A(),I(c,{key:1,type:"warning",size:"small",onClick:h=>La(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(pl))]),_:1}),e[28]||(e[28]=y(" 撤回 "))]),_:2},1032,["onClick"])):Ne("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ve,q.value]]),g("div",zl,[t(W,{"current-page":ee.value,"onUpdate:currentPage":e[1]||(e[1]=l=>ee.value=l),"page-size":ke.value,"onUpdate:pageSize":e[2]||(e[2]=l=>ke.value=l),"page-sizes":[10,20,50,100],total:ea.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:al,onCurrentChange:ka},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[Qe,G.value==="applications"]])]),_:1})]),_:1}),t(me,{label:"我的工具",name:"tools"},{default:o(()=>[t(da,{name:"tab-slide",mode:"out-in"},{default:o(()=>[ge(g("div",kl,[t(d,{class:"search-card"},{default:o(()=>[g("div",$l,[g("div",xl,[t(u,{modelValue:he.value,"onUpdate:modelValue":e[3]||(e[3]=l=>he.value=l),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":R(Be),onClear:Ye,onInput:Ye},null,8,["modelValue","prefix-icon"]),g("div",Ul,[t(c,{type:"primary",onClick:Ye},{default:o(()=>[t(p,null,{default:o(()=>[t(R(Be))]),_:1}),e[29]||(e[29]=y(" 搜索 "))]),_:1}),t(c,{onClick:Ca},{default:o(()=>[t(p,null,{default:o(()=>[t(R(ca))]),_:1}),e[30]||(e[30]=y(" 重置 "))]),_:1})])])])]),_:1}),t(d,{class:"table-card"},{header:o(()=>[g("div",Vl,[e[32]||(e[32]=g("span",{class:"title"},"我的工具",-1)),t(c,{type:"primary",onClick:ja},{default:o(()=>[t(p,null,{default:o(()=>[t(R(_a))]),_:1}),e[31]||(e[31]=y(" 上传工具 "))]),_:1})])]),default:o(()=>[g("div",Tl,[ge((A(),I(N,{data:ba.value,style:{width:"100%"}},{default:o(()=>[t(n,{type:"index",label:"序号",width:"80",align:"center"}),t(n,{prop:"fmw_name",label:"工具名称","min-width":"200","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"project",label:"所属项目","min-width":"150","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"run_times",label:"运行次数",width:"120",align:"center"},{default:o(({row:l})=>[g("span",{class:je({highlight:l.run_times>0})},B(l.run_times),3)]),_:1}),t(n,{prop:"created_at",label:"上传时间",width:"180",align:"center"},{default:o(({row:l})=>[t(b,{content:J(l.created_at,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:o(()=>[g("span",null,B(J(l.created_at,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(n,{label:"工具更新",width:"120",align:"center"},{default:o(({row:l})=>[t(c,{type:"primary",size:"small",onClick:h=>Xa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(_a))]),_:1}),e[33]||(e[33]=y(" 更新 "))]),_:2},1032,["onClick"])]),_:1}),t(n,{label:"工具下载",width:"120",align:"center"},{default:o(({row:l})=>[t(c,{type:"success",size:"small",onClick:h=>Wa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(cl))]),_:1}),e[34]||(e[34]=y(" 下载 "))]),_:2},1032,["onClick"])]),_:1}),t(n,{label:"操作",width:"400",align:"center",fixed:"right"},{default:o(({row:l})=>[t(w,null,{default:o(()=>[t(c,{type:"primary",size:"small",onClick:h=>$a(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(fa))]),_:1}),e[35]||(e[35]=y(" 运行 "))]),_:2},1032,["onClick"]),t(c,{type:"success",size:"small",onClick:h=>sa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(ma))]),_:1}),e[36]||(e[36]=y(" 运行成果 "))]),_:2},1032,["onClick"]),t(c,{type:"danger",size:"small",onClick:h=>Ga(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(va))]),_:1}),e[37]||(e[37]=y(" 删除 "))]),_:2},1032,["onClick"]),t(c,{type:"info",size:"small",onClick:h=>Qa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(R(fl))]),_:1}),e[38]||(e[38]=y(" 详情 "))]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ve,q.value]]),g("div",Al,[t(W,{"current-page":ie.value,"onUpdate:currentPage":e[4]||(e[4]=l=>ie.value=l),"page-size":ze.value,"onUpdate:pageSize":e[5]||(e[5]=l=>ze.value=l),"page-sizes":[10,20,50,100],total:Ke.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:el,onCurrentChange:Sa},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[Qe,G.value==="tools"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(x,{modelValue:le.value,"onUpdate:modelValue":e[8]||(e[8]=l=>le.value=l),title:`运行工具 - ${(O=k.value)==null?void 0:O.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:Ma,onAfterClose:qa},{footer:o(()=>[g("span",Ll,[t(c,{onClick:e[7]||(e[7]=l=>le.value=!1)},{default:o(()=>e[40]||(e[40]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Na,loading:Ue.value,disabled:Ue.value},{default:o(()=>e[41]||(e[41]=[y("提交任务")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(s,{ref_key:"formRef",ref:F,model:m.value,rules:$e.value,"label-width":"200px",size:"small",class:"run-form"},{default:o(()=>[Q.value.length>0?(A(!0),_e(ga,{key:0},ya(Q.value,l=>ge((A(),I(f,{key:l.prop,label:l.type==="message"?"":l.label,prop:l.prop,required:l.required,class:je({"message-form-item":l.type==="message"})},{default:o(()=>{var h,We,Te;return[l.type==="message"?(A(),_e("div",Rl,B(l.component.content),1)):l.type==="upload"?(A(),I(re,ne({key:1,ref_for:!0},l.component.props,{class:["upload-area",{"is-error":((Te=(We=(h=F.value)==null?void 0:h.fields)==null?void 0:We.find(T=>T.prop===l.prop))==null?void 0:Te.validateState)==="error"}],drag:""}),{default:o(()=>[t(p,{class:"el-icon--upload"},{default:o(()=>[t(R(Ze))]),_:1}),e[39]||(e[39]=g("div",{class:"el-upload__text"},[y(" 拖拽文件到此处"),g("br"),y("或"),g("em",null,"点击上传")],-1))]),_:2},1040,["class"])):l.type==="select"?(A(),I(v,ne({key:2,modelValue:m.value[l.prop],"onUpdate:modelValue":T=>m.value[l.prop]=T,ref_for:!0},l.component.props,{"collapse-tags":l.component.props.multiple,"collapse-tags-tooltip":!0,style:{width:"100%"}}),{default:o(()=>[(A(!0),_e(ga,null,ya(l.component.options,T=>(A(),I($,{key:T.value,label:T.label,value:T.value,title:T.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue","collapse-tags"])):l.type==="number"?(A(),I(r,ne({key:3,modelValue:m.value[l.prop],"onUpdate:modelValue":T=>m.value[l.prop]=T,ref_for:!0},l.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="datetime"?(A(),I(Y,ne({key:4,modelValue:m.value[l.prop],"onUpdate:modelValue":T=>m.value[l.prop]=T,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"])):l.type==="checkbox"?(A(),I(K,ne({key:5,modelValue:m.value[l.prop],"onUpdate:modelValue":T=>m.value[l.prop]=T,ref_for:!0},l.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="color"?(A(),_e("div",Sl,[t(D,ne({modelValue:m.value[l.prop],"onUpdate:modelValue":T=>m.value[l.prop]=T,ref_for:!0},l.component.props,{onChange:T=>Za(l.prop,T),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),g("span",Dl,B(m.value[`${l.prop}_value`]||"255,255,255"),1)])):(A(),I(u,ne({key:7,modelValue:m.value[l.prop],"onUpdate:modelValue":T=>m.value[l.prop]=T,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[Qe,xe(l)]])),128)):(A(),_e("div",Pl," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),t(x,{modelValue:de.value,"onUpdate:modelValue":e[15]||(e[15]=l=>de.value=l),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:Ea,onAfterClose:Fa},{footer:o(()=>[g("span",Il,[t(c,{onClick:e[14]||(e[14]=l=>de.value=!1)},{default:o(()=>e[45]||(e[45]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Ya,loading:Ve.value,disabled:Ve.value},{default:o(()=>e[46]||(e[46]=[y("确定")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(s,{ref_key:"uploadFormRef",ref:se,model:C.value,rules:Ia,"label-width":"100px",size:"small"},{default:o(()=>[t(f,{label:"工具名称",prop:"fmw_name"},{default:o(()=>[t(u,{modelValue:C.value.fmw_name,"onUpdate:modelValue":e[9]||(e[9]=l=>C.value.fmw_name=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"所属项目",prop:"project"},{default:o(()=>[t(u,{modelValue:C.value.project,"onUpdate:modelValue":e[10]||(e[10]=l=>C.value.project=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"工具类型",prop:"tools_class"},{default:o(()=>[t(v,{modelValue:C.value.tools_class,"onUpdate:modelValue":e[11]||(e[11]=l=>C.value.tools_class=l),placeholder:"请选择工具类型",style:{width:"100%"}},{default:o(()=>[t($,{label:"格式转换",value:"格式转换"}),t($,{label:"坐标系转换",value:"坐标系转换"}),t($,{label:"数据结构转换",value:"数据结构转换"}),t($,{label:"数据清洗",value:"数据清洗"}),t($,{label:"几何处理",value:"几何处理"}),t($,{label:"属性处理",value:"属性处理"}),t($,{label:"数据合并",value:"数据合并"}),t($,{label:"数据融合",value:"数据融合"}),t($,{label:"数据获取",value:"数据同步"}),t($,{label:"批处理",value:"批处理"}),t($,{label:"数据质检",value:"数据质检"}),t($,{label:"空间分析",value:"空间分析"}),t($,{label:"统计分析",value:"统计分析"})]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"工具描述",prop:"description"},{default:o(()=>[t(u,{modelValue:C.value.description,"onUpdate:modelValue":e[12]||(e[12]=l=>C.value.description=l),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"工具文件",prop:"file"},{default:o(()=>[t(re,{ref_key:"uploadRef",ref:te,class:"upload-demo",action:Pe("/api/upload"),headers:Le(),"on-success":ol,"on-error":sl,limit:1,accept:".fmw",drag:"","show-file-list":!0,"auto-upload":!0},{tip:o(()=>e[42]||(e[42]=[g("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:o(()=>[t(p,{class:"el-icon--upload"},{default:o(()=>[t(R(Ze))]),_:1}),e[43]||(e[43]=g("div",{class:"el-upload__text"},[y(" 将文件拖到此处，或"),g("em",null,"点击上传")],-1))]),_:1},8,["action","headers"])]),_:1}),t(f,{label:"使用默认参数",prop:"use_default_params"},{default:o(()=>[t(K,{modelValue:C.value.use_default_params,"onUpdate:modelValue":e[13]||(e[13]=l=>C.value.use_default_params=l)},{default:o(()=>e[44]||(e[44]=[y(" 勾选此项代表运行工具时自动读取默认参数 ")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(x,{modelValue:ce.value,"onUpdate:modelValue":e[17]||(e[17]=l=>ce.value=l),title:"更新工具",width:"500px","close-on-click-modal":!1,onClose:Oa,onAfterClose:Ba},{footer:o(()=>[g("span",Ml,[t(c,{onClick:e[16]||(e[16]=l=>ce.value=!1)},{default:o(()=>e[49]||(e[49]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Ha,loading:fe.value,disabled:fe.value},{default:o(()=>e[50]||(e[50]=[y("确认更新")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(re,{ref_key:"updateUploadRef",ref:oe,class:"upload-demo",action:Pe("/api/upload"),headers:Le(),"on-success":ll,"on-error":tl,limit:1,accept:".fmw",drag:"","show-file-list":!0,"auto-upload":!0},{tip:o(()=>e[47]||(e[47]=[g("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:o(()=>[t(p,{class:"el-icon--upload"},{default:o(()=>[t(R(Ze))]),_:1}),e[48]||(e[48]=g("div",{class:"el-upload__text"},[y(" 将文件拖到此处，或"),g("em",null,"点击上传")],-1))]),_:1},8,["action","headers"])]),_:1},8,["modelValue"]),t(x,{modelValue:Me.value,"onUpdate:modelValue":e[19]||(e[19]=l=>Me.value=l),title:`运行成果 - ${(U=k.value)==null?void 0:U.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:o(()=>[ge((A(),I(N,{data:H.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:o(()=>[t(n,{type:"index",label:"序号","min-width":"80",align:"center"}),t(n,{prop:"submit_time",label:"提交时间","min-width":"180",align:"center"},{default:o(({row:l})=>[y(B(J(l.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),t(n,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:o(({row:l})=>[t(M,{type:Ua(l.status)},{default:o(()=>[y(B(Va(l.status)),1)]),_:2},1032,["type"])]),_:1}),t(n,{prop:"time_consuming",label:"运行耗时","min-width":"100",align:"center"},{default:o(({row:l})=>[y(B(Ta(l.time_consuming)),1)]),_:1}),t(n,{prop:"file_size",label:"文件大小","min-width":"100",align:"center"}),t(n,{label:"操作","min-width":"200",align:"center"},{default:o(({row:l})=>[t(w,null,{default:o(()=>[l.status==="failed"?(A(),I(c,{key:0,type:"warning",size:"small",onClick:h=>Ka(l.error_message)},{default:o(()=>e[51]||(e[51]=[y(" 日志 ")])),_:2},1032,["onClick"])):Ne("",!0),l.file_name&&l.file_size!=="0.0MB"?(A(),I(c,{key:1,type:"primary",size:"small",onClick:h=>Aa(l)},{default:o(()=>e[52]||(e[52]=[y(" 下载 ")])),_:2},1032,["onClick"])):Ne("",!0),t(c,{type:"danger",size:"small",onClick:h=>Ra(l)},{default:o(()=>e[53]||(e[53]=[y(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ve,q.value]]),g("div",ql,[t(W,{"current-page":ue.value,"onUpdate:currentPage":e[18]||(e[18]=l=>ue.value=l),"page-size":10,total:ae.value,layout:"total, prev, pager, next, jumper",onCurrentChange:Da},null,8,["current-page","total"])])]),_:1},8,["modelValue","title"]),t(x,{modelValue:Fe.value,"onUpdate:modelValue":e[21]||(e[21]=l=>Fe.value=l),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:o(()=>[g("span",Fl,[t(c,{onClick:e[20]||(e[20]=l=>Fe.value=!1)},{default:o(()=>e[54]||(e[54]=[y("关闭")])),_:1})])]),default:o(()=>[g("div",El,[g("pre",null,B(ra.value),1)])]),_:1},8,["modelValue"])])}}}),Hl=vl(Bl,[["__scopeId","data-v-120ac0c1"]]);export{Hl as default};
