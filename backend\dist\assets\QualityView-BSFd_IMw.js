import{d as yt,u as ht,r as p,M as O,O as xe,c as b,e as a,w as s,f as d,Y as oe,P as M,Z as H,b as o,J as bt,A as m,m as x,D as re,C as V,s as S,a7 as wt,n as Ce,B as xt,F as c,a8 as ie,H as ke,I as De,a4 as Ct,g as kt,a9 as Dt,aa as $t,Q as Mt,R as Vt,ab as Gt,a1 as St,i as E,E as _,j as Ut,T as zt,l as f,_ as Lt}from"./index-B7WNRWO3.js";const Bt={class:"tools-container"},Tt={key:"quality"},jt={class:"steps-progress"},qt={class:"step-content-container"},Ht={class:"start-container"},Et={class:"start-content"},Ft={class:"step-content"},Rt={class:"license-toolbar"},Nt={style:{display:"none"}},Pt={class:"step-footer"},Wt={class:"error-message"},Yt={class:"error-message"},Kt={class:"error-message"},Xt={class:"error-message"},At={class:"error-message"},Qt={class:"dialog-footer"},Zt={class:"step-content"},It={class:"three-column-layout"},Ot={class:"left-column panel"},Jt={class:"upload-section"},ea={key:0,class:"upload-progress"},ta={class:"progress-text"},aa={class:"right-column panel"},la={key:0,class:"gdb-selection"},sa={class:"gdb-name-compact"},na={class:"number-text"},oa={class:"number-text"},ra={key:1,class:"empty-state"},ia={key:0,class:"bottom-section panel"},ua={class:"layer-preview"},da={class:"layer-preview-header"},ca={class:"layer-stats"},pa={style:{display:"flex","align-items":"center",gap:"8px"}},_a={key:0},fa={key:1,style:{color:"#999"}},ma=["title"],ga={key:1,style:{color:"#999"}},va={key:0,class:"more-fields"},ya={key:1,style:{color:"#999"}},ha={class:"pagination-container"},ba={class:"step-footer"},wa={class:"step-content"},xa={class:"step-footer"},Ca={key:"history"},ka={class:"table-container"},Da={style:{display:"flex","justify-content":"center"}},$a={class:"error-log-content"},Ma={class:"dialog-footer"},Va=4,Ga=yt({__name:"QualityView",setup(Sa){const U=ht(),$e=t=>window.location.pathname.startsWith("/gsi/")?`/gsi${t}`:`${E.defaults.baseURL}${t}`,w=p(0),K=p([]),J=p(!1),G=p(null),z=p(!1),X=p(!1),F=p(),ee=p(!1),ue=p([]),v=p({tool_name:"数据质检",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),Me={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},Ve=t=>t.getTime()<Date.now()-864e5;function Ge(){z.value=!0,Ue()}function Se(){X.value=!0,A().finally(()=>{setTimeout(()=>{X.value=!1},600)})}async function Ue(){var e;const t=(e=U.user)==null?void 0:e.username;if(!t){_.error("未登录，无法获取审批人");return}try{const n=await E.get("/api/admin-users",{headers:{"X-Username":t}});n.data.success?ue.value=n.data.data:_.error("获取审批人失败")}catch{_.error("获取审批人失败")}}async function ze(){F.value&&await F.value.validate(async t=>{var e;if(t){ee.value=!0;try{const n=(e=U.user)==null?void 0:e.username,r=await E.post("/api/quality/tools/apply",{fmw_id:"quality",fmw_name:"数据质检",tool_name:"数据质检",applicant:n,reason:v.value.reason,end_date:v.value.end_date,usage_count:v.value.usage_count,user_project:v.value.user_project,reviewer:v.value.approver},{headers:{"Content-Type":"application/json","X-Username":n}});r.data.success?(_.success("申请提交成功"),z.value=!1,de(),await A()):_.error(r.data.message||"申请提交失败")}catch{_.error("申请提交失败")}finally{ee.value=!1}}})}function de(){F.value&&F.value.resetFields(),Object.assign(v.value,{tool_name:"数据质检",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})}const Le=O(()=>{var t;return{"X-Username":((t=U.user)==null?void 0:t.username)||""}}),k=p([]),C=p(""),L=p(!1),D=p(0),B=p(""),T=p(1),R=p(20),Be=p(!1),j=O(()=>{if(!C.value)return[];const t=k.value.find(e=>e.path===C.value);return t?t.layers:[]}),Te=O(()=>{const t=(T.value-1)*R.value,e=t+R.value;return j.value.slice(t,e)});function te(){if(w.value===1&&!G.value){_.warning("请先选择一个质检许可");return}if(w.value===2&&!C.value){_.warning("请先选择一个GDB数据库");return}w.value++}function ae(){w.value>0&&w.value--}async function A(){var t;J.value=!0;try{const e=(t=U.user)==null?void 0:t.username;if(!e){_.error("未登录，无法获取许可列表"),K.value=[];return}const n=await E.post("/api/quality/tools/my-approvals",{},{headers:{"X-Username":e}});if(n.data.success){K.value=n.data.data;const r=K.value.find(u=>{if(N(u.status).text!=="已通过")return!1;const g=new Date(u.end_date),h=new Date;return h.setHours(0,0,0,0),!(g<h||u.count>=u.usage_count)});G.value=r?r.id:null}else _.error(n.data.message||"获取许可列表失败")}catch{_.error("获取许可列表失败")}finally{J.value=!1}}xe(w,t=>{t===1&&A()});function N(t){return{pending:{type:"info",text:"审批中"},approved:{type:"success",text:"已通过"},rejected:{type:"danger",text:"已驳回"},expired:{type:"warning",text:"已过期"},exhausted:{type:"warning",text:"已耗尽"},available:{type:"success",text:"可用"}}[t]||{type:"default",text:t}}function ce({row:t}){if(N(t.status).text!=="已通过")return"disabled-row";const e=new Date(t.end_date),n=new Date;return n.setHours(0,0,0,0),e<n||t.count>=t.usage_count?"disabled-row":t.id===G.value?"clickable-row selected-row":"clickable-row"}function je(t){if(N(t.status).text!=="已通过")return;const e=new Date(t.end_date),n=new Date;n.setHours(0,0,0,0),!(e<n)&&(t.count>=t.usage_count||(G.value=t.id))}function qe(t){var r;const e=(r=t.name.split(".").pop())==null?void 0:r.toLowerCase();if(!["zip","rar","7z"].includes(e))return _.error("只支持zip、rar、7z格式的压缩包"),!1;const n=10*1024*1024*1024;return t.size>n?(_.error("文件大小不能超过10GB"),!1):(L.value=!0,D.value=0,B.value="开始上传...",k.value=[],C.value="",!0)}function He(t){D.value=Math.round(t.loaded/t.total*100),D.value<100?B.value=`上传中... ${D.value}%`:B.value="解析GDB文件中..."}function Ee(t){if(L.value=!1,D.value=100,B.value="上传完成",t.success&&t.data&&t.data.gdb_databases){k.value=t.data.gdb_databases.map(n=>({...n,name:n.path.split(/[/\\]/).pop()||"",relativePath:n.path.replace(t.data.base_path||"","").replace(/^[/\\]/,""),totalFeatures:n.layers.reduce((r,u)=>r+(u.feature_count||0),0)})),k.value.length>0&&(C.value=k.value[0].path);const e=k.value.reduce((n,r)=>n+r.layers.length,0);_.success(`GDB解析成功，发现 ${k.value.length} 个数据库，共 ${e} 个图层`)}else _.error(t.message||"GDB解析失败")}function Fe(t){L.value=!1,D.value=0,B.value="",console.error("GDB上传错误详情:",t),_.error("GDB上传失败："+(t.message||"未知错误"))}function Re(t){return t.feature_set&&t.feature_set!==t.name?`${t.feature_set}/${t.name}`:t.name}function Ne(t){return typeof t=="object"&&t.name?t.name:t}function Pe(t){if(typeof t=="object"){const e=[];return t.name&&e.push(`字段名: ${t.name}`),t.type&&e.push(`类型: ${t.type}`),t.length&&e.push(`长度: ${t.length}`),e.join(`
`)}return t}function We(t){C.value=t.path,T.value=1,$.value={}}function Ye(t){R.value=t,T.value=1,$.value={}}function Ke(t){T.value=t,$.value={}}const $=p({});function Xe(t,e,n){const r=_e(n);return t>=r}function pe(t,e){const n=_e(e);return t>n?t-n:0}function _e(t){const e=`row-${t}`;if($.value[e])return $.value[e];const n=window.innerWidth;let r=3;return n>=1920?r=8:n>=1600?r=6:n>=1400?r=5:n>=1200&&(r=4),$.value[e]=r,Ut(()=>{Ae(t)}),r}function Ae(t){try{const e=`fieldsContainer-${t}`,n=document.querySelector(`[ref="${e}"]`);if(!n)return;const r=n.clientWidth-120;let u=0,g=0;const h=n.querySelectorAll(".field-tag:not(.field-hidden)");if(h.length<=3){const i=`row-${t}`;$.value[i]=h.length;return}for(let i=0;i<h.length;i++){const Y=h[i].offsetWidth+6;if(i<2||u+Y<=r)u+=Y,g++;else break}const se=`row-${t}`;$.value[se]=Math.max(2,g)}catch(e){console.warn("计算字段可见性失败:",e)}}function Qe(t){return t?{CGCS2000_3_Degree_GK_CM_114E:"CGCS2000 114°E",CGCS2000_3_Degree_GK_CM_117E:"CGCS2000 117°E",CGCS2000_3_Degree_GK_CM_120E:"CGCS2000 120°E",WGS_1984_UTM_Zone_49N:"WGS84 UTM 49N",WGS_1984_UTM_Zone_50N:"WGS84 UTM 50N",Beijing_1954_3_Degree_GK_CM_114E:"Beijing54 114°E",Beijing_1954_3_Degree_GK_CM_117E:"Beijing54 117°E",Xian_1980_3_Degree_GK_CM_114E:"Xian80 114°E",Xian_1980_3_Degree_GK_CM_117E:"Xian80 117°E"}[t]||t:"未知"}function Ze(t){switch(t){case"point":return"Location";case"line":return"Connection";case"polygon":return"Grid";default:return"Document"}}function Ie(t){switch(t){case"point":return"success";case"line":return"warning";case"polygon":return"info";default:return""}}const Oe=O(()=>j.value.reduce((t,e)=>t+(e.feature_count||0),0)),P=p("quality");function Je(t){t.name==="history"&&ge()}const Q=p([]),le=p(!1),Z=p(!1),fe=p("");function me(t,e="yyyy-MM-dd HH:mm"){if(!t)return"--";try{const n=new Date(t),r=u=>u<10?"0"+u:u;return e==="yyyy-MM-dd HH:mm:ss"?`${n.getFullYear()}-${r(n.getMonth()+1)}-${r(n.getDate())} ${r(n.getHours())}:${r(n.getMinutes())}:${r(n.getSeconds())}`:`${n.getFullYear()}-${r(n.getMonth()+1)}-${r(n.getDate())} ${r(n.getHours())}:${r(n.getMinutes())}`}catch{return"--"}}function et(t){if(!t)return"--";const e=Math.floor(Number(t)/1e3);if(e<60)return`${e}s`;const n=Math.floor(e/60),r=e%60;return`${n}m${r}s`}function tt(t){return{running:"warning",success:"success",completed:"success",pending:"info",failed:"danger"}[t]||"info"}function at(t){return{running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"}[t]||t}async function ge(){var t;le.value=!0;try{const e=(t=U.user)==null?void 0:t.username,n=await E.post("/api/quality/tools/run_records",{fmw_id:"quality",username:e},{headers:{"X-Username":e}});n.data.success?Q.value=n.data.data.records:_.error(n.data.message||"获取历史记录失败")}catch{_.error("获取历史记录失败")}finally{le.value=!1}}function lt(t){try{const e=`tools/quality/output/${t.task_id}/${t.file_name}`,n=`/api/tools/download_result?file_path=${encodeURIComponent(e)}`,r=document.createElement("a");r.style.display="none",document.body.appendChild(r),r.href=n,r.download=t.file_name,r.click(),setTimeout(()=>{document.body.removeChild(r)},100),_.success("开始下载")}catch{_.error("下载失败，请稍后重试")}}function st(t){t.error_message?(fe.value=t.error_message,Z.value=!0):_.warning("暂无错误信息")}async function nt(t){var e;try{await zt.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const n=Q.value.findIndex(r=>r.task_id===t.task_id);n!==-1&&Q.value.splice(n,1),await E.post("/api/quality/tools/delete_result",{task_id:t.task_id},{headers:{"X-Username":(e=U.user)==null?void 0:e.username}}),_.success("删除成功")}catch(n){n!=="cancel"&&_.error("删除失败")}}function ot(){_.info("后续实现：提交质检任务"),P.value="history",ge()}return xe(w,t=>{t===1&&A()}),(t,e)=>{const n=d("el-step"),r=d("el-steps"),u=d("el-icon"),g=d("el-button"),h=d("el-skeleton-item"),se=d("el-skeleton"),i=d("el-table-column"),W=d("el-tag"),Y=d("el-radio"),I=d("el-table"),ne=d("el-input"),q=d("el-form-item"),rt=d("el-date-picker"),it=d("el-input-number"),ut=d("el-option"),dt=d("el-select"),ct=d("el-form"),ve=d("el-dialog"),pt=d("el-upload"),_t=d("el-progress"),ft=d("el-pagination"),ye=d("el-card"),he=d("el-tab-pane"),mt=d("el-tooltip"),gt=d("el-button-group"),vt=d("el-tabs"),be=Mt("loading");return f(),b("div",Bt,[a(vt,{modelValue:P.value,"onUpdate:modelValue":e[14]||(e[14]=l=>P.value=l),class:"cad-tabs",onTabClick:Je},{default:s(()=>[a(he,{label:"质检工具",name:"quality"},{default:s(()=>[a(oe,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(o("div",Tt,[a(ye,{class:"license-card",style:{"margin-top":"0"}},{default:s(()=>[a(r,{active:w.value,"finish-status":"success",simple:"",class:"quality-steps"},{default:s(()=>[a(n,{title:"开始"}),a(n,{title:"选择许可"}),a(n,{title:"上传GDB"}),a(n,{title:"质检配置"})]),_:1},8,["active"]),o("div",jt,[o("div",{class:"progress-bar",style:bt({width:`${w.value/(Va-1)*100}%`})},null,4)]),o("div",qt,[a(oe,{name:"step-fade",mode:"out-in"},{default:s(()=>[(f(),b("div",{key:w.value},[M(o("div",Ht,[o("div",Et,[a(g,{type:"primary",size:"large",class:"start-button",onClick:te},{default:s(()=>[a(u,null,{default:s(()=>[a(x(re))]),_:1}),e[17]||(e[17]=m(" 开始质检 "))]),_:1})])],512),[[H,w.value===0]]),M(o("div",Ft,[o("div",Rt,[a(g,{type:"primary",onClick:Ge},{default:s(()=>[a(u,null,{default:s(()=>[a(x(wt))]),_:1}),e[18]||(e[18]=m(" 申请许可 "))]),_:1}),a(g,{type:"primary",onClick:Se,disabled:X.value},{default:s(()=>[a(u,{class:Ce({"refresh-rotate":X.value})},{default:s(()=>[a(x(xt))]),_:1},8,["class"]),e[19]||(e[19]=m(" 刷新许可 "))]),_:1},8,["disabled"])]),J.value?(f(),V(se,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:s(()=>[a(h,{variant:"text",style:{width:"80px","margin-right":"16px"}}),a(h,{variant:"text",style:{width:"150px","margin-right":"16px"}}),a(h,{variant:"text",style:{width:"150px","margin-right":"16px"}}),a(h,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(h,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(h,{variant:"text",style:{width:"180px","margin-right":"16px"}}),a(h,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(h,{variant:"text",style:{width:"80px"}})]),_:1})):S("",!0),a(I,{data:K.value,style:{width:"100%"},border:"",onRowClick:je,"row-class-name":ce,class:"quality-table"},{default:s(()=>[a(i,{type:"index",label:"序号",width:"80",align:"center"}),a(i,{prop:"user_project",label:"项目","min-width":"150","show-overflow-tooltip":""}),a(i,{prop:"reason",label:"原因","min-width":"150","show-overflow-tooltip":""}),a(i,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),a(i,{prop:"count",label:"已用次数",width:"100",align:"center"}),a(i,{prop:"end_date",label:"截止时间",width:"180",align:"center"}),a(i,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[a(W,{type:N(l.status).type},{default:s(()=>[m(c(N(l.status).text),1)]),_:2},1032,["type"])]),_:1}),a(i,{label:"选择",width:"80",align:"center"},{default:s(({row:l})=>[a(Y,{modelValue:G.value,"onUpdate:modelValue":e[0]||(e[0]=y=>G.value=y),label:l.id,disabled:ce({row:l})==="disabled-row",class:"custom-radio"},{default:s(()=>[o("span",Nt,c(l.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"]),o("div",Pt,[a(g,{onClick:ae},{default:s(()=>[a(u,null,{default:s(()=>[a(x(ie))]),_:1}),e[20]||(e[20]=m(" 上一步 "))]),_:1}),a(g,{type:"primary",onClick:te,disabled:!G.value},{default:s(()=>[e[21]||(e[21]=m(" 下一步 ")),a(u,null,{default:s(()=>[a(x(re))]),_:1})]),_:1},8,["disabled"])]),a(ve,{modelValue:z.value,"onUpdate:modelValue":e[8]||(e[8]=l=>z.value=l),title:"申请许可-数据质检",width:"500px","close-on-click-modal":!1,onClose:e[9]||(e[9]=l=>z.value=!1),onAfterClose:de},{footer:s(()=>[o("span",Qt,[a(g,{onClick:e[7]||(e[7]=l=>z.value=!1)},{default:s(()=>e[22]||(e[22]=[m("取消")])),_:1}),a(g,{type:"primary",loading:ee.value,onClick:ze},{default:s(()=>e[23]||(e[23]=[m("提交")])),_:1},8,["loading"])])]),default:s(()=>[a(ct,{ref_key:"licenseFormRef",ref:F,model:v.value,rules:Me,"label-width":"100px",class:"apply-form",size:"small"},{default:s(()=>[a(q,{label:"工具名称",prop:"tool_name"},{default:s(()=>[a(ne,{modelValue:v.value.tool_name,"onUpdate:modelValue":e[1]||(e[1]=l=>v.value.tool_name=l),value:"数据质检",disabled:""},null,8,["modelValue"])]),_:1}),a(q,{label:"使用项目",prop:"user_project"},{error:s(({error:l})=>[o("span",Wt,c(l),1)]),default:s(()=>[a(ne,{modelValue:v.value.user_project,"onUpdate:modelValue":e[2]||(e[2]=l=>v.value.user_project=l),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),a(q,{label:"申请原因",prop:"reason"},{error:s(({error:l})=>[o("span",Yt,c(l),1)]),default:s(()=>[a(ne,{modelValue:v.value.reason,"onUpdate:modelValue":e[3]||(e[3]=l=>v.value.reason=l),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),a(q,{label:"有效期",prop:"end_date"},{error:s(({error:l})=>[o("span",Kt,c(l),1)]),default:s(()=>[a(rt,{modelValue:v.value.end_date,"onUpdate:modelValue":e[4]||(e[4]=l=>v.value.end_date=l),type:"date",placeholder:"请选择有效期","disabled-date":Ve,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),a(q,{label:"申请次数",prop:"usage_count"},{error:s(({error:l})=>[o("span",Xt,c(l),1)]),default:s(()=>[a(it,{modelValue:v.value.usage_count,"onUpdate:modelValue":e[5]||(e[5]=l=>v.value.usage_count=l),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(q,{label:"审批人",prop:"approver"},{error:s(({error:l})=>[o("span",At,c(l),1)]),default:s(()=>[a(dt,{modelValue:v.value.approver,"onUpdate:modelValue":e[6]||(e[6]=l=>v.value.approver=l),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:s(()=>[(f(!0),b(ke,null,De(ue.value,l=>(f(),V(ut,{key:l.username,label:l.real_name,value:l.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],512),[[H,w.value===1]]),M(o("div",Zt,[e[32]||(e[32]=o("h2",{class:"step-title"},"上传GDB压缩包并选择数据库",-1)),o("div",It,[o("div",Ot,[o("div",Jt,[e[26]||(e[26]=o("h3",null,"上传GDB压缩包",-1)),a(pt,{class:"gdb-upload",drag:"",action:$e("/api/quality/upload-gdb"),headers:Le.value,"show-file-list":!1,"before-upload":qe,"on-success":Ee,"on-error":Fe,"on-progress":He,accept:".zip,.rar,.7z",disabled:L.value},{tip:s(()=>e[24]||(e[24]=[o("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包，单个文件不超过10GB ",-1)])),default:s(()=>[a(u,{class:"el-icon--upload"},{default:s(()=>[a(x(Ct))]),_:1}),e[25]||(e[25]=o("div",{class:"el-upload__text"},[m(" 将GDB压缩包拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},8,["action","headers","disabled"]),L.value?(f(),b("div",ea,[a(_t,{percentage:D.value,status:D.value===100?"success":void 0},null,8,["percentage","status"]),o("p",ta,c(B.value),1)])):S("",!0)])]),o("div",aa,[k.value.length>0?(f(),b("div",la,[e[28]||(e[28]=o("h3",null,"选择GDB数据库",-1)),a(I,{data:k.value,style:{width:"100%"},border:"",size:"small",onRowClick:We,"highlight-current-row":""},{default:s(()=>[a(i,{label:"选择",width:"50",align:"center"},{default:s(l=>[a(Y,{modelValue:C.value,"onUpdate:modelValue":e[10]||(e[10]=y=>C.value=y),label:l.row.path,onClick:e[11]||(e[11]=kt(()=>{},["stop"]))},{default:s(()=>e[27]||(e[27]=[o("span",null,null,-1)])),_:2},1032,["modelValue","label"])]),_:1}),a(i,{prop:"name",label:"GDB名称","min-width":"120"},{default:s(l=>[o("div",sa,c(l.row.name),1)]),_:1}),a(i,{prop:"layers",label:"图层数",width:"80",align:"center"},{default:s(l=>[o("span",na,c(l.row.layers.length),1)]),_:1}),a(i,{prop:"totalFeatures",label:"要素数",width:"100",align:"center"},{default:s(l=>{var y;return[o("span",oa,c(((y=l.row.totalFeatures)==null?void 0:y.toLocaleString())||0),1)]}),_:1})]),_:1},8,["data"])])):L.value?S("",!0):(f(),b("div",ra,[a(u,{class:"empty-icon"},{default:s(()=>[a(x(Dt))]),_:1}),e[29]||(e[29]=o("p",null,"请先上传GDB压缩包",-1))]))])]),C.value&&j.value.length>0?(f(),b("div",ia,[o("div",ua,[o("div",da,[e[30]||(e[30]=o("h3",null,"图层预览 (所有图层将进入质检流程)",-1)),o("div",ca," 共 "+c(j.value.length)+" 个图层，"+c(Oe.value.toLocaleString())+" 个要素 ",1)]),M((f(),V(I,{data:Te.value,style:{width:"100%"},border:""},{default:s(()=>[a(i,{prop:"name",label:"图层名称",width:"300"},{default:s(l=>[o("div",pa,[a(u,null,{default:s(()=>[(f(),V($t(Ze(l.row.type))))]),_:2},1024),o("span",null,c(Re(l.row)),1)])]),_:1}),a(i,{prop:"geometry_type",label:"几何类型",width:"120"},{default:s(l=>[a(W,{type:Ie(l.row.type),size:"small"},{default:s(()=>[m(c(l.row.geometry_type),1)]),_:2},1032,["type"])]),_:1}),a(i,{prop:"feature_set",label:"要素集",width:"200"},{default:s(l=>[l.row.feature_set?(f(),b("span",_a,c(l.row.feature_set),1)):(f(),b("span",fa,"-"))]),_:1}),a(i,{prop:"feature_count",label:"要素数量",width:"120"},{default:s(l=>{var y;return[m(c(((y=l.row.feature_count)==null?void 0:y.toLocaleString())||0),1)]}),_:1}),a(i,{prop:"coordinate_system",label:"坐标系",width:"200"},{default:s(l=>[l.row.coordinate_system?(f(),b("span",{key:0,title:l.row.coordinate_system},c(Qe(l.row.coordinate_system)),9,ma)):(f(),b("span",ga,"未知"))]),_:1}),a(i,{prop:"fields",label:"字段信息","min-width":"300"},{default:s(l=>[l.row.fields&&l.row.fields.length>0?(f(),b("div",{key:0,class:"fields-display",ref:`fieldsContainer-${l.$index}`},[(f(!0),b(ke,null,De(l.row.fields,(y,we)=>(f(),V(W,{key:y.name||y,size:"small",effect:"plain",class:Ce(["field-tag",{"field-hidden":Xe(we,l.row.fields.length,l.$index)}]),title:Pe(y),ref_for:!0,ref:`fieldTag-${l.$index}-${we}`},{default:s(()=>[m(c(Ne(y)),1)]),_:2},1032,["class","title"]))),128)),pe(l.row.fields.length,l.$index)>0?(f(),b("span",va," +"+c(pe(l.row.fields.length,l.$index))+" 个字段 ",1)):S("",!0)],512)):(f(),b("span",ya,"无字段信息"))]),_:1})]),_:1},8,["data"])),[[be,Be.value]]),o("div",ha,[a(ft,{"current-page":T.value,"onUpdate:currentPage":e[12]||(e[12]=l=>T.value=l),"page-size":R.value,"onUpdate:pageSize":e[13]||(e[13]=l=>R.value=l),"page-sizes":[10,20,50,100],total:j.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ye,onCurrentChange:Ke},null,8,["current-page","page-size","total"])])])])):S("",!0),o("div",ba,[a(g,{onClick:ae},{default:s(()=>[a(u,null,{default:s(()=>[a(x(ie))]),_:1}),e[31]||(e[31]=m(" 上一步 "))]),_:1}),a(g,{type:"primary",disabled:!C.value,onClick:te},{default:s(()=>[m(" 下一步（"+c(j.value.length)+"个图层） ",1),a(u,null,{default:s(()=>[a(x(re))]),_:1})]),_:1},8,["disabled"])])],512),[[H,w.value===2]]),M(o("div",wa,[e[35]||(e[35]=o("h2",{class:"step-title"},"质检项配置",-1)),e[36]||(e[36]=o("p",null,"支持配置表模板下载、上传、在线配置、历史配置读取等功能。后续详细实现。",-1)),o("div",xa,[a(g,{onClick:ae},{default:s(()=>[a(u,null,{default:s(()=>[a(x(ie))]),_:1}),e[33]||(e[33]=m(" 上一步 "))]),_:1}),a(g,{type:"primary",onClick:ot},{default:s(()=>e[34]||(e[34]=[m("提交任务")])),_:1})])],512),[[H,w.value===3]])]))]),_:1})])]),_:1})],512),[[H,P.value==="quality"]])]),_:1})]),_:1}),a(he,{label:"历史记录",name:"history"},{default:s(()=>[a(oe,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(o("div",Ca,[a(ye,{class:"history-card",style:{"margin-top":"0px"}},{default:s(()=>[o("div",ka,[M((f(),V(I,{data:Q.value,style:{width:"100%"}},{default:s(()=>[a(i,{type:"index",label:"序号",width:"80",align:"center"}),a(i,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:s(({row:l})=>[a(mt,{content:me(l.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:s(()=>[o("span",null,c(me(l.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),a(i,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[a(W,{type:tt(l.status)},{default:s(()=>[m(c(at(l.status)),1)]),_:2},1032,["type"])]),_:1}),a(i,{prop:"time_consuming",label:"运行耗时",width:"120",align:"center"},{default:s(({row:l})=>[m(c(et(l.time_consuming)),1)]),_:1}),a(i,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),a(i,{prop:"up_nums",label:"质检文件数量","min-width":"130",align:"center","show-overflow-tooltip":""}),a(i,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:l})=>[o("div",Da,[a(gt,null,{default:s(()=>[l.status==="success"&&l.up_nums>0&&l.file_size!=="0.0MB"?(f(),V(g,{key:0,type:"success",size:"small",onClick:y=>lt(l),disabled:l.status!=="success"},{default:s(()=>[a(u,null,{default:s(()=>[a(x(Vt))]),_:1}),e[37]||(e[37]=m(" 下载 "))]),_:2},1032,["onClick","disabled"])):S("",!0),l.error_message?(f(),V(g,{key:1,type:"info",size:"small",onClick:y=>st(l)},{default:s(()=>[a(u,null,{default:s(()=>[a(x(Gt))]),_:1}),e[38]||(e[38]=m(" 日志 "))]),_:2},1032,["onClick"])):S("",!0),a(g,{type:"danger",size:"small",onClick:y=>nt(l)},{default:s(()=>[a(u,null,{default:s(()=>[a(x(St))]),_:1}),e[39]||(e[39]=m(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[be,le.value]])])]),_:1})],512),[[H,P.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),a(ve,{modelValue:Z.value,"onUpdate:modelValue":e[16]||(e[16]=l=>Z.value=l),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:s(()=>[o("span",Ma,[a(g,{onClick:e[15]||(e[15]=l=>Z.value=!1)},{default:s(()=>e[40]||(e[40]=[m("关闭")])),_:1})])]),default:s(()=>[o("div",$a,[o("pre",null,c(fe.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"])])}}}),za=Lt(Ga,[["__scopeId","data-v-fe3abb91"]]);export{za as default};
