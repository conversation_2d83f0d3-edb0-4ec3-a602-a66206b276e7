import{d as $e,J as oe,r as i,u as Ge,K as He,I as le,o as Ke,a as Qe,c as M,b as l,v as ve,e as t,f as u,w as s,A as T,R as b,E as d,i as ge,y as m,l as v,ad as We,U as F,O as fe,s as ne,F as _e,G as Xe,x as Ze,ae as ye,t as et,af as tt,p as at,Q as A,k as g,_ as st}from"./index-Dn7OnccA.js";const ot={class:"settings"},lt={key:0,class:"loading-container"},nt={key:1,class:"content-wrapper"},rt={class:"card-header"},it={class:"card-title"},dt={class:"card-actions"},ut={class:"task-stats"},ct={class:"stat-item"},mt={class:"stat-value running"},pt={class:"stat-item"},vt={class:"stat-value pending"},gt={class:"task-list-container"},ft={key:0,class:"no-tasks"},_t={key:1},yt={class:"card-header"},bt={class:"card-title"},wt={class:"action-buttons"},ht={class:"card-header"},kt={class:"card-title"},xt={class:"port-control"},Mt={class:"port-status"},Vt={class:"action-buttons"},Pt={class:"card-header"},St={class:"card-title"},Tt={class:"action-buttons"},It={class:"card-header"},Ct={class:"card-title"},Nt={key:2,class:"restart-mask"},Rt={class:"restart-mask-content"},Ut={class:"restart-title"},Ot=$e({__name:"SettingsView",setup(Dt){const n=oe({limits:1,server_host:"127.0.0.1",server_port:9997,mode:"offline"}),r=oe({visibleItems:{home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},itemModes:{home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},itemProgress:{home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:75,settings:75,about:75}}),be=[{key:"home",label:"首页",description:"系统首页和仪表板"},{key:"tools",label:"我的工具",description:"个人工具管理"},{key:"market",label:"工具市场",description:"浏览和申请工具"},{key:"quality",label:"数据质检",description:"数据质量检查平台"},{key:"coordinate",label:"坐标转换",description:"坐标系转换工具"},{key:"cadtogis",label:"CAD转GIS",description:"CAD文件转换工具"},{key:"requirement",label:"需求提交",description:"功能需求申请"},{key:"layerPreview",label:"图层预览",description:"地理数据预览"},{key:"userManagement",label:"用户管理",description:"用户账户管理（管理员）"},{key:"settings",label:"设置",description:"系统设置（管理员）"},{key:"about",label:"关于",description:"系统信息"}],w=i(null),z=i(null),P=i([]),I=i(!1),E=i(!1),C=i(!1),we=i(),he=i(),q=i(!1),N=i("."),Y=i(!1),h=i(!1),k=i(!1),ke=Ge(),V=i(!1),B=i(!0),j=i(!0),$=i(!0),G=i(!0),H=i([]),K=i({running_count:0,pending_count:0}),Q=i(!1);let S=null;const f=oe({tableName:"task_records_all",dateRange:null}),W=i(!1),xe=[{text:"最近一周",value:()=>{const a=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*7),[e,a]}},{text:"最近一个月",value:()=>{const a=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*30),[e,a]}},{text:"最近三个月",value:()=>{const a=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*90),[e,a]}}];let R=null,X=null;He(()=>n.server_port,()=>{X&&clearTimeout(X),X=setTimeout(()=>{h.value=!1,k.value=!1},300)},{flush:"post"});const Me=async()=>{j.value=!0;try{const a=await b.post("/api/settings/get",{},{timeout:1e4});a.data.success?(Object.assign(n,a.data.data),w.value=JSON.parse(JSON.stringify(a.data.data)),n.mode==="online"&&re().catch(console.error)):d.error(a.data.message||"获取设置失败")}catch(a){console.error("获取设置失败:",a),d.error("获取设置失败")}finally{j.value=!1,ee()}},re=async()=>{try{const a=await b.post("/api/settings/ips",{},{timeout:5e3});a.data.success&&Array.isArray(a.data.data)?(P.value=a.data.data,n.mode==="online"&&!P.value.includes(n.server_host)&&(n.server_host=P.value[0]||"127.0.0.1")):P.value=["127.0.0.1"]}catch(a){console.error("获取IP列表失败:",a),P.value=["127.0.0.1"]}},Ve=async a=>{a==="offline"?n.server_host="127.0.0.1":setTimeout(()=>{re().catch(console.error)},0)},ie=le(()=>w.value&&n.server_port!==w.value.server_port),de=le(()=>w.value?n.limits!==w.value.limits||n.server_host!==w.value.server_host||n.server_port!==w.value.server_port||n.mode!==w.value.mode:!1),Pe=le(()=>de.value?ie.value?h.value&&k.value&&!I.value:!I.value:!1),Se=async()=>{if(!de.value){d.info("未检测到任何更改，无需保存");return}if(ie.value&&(!h.value||!k.value)){d.warning("端口更改后必须检测端口占用，并确保端口可用后才能保存！");return}A.confirm("确定要保存当前设置吗？保存后需重启服务器才能生效。","确认保存",{confirmButtonText:"保存",cancelButtonText:"取消",type:"warning"}).then(async()=>{I.value=!0;try{const a=await b.post("/api/settings/set",{...n});a.data.success?(d.success("设置已保存，将在重启服务器后生效"),w.value=JSON.parse(JSON.stringify(n))):d.error(a.data.message||"保存失败")}catch{d.error("保存失败")}finally{I.value=!1}})},Te=async()=>{A.confirm("确认要重启服务器吗？","确认重启",{confirmButtonText:"重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{C.value=!0;try{const a=await b.post("/api/settings/restart",{});a.data.success?(d.success("服务器重启命令已发送"),q.value=!0,ue(),ce()):a.data.needForce?A.confirm(a.data.message||"当前有任务正在进行，是否强制重启？","警告",{confirmButtonText:"强制重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{C.value=!0;try{const e=await b.post("/api/settings/restart",{force:!0});e.data.success?(d.success("服务器重启命令已发送"),q.value=!0,ue(),ce()):d.error(e.data.message||"重启失败")}finally{C.value=!1}}):d.error(a.data.message||"重启失败")}catch{d.error("重启失败")}finally{C.value=!1}})};function ue(){N.value=".",R&&clearInterval(R),R=setInterval(()=>{N.value=N.value.length<3?N.value+".":"."},500)}function ce(){const a=window.location.protocol;let e=n.server_host;e==="0.0.0.0"&&(e=window.location.hostname||"127.0.0.1");const x=n.server_port;setTimeout(()=>{q.value=!1,ke.logout(),window.location.href=`${a}//${e}:${x}/`},5e3)}const Ie=async()=>{Y.value=!0;try{const a=await b.post("/api/settings/check_port",{port:n.server_port});await new Promise(e=>setTimeout(e,200)),h.value=!0,a.data.success?k.value=!0:k.value=!1}catch{await new Promise(e=>setTimeout(e,200)),h.value=!0,k.value=!1}finally{Y.value=!1}},Ce=async()=>{$.value=!0;try{const a=await b.post("/api/settings/get-nav-settings",{},{timeout:8e3});if(a.data.success){const e={...a.data.data.visibleItems,userManagement:!0,settings:!0};Object.assign(r.visibleItems,e),a.data.data.itemModes&&Object.assign(r.itemModes,a.data.data.itemModes),a.data.data.itemProgress&&Object.assign(r.itemProgress,a.data.data.itemProgress),z.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:r.itemModes,itemProgress:r.itemProgress}))}else{const e={...r.visibleItems,userManagement:!0,settings:!0};z.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:r.itemModes,itemProgress:r.itemProgress}))}}catch(a){console.error("获取导航栏设置失败:",a);const e={...r.visibleItems,userManagement:!0,settings:!0};z.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:r.itemModes,itemProgress:r.itemProgress}))}finally{$.value=!1,ee()}},Ne=async()=>{E.value=!0;try{const a={visibleItems:{...r.visibleItems,userManagement:!0,settings:!0},itemModes:{...r.itemModes,userManagement:"production",settings:"production"},itemProgress:{...r.itemProgress,userManagement:100,settings:100}},e=await b.post("/api/settings/set-nav-settings",a);e.data.success?(d.success("导航栏设置已保存"),r.visibleItems=a.visibleItems,r.itemModes=a.itemModes,r.itemProgress=a.itemProgress,z.value=JSON.parse(JSON.stringify(a)),window.dispatchEvent(new CustomEvent("navSettingsChanged",{detail:a}))):d.error(e.data.message||"保存导航栏设置失败")}catch{d.error("保存导航栏设置失败")}finally{E.value=!1}},Re=()=>{A.confirm("确定要重置导航栏设置为默认值吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{r.visibleItems={home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},r.itemModes={home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},r.itemProgress={home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:100,settings:100,about:75},d.success("已重置为默认设置")})},Ue=async()=>{G.value=!0;try{const a=await ge.post("/api/get_allow_register",{},{timeout:5e3});a.data&&typeof a.data.allow_register=="boolean"?V.value=a.data.allow_register:a.data&&typeof a.data.allow_register=="string"?V.value=a.data.allow_register==="true":V.value=!1}catch(a){console.error("获取注册开关状态失败:",a),V.value=!1}finally{G.value=!1,ee()}},Oe=async a=>{try{await ge.post("/api/set_allow_register",{allow_register:a})}catch{V.value=!a}},Z=async()=>{Q.value=!0;try{const a=await b.post("/api/task/current/list",{});a.data.success?(H.value=a.data.data.tasks||[],K.value={running_count:a.data.data.running_count||0,pending_count:a.data.data.pending_count||0}):d.error(a.data.message||"获取任务列表失败")}catch(a){console.error("获取任务列表失败:",a),d.error("获取任务列表失败")}finally{Q.value=!1}},De=a=>{if(!a)return"";try{return new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return a}},ze=()=>{S&&clearInterval(S),Z(),S=setInterval(()=>{Z()},5e3)},qe=()=>{S&&(clearInterval(S),S=null)},Be=async()=>{var a,e;if(!f.tableName){d.warning("请选择要导出的数据表");return}W.value=!0;try{const x={table_name:f.tableName,date_range:f.dateRange},p=await b.post("/api/task/export-excel",x,{responseType:"blob"}),_=new Blob([p.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),L=window.URL.createObjectURL(_),c=document.createElement("a");c.href=L;const te=new Date().toISOString().slice(0,19).replace(/:/g,"-");c.download=`${f.tableName}_${te}.xlsx`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(L),d.success("导出成功")}catch(x){console.error("导出失败:",x),(e=(a=x.response)==null?void 0:a.data)!=null&&e.message?d.error(x.response.data.message):d.error("导出失败")}finally{W.value=!1}},Le=()=>{f.tableName="task_records_all",f.dateRange=null},ee=()=>{B.value=j.value||$.value||G.value},Je=async()=>{B.value=!0;const a=[Me().catch(console.error),Ce().catch(console.error),Ue().catch(console.error)];try{await Promise.allSettled(a)}catch(e){console.error("加载数据时发生错误:",e)}setTimeout(()=>{B.value=!1},100)};return Ke(()=>{Je(),ze()}),Qe(()=>{qe(),R&&clearInterval(R)}),(a,e)=>{const x=u("el-skeleton"),p=u("el-icon"),_=u("el-button"),L=u("el-empty"),c=u("el-table-column"),te=u("el-tag"),me=u("el-table"),U=u("el-card"),O=u("el-option"),ae=u("el-select"),y=u("el-form-item"),Fe=u("el-date-picker"),J=u("el-form"),se=u("el-input-number"),pe=u("el-radio"),Ae=u("el-radio-group"),Ee=u("el-input"),Ye=u("el-checkbox"),je=u("el-switch");return g(),M("div",ot,[e[28]||(e[28]=l("div",{class:"page-header"},null,-1)),B.value?(g(),M("div",lt,[t(x,{rows:8,animated:""}),e[8]||(e[8]=l("div",{class:"loading-text"},"正在加载设置...",-1))])):(g(),M("div",nt,[t(U,{class:"settings-card task-monitor-card"},{header:s(()=>[l("div",rt,[l("span",it,[t(p,{class:"card-icon"},{default:s(()=>[t(v(We))]),_:1}),e[9]||(e[9]=m(" 任务监控 "))]),l("div",dt,[t(_,{type:"primary",size:"small",onClick:Z,loading:Q.value},{default:s(()=>[t(p,null,{default:s(()=>[t(v(F))]),_:1}),e[10]||(e[10]=m(" 刷新 "))]),_:1},8,["loading"])])])]),default:s(()=>[l("div",ut,[l("div",ct,[e[11]||(e[11]=l("span",{class:"stat-label"},"运行中",-1)),l("span",mt,T(K.value.running_count),1)]),l("div",pt,[e[12]||(e[12]=l("span",{class:"stat-label"},"等待中",-1)),l("span",vt,T(K.value.pending_count),1)])]),l("div",gt,[H.value.length===0?(g(),M("div",ft,[t(L,{description:"暂无任务"})])):(g(),M("div",_t,[t(me,{data:H.value,border:"",size:"small",style:{width:"100%"}},{default:s(()=>[t(c,{prop:"task_id",label:"任务ID",width:"180"}),t(c,{label:"状态",width:"80"},{default:s(o=>[t(te,{type:o.row.status==="running"?"success":"warning",size:"small"},{default:s(()=>[m(T(o.row.status==="running"?"运行中":"等待中"),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"tool_name",label:"工具名称","min-width":"120"}),t(c,{prop:"submitter",label:"提交者",width:"100"}),t(c,{prop:"project",label:"项目",width:"120"}),t(c,{prop:"file_name",label:"文件名","min-width":"150","show-overflow-tooltip":""}),t(c,{prop:"up_nums",label:"文件数",width:"80",align:"center"}),t(c,{label:"提交时间",width:"160"},{default:s(o=>[m(T(De(o.row.submit_time)),1)]),_:1})]),_:1},8,["data"])]))])]),_:1}),t(U,{class:"settings-card data-export-card"},{header:s(()=>[l("div",yt,[l("span",bt,[t(p,{class:"card-icon"},{default:s(()=>[t(v(fe))]),_:1}),e[13]||(e[13]=m(" 运行记录导出 "))])])]),default:s(()=>[t(J,{"label-width":"120px",class:"export-form"},{default:s(()=>[t(y,{label:"选择数据表"},{default:s(()=>[t(ae,{modelValue:f.tableName,"onUpdate:modelValue":e[0]||(e[0]=o=>f.tableName=o),placeholder:"请选择要导出的数据表"},{default:s(()=>[t(O,{label:"任务记录总表",value:"task_records_all"}),t(O,{label:"当前任务记录",value:"task_records"})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"日期范围"},{default:s(()=>[t(Fe,{modelValue:f.dateRange,"onUpdate:modelValue":e[1]||(e[1]=o=>f.dateRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",shortcuts:xe},null,8,["modelValue"])]),_:1}),t(y,{class:"export-actions"},{default:s(()=>[l("div",wt,[t(_,{type:"primary",onClick:Be,loading:W.value,size:"large"},{default:s(()=>[t(p,null,{default:s(()=>[t(v(fe))]),_:1}),e[14]||(e[14]=m(" 导出Excel "))]),_:1},8,["loading"]),t(_,{onClick:Le,size:"large"},{default:s(()=>[t(p,null,{default:s(()=>[t(v(F))]),_:1}),e[15]||(e[15]=m(" 重置 "))]),_:1})])]),_:1})]),_:1})]),_:1}),t(U,{class:"settings-card"},{header:s(()=>[l("div",ht,[l("span",kt,[t(p,{class:"card-icon"},{default:s(()=>[t(v(et))]),_:1}),e[16]||(e[16]=m(" 后端配置 "))])])]),default:s(()=>[t(J,{"label-width":"120px",model:n,ref_key:"formRef",ref:we,class:"settings-form"},{default:s(()=>[t(y,{label:"最大并发任务数"},{default:s(()=>[t(se,{modelValue:n.limits,"onUpdate:modelValue":e[2]||(e[2]=o=>n.limits=o),min:1,max:8},null,8,["modelValue"])]),_:1}),t(y,{label:"运行模式"},{default:s(()=>[t(Ae,{modelValue:n.mode,"onUpdate:modelValue":e[3]||(e[3]=o=>n.mode=o),onChange:Ve},{default:s(()=>[t(pe,{label:"offline"},{default:s(()=>e[17]||(e[17]=[m("离线（本地127.0.0.1）")])),_:1}),t(pe,{label:"online"},{default:s(()=>e[18]||(e[18]=[m("在线（局域网/公网）")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"IP"},{default:s(()=>[n.mode==="online"?(g(),ne(ae,{key:0,modelValue:n.server_host,"onUpdate:modelValue":e[4]||(e[4]=o=>n.server_host=o),filterable:"",placeholder:"请选择IP","filter-method":null,remote:!1},{default:s(()=>[(g(!0),M(_e,null,Xe(P.value,o=>(g(),ne(O,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):(g(),ne(Ee,{key:1,modelValue:n.server_host,"onUpdate:modelValue":e[5]||(e[5]=o=>n.server_host=o),disabled:""},null,8,["modelValue"]))]),_:1}),t(y,{label:"端口"},{default:s(()=>[l("div",xt,[t(se,{modelValue:n.server_port,"onUpdate:modelValue":e[6]||(e[6]=o=>n.server_port=o),min:1024,max:65535,class:"port-input"},null,8,["modelValue"]),t(_,{size:"small",class:"port-check-btn",onClick:Ie,loading:Y.value},{default:s(()=>e[19]||(e[19]=[m("检测端口占用")])),_:1},8,["loading"])]),l("div",Mt,[l("div",{class:Ze({"status-warning":!h.value,"status-error":h.value&&!k.value,"status-success":h.value&&k.value})},T(h.value?k.value?"端口可用，可以保存设置。":"端口未通过占用检测，无法保存！":'端口更改后必须点击"检测端口占用"并通过检测后才能保存。'),3)])]),_:1}),t(y,{class:"form-actions"},{default:s(()=>[l("div",Vt,[t(_,{type:"primary",onClick:Se,loading:I.value,disabled:!Pe.value,size:"large",class:"save-btn"},{default:s(()=>[t(p,null,{default:s(()=>[t(v(ye))]),_:1}),e[20]||(e[20]=m(" 保存设置 "))]),_:1},8,["loading","disabled"]),t(_,{type:"danger",onClick:Te,loading:C.value,size:"large",class:"restart-btn"},{default:s(()=>[t(p,null,{default:s(()=>[t(v(F))]),_:1}),e[21]||(e[21]=m(" 重启服务器 "))]),_:1},8,["loading"])])]),_:1})]),_:1},8,["model"])]),_:1}),t(U,{class:"settings-card nav-control-card"},{header:s(()=>[l("div",Pt,[l("span",St,[t(p,{class:"card-icon"},{default:s(()=>[t(v(tt))]),_:1}),e[22]||(e[22]=m(" 导航栏显示控制 "))])])]),default:s(()=>[t(J,{"label-width":"120px",model:r,ref_key:"navFormRef",ref:he,class:"settings-form nav-form"},{default:s(()=>[t(me,{data:be,border:"",size:"small",class:"nav-items-table",style:{width:"100%"}},{default:s(()=>[t(c,{label:"显示",width:"60"},{default:s(o=>[t(Ye,{modelValue:r.visibleItems[o.row.key],"onUpdate:modelValue":D=>r.visibleItems[o.row.key]=D,disabled:o.row.key==="userManagement"||o.row.key==="settings"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),t(c,{prop:"label",label:"菜单项","min-width":"80"}),t(c,{label:"模式",width:"100"},{default:s(o=>[t(ae,{modelValue:r.itemModes[o.row.key],"onUpdate:modelValue":D=>r.itemModes[o.row.key]=D,size:"small",disabled:o.row.key==="userManagement"||o.row.key==="settings",style:{width:"80px"}},{default:s(()=>[t(O,{label:"生产",value:"production"}),t(O,{label:"开发",value:"development"})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),t(c,{label:"开发进度",width:"120"},{default:s(o=>[r.itemModes[o.row.key]==="development"?(g(),M(_e,{key:0},[t(se,{modelValue:r.itemProgress[o.row.key],"onUpdate:modelValue":D=>r.itemProgress[o.row.key]=D,min:0,max:100,step:1,size:"small",style:{width:"80px","vertical-align":"middle"},disabled:o.row.key==="userManagement"||o.row.key==="settings"},null,8,["modelValue","onUpdate:modelValue","disabled"]),e[23]||(e[23]=l("span",{class:"progress-value"},"%",-1))],64)):ve("",!0)]),_:1})]),_:1}),t(y,{class:"nav-actions"},{default:s(()=>[l("div",Tt,[t(_,{type:"primary",onClick:Ne,loading:E.value,size:"large",class:"save-btn"},{default:s(()=>[t(p,null,{default:s(()=>[t(v(ye))]),_:1}),e[24]||(e[24]=m(" 保存导航设置 "))]),_:1},8,["loading"]),t(_,{onClick:Re,size:"large",class:"reset-btn"},{default:s(()=>[t(p,null,{default:s(()=>[t(v(F))]),_:1}),e[25]||(e[25]=m(" 重置为默认 "))]),_:1})])]),_:1})]),_:1},8,["model"])]),_:1}),t(U,{class:"settings-card register-control-card"},{header:s(()=>[l("div",It,[l("span",Ct,[t(p,{class:"card-icon"},{default:s(()=>[t(v(at))]),_:1}),e[26]||(e[26]=m(" 注册功能控制 "))])])]),default:s(()=>[t(J,{"label-width":"120px",class:"settings-form"},{default:s(()=>[t(y,{label:"是否开放注册"},{default:s(()=>[t(je,{modelValue:V.value,"onUpdate:modelValue":e[7]||(e[7]=o=>V.value=o),"active-text":"开放","inactive-text":"关闭",onChange:Oe},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})])),q.value?(g(),M("div",Nt,[l("div",Rt,[l("div",Ut,"服务器重启中"+T(N.value),1),e[27]||(e[27]=l("div",{class:"restart-desc"},"请勿关闭页面，重启完成后将自动跳转...",-1))])])):ve("",!0)])}}}),qt=st(Ot,[["__scopeId","data-v-f70a8385"]]);export{qt as default};
