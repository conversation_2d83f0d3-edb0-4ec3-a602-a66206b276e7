{"version": 3, "file": "carousel.mjs", "sources": ["../../../../../../packages/components/carousel/src/carousel.ts"], "sourcesContent": ["import { buildProps, isNumber } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\n\nexport const carouselProps = buildProps({\n  /**\n   * @description index of the initially active slide (starting from 0)\n   */\n  initialIndex: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description height of the carousel\n   */\n  height: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description how indicators are triggered\n   */\n  trigger: {\n    type: String,\n    values: ['hover', 'click'],\n    default: 'hover',\n  },\n  /**\n   * @description whether automatically loop the slides\n   */\n  autoplay: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description interval of the auto loop, in milliseconds\n   */\n  interval: {\n    type: Number,\n    default: 3000,\n  },\n  /**\n   * @description position of the indicators\n   */\n  indicatorPosition: {\n    type: String,\n    values: ['', 'none', 'outside'],\n    default: '',\n  },\n  /**\n   * @description when arrows are shown\n   */\n  arrow: {\n    type: String,\n    values: ['always', 'hover', 'never'],\n    default: 'hover',\n  },\n  /**\n   * @description type of the Carousel\n   */\n  type: {\n    type: String,\n    values: ['', 'card'],\n    default: '',\n  },\n  /**\n   * @description when type is card, scaled size of secondary cards\n   */\n  cardScale: {\n    type: Number,\n    default: 0.83,\n  },\n  /**\n   * @description display the items in loop\n   */\n  loop: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description display direction\n   */\n  direction: {\n    type: String,\n    values: ['horizontal', 'vertical'],\n    default: 'horizontal',\n  },\n  /**\n   * @description pause autoplay when hover\n   */\n  pauseOnHover: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description infuse dynamism and smoothness into the carousel\n   */\n  motionBlur: Boolean,\n} as const)\n\nexport const carouselEmits = {\n  /**\n   * @description triggers when the active slide switches\n   * @param current index of the new active slide\n   * @param prev index of the old active slide\n   */\n  change: (current: number, prev: number) => [current, prev].every(isNumber),\n}\n\nexport type CarouselProps = ExtractPropTypes<typeof carouselProps>\nexport type CarouselEmits = typeof carouselEmits\n"], "names": [], "mappings": ";;;AACY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;AAC9B,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC;AACnC,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;AACxC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC;AACxB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACtC,IAAI,OAAO,EAAE,YAAY;AACzB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,MAAM,EAAE,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC5D;;;;"}