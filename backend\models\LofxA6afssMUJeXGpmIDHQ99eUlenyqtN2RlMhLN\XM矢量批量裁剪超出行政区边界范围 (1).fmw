#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2025\0106苏州市\新建文件夹\none2none.fmw
#          --dir "C:\Users\<USER>\Desktop\质检成果\无加密版\待检数据\苏州市\苏州市常熟市\苏州市\常熟市"
#          --dir_2 "E:\YC\每日任务\2025\0106苏州市\常熟市行政区"
#          --PARAMETER_2 "C:\Users\<USER>\Desktop\新建文件夹 (11)"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="6843.19 1102.26"
#!   DOC_TOP_LEFT="-712.507 -1442.89"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="a7360f19-8db2-4ef6-a90b-941de8eb31a2"
#!   FME_DOCUMENT_PRIORGUID="104d7cd5-1697-4b1e-9609-4d9ddcd7889e"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-01-06T15:46:41"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="-218.752 34.3753"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_3"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_3"
#!   DATASET="@Value(path_windows)"
#! />
#! <DATASET
#!   NAME="FeatureReader_4"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_4"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="C:\Users\<USER>\Desktop\新建文件夹 (11)\@Value(_list{1})"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 待裁剪数据"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\质检成果\无加密版\待检数据\苏州市\苏州市常熟市\苏州市\常熟市"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 行政区shp压缩包"
#!   DEFAULT_VALUE="E:\YC\每日任务\2025\0106苏州市\常熟市行政区"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (11)"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJhY2Nlc3NNb2RlIjoicmVhZCIsImRlZmF1bHRWYWx1ZSI6IkM6XFxVc2Vyc1xcZGp6XFxEZXNrdG9wXFzotKjmo4DmiJDmnpxcXOaXoOWKoOWvhueJiFxc5b6F5qOA5pWw5o2uXFzoi4/lt57luIJcXOiLj+W3nuW4guW4uOeGn+W4glxc6IuP5bee5biCXFzluLjnhp/luIIiLCJpbmNsdWRlV2ViQnJvd3NlciI6dHJ1ZSwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiZGlyIiwicHJvbXB0Ijoi5b6F6KOB5Ymq5pWw5o2uIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjp0cnVlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6ZmFsc2UsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiYWNjZXNzTW9kZSI6InJlYWQiLCJkZWZhdWx0VmFsdWUiOiJFOlxcWUNcXOavj+aXpeS7u+WKoVxcMjAyNVxcMDEwNuiLj+W3nuW4glxc5bi454af5biC6KGM5pS/5Yy6IiwiaW5jbHVkZVdlYkJyb3dzZXIiOnRydWUsIml0ZW1zVG9TZWxlY3QiOiJmb2xkZXJzIiwibmFtZSI6ImRpcl8yIiwicHJvbXB0Ijoi6KGM5pS/5Yy6c2hw5Y6L57yp5YyFIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjp0cnVlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6ZmFsc2UsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiYWNjZXNzTW9kZSI6IndyaXRlIiwiYWxsb3dVUkwiOmZhbHNlLCJkZWZhdWx0VmFsdWUiOiJDOlxcVXNlcnNcXGRqelxcRGVza3RvcFxc5paw5bu65paH5Lu25aS5ICgxMSkiLCJpdGVtc1RvU2VsZWN0IjoiZm9sZGVycyIsIm5hbWUiOiJQQVJBTUVURVJfMiIsInByb21wdCI6IuS/neWtmOi3r+W+hCIsInJlcXVpcmVkIjp0cnVlLCJzZWxlY3RNdWx0aXBsZSI6ZmFsc2UsInNob3dQcm9tcHQiOnRydWUsInN1cHBvcnRlZFZhbHVlVHlwZXMiOlsiZXhwcmVzc2lvbiIsImdsb2JhbFBhcmFtZXRlciJdLCJ0eXBlIjoiZmlsZSIsInZhbGlkYXRlRXhpc3RlbmNlIjp0cnVlLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifV19"
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\质检成果\无加密版\待检数据\苏州市\苏州市常熟市\苏州市\常熟市"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 待裁剪数据"
#! />
#!     <INFO NAME="dir_2" 
#!   DEFAULT_VALUE="E:\YC\每日任务\2025\0106苏州市\常熟市行政区"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 行政区shp压缩包"
#! />
#!     <INFO NAME="PARAMETER_2" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (11)"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-712.50712507125013 -468.75468754687529"
#!   BOUNDING_RECT="-712.50712507125013 -468.75468754687529 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-178.12678126781225 -371.87871878718772"
#!   BOUNDING_RECT="-178.12678126781225 -371.87871878718772 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="390.62890628906416 -468.75468754687529"
#!   BOUNDING_RECT="390.62890628906416 -468.75468754687529 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="AND"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp&#10;CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;path_filename&lt;closeparen&gt; BEGINS_WITH XM"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="831.88545918906493 -1128.5742410169155"
#!   BOUNDING_RECT="831.88545918906493 -1128.5742410169155 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir_2)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="8"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="1400.6411467459411 -1225.4502097766031"
#!   BOUNDING_RECT="1400.6411467459411 -1225.4502097766031 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="PER_TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="10"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="319.38015742019661 -1198.8874249887513"
#!   BOUNDING_RECT="319.38015742019661 -1198.8874249887513 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="3568.7856878568782 -340.62840628406286"
#!   BOUNDING_RECT="3568.7856878568782 -340.62840628406286 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="14"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="4175.0417504175039 -468.75468754687529"
#!   BOUNDING_RECT="4175.0417504175039 -468.75468754687529 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="GGSJBZL,buffer,JCMJ,buffer,JGRQ,buffer,JSDD,buffer,JSGGSSPT,buffer,JSMJ,buffer,LXNF,buffer,NTPSBZ,buffer,SDXLPTCD,buffer,Shape_Area,buffer,Shape_Leng,buffer,shapefile_type,buffer,SJXZQHDM,buffer,SJXZQHMC,buffer,SXZQHDM,buffer,SXZQHMC,buffer,XJXZQHDM,buffer,XJXZQHMC,buffer,XMDM,buffer,XMJD,buffer,XMLX,buffer,XMMC,buffer,XMTZJE,buffer,XMZGBM,buffer,YSRQ,buffer"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="16"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1953.7753920056293 -1236.3878077953141"
#!   BOUNDING_RECT="1953.7753920056293 -1236.3878077953141 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="2550.025500255002 -1371.888718887189"
#!   BOUNDING_RECT="2550.025500255002 -1371.888718887189 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="20"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="4725.6698777021948 -468.75468754687529"
#!   BOUNDING_RECT="4725.6698777021948 -468.75468754687529 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="21"
#!   TYPE="Dissolver"
#!   VERSION="17"
#!   POSITION="3059.405594055941 -1287.5128751287516"
#!   BOUNDING_RECT="3059.405594055941 -1287.5128751287516 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AREA"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="fme_remnant_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ACCUM_INPUT_ATTRS" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="AV" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="First Wins"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="DIS_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUM" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WEIGHT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Dissolver"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="24"
#!   TYPE="Clipper"
#!   VERSION="16"
#!   POSITION="3784.412844128442 -984.38484384843855"
#!   BOUNDING_RECT="3784.412844128442 -984.38484384843855 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="INSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="OUTSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ADD_NODATA" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BOUNDARY_CASES" PARM_VALUE="INSIDE"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CLIPPED_ATTR" PARM_VALUE="_clipped"/>
#!     <XFORM_PARM PARM_NAME="CLIPPERS_FIRST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="CLIPPER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INVALID_PARTS_HANDLING" PARM_VALUE="REJECT_WHOLE"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MISSING_M_MODE" PARM_VALUE="Linear Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MISSING_Z_MODE" PARM_VALUE="Planar Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MULTIPLE_CLIPPERS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="M_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVERLAPPING_CLIPPERS" PARM_VALUE="CLIP_OUTSIDE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_RASTER_EXTENTS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_MODE" PARM_VALUE="CENTER"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Clipper"/>
#!     <XFORM_PARM PARM_NAME="Z_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Z_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="27"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="5700.680819162053 -1025.7575168268772"
#!   BOUNDING_RECT="5700.680819162053 -1025.7575168268772 430 71"
#!   ORDER="500000000000054"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;fme-system&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;FME_DISCLOSURE_OPEN&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Split&lt;space&gt;Dataset&lt;space&gt;into&lt;space&gt;2GB&lt;space&gt;Files,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Elevation&lt;space&gt;as&lt;space&gt;Measures,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Compressed&lt;space&gt;Shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;Surface&lt;space&gt;and&lt;space&gt;Solid&lt;space&gt;Storage,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Spatial&lt;space&gt;Index:,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;space&gt;Type&lt;space&gt;Storage,SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;Output&lt;space&gt;Dimension"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-system"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;:Output,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,SXZQHDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SXZQHMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SJXZQHDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SJXZQHMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQHDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQHMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMZGBM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMLX&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMJD&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LXNF&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JSDD&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JGRQ&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;YSRQ&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMTZJE&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JSMJ&lt;comma&gt;double&lt;comma&gt;JCMJ&lt;comma&gt;double&lt;comma&gt;GGSJBZL&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JSGGSSPT&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SDXLPTCD&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;NTPSBZ&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;Shape_Leng&lt;comma&gt;double&lt;comma&gt;Shape_Area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="29"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="4428.7990213123585 -1145.7575168268772"
#!   BOUNDING_RECT="4428.7990213123585 -1145.7575168268772 430 71"
#!   ORDER="500000000000060"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;u65b0&gt;&lt;u5efa&gt;&lt;u6587&gt;&lt;u4ef6&gt;&lt;u5939&gt;&lt;space&gt;&lt;openparen&gt;2&lt;closeparen&gt;&lt;openparen&gt;1&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="path_windows,path_directory_windows"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="32"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="1162.511625116251 -556.25556255562549"
#!   BOUNDING_RECT="1162.511625116251 -556.25556255562549 430 71"
#!   ORDER="500000000000061"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt; CONTAINS &lt;u9879&gt;&lt;u76ee&gt;&lt;u7ae3&gt;&lt;u5de5&gt;&lt;u9a8c&gt;&lt;u6536&gt;&lt;u7b49&gt;&lt;u8d44&gt;&lt;u6599&gt;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="35"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="2312.5231252312524 -616.25556255562549"
#!   BOUNDING_RECT="2312.5231252312524 -616.25556255562549 430 71"
#!   ORDER="500000000000062"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="path_directory_windows"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;open_p&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="34"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="1740.6424064240646 -715.63215632156312"
#!   BOUNDING_RECT="1740.6424064240646 -715.63215632156312 430 71"
#!   ORDER="500000000000063"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; open_p SET_TO $(dir) varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="36"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="5090.6759067590692 -1145.7575168268772"
#!   BOUNDING_RECT="5090.6759067590692 -1145.7575168268772 430 71"
#!   ORDER="500000000000064"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="save_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_p" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; save_path SET_TO $(PARAMETER_2)&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="33"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="32"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="17"
#!   SOURCE_NODE="8"
#!   TARGET_NODE="16"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="23"
#!   SOURCE_NODE="14"
#!   TARGET_NODE="20"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="22"
#!   SOURCE_NODE="18"
#!   TARGET_NODE="21"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="24"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 1 CANDIDATE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="26"
#!   SOURCE_NODE="21"
#!   TARGET_NODE="24"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 CLIPPER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="30"
#!   SOURCE_NODE="24"
#!   TARGET_NODE="29"
#!   SOURCE_PORT_DESC="fo 0 INSIDE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="40"
#!   SOURCE_NODE="29"
#!   TARGET_NODE="36"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="39"
#!   SOURCE_NODE="34"
#!   TARGET_NODE="35"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="37"
#!   SOURCE_NODE="35"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="41"
#!   SOURCE_NODE="36"
#!   TARGET_NODE="27"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="6"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="9"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="8"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="14"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="19"
#!   SOURCE_NODE="16"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="38"
#!   SOURCE_NODE="32"
#!   TARGET_NODE="34"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO dir C:\Users\<USER>\Desktop\质检成果\无加密版\待检数据\苏州市\苏州市常熟市\苏州市\常熟市

DEFAULT_MACRO dir_2 E:\YC\每日任务\2025\0106苏州市\常熟市行政区

DEFAULT_MACRO PARAMETER_2 C:\Users\<USER>\Desktop\新建文件夹 (11)

# ============================================================================
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir_2$encode)} == {}} { puts_real {Parameter 'dir_2' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_2$encode)} == {}} { puts_real {Parameter 'PARAMETER_2' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)none2none.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME none2none
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_2_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_2_XML     NOT_ACTIVATED
MACRO Creator_2_CLASSIC NOT_ACTIVATED
MACRO Creator_2_2D3D    2D_GEOMETRY
MACRO Creator_2_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_2_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2_2D3D 3D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_2: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_2_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
FACTORY_DEF {$(Creator_2_XML)} CreationFactory    FACTORY_NAME { Creator_2_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_2_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_2_CLASSIC_Creator }    $(Creator_2_2D3D) { $(Creator_2_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_2_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_2_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_2_CREATED        fme_feature_type Creator_2         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_2_CREATED Brancher -1 11"   INPUT FEATURE_TYPE Creator_2_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_2_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(dir_2$encode),FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Creator_2_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_2_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_2_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_2_<Rejected> INPUT FEATURE_TYPE FeatureReader_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE FeatureReader_2_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester_2)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_4_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_4_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_4_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_4_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_4_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_4 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_4)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_4 }    INPUT  FEATURE_TYPE Tester_2_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_4_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_4)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_4_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_4_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_4_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_4_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_4_<Rejected> INPUT FEATURE_TYPE FeatureReader_4_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_4<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE FeatureReader_4_<OTHER>    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} PolygonDissolveFactory    FACTORY_NAME { Dissolver }    INPUT  FEATURE_TYPE Reprojector_REPROJECTED    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    DEAGGREGATE_INPUT { Yes }    MODE COMPLETE    CONNECT_Z_MODE { FIRST_WINS }    CLEANING_TOLERANCE { AUTO }    ACCUM_INPUT_ATTRS { One }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    OUTPUT_REMNANTS    OUTPUT { POLYGON FEATURE_TYPE Dissolver_AREA         }    OUTPUT { REJECTED FEATURE_TYPE Dissolver_<REJECTED>         }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Dissolver_<Rejected> INPUT FEATURE_TYPE Dissolver_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Dissolver<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = shp ENCODED } CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_filename<closeparen>,Tester)" BEGINS_WITH XM ENCODED }    BOOLEAN_OPERATOR { AND }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_3 }    INPUT  FEATURE_TYPE Tester_PASSED    CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,Tester_3)" CONTAINS <u9879><u76ee><u7ae3><u5de5><u9a8c><u6536><u7b49><u8d44><u6599> ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_3_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Tester_3_FAILED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "open_p" "SET_TO" "$(dir$encode)" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_27a9f17a_fdde_4fe6_8b8b_51f1d4186f976} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>open_p<closeparen>,AttributeSplitter)}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {path_directory_windows}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {path_directory_windows}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_3_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE AttributeSplitter_OUTPUT    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_3_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_3_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_3_<Rejected> INPUT FEATURE_TYPE FeatureReader_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer }    INPUT  FEATURE_TYPE FeatureReader_3_<OTHER>    OUTPUT { FEATURE_TYPE AttributeExposer_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector_2 }    INPUT  FEATURE_TYPE AttributeExposer_OUTPUT    OUTPUT { FEATURE_TYPE Reprojector_2_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector_2,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ClipperFactory    FACTORY_NAME { Clipper }    INPUT CLIPPER FEATURE_TYPE Dissolver_AREA    INPUT CANDIDATE FEATURE_TYPE Reprojector_2_REPROJECTED    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    MULTIPLE_CLIPPERS { YES }    CLIPPERS_FIRST { NO }    OVERLAPPING_CLIPPERS { CLIP_OUTSIDE }    INVALID_PARTS_HANDLING { REJECT_WHOLE }    ADD_NODATA { YES }    Z_VAL_SOURCE { CANDIDATE }    MISSING_Z_MODE { PLANAR }    CONNECT_Z_MODE { <Unused> }    M_VAL_SOURCE { CANDIDATE }    MISSING_M_MODE { LINEAR }    CLEANING_TOLERANCE { AUTO }    CANDIDATE_ON_BOUNDARY { INSIDE }    PRESERVE_RASTER_EXTENTS { NO }    RASTER_CELL_MODE { CENTER }    CLIPPED_INDICATOR_ATTR { "_clipped" }    MERGE_CLIPPER_ATTRIBUTES { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    CLIPPER_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { INSIDE FEATURE_TYPE Clipper_INSIDE         }    OUTPUT { REJECTED FEATURE_TYPE Clipper_<REJECTED>         }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Clipper_<Rejected> INPUT FEATURE_TYPE Clipper_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Clipper<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer_2 }    INPUT  FEATURE_TYPE Clipper_INSIDE    USE_REGEX { NO }    CASE_SENSITIVE { YES }    SOURCE_ATTRIBUTES { path_windows,path_directory_windows }    FIND_TEXT { "<u65b0><u5efa><u6587><u4ef6><u5939><space><openparen>2<closeparen><openparen>1<closeparen>" }    REPLACE_TEXT { "<u8f6c><u6362><u540e>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_2_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StringReplacer_2_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "save_path" "SET_TO" "$(PARAMETER_2$encode)<at>Value<openparen>_list<opencurly>1<closecurly><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash><at>Value<openparen>_list<opencurly>1<closecurly><closeparen>,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>fme-system<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma>FME_DISCLOSURE_OPEN<comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<at>Value<openparen>fme_feature_type<closeparen>:Output,ftp_feature_type_name_exp,<at>Value<openparen>fme_feature_type<closeparen>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,SXZQHDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SXZQHMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SJXZQHDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SJXZQHMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XJXZQHDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XJXZQHMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMZGBM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMLX<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMJD<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>LXNF<comma>varchar<lt>openparen<gt>20<lt>closeparen<gt><comma>XMDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMMC<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>JSDD<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>JGRQ<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>YSRQ<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMTZJE<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>JSMJ<comma>double<comma>JCMJ<comma>double<comma>GGSJBZL<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>JSGGSSPT<comma>varchar<lt>openparen<gt>20<lt>closeparen<gt><comma>SDXLPTCD<comma>varchar<lt>openparen<gt>20<lt>closeparen<gt><comma>NTPSBZ<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>Shape_Leng<comma>double<comma>Shape_Area<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,FME_DISCLOSURE_OPEN,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,fme-system,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE AttributeCreator_2_OUTPUT  @SupplyAttributes(ENCODED,fme_template_feature_type,Output)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_feature_type<closeparen>,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

