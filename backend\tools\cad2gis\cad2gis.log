2025-07-17 13:25:08|   0.0|  0.0|INFORM|Current FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-07-17 13:25:08|   0.0|  0.0|INFORM|System Time: 20250717052508 UTC
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Workspace was last saved in FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME_HOME is 'C:\Program Files\FME\'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME ESRI ArcGIS Server Edition (floating)
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Permanent License.
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Machine host name is: DESKTOP-9BLU554
2025-07-17 13:25:08|   0.0|  0.0|INFORM|OS Locale Name     : zh_CN
2025-07-17 13:25:08|   0.0|  0.0|INFORM|OS Locale Encoding : GBK
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Process Encoding   : UTF-8
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME API version: '4.0 20230426'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: FME_BASE is 'no'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: FME_MF_DIR is 'E:\GeoStream_Integration\frontend\backend\tools\cad2gis/'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: FME_MF_NAME is 'wb-tmp2-1752729907790_18488'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: FME_PRODUCT_NAME is 'FME(R) 2023.1.0.0'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Operating System: Microsoft Windows 10 64-bit  (Build 19045)
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Platform: WIN64
2025-07-17 13:25:08|   0.0|  0.0|INFORM|System Status: 181.55 GB of disk space available in the FME temporary folder (C:\Users\<USER>\AppData\Local\Temp)
2025-07-17 13:25:08|   0.0|  0.0|INFORM|System Status: 15.74 GB of physical memory available
2025-07-17 13:25:08|   0.0|  0.0|INFORM|System Status: 62.96 GB of virtual memory available
2025-07-17 13:25:08|   0.0|  0.0|INFORM|START - ProcessID: 18488, peak process memory usage: 45380 kB, current process memory usage: 45380 kB
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Command line arguments are `C:\Program Files\FME\fme.exe' `E:\GeoStream_Integration\frontend\backend\tools\cad2gis\cad2gis.fmw' `--dwg_path' `E:\GeoStream_Integration\frontend\backend\temp\QnAAMYH8hwcy8ZJPFKyBpYeefA25fMeACcY0INqc' `--output_Choice' `点（Point） 富点（MultiPoint） 线（Line） 面（Area） 文本（Text） 富文本（MultiText） 曲线（Curve） 圆弧（Arc） 椭圆（Ellipse） 其他（Other）' `--save_path' `E:\GeoStream_Integration\frontend\backend\tools\cad2gis\output\task_1752729904701_8kas88bxa'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Connection Storage: 'C:\Users\<USER>\AppData\Roaming\Safe Software\FME\'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Shared folders for formats are : C:\Program Files\FME\datasources;C:\Users\<USER>\Documents\FME\Formats
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Shared folders for transformers are : C:\Users\<USER>\AppData\Roaming\Safe Software\FME\Packages\23619-win64\transformers;C:\Program Files\FME\transformers;C:\Users\<USER>\AppData\Roaming\Safe Software\FME\FME Store\Transformers
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Shared folders for coordinate systems are : C:\Users\<USER>\Documents\FME\CoordinateSystems
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Shared folders for coordinate system exceptions are : C:\Users\<USER>\Documents\FME\CoordinateSystemExceptions
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Shared folders for coordinate system grid overrides are : C:\Users\<USER>\Documents\FME\CoordinateSystemGridOverrides
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Shared folders for CS-MAP transformation exceptions are : C:\Users\<USER>\Documents\FME\CsmapTransformationExceptions
2025-07-17 13:25:08|   0.0|  0.0|INFORM|Shared folders for transformer categories are : C:\Users\<USER>\Documents\FME\TransformerCategories
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Reader Keyword is `MULTI_READER'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Writer Keyword is `MULTI_DEST'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Writer Group Definition Keyword is `MULTI_DEST_DEF'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Reader type is `MULTI_READER'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Writer type is `MULTI_WRITER'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Current working folder is `E:\GeoStream_Integration\frontend\backend'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Temporary folder is `C:\Users\<USER>\AppData\Local\Temp', set from environment variable `TEMP'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Cache folder is 'C:\Users\<USER>\AppData\Local\Temp'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: FME_HOME is `C:\Program Files\FME\'
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Start freeing memory when the process exceeds 47.22 GB
2025-07-17 13:25:08|   0.0|  0.0|INFORM|FME Configuration: Stop freeing memory when the process is below 35.42 GB
2025-07-17 13:25:08|   0.1|  0.1|INFORM|Creating writer for format: 
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Creating reader for format: 
2025-07-17 13:25:08|   0.1|  0.0|INFORM|MULTI_READER(MULTI_READER): Will fail with first member reader failure
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Using Multi Reader with keyword `MULTI_READER' to read multiple datasets
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Using MultiWriter with keyword `MULTI_DEST' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Loaded module 'Geometry_func' from file 'C:\Program Files\FME\plugins/Geometry_func.dll'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|FME API version of module 'Geometry_func' matches current internal version (4.0 20230426)
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Loaded module 'QueryFactory' from file 'C:\Program Files\FME\plugins/QueryFactory.dll'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|FME API version of module 'QueryFactory' matches current internal version (4.0 20230426)
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Loaded module 'GeometryFilterFactory' from file 'C:\Program Files\FME\plugins/GeometryFilterFactory.dll'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|FME API version of module 'GeometryFilterFactory' matches current internal version (4.0 20230426)
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Loaded module 'GQueryFactory' from file 'C:\Program Files\FME\plugins/GQueryFactory.dll'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|FME API version of module 'GQueryFactory' matches current internal version (4.0 20230426)
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Emptying factory pipeline
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Loaded module 'PATH' from file 'C:\Program Files\FME\plugins/PATH.dll'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|FME API version of module 'PATH' matches current internal version (4.0 20230426)
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Performing query against PATH dataset `E:\GeoStream_Integration\frontend\backend\temp\QnAAMYH8hwcy8ZJPFKyBpYeefA25fMeACcY0INqc'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Path Reader: Opening the PATH Reader on folder 'E:\GeoStream_Integration\frontend\backend\temp\QnAAMYH8hwcy8ZJPFKyBpYeefA25fMeACcY0INqc'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Path Reader: Using Glob Pattern '*'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Path Reader: Allowed Path Type set to 'ANY'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Path Reader: Recurse into subdirectories 'true'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Path Reader: Hidden Files and Folders set to 'INCLUDE'
2025-07-17 13:25:08|   0.1|  0.0|INFORM|Path Reader: Retrieve file properties 'false'
2025-07-17 13:25:08|   0.3|  0.2|INFORM|Creating reader for format: Autodesk AutoCAD DWG/DXF
2025-07-17 13:25:08|   0.4|  0.1|INFORM|Trying to find a DYNAMIC plugin for reader named `ACAD'
2025-07-17 13:25:11|   1.4|  1.0|INFORM|Loaded module 'ACAD' from file 'C:\Program Files\FME\plugins/acad/ACAD.dll'
2025-07-17 13:25:11|   1.4|  0.0|INFORM|FME API version of module 'acad/ACAD' matches current internal version (4.0 20230426)
2025-07-17 13:25:11|   1.4|  0.0|INFORM|Performing query against ACAD dataset `E:\GeoStream_Integration\frontend\backend\temp\QnAAMYH8hwcy8ZJPFKyBpYeefA25fMeACcY0INqc\222 - 副本 - 副本 (4)(转换后)_1.dwg'
2025-07-17 13:25:11|   1.5|  0.1|INFORM|Creating reader for format: Autodesk AutoCAD DWG/DXF
2025-07-17 13:25:11|   1.5|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `ACAD'
2025-07-17 13:25:11|   1.6|  0.1|INFORM|R_4 Reader: Using rich geometry.
2025-07-17 13:25:11|   1.6|  0.0|INFORM|AutoCAD Reader: Successfully opened the 'Release2007' AutoCAD file 'E:\GeoStream_Integration\frontend\backend\temp\QnAAMYH8hwcy8ZJPFKyBpYeefA25fMeACcY0INqc\222 - 副本 - 副本 (4)(转换后)_1.dwg'
2025-07-17 13:25:11|   1.6|  0.0|INFORM|Creating writer for format: 
2025-07-17 13:25:11|   1.6|  0.0|INFORM|Using MultiWriter with keyword `FeatureWriter' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-07-17 13:25:12|   2.0|  0.4|INFORM|Creating writer for format: Esri Geodatabase (File Geodb)
2025-07-17 13:25:12|   2.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for writer named `GEODATABASE_FILE'
2025-07-17 13:25:12|   2.1|  0.1|INFORM|Loaded module 'GEODATABASE_FILE' from file 'C:\Program Files\FME\plugins/..\geodatabase9.dll'
2025-07-17 13:25:12|   2.1|  0.0|INFORM|FME API version of module '..\geodatabase9' matches current internal version (4.0 20230426)
2025-07-17 13:25:12|   2.1|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-07-17 13:25:12|   2.1|  0.0|INFORM|Writer `FeatureWriter_0' of type `GEODATABASE_FILE' using group definition keyword `FeatureWriter_0_DEF'
2025-07-17 13:25:15|   5.1|  3.0|INFORM|The ArcObjects license 'Advanced' is being selected from the ESRILicenseInfo ArcObjects call
2025-07-17 13:25:17|   6.0|  0.9|INFORM|FME has checked out an Esri license. The product checked out is 'Advanced'
2025-07-17 13:25:17|   6.0|  0.0|INFORM|Installed ArcGIS version is '3.1.6'
2025-07-17 13:25:18|   6.4|  0.4|INFORM|Created and connected to the ArcGIS 10.0 File Geodatabase at 'E:\GeoStream_Integration\frontend\backend\tools\cad2gis\output\task_1752729904701_8kas88bxa\222 - 副本 - 副本 (4)(转换后)_1（转换后）.gdb'
2025-07-17 13:25:18|   6.4|  0.0|INFORM|Fast deletes enabled
2025-07-17 13:25:18|   6.4|  0.0|INFORM|The 'HAS_Z_VALUES' keyword is set to 'AUTO_DETECT'. With the exception of MultiPatch feature classes, the dimensionality of new feature classes will be based on the first feature written to the feature class. (Features with no geometry are considered 2D)
2025-07-17 13:25:18|   6.4|  0.0|INFORM|A default z-value of '0' will be used for all 3D features where z-values are not provided
2025-07-17 13:25:18|   6.4|  0.0|INFORM|Esri Geodatabase Writer: Not simplifying geometries being written
2025-07-17 13:25:18|   6.4|  0.0|INFORM|Transactions are being used by the Esri Geodatabase Writer
2025-07-17 13:25:18|   6.6|  0.2|INFORM|Esri Geodatabase Writer: Creating feature type `Line'
2025-07-17 13:25:19|   7.1|  0.5|INFORM|Esri Geodatabase Writer: The field 'OBJECTID' in feature type 'Line' will not be updated since it is not editable
2025-07-17 13:25:19|   7.1|  0.0|INFORM|Esri Geodatabase Writer: The field 'SHAPE_Length' in feature type 'Line' will not be updated since it is not editable
2025-07-17 13:25:19|   7.1|  0.0|INFORM|Transaction #1 was successfully committed
2025-07-17 13:25:19|   7.2|  0.1|INFORM|Esri Geodatabase Writer: Creating feature type `Arc'
2025-07-17 13:25:20|   7.4|  0.2|INFORM|Esri Geodatabase Writer: The field 'OBJECTID' in feature type 'Arc' will not be updated since it is not editable
2025-07-17 13:25:20|   7.4|  0.0|INFORM|Esri Geodatabase Writer: The field 'SHAPE_Length' in feature type 'Arc' will not be updated since it is not editable
2025-07-17 13:25:20|   7.9|  0.5|INFORM|Transaction #2 was successfully committed
2025-07-17 13:25:20|   8.3|  0.4|INFORM|Transaction #3 was successfully committed
2025-07-17 13:25:21|   8.7|  0.4|INFORM|Transaction #4 was successfully committed
2025-07-17 13:25:21|   9.0|  0.3|INFORM|Transaction #5 was successfully committed
2025-07-17 13:25:21|   9.3|  0.3|INFORM|Transaction #6 was successfully committed
2025-07-17 13:25:22|   9.6|  0.3|INFORM|Transaction #7 was successfully committed
2025-07-17 13:25:22|  10.0|  0.4|INFORM|Transaction #8 was successfully committed
2025-07-17 13:25:22|  10.3|  0.3|INFORM|Transaction #9 was successfully committed
2025-07-17 13:25:23|  10.5|  0.2|INFORM|Transaction #10 was successfully committed
2025-07-17 13:25:23|  10.8|  0.3|INFORM|Transaction #11 was successfully committed
2025-07-17 13:25:23|  11.2|  0.4|INFORM|Transaction #12 was successfully committed
2025-07-17 13:25:24|  11.5|  0.3|INFORM|Transaction #13 was successfully committed
2025-07-17 13:25:24|  11.7|  0.2|INFORM|Transaction #14 was successfully committed
2025-07-17 13:25:24|  12.1|  0.4|INFORM|Transaction #15 was successfully committed
2025-07-17 13:25:25|  12.5|  0.4|INFORM|Transaction #16 was successfully committed
2025-07-17 13:25:25|  13.0|  0.5|INFORM|Transaction #17 was successfully committed
2025-07-17 13:25:26|  13.5|  0.5|INFORM|Transaction #18 was successfully committed
2025-07-17 13:25:27|  13.9|  0.4|INFORM|Transaction #19 was successfully committed
2025-07-17 13:25:27|  14.4|  0.5|INFORM|Transaction #20 was successfully committed
2025-07-17 13:25:27|  14.7|  0.3|INFORM|Transaction #21 was successfully committed
2025-07-17 13:25:28|  15.0|  0.3|INFORM|Transaction #22 was successfully committed
2025-07-17 13:25:28|  15.3|  0.3|INFORM|Transaction #23 was successfully committed
2025-07-17 13:25:28|  15.5|  0.2|INFORM|Transaction #24 was successfully committed
2025-07-17 13:25:29|  15.8|  0.3|INFORM|Transaction #25 was successfully committed
2025-07-17 13:25:29|  16.1|  0.3|INFORM|Transaction #26 was successfully committed
2025-07-17 13:25:29|  16.5|  0.4|INFORM|Transaction #27 was successfully committed
2025-07-17 13:25:30|  16.7|  0.2|INFORM|Transaction #28 was successfully committed
2025-07-17 13:25:30|  17.0|  0.3|INFORM|Transaction #29 was successfully committed
2025-07-17 13:25:30|  17.3|  0.3|INFORM|Transaction #30 was successfully committed
2025-07-17 13:25:30|  17.7|  0.4|INFORM|Transaction #31 was successfully committed
2025-07-17 13:25:31|  17.9|  0.2|INFORM|Transaction #32 was successfully committed
2025-07-17 13:25:31|  18.2|  0.3|INFORM|Transaction #33 was successfully committed
2025-07-17 13:25:31|  18.5|  0.3|INFORM|Transaction #34 was successfully committed
2025-07-17 13:25:32|  18.8|  0.3|INFORM|Transaction #35 was successfully committed
2025-07-17 13:25:32|  19.2|  0.4|INFORM|Transaction #36 was successfully committed
2025-07-17 13:25:32|  19.5|  0.3|INFORM|Transaction #37 was successfully committed
2025-07-17 13:25:33|  19.7|  0.2|INFORM|Transaction #38 was successfully committed
2025-07-17 13:25:33|  20.0|  0.3|INFORM|Transaction #39 was successfully committed
2025-07-17 13:25:33|  20.3|  0.3|INFORM|Transaction #40 was successfully committed
2025-07-17 13:25:33|  20.5|  0.2|STATS |Creator_XML_Creator (CreationFactory): Created 1 features
2025-07-17 13:25:33|  20.5|  0.0|STATS |Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:25:33|  20.5|  0.0|STATS |Creator_CREATED Brancher -1 218 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:25:33|  20.5|  0.0|STATS |_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:25:33|  20.5|  0.0|INFORM|Path Reader: Closing the PATH Reader
2025-07-17 13:25:33|  20.5|  0.0|STATS |Tester_3 (TestFactory): Tested 1 input feature(s) -- 1 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.1|STATS |AttributeExposer_2 (TeeFactory): Cloned 39545 input feature(s) into 39545 output feature(s)
2025-07-17 13:25:33|  20.6|  0.0|STATS |AttributeExposer_3 (TeeFactory): Cloned 39545 input feature(s) into 39545 output feature(s)
2025-07-17 13:25:33|  20.6|  0.0|STATS |GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_12 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_7 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_8 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_9 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_13 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_5 (TestFactory): Tested 39364 input feature(s) -- 39364 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_10 (TestFactory): Tested 181 input feature(s) -- 181 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_14 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_15 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_16 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_17 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_18 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_19 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_11 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_20 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_21 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_22 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_23 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_24 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_25 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_26 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_27 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_28 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_29 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_30 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_31 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_32 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_33 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_34 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_35 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_36 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_37 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_38 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_39 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_40 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_41 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_42 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_43 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |Tester_44 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:25:33|  20.6|  0.0|STATS |GeodatabaseRelationshipFeaturesPipeline::GeodatabaseRelationshipFeatures (SortFactory): Finished sorting a total of 0 features.
2025-07-17 13:25:33|  20.6|  0.0|INFORM|Transaction #41 (final transaction) was successfully committed
2025-07-17 13:25:34|  20.8|  0.2|INFORM|Closing the Esri Geodatabase writer
2025-07-17 13:25:34|  20.8|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|INFORM|Feature output statistics for `GEODATABASE_FILE' writer using keyword `FeatureWriter_0':
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |                               Features Written
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |Arc                                                                        181
2025-07-17 13:25:34|  20.8|  0.0|STATS |Line                                                                     39364
2025-07-17 13:25:34|  20.8|  0.0|STATS |==============================================================================
2025-07-17 13:25:34|  20.8|  0.0|STATS |Total Features Written                                                   39545
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |Destination Feature Type Routing Correlator (RoutingFactory): Tested 0 input feature(s), wrote 0 output feature(s): 0 matched merge filters, 0 were routed to output, 0 could not be routed.
2025-07-17 13:25:34|  20.8|  0.0|STATS |Final Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |                            Features Read Summary
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |==============================================================================
2025-07-17 13:25:34|  20.8|  0.0|STATS |Total Features Read                                                          0
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |                           Features Written Summary
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|STATS |==============================================================================
2025-07-17 13:25:34|  20.8|  0.0|STATS |Total Features Written                                                       0
2025-07-17 13:25:34|  20.8|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:25:34|  20.8|  0.0|INFORM|Translation was SUCCESSFUL with 0 warning(s) (0 feature(s) output)
2025-07-17 13:25:34|  20.8|  0.0|INFORM|FME Session Duration: 26.2 seconds. (CPU: 17.6s user, 3.2s system)
2025-07-17 13:25:34|  20.8|  0.0|INFORM|END - ProcessID: 18488, peak process memory usage: 275440 kB, current process memory usage: 179476 kB
