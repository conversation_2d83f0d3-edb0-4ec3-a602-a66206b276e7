#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2024\10\1024\经纬度坐标转点、线图层.fmw
#          --file "C:\Users\<USER>\Desktop\经纬度坐标转点、线图层(2).xlsx"
#          --DEF_VAL ""
#          --DEF_VAL_2 ""
#          --DEF_VAL_3 ""
#          --PARAMETER ""
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="9301.96 799.571"
#!   DOC_TOP_LEFT="462.505 -1083.95"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="27878bc0-d09a-4dec-ac4b-ad9a5bda1853"
#!   FME_DOCUMENT_PRIORGUID="4ed78cbc-0111-451e-88d5-64852cf5cc1b,1dc982dd-0efd-4129-9388-08e91b6915a2,702a8bd9-a744-431f-957b-c87fca716db9,0e48d1ed-3979-46ed-8ba7-7524a3230d18"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2024-10-24T17:06:38"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="NO"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="5309.43 121.876"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader"
#!   DATASET="FeatureReader/经纬度坐标转点、线图层(2).xlsx"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/转换后.gdb"
#! />
#! <DATASET
#!   NAME="FeatureWriter_2"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter_2"
#!   DATASET="FeatureWriter_2/转换错误.xlsx"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Excel_Files(*.xlsx;*.xlsm;*.xls)|*.xlsx;*.xlsm;*.xls|Microsoft_Excel_2007+_Workbook(*.xlsx)|*.xlsx|Microsoft_Excel_2007+_Macro_Workbook(*.xlsm)|*.xlsm|Microsoft_Excel_2007+_Binary_Workbook_-_Windows_Only(*.xlsb)|*.xlsb|Microsoft_Excel_Pre-2007_Workbook(*.xls)|*.xls|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 上传转换模板（excel）"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\经纬度坐标转点、线图层(2).xlsx"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL TEXT_EDIT_OR_ATTR DEF_VAL FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES 所属项目名称"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL TEXT_EDIT_OR_ATTR DEF_VAL_2 FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES 区县"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL TEXT_EDIT_OR_ATTR DEF_VAL_3 FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES 乡镇"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER Select a File"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="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"
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="file" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\经纬度坐标转点、线图层(2).xlsx"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Excel_Files(*.xlsx;*.xlsm;*.xls)|*.xlsx;*.xlsm;*.xls|Microsoft_Excel_2007+_Workbook(*.xlsx)|*.xlsx|Microsoft_Excel_2007+_Macro_Workbook(*.xlsm)|*.xlsm|Microsoft_Excel_2007+_Binary_Workbook_-_Windows_Only(*.xlsb)|*.xlsb|Microsoft_Excel_Pre-2007_Workbook(*.xls)|*.xls|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 上传转换模板（excel）"
#! />
#!     <INFO NAME="DEF_VAL" 
#!   DEFAULT_VALUE=""
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL TEXT_EDIT_OR_ATTR DEF_VAL FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES 所属项目名称"
#! />
#!     <INFO NAME="DEF_VAL_2" 
#!   DEFAULT_VALUE=""
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL TEXT_EDIT_OR_ATTR DEF_VAL_2 FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES 区县"
#! />
#!     <INFO NAME="DEF_VAL_3" 
#!   DEFAULT_VALUE=""
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL TEXT_EDIT_OR_ATTR DEF_VAL_3 FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES 乡镇"
#! />
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER Select a File"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="462.50462504625034 -846.88346883468853"
#!   BOUNDING_RECT="462.50462504625034 -846.88346883468853 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1046.8854688546883 -668.7566875668756"
#!   BOUNDING_RECT="1046.8854688546883 -668.7566875668756 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;u70b9&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;u7ebf&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="5"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="点,&quot;工程名称,工程编号,管径,B,L&quot;,线,&quot;工程名称,工程编号,长度,B1,L1,B2,L2&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="点,&quot;varchar(14),varchar(4),varchar(12),varchar(18),varchar(17)&quot;,线,&quot;varchar(9),varchar(4),int16,varchar(18),varchar(17),varchar(18),varchar(17)&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%点%线"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(file)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="&lt;u70b9&gt; &lt;u7ebf&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="点,,线,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXR_CONFIGURATION_DATASET,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_TRIM_ATTR_NAME_WHITESPACE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Whitespace&lt;space&gt;From&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_READ_RASTER_MODE,&quot;OPTIONAL LOOKUP_CHOICE None%Attribute%Geometry&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Embedded&lt;space&gt;Images&lt;space&gt;As:,XLSXR_SCAN_FOR_GEOMETRIC_TYPES,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Scan&lt;space&gt;For&lt;space&gt;Geometric&lt;space&gt;Types:,XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS XLSXR%Source&quot;,XLSXR&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,XLSXR_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP XLSXR_EXPOSE_FORMAT_ATTRS&quot;,XLSXR&lt;space&gt;Schema&lt;space&gt;Attributes,XLSXR_TABLELIST,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_TRIM_ATTR_NAME_CHARACTERS,&quot;OPTIONAL TEXT_EDIT_ENCODED FME_INCLUDEBROWSE%NO&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Characters&lt;space&gt;from&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_ALLOW_DOLLAR_SIGNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_APPLY_FILTERS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Apply&lt;space&gt;Filter&lt;openparen&gt;s&lt;closeparen&gt;:,XLSXR_READ_BLANK_AS,&quot;OPTIONAL CHOICE Missing%Null&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Cells&lt;space&gt;As:,XLSXR_SCHEMA_HANDLING_REVISION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_READ_FORM_CONTROLS,&quot;OPTIONAL CHECKBOX CELL_VALUE%NONE&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Form&lt;space&gt;Control&lt;space&gt;as&lt;space&gt;Cell&lt;space&gt;Values:,XLSXR_EXCEL_COL_NAMES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_EXPAND_MERGED_CELLS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Expand&lt;space&gt;Merged&lt;space&gt;Cells:,XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_CREATE_FEATURE_TABLES_FROM_DATA,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXR&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_CASE_SENSITIVE_FEATURE_TYPES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_STRIP_SHEETNAME_SPACES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SKIP_EMPTY_ROWS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SCAN_MAX_FEATURES,&quot;OPTIONAL RANGE_SLIDER 0%MAX%0&quot;,XLSXR&lt;space&gt;Maximum&lt;space&gt;Rows&lt;space&gt;to&lt;space&gt;Scan:,XLSXR_SCHEMA,&quot;OPTIONAL STRING&quot;,XLSXR&lt;space&gt;To&lt;space&gt;be&lt;space&gt;populated,XLSXR_USE_CUSTOM_SCHEMA,&quot;OPTIONAL RADIO_GROUP 2%Automatic,NO%Manual,YES&quot;,XLSXR&lt;space&gt;Attribute&lt;space&gt;Definition,XLSXR_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP APPLY_FILTERS%SCAN_MAX_FEATURES%TRIM_ATTR_NAME_WHITESPACE%TRIM_ATTR_NAME_CHARACTERS%READ_BLANK_AS%EXPAND_MERGED_CELLS%READ_RASTER_MODE%READ_FORM_CONTROLS%SCAN_FOR_GEOMETRIC_TYPES&quot;,XLSXR&lt;space&gt;Advanced,XLSXR_FORCE_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ALLOW_DOLLAR_SIGNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_APPLY_FILTERS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CASE_SENSITIVE_FEATURE_TYPES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CONFIGURATION_DATASET" PARM_VALUE="C:\Users\<USER>\Desktop\经纬度坐标转点、线图层(2).xlsx"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CREATE_FEATURE_TABLES_FROM_DATA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXCEL_COL_NAMES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPAND_MERGED_CELLS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_FORCE_DATETIME" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_BLANK_AS" PARM_VALUE="Missing"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_FORM_CONTROLS" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_RASTER_MODE" PARM_VALUE="None"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_FOR_GEOMETRIC_TYPES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_MAX_FEATURES" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA" PARM_VALUE="&lt;u70b9&gt;,0&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;14&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;comma&gt;char&lt;comma&gt;4&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u7ba1&gt;&lt;u5f84&gt;&lt;comma&gt;char&lt;comma&gt;12&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;B&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;L&lt;comma&gt;char&lt;comma&gt;17&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;C:&lt;lt&gt;backslash&lt;gt&gt;Users&lt;lt&gt;backslash&lt;gt&gt;djz&lt;lt&gt;backslash&lt;gt&gt;Desktop&lt;lt&gt;backslash&lt;gt&gt;&lt;lt&gt;u7ecf&lt;gt&gt;&lt;lt&gt;u7eac&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;lt&gt;u5750&lt;gt&gt;&lt;lt&gt;u6807&lt;gt&gt;&lt;lt&gt;u8f6c&lt;gt&gt;&lt;lt&gt;u70b9&lt;gt&gt;&lt;lt&gt;u3001&lt;gt&gt;&lt;lt&gt;u7ebf&lt;gt&gt;&lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5c42&lt;gt&gt;&lt;lt&gt;openparen&lt;gt&gt;2&lt;lt&gt;closeparen&lt;gt&gt;.xlsx&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;4&lt;comma&gt;17&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,&lt;u7ebf&gt;,0&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;9&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;comma&gt;char&lt;comma&gt;4&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;comma&gt;number&lt;comma&gt;4&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;B1&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;L1&lt;comma&gt;char&lt;comma&gt;17&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;B2&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;L2&lt;comma&gt;char&lt;comma&gt;17&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;6&lt;comma&gt;45&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA_HANDLING_REVISION" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SKIP_EMPTY_ROWS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_STRIP_SHEETNAME_SPACES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TABLELIST" PARM_VALUE="点 线"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_CHARACTERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_WHITESPACE" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_USE_CUSTOM_SCHEMA" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="55"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="1731.5801778482873 -785.38345433625238"
#!   BOUNDING_RECT="1731.5801778482873 -785.38345433625238 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;u00b0&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="B,L"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="56"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="2319.7106898223519 -785.38345433625238"
#!   BOUNDING_RECT="2319.7106898223519 -785.38345433625238 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;u2032&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="B,L"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="57"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="2869.7161898773525 -785.38345433625238"
#!   BOUNDING_RECT="2869.7161898773525 -785.38345433625238 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;u2033&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="B,L"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="67"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="3488.1625747946937 -785.38345433625238"
#!   BOUNDING_RECT="3488.1625747946937 -785.38345433625238 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="B"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter_19"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="69"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="4035.0376447331373 -785.38345433625238"
#!   BOUNDING_RECT="4035.0376447331373 -785.38345433625238 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="L"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list1"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter_20"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="71"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="4681.9269637593825 -757.25817308343983"
#!   BOUNDING_RECT="4681.9269637593825 -757.25817308343983 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;&lt;u7ecf&gt;&lt;u5ea6&gt; SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;60+&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;3600&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;&lt;u7eac&gt;&lt;u5ea6&gt; SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;60+&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;2&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;3600&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="64"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="1731.5801778482873 -1012.9483773121802"
#!   BOUNDING_RECT="1731.5801778482873 -1012.9483773121802 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;u00b0&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="B1,B2,L1,L2"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="66"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="2319.7106898223519 -1012.9483773121802"
#!   BOUNDING_RECT="2319.7106898223519 -1012.9483773121802 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;u2032&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="B1,B2,L1,L2"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="73"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="2869.7161898773525 -1012.9483773121802"
#!   BOUNDING_RECT="2869.7161898773525 -1012.9483773121802 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;u2033&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="B1,B2,L1,L2"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="74"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="3488.1625747946937 -1012.9483773121802"
#!   BOUNDING_RECT="3488.1625747946937 -1012.9483773121802 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="B1"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter_21"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="75"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="4035.0376447331373 -1012.9483773121802"
#!   BOUNDING_RECT="4035.0376447331373 -1012.9483773121802 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="L1"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list1"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter_22"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="76"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="4681.9269637593825 -1012.9483773121802"
#!   BOUNDING_RECT="4681.9269637593825 -1012.9483773121802 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;&lt;u7ecf&gt;&lt;u5ea6&gt;1 SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;60+&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;3600&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;&lt;u7eac&gt;&lt;u5ea6&gt;1 SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;60+&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;2&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;3600&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_8"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="83"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="5284.1254574081377 -1012.9483773121802"
#!   BOUNDING_RECT="5284.1254574081377 -1012.9483773121802 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="B2"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter_23"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="84"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="5831.0005273465831 -1012.9483773121802"
#!   BOUNDING_RECT="5831.0005273465831 -1012.9483773121802 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="L2"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list1"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter_24"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="87"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="6422.5709410097006 -1012.9483773121802"
#!   BOUNDING_RECT="6422.5709410097006 -1012.9483773121802 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;&lt;u7ecf&gt;&lt;u5ea6&gt;2 SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;60+&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;3600&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;&lt;u7eac&gt;&lt;u5ea6&gt;2 SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;60+&lt;at&gt;Value&lt;openparen&gt;_list1&lt;opencurly&gt;2&lt;closecurly&gt;&lt;closeparen&gt;&lt;solidus&gt;3600&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_9"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="34"
#!   TYPE="VertexCreator"
#!   VERSION="5"
#!   POSITION="5356.3035630356308 -725.38345433625238"
#!   BOUNDING_RECT="5356.3035630356308 -725.38345433625238 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_PARAMETERS" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CLOSE_LINES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_DUPLICATES" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="INDEX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MISSING_VAL_MODE" PARM_VALUE="Compute"/>
#!     <XFORM_PARM PARM_NAME="MODE_NAME" PARM_VALUE="Add Point"/>
#!     <XFORM_PARM PARM_NAME="PARAMETER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="VertexCreator"/>
#!     <XFORM_PARM PARM_NAME="XVAL" PARM_VALUE="@Value(转换后经度)"/>
#!     <XFORM_PARM PARM_NAME="YVAL" PARM_VALUE="@Value(转换后纬度)"/>
#!     <XFORM_PARM PARM_NAME="ZVAL" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="36"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="5934.4343443434427 -757.25817308343983"
#!   BOUNDING_RECT="5934.4343443434427 -757.25817308343983 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE="EPSG:4490"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="38"
#!   TYPE="VertexCreator"
#!   VERSION="5"
#!   POSITION="7064.7644441636066 -952.94837731218024"
#!   BOUNDING_RECT="7064.7644441636066 -952.94837731218024 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_PARAMETERS" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CLOSE_LINES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_DUPLICATES" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="INDEX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MISSING_VAL_MODE" PARM_VALUE="Compute"/>
#!     <XFORM_PARM PARM_NAME="MODE_NAME" PARM_VALUE="Add Point"/>
#!     <XFORM_PARM PARM_NAME="PARAMETER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="VertexCreator_2"/>
#!     <XFORM_PARM PARM_NAME="XVAL" PARM_VALUE="@Value(转换后经度1)"/>
#!     <XFORM_PARM PARM_NAME="YVAL" PARM_VALUE="@Value(转换后纬度1)"/>
#!     <XFORM_PARM PARM_NAME="ZVAL" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="39"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="8074.1495380145461 -981.69806480905527"
#!   BOUNDING_RECT="8074.1495380145461 -981.69806480905527 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE="EPSG:4490"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="42"
#!   TYPE="VertexCreator"
#!   VERSION="5"
#!   POSITION="7569.4575230670534 -952.94837731218024"
#!   BOUNDING_RECT="7569.4575230670534 -952.94837731218024 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="转换后经度2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_PARAMETERS" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CLOSE_LINES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_DUPLICATES" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="INDEX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MISSING_VAL_MODE" PARM_VALUE="Compute"/>
#!     <XFORM_PARM PARM_NAME="MODE_NAME" PARM_VALUE="Add Point"/>
#!     <XFORM_PARM PARM_NAME="PARAMETER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="VertexCreator_3"/>
#!     <XFORM_PARM PARM_NAME="XVAL" PARM_VALUE="@Value(转换后经度2)"/>
#!     <XFORM_PARM PARM_NAME="YVAL" PARM_VALUE="@Value(转换后纬度2)"/>
#!     <XFORM_PARM PARM_NAME="ZVAL" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="45"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="6628.1912819128174 -725.38345433625238"
#!   BOUNDING_RECT="6628.1912819128174 -725.38345433625238 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否该项目工程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="所属项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="区县" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="乡镇" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="调查编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程完工时间" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程大类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程长度（米）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程宽度（米）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程面积（平方米）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径大小" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="坐落单位" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程权属（管护主体）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护实施主体" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护责任人" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否签订管护协议" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施运行状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施使用状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程位置（wkt）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u5e8f&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;6&lt;closeparen&gt;  &lt;u662f&gt;&lt;u5426&gt;&lt;u8be5&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u5de5&gt;&lt;u7a0b&gt; SET_TO  varchar&lt;openparen&gt;21&lt;closeparen&gt;  &lt;u6240&gt;&lt;u5c5e&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO $(DEF_VAL) varchar&lt;openparen&gt;18&lt;closeparen&gt;  &lt;u9879&gt;&lt;u76ee&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u533a&gt;&lt;u53bf&gt; SET_TO $(DEF_VAL_2) varchar&lt;openparen&gt;6&lt;closeparen&gt;  &lt;u4e61&gt;&lt;u9547&gt; SET_TO $(DEF_VAL_3) varchar&lt;openparen&gt;6&lt;closeparen&gt;  &lt;u8c03&gt;&lt;u67e5&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u539f&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;15&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5b8c&gt;&lt;u5de5&gt;&lt;u65f6&gt;&lt;u95f4&gt; SET_TO  varchar&lt;openparen&gt;18&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5927&gt;&lt;u7c7b&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u522b&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u578b&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;21&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5bbd&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;21&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u5e73&gt;&lt;u65b9&gt;&lt;u7c73&gt;&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;27&lt;closeparen&gt;  &lt;u7ba1&gt;&lt;u5f84&gt;&lt;u5927&gt;&lt;u5c0f&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u7ba1&gt;&lt;u5f84&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5750&gt;&lt;u843d&gt;&lt;u5355&gt;&lt;u4f4d&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u6743&gt;&lt;u5c5e&gt;&lt;uff08&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u4e3b&gt;&lt;u4f53&gt;&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;30&lt;closeparen&gt;  &lt;u7ba1&gt;&lt;u62a4&gt;&lt;u5b9e&gt;&lt;u65bd&gt;&lt;u4e3b&gt;&lt;u4f53&gt; SET_TO  varchar&lt;openparen&gt;18&lt;closeparen&gt;  &lt;u7ba1&gt;&lt;u62a4&gt;&lt;u8d23&gt;&lt;u4efb&gt;&lt;u4eba&gt; SET_TO  varchar&lt;openparen&gt;15&lt;closeparen&gt;  &lt;u662f&gt;&lt;u5426&gt;&lt;u7b7e&gt;&lt;u8ba2&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u534f&gt;&lt;u8bae&gt; SET_TO  varchar&lt;openparen&gt;24&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u8fd0&gt;&lt;u884c&gt;&lt;u72b6&gt;&lt;u6001&gt; SET_TO  varchar&lt;openparen&gt;24&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u72b6&gt;&lt;u6001&gt; SET_TO  varchar&lt;openparen&gt;24&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4f4d&gt;&lt;u7f6e&gt;&lt;uff08&gt;wkt&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;21&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE="&lt;bell&gt;&lt;u8c03&gt;&lt;u67e5&gt;&lt;u7f16&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5927&gt;&lt;u7c7b&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5bbd&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u522b&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u578b&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u5e73&gt;&lt;u65b9&gt;&lt;u7c73&gt;&lt;uff09&gt; varchar&lt;openparen&gt;27&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u6743&gt;&lt;u5c5e&gt;&lt;uff08&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u4e3b&gt;&lt;u4f53&gt;&lt;uff09&gt; varchar&lt;openparen&gt;30&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u72b6&gt;&lt;u6001&gt; varchar&lt;openparen&gt;24&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u8fd0&gt;&lt;u884c&gt;&lt;u72b6&gt;&lt;u6001&gt; varchar&lt;openparen&gt;24&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5b8c&gt;&lt;u5de5&gt;&lt;u65f6&gt;&lt;u95f4&gt; varchar&lt;openparen&gt;18&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4f4d&gt;&lt;u7f6e&gt;&lt;uff08&gt;wkt&lt;uff09&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u5b9e&gt;&lt;u65bd&gt;&lt;u4e3b&gt;&lt;u4f53&gt; varchar&lt;openparen&gt;18&lt;closeparen&gt; &lt;bell&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u8d23&gt;&lt;u4efb&gt;&lt;u4eba&gt; varchar&lt;openparen&gt;15&lt;closeparen&gt; &lt;bell&gt;&lt;u7ba1&gt;&lt;u5f84&gt;&lt;u5927&gt;&lt;u5c0f&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u533a&gt;&lt;u53bf&gt; varchar&lt;openparen&gt;6&lt;closeparen&gt; &lt;bell&gt;&lt;u662f&gt;&lt;u5426&gt;&lt;u8be5&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u5de5&gt;&lt;u7a0b&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u662f&gt;&lt;u5426&gt;&lt;u7b7e&gt;&lt;u8ba2&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u534f&gt;&lt;u8bae&gt; varchar&lt;openparen&gt;24&lt;closeparen&gt; &lt;bell&gt;&lt;u6240&gt;&lt;u5c5e&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u540d&gt;&lt;u79f0&gt; varchar&lt;openparen&gt;18&lt;closeparen&gt; &lt;bell&gt;&lt;u4e61&gt;&lt;u9547&gt; varchar&lt;openparen&gt;6&lt;closeparen&gt; &lt;bell&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u7f16&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5e8f&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;6&lt;closeparen&gt; &lt;bell&gt;&lt;u539f&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;15&lt;closeparen&gt; &lt;bell&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u540d&gt; varchar&lt;openparen&gt;59&lt;closeparen&gt; &lt;bell&gt;&lt;u5750&gt;&lt;u843d&gt;&lt;u5355&gt;&lt;u4f4d&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="47"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="8688.2153726468896 -981.69806480905527"
#!   BOUNDING_RECT="8688.2153726468896 -981.69806480905527 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否该项目工程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="所属项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="区县" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="乡镇" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="调查编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程完工时间" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程大类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程长度（米）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程宽度（米）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程面积（平方米）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径大小" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="坐落单位" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程权属（管护主体）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护实施主体" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护责任人" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否签订管护协议" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施运行状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施使用状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程位置（wkt）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后经度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="转换后纬度1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="B2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="L2" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list1{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u5e8f&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;6&lt;closeparen&gt;  &lt;u662f&gt;&lt;u5426&gt;&lt;u8be5&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u5de5&gt;&lt;u7a0b&gt; SET_TO  varchar&lt;openparen&gt;21&lt;closeparen&gt;  &lt;u6240&gt;&lt;u5c5e&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO $(DEF_VAL) varchar&lt;openparen&gt;18&lt;closeparen&gt;  &lt;u9879&gt;&lt;u76ee&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u533a&gt;&lt;u53bf&gt; SET_TO $(DEF_VAL_2) varchar&lt;openparen&gt;6&lt;closeparen&gt;  &lt;u4e61&gt;&lt;u9547&gt; SET_TO $(DEF_VAL_3) varchar&lt;openparen&gt;6&lt;closeparen&gt;  &lt;u8c03&gt;&lt;u67e5&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u539f&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;15&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5b8c&gt;&lt;u5de5&gt;&lt;u65f6&gt;&lt;u95f4&gt; SET_TO  varchar&lt;openparen&gt;18&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5927&gt;&lt;u7c7b&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u522b&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u578b&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5bbd&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;21&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u5e73&gt;&lt;u65b9&gt;&lt;u7c73&gt;&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;27&lt;closeparen&gt;  &lt;u7ba1&gt;&lt;u5f84&gt;&lt;u5927&gt;&lt;u5c0f&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5750&gt;&lt;u843d&gt;&lt;u5355&gt;&lt;u4f4d&gt; SET_TO  varchar&lt;openparen&gt;12&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u6743&gt;&lt;u5c5e&gt;&lt;uff08&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u4e3b&gt;&lt;u4f53&gt;&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;30&lt;closeparen&gt;  &lt;u7ba1&gt;&lt;u62a4&gt;&lt;u5b9e&gt;&lt;u65bd&gt;&lt;u4e3b&gt;&lt;u4f53&gt; SET_TO  varchar&lt;openparen&gt;18&lt;closeparen&gt;  &lt;u7ba1&gt;&lt;u62a4&gt;&lt;u8d23&gt;&lt;u4efb&gt;&lt;u4eba&gt; SET_TO  varchar&lt;openparen&gt;15&lt;closeparen&gt;  &lt;u662f&gt;&lt;u5426&gt;&lt;u7b7e&gt;&lt;u8ba2&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u534f&gt;&lt;u8bae&gt; SET_TO  varchar&lt;openparen&gt;24&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u8fd0&gt;&lt;u884c&gt;&lt;u72b6&gt;&lt;u6001&gt; SET_TO  varchar&lt;openparen&gt;24&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u72b6&gt;&lt;u6001&gt; SET_TO  varchar&lt;openparen&gt;24&lt;closeparen&gt;  &lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4f4d&gt;&lt;u7f6e&gt;&lt;uff08&gt;wkt&lt;uff09&gt; SET_TO  varchar&lt;openparen&gt;21&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE="&lt;bell&gt;&lt;u8c03&gt;&lt;u67e5&gt;&lt;u7f16&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5927&gt;&lt;u7c7b&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5bbd&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u522b&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7c7b&gt;&lt;u578b&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u5e73&gt;&lt;u65b9&gt;&lt;u7c73&gt;&lt;uff09&gt; varchar&lt;openparen&gt;27&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u6743&gt;&lt;u5c5e&gt;&lt;uff08&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u4e3b&gt;&lt;u4f53&gt;&lt;uff09&gt; varchar&lt;openparen&gt;30&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u72b6&gt;&lt;u6001&gt; varchar&lt;openparen&gt;24&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u8fd0&gt;&lt;u884c&gt;&lt;u72b6&gt;&lt;u6001&gt; varchar&lt;openparen&gt;24&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u5b8c&gt;&lt;u5de5&gt;&lt;u65f6&gt;&lt;u95f4&gt; varchar&lt;openparen&gt;18&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4f4d&gt;&lt;u7f6e&gt;&lt;uff08&gt;wkt&lt;uff09&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;uff08&gt;&lt;u7c73&gt;&lt;uff09&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u5b9e&gt;&lt;u65bd&gt;&lt;u4e3b&gt;&lt;u4f53&gt; varchar&lt;openparen&gt;18&lt;closeparen&gt; &lt;bell&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u8d23&gt;&lt;u4efb&gt;&lt;u4eba&gt; varchar&lt;openparen&gt;15&lt;closeparen&gt; &lt;bell&gt;&lt;u7ba1&gt;&lt;u5f84&gt;&lt;u5927&gt;&lt;u5c0f&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u533a&gt;&lt;u53bf&gt; varchar&lt;openparen&gt;6&lt;closeparen&gt; &lt;bell&gt;&lt;u662f&gt;&lt;u5426&gt;&lt;u8be5&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u5de5&gt;&lt;u7a0b&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt; &lt;bell&gt;&lt;u662f&gt;&lt;u5426&gt;&lt;u7b7e&gt;&lt;u8ba2&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u534f&gt;&lt;u8bae&gt; varchar&lt;openparen&gt;24&lt;closeparen&gt; &lt;bell&gt;&lt;u6240&gt;&lt;u5c5e&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u540d&gt;&lt;u79f0&gt; varchar&lt;openparen&gt;18&lt;closeparen&gt; &lt;bell&gt;&lt;u4e61&gt;&lt;u9547&gt; varchar&lt;openparen&gt;6&lt;closeparen&gt; &lt;bell&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u7f16&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt; &lt;bell&gt;&lt;u5e8f&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;6&lt;closeparen&gt; &lt;bell&gt;&lt;u539f&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt; varchar&lt;openparen&gt;15&lt;closeparen&gt; &lt;bell&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u540d&gt; varchar&lt;openparen&gt;59&lt;closeparen&gt; &lt;bell&gt;&lt;u5750&gt;&lt;u843d&gt;&lt;u5355&gt;&lt;u4f4d&gt; varchar&lt;openparen&gt;12&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="49"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="9334.468344683446 -803.13303133031332"
#!   BOUNDING_RECT="9334.468344683446 -803.13303133031332 430 71"
#!   ORDER="500000000000012"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER)&lt;backslash&gt;&lt;u8f6c&gt;&lt;u6362&gt;&lt;u540e&gt;.gdb"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_GEODB&lt;comma&gt;NO&lt;comma&gt;DATASET_TEMPLATE&lt;comma&gt;&lt;comma&gt;IMPORT_XML_TEMPLATE_GROUP&lt;comma&gt;NO&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;IMPORT_KIND&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;TRANSACTION_TYPE&lt;comma&gt;TRANSACTIONS&lt;comma&gt;FEATURE_DATASET_HANDLING&lt;comma&gt;WRITE&lt;comma&gt;SIMPLIFY_GEOM&lt;comma&gt;No&lt;comma&gt;HAS_Z_VALUES&lt;comma&gt;auto_detect&lt;comma&gt;X_ORIGIN&lt;comma&gt;0&lt;comma&gt;Y_ORIGIN&lt;comma&gt;0&lt;comma&gt;XY_SCALE&lt;comma&gt;0&lt;comma&gt;Z_ORIGIN&lt;comma&gt;0&lt;comma&gt;Z_SCALE&lt;comma&gt;0&lt;comma&gt;GRID_1&lt;comma&gt;0&lt;comma&gt;GEODB_SHARED_WRT_ADV_PARM_GROUP&lt;comma&gt;&lt;comma&gt;REQUESTED_GEODATABASE_VERSION&lt;comma&gt;CURRENT&lt;comma&gt;DEFAULT_Z_VALUE&lt;comma&gt;0&lt;comma&gt;TRANSACTION&lt;comma&gt;0&lt;comma&gt;TRANSACTION_INTERVAL&lt;comma&gt;1000&lt;comma&gt;IGNORE_FAILED_FEATURE_ENTRY&lt;comma&gt;no&lt;comma&gt;MAX_NUMBER_FAILED_FEATURES&lt;comma&gt;-1&lt;comma&gt;DUMP_FAILED_FEATURES&lt;comma&gt;no&lt;comma&gt;FFS_DUMP_FILE&lt;comma&gt;&lt;comma&gt;ANNOTATION_UNITS&lt;comma&gt;unknown_units&lt;comma&gt;HAS_MEASURES&lt;comma&gt;no&lt;comma&gt;COMPRESS_AT_END&lt;comma&gt;no&lt;comma&gt;ENABLE_FAST_DELETES&lt;comma&gt;yes&lt;comma&gt;PRESERVE_GLOBALID&lt;comma&gt;no&lt;comma&gt;ENABLE_LOAD_ONLY_MODE&lt;comma&gt;no&lt;comma&gt;VALIDATE_FEATURES&lt;comma&gt;no&lt;comma&gt;SIMPLIFY_NETWORK_FEATURES&lt;comma&gt;no&lt;comma&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE_TYPE,METAFILE,GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Network&lt;space&gt;Features:,GEODATABASE_FILE_Y_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Max&lt;space&gt;number&lt;space&gt;of&lt;space&gt;features&lt;space&gt;to&lt;space&gt;ignore:,GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP,&quot;OPTIONAL DISCLOSUREGROUP REQUESTED_GEODATABASE_VERSION%DEFAULT_Z_VALUE%TRANSACTION%TRANSACTION_INTERVAL%IGNORE_FAILED_FEATURE_ENTRY%MAX_NUMBER_FAILED_FEATURES%DUMP_FAILED_FEATURES%FFS_DUMP_FILE%ANNOTATION_UNITS%HAS_MEASURES%MEASURES_ORIGIN%MEASURES_SCALE%COMPRESS_AT_END%ENABLE_FAST_DELETES%PRESERVE_GLOBALID%ENABLE_LOAD_ONLY_MODE%VALIDATE_FEATURES%SIMPLIFY_NETWORK_FEATURES%BEGIN_SQL{0}%END_SQL{0}&quot;,GEODATABASE_FILE&lt;space&gt;Advanced,GEODATABASE_FILE_X_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_FFS_DUMP_FILE,&quot;OPTIONAL FILENAME FME_Feature_Store_Files(*.ffs)|*.ffs|All_files(*)|*&quot;,GEODATABASE_FILE&lt;space&gt;Failed&lt;space&gt;Feature&lt;space&gt;Dump&lt;space&gt;filename:,GEODATABASE_FILE_TRANSACTION_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Edit&lt;space&gt;Session,EDIT_SESSION%Transactions,TRANSACTIONS%None,NONE&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Type:,GEODATABASE_FILE_DUMP_FAILED_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Dump&lt;space&gt;Failed&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;File:,GEODATABASE_FILE_COMPRESS_AT_END,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Compact&lt;space&gt;Database&lt;space&gt;When&lt;space&gt;Done:,GEODATABASE_FILE_SIMPLIFY_GEOM,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Geometry:,GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION,&quot;OPTIONAL LOOKUP_CHOICE Current,CURRENT%10.0%9.3&quot;,GEODATABASE_FILE&lt;space&gt;Geodatabase&lt;space&gt;Version:,GEODATABASE_FILE_Z_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_XY_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Failed&lt;space&gt;Features:,GEODATABASE_FILE_ANNOTATION_UNITS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Unknown Units&quot;&quot;,unknown_units%&quot;&quot;Decimal Degrees&quot;&quot;,decimal_degrees%Inches,inches%Points,points%Feet,feet%Yards,yards%Miles,miles%&quot;&quot;Nautical Miles&quot;&quot;,nautical_miles%Millimeters,millimeters%Centimeters,centimeters%Meters,meters%Kilometers,kilometers%Decimeters,decimeters&quot;,GEODATABASE_FILE&lt;space&gt;Annotation&lt;space&gt;Units:,GEODATABASE_FILE_HAS_Z_VALUES,&quot;OPTIONAL LOOKUP_CHOICE Yes,yes%No,no%Auto&lt;space&gt;Detect,auto_detect&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values:,GEODATABASE_FILE_BEGIN_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;Before&lt;space&gt;Write,GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Load&lt;space&gt;Only&lt;space&gt;Mode:,GEODATABASE_FILE_FEATURE_DATASET_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Write&lt;space&gt;Feature&lt;space&gt;Dataset,WRITE%Warn&lt;space&gt;and&lt;space&gt;Ignore&lt;space&gt;Feature&lt;space&gt;Dataset,IGNORE%Error&lt;space&gt;and&lt;space&gt;End&lt;space&gt;Translation,END&quot;,GEODATABASE_FILE&lt;space&gt;Feature&lt;space&gt;Dataset&lt;space&gt;Handling:,GEODATABASE_FILE_END_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;After&lt;space&gt;Write,GEODATABASE_FILE_VALIDATE_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Validate&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;Write:,GEODATABASE_FILE_TRANSACTION,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Number:,GEODATABASE_FILE_GRID_1,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_PRESERVE_GLOBALID,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Preserve&lt;space&gt;GlobalID:,GEODATABASE_FILE_HAS_MEASURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Measures,GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP TEMPLATEFILE%IMPORT_KIND%++YES+OVERWRITE_GEODB+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Import&lt;space&gt;XML&lt;space&gt;Workspace&lt;space&gt;Document,GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_DEFAULT_Z_VALUE,&quot;OPTIONAL FLOAT&quot;,GEODATABASE_FILE&lt;space&gt;Default&lt;space&gt;Z&lt;space&gt;Value:,GEODATABASE_FILE_ENABLE_FAST_DELETES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Fast&lt;space&gt;Deletes:,GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_OVERWRITE_GEODB,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_OPEN%DATASET_TEMPLATE%++YES+IMPORT_XML_TEMPLATE_GROUP+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;Geodatabase,GEODATABASE_FILE_Z_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_TRANSACTION_INTERVAL,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Features&lt;space&gt;Per&lt;space&gt;Transaction"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ANNOTATION_UNITS" PARM_VALUE="unknown_units"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_BEGIN_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COMPRESS_AT_END" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE_TYPE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DEFAULT_Z_VALUE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DUMP_FAILED_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_FAST_DELETES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_END_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FEATURE_DATASET_HANDLING" PARM_VALUE="WRITE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FFS_DUMP_FILE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GRID_1" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_MEASURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_Z_VALUES" PARM_VALUE="auto_detect"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES" PARM_VALUE="-1"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_OVERWRITE_GEODB" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_PRESERVE_GLOBALID" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION" PARM_VALUE="CURRENT"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_GEOM" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_INTERVAL" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_TYPE" PARM_VALUE="TRANSACTIONS"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_VALIDATE_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_XY_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_X_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Y_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;u70b9&gt;:Output,ftp_feature_type_name,&lt;u70b9&gt;,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_point,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u5e8f&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u662f&lt;gt&gt;&lt;lt&gt;u5426&lt;gt&gt;&lt;lt&gt;u8be5&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6240&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;128&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u533a&lt;gt&gt;&lt;lt&gt;u53bf&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u4e61&lt;gt&gt;&lt;lt&gt;u9547&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u8c03&lt;gt&gt;&lt;lt&gt;u67e5&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u539f&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;128&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u5b8c&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u65f6&lt;gt&gt;&lt;lt&gt;u95f4&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u5927&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u522b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u578b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u957f&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u7c73&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u5bbd&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u7c73&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u5e73&lt;gt&gt;&lt;lt&gt;u65b9&lt;gt&gt;&lt;lt&gt;u7c73&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u5f84&lt;gt&gt;&lt;lt&gt;u5927&lt;gt&gt;&lt;lt&gt;u5c0f&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5750&lt;gt&gt;&lt;lt&gt;u843d&lt;gt&gt;&lt;lt&gt;u5355&lt;gt&gt;&lt;lt&gt;u4f4d&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;100&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u6743&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u4e3b&lt;gt&gt;&lt;lt&gt;u4f53&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;100&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u5b9e&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u4e3b&lt;gt&gt;&lt;lt&gt;u4f53&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;100&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u8d23&lt;gt&gt;&lt;lt&gt;u4efb&lt;gt&gt;&lt;lt&gt;u4eba&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;15&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u662f&lt;gt&gt;&lt;lt&gt;u5426&lt;gt&gt;&lt;lt&gt;u7b7e&lt;gt&gt;&lt;lt&gt;u8ba2&lt;gt&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u534f&lt;gt&gt;&lt;lt&gt;u8bae&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;23&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u8fd0&lt;gt&gt;&lt;lt&gt;u884c&lt;gt&gt;&lt;lt&gt;u72b6&lt;gt&gt;&lt;lt&gt;u6001&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;24&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u4f7f&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u72b6&lt;gt&gt;&lt;lt&gt;u6001&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;24&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u4f4d&lt;gt&gt;&lt;lt&gt;u7f6e&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;wkt&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;1280&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;GEODB_ORIGIN_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ADVANCED_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_XY_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_Z_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;comma&gt;GEODB_XORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_YORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_XYSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;comma&gt;GEODB_ZORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_ZSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_ORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_SCALE&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;;&lt;u7ebf&gt;:Output00,ftp_feature_type_name,&lt;u7ebf&gt;,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_polyline,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u5e8f&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u662f&lt;gt&gt;&lt;lt&gt;u5426&lt;gt&gt;&lt;lt&gt;u8be5&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6240&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;128&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u533a&lt;gt&gt;&lt;lt&gt;u53bf&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u4e61&lt;gt&gt;&lt;lt&gt;u9547&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u8c03&lt;gt&gt;&lt;lt&gt;u67e5&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u539f&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;128&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u5b8c&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u65f6&lt;gt&gt;&lt;lt&gt;u95f4&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u5927&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u522b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u578b&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u957f&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u7c73&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u5bbd&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u7c73&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u5e73&lt;gt&gt;&lt;lt&gt;u65b9&lt;gt&gt;&lt;lt&gt;u7c73&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u5f84&lt;gt&gt;&lt;lt&gt;u5927&lt;gt&gt;&lt;lt&gt;u5c0f&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5750&lt;gt&gt;&lt;lt&gt;u843d&lt;gt&gt;&lt;lt&gt;u5355&lt;gt&gt;&lt;lt&gt;u4f4d&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;100&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u6743&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u4e3b&lt;gt&gt;&lt;lt&gt;u4f53&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;100&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u5b9e&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u4e3b&lt;gt&gt;&lt;lt&gt;u4f53&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;100&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u8d23&lt;gt&gt;&lt;lt&gt;u4efb&lt;gt&gt;&lt;lt&gt;u4eba&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;15&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u662f&lt;gt&gt;&lt;lt&gt;u5426&lt;gt&gt;&lt;lt&gt;u7b7e&lt;gt&gt;&lt;lt&gt;u8ba2&lt;gt&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u534f&lt;gt&gt;&lt;lt&gt;u8bae&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;23&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u8fd0&lt;gt&gt;&lt;lt&gt;u884c&lt;gt&gt;&lt;lt&gt;u72b6&lt;gt&gt;&lt;lt&gt;u6001&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;24&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u4f7f&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u72b6&lt;gt&gt;&lt;lt&gt;u6001&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;24&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u4f4d&lt;gt&gt;&lt;lt&gt;u7f6e&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;wkt&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;1280&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;GEODB_ORIGIN_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ADVANCED_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_XY_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_Z_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;comma&gt;GEODB_XORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_YORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_XYSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;comma&gt;GEODB_ZORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_ZSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_ORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_SCALE&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.&lt;space&gt;%-#&lt;openbracket&gt;&lt;closebracket&gt;&lt;quote&gt;&lt;openparen&gt;&lt;closeparen&gt;!?*&lt;apos&gt;&lt;amp&gt;+&lt;backslash&gt;&lt;solidus&gt;&lt;opencurly&gt;&lt;closecurly&gt;|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;blob&lt;comma&gt;fme_binarybuffer&lt;comma&gt;blob&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;blob&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;globalid&lt;comma&gt;fme_buffer&lt;comma&gt;guid&lt;comma&gt;fme_buffer&lt;comma&gt;date&lt;comma&gt;fme_datetime&lt;comma&gt;timestamp_offset&lt;comma&gt;fme_datetime&lt;comma&gt;date_only&lt;comma&gt;fme_date&lt;comma&gt;time_only&lt;comma&gt;fme_time&lt;comma&gt;integer&lt;comma&gt;fme_int32&lt;comma&gt;integer&lt;comma&gt;fme_uint16&lt;comma&gt;smallint&lt;comma&gt;fme_int16&lt;comma&gt;smallint&lt;comma&gt;fme_int8&lt;comma&gt;smallint&lt;comma&gt;fme_uint8&lt;comma&gt;bigint&lt;comma&gt;fme_int64&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;char&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;boolean&lt;comma&gt;fme_boolean&lt;comma&gt;&lt;quote&gt;double&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;subtype&lt;openparen&gt;stringset&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;subtype_codes&lt;openparen&gt;stringmap&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;range_domain&lt;openparen&gt;range_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;coded_domain&lt;openparen&gt;coded_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,&lt;backslash&gt;&lt;backslash&gt;&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|&lt;openbracket&gt;%#&lt;space&gt;&lt;apos&gt;&lt;amp&gt;+-&lt;closebracket&gt;.^~&lt;dollar&gt;&lt;comma&gt;&lt;closeparen&gt;&lt;openparen&gt;,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point&lt;comma&gt;fme_point&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_line&lt;comma&gt;geodb_multipoint&lt;comma&gt;fme_point&lt;comma&gt;geodb_table&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_table&lt;comma&gt;fme_collection&lt;comma&gt;geodb_arc&lt;comma&gt;fme_arc&lt;comma&gt;geodb_ellipse&lt;comma&gt;fme_ellipse&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;geodb_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_pro_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_dimension&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_junction&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_complex_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_attributed_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_undefined&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_metadata&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_raster&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;geodb_table&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_group&lt;space&gt;fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_common_group&lt;space&gt;fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group&lt;space&gt;General&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_feature_operation&lt;space&gt;Insert&lt;comma&gt;INSERT&lt;comma&gt;fme_update_geometry&lt;comma&gt;fme_selection_group&lt;comma&gt;mie_pack%Update&lt;comma&gt;UPDATE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert&lt;comma&gt;UPSERT&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Delete&lt;comma&gt;DELETE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;fme_update_geometry&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;comma&gt;fme_spatial_group&lt;comma&gt;fme_advanced_group&lt;comma&gt;oracle_sequenced_cols%&lt;lt&gt;at&lt;gt&gt;Value&lt;lt&gt;openparen&lt;gt&gt;fme_db_operation&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;MULTIPLE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;space&gt;Feature&lt;space&gt;Operation&lt;quote&gt;&lt;comma&gt;INSERT&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_table_handling&lt;space&gt;Use&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;USE_EXISTING&lt;comma&gt;fme_table_creation_group%Create&lt;lt&gt;space&lt;gt&gt;If&lt;lt&gt;space&lt;gt&gt;Needed&lt;comma&gt;CREATE_IF_MISSING%Drop&lt;lt&gt;space&lt;gt&gt;and&lt;lt&gt;space&lt;gt&gt;Create&lt;comma&gt;DROP_CREATE%Truncate&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;TRUNCATE_EXISTING&lt;comma&gt;fme_table_creation_group&lt;space&gt;Table&lt;space&gt;Handling&lt;quote&gt;&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;fme_update_geometry&lt;space&gt;Yes&lt;comma&gt;YES%No&lt;comma&gt;NO&lt;space&gt;Update&lt;space&gt;Spatial&lt;space&gt;Column&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;YES&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_selection_group&lt;space&gt;fme_selection_method&lt;space&gt;Row&lt;space&gt;Selection&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;RADIOPARAMETERGROUP&lt;space&gt;fme_selection_method&lt;space&gt;fme_match_columns&lt;comma&gt;MATCH_COLUMNS%fme_where_builder_clause&lt;comma&gt;BUILDER&lt;space&gt;Row&lt;space&gt;Selection&lt;space&gt;Method&lt;quote&gt;&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;ATTRLIST_COMMAS&lt;space&gt;fme_match_columns&lt;space&gt;Match&lt;space&gt;Columns&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;TEXT_EDIT_SQL_CFG_OR_ATTR&lt;space&gt;fme_where_builder_clause&lt;space&gt;MODE&lt;comma&gt;WHERE&lt;space&gt;WHERE&lt;space&gt;Clause&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_table_creation_group&lt;space&gt;FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP&lt;space&gt;Table&lt;space&gt;Creation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ORIGIN_GROUP&lt;space&gt;GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP&lt;space&gt;Origin&lt;space&gt;and&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ANNO_GROUP&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ADVANCED_GROUP&lt;space&gt;GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;%GEODB_AVG_NUM_POINTS&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_XY_GROUP&lt;space&gt;GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_Z_GROUP&lt;space&gt;GEODB_ZORIGIN%GEODB_ZSCALE&lt;space&gt;Z&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_MEASURES_GROUP&lt;space&gt;GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_NAME&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_ALIAS&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_NAME&lt;space&gt;Shape&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_ALIAS&lt;space&gt;Shape&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_CONFIG_KEYWORD&lt;space&gt;Configuration&lt;space&gt;Keyword&lt;quote&gt;&lt;comma&gt;DEFAULTS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;space&gt;Grid&lt;space&gt;1&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;GEODB_AVG_NUM_POINTS&lt;space&gt;Average&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Points&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XORIGIN&lt;space&gt;X&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_YORIGIN&lt;space&gt;Y&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_Z_VALUES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZORIGIN&lt;space&gt;Z&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZSCALE&lt;space&gt;Z&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_MEASURES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_ORIGIN&lt;space&gt;Measure&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_SCALE&lt;space&gt;Measure&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;space&gt;Reference&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;geodb_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;GEODB_UPDATE_KEY_COLUMNS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_DROP_TABLE&lt;comma&gt;NO&lt;comma&gt;GEODB_TRUNCATE_TABLE&lt;comma&gt;NO&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_selection_method&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_update_geometry&lt;comma&gt;YES&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_FEATURE_DATASET&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;_GEODBOverwriteGEODB&lt;space&gt;_GEODBOutTransactionType&lt;space&gt;_GEODBOutSimplifyGeometry&lt;space&gt;_GEODBOutXOrigin&lt;space&gt;_GEODBOutYOrigin&lt;space&gt;_GEODBOutScale&lt;space&gt;_GEODBOutZOrigin&lt;space&gt;_GEODBOutZScale&lt;space&gt;_GEODBOutGrid1&lt;space&gt;_TRANSLATE_SPATIAL_DATA_ONLY&lt;space&gt;_GEODBInResolveDomains&lt;space&gt;_GEODBInResolveSubtypeNames&lt;space&gt;_GEODBInIgnoreNetworkInfo&lt;space&gt;_GEODBInIgnoreRelationshipInfo&lt;space&gt;_GEODBInSplitComplexEdges&lt;space&gt;_GEODBInWhereClause&lt;space&gt;NULL_IN_SPLIT_COMPLEX_ANNOS&lt;space&gt;NULL_IN_CACHE_MULTIPATCH_TEXTURES&lt;space&gt;NULL_IN_SEARCH_METHOD&lt;space&gt;NULL_IN_SEARCH_ORDER&lt;space&gt;NULL_IN_SEARCH_FEATURE&lt;space&gt;NULL_IN_CHECK_SIMPLE_GEOM&lt;space&gt;NULL_IN_MERGE_FEAT_LINKED_ANNOS&lt;space&gt;NULL_IN_READ_THREE_POINT_ARCS&lt;space&gt;NULL_IN_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_SIMPLE_DONUT_GEOMETRY&lt;space&gt;_GEODB_IN_ALIAS_MODE&lt;space&gt;GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION&lt;space&gt;GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE&lt;space&gt;GEODATABASE_FILE_OUT_WRITER_MODE&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL&lt;space&gt;GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY&lt;space&gt;GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_FFS_DUMP_FILE&lt;space&gt;GEODATABASE_FILE_OUT_ANNOTATION_UNITS&lt;space&gt;GEODATABASE_FILE_OUT_HAS_MEASURES&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_ORIGIN&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_SCALE&lt;space&gt;GEODATABASE_FILE_OUT_COMPRESS_AT_END&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES&lt;space&gt;GEODATABASE_FILE_OUT_PRESERVE_GLOBALID&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE&lt;space&gt;GEODATABASE_FILE_OUT_VALIDATE_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;GEODATABASE_FILE_OUT_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;PARAMS_TO_NOT_PROPAGATE_ON_INSPECT&lt;comma&gt;&lt;quote&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;ATTRIBUTE_READING_HISTORIC&lt;comma&gt;ALL&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;&lt;quote&gt;Feature&lt;space&gt;Class&lt;space&gt;or&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;FeatureClass1&lt;comma&gt;SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME&lt;comma&gt;NO&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;SQL_EXECUTE_DIRECTIVES&lt;comma&gt;INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="52"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="9131.3413134131333 -284.37784377843775"
#!   BOUNDING_RECT="9131.3413134131333 -284.37784377843775 430 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER)&lt;backslash&gt;&lt;u8f6c&gt;&lt;u6362&gt;&lt;u9519&gt;&lt;u8bef&gt;.xlsx"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_FILE&lt;comma&gt;No&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;comma&gt;TEMPLATE_SHEET&lt;comma&gt;&lt;comma&gt;REMOVE_UNCHANGED_TEMPLATE_SHEET&lt;comma&gt;No&lt;comma&gt;MULTIPLE_TEMPLATE_SHEETS&lt;comma&gt;Yes&lt;comma&gt;INSERT_IGNORE_DB_OP&lt;comma&gt;Yes&lt;comma&gt;DROP_TABLE&lt;comma&gt;No&lt;comma&gt;TRUNCATE_TABLE&lt;comma&gt;No&lt;comma&gt;FIELD_NAMES_OUT&lt;comma&gt;Yes&lt;comma&gt;FIELD_NAMES_FORMATTING&lt;comma&gt;Yes&lt;comma&gt;WRITER_MODE&lt;comma&gt;Insert&lt;comma&gt;RASTER_FORMAT&lt;comma&gt;PNG&lt;comma&gt;PROTECT_SHEET&lt;comma&gt;NO&lt;comma&gt;PROTECT_SHEET_PASSWORD&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_LEVEL&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_PERMISSIONS&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING&lt;comma&gt;yes&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE&lt;comma&gt;CUSTOM_NUMBER_FORMATTING&lt;comma&gt;ENABLE_NATIVE&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXW_FIELD_NAMES_FORMATTING,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:,XLSXW_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_CUSTOM_NUMBER_FORMATTING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_MULTIPLE_TEMPLATE_SHEETS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_FIELD_NAMES_OUT,&quot;OPTIONAL ACTIVECHOICE Yes%No,FIELD_NAMES_FORMATTING,++FIELD_NAMES_FORMATTING+No&quot;,XLSXW&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:,XLSXW_WRITER_MODE,&quot;OPTIONAL CHOICE Insert%Update%Delete&quot;,XLSXW&lt;space&gt;Default&lt;space&gt;Feature&lt;space&gt;Type&lt;space&gt;Writer&lt;space&gt;Mode:,XLSXW_INSERT_IGNORE_DB_OP,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_PROTECT_SHEET,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP PROTECT_SHEET_PASSWORD%PROTECT_SHEET_LEVEL%PROTECT_SHEET_PERMISSIONS&quot;,XLSXW&lt;space&gt;Protect&lt;space&gt;Sheet,XLSXW_TRUNCATE_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_DROP_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_OVERWRITE_FILE,&quot;OPTIONAL ACTIVECHOICE Yes%No,TEMPLATEFILE,TEMPLATE_SHEET,REMOVE_UNCHANGED_TEMPLATE_SHEET&quot;,XLSXW&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;File:,XLSXW_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXW&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,XLSXW_RASTER_FORMAT,&quot;OPTIONAL CHOICE BMP%JPEG%PNG&quot;,XLSXW&lt;space&gt;Raster&lt;space&gt;Format:"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="point_Rejected:&lt;lt&gt;Rejected&lt;gt&gt;,ftp_feature_type_name,point_Rejected,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;14&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;4&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u5f84&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;12&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;B&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;L&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;17&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_rejection_code&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;17&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No;line_Rejected:&lt;lt&gt;Rejected&lt;gt&gt;00,ftp_feature_type_name,line_Rejected,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;9&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;4&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u957f&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;comma&gt;number&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;B1&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;L1&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;17&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;B2&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;L2&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;17&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_rejection_code&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_xml&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_json&lt;comma&gt;&lt;quote&gt;auto&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;datetime&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_datetime&lt;comma&gt;&lt;quote&gt;time&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_time&lt;comma&gt;&lt;quote&gt;date&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_date&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;boolean&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;openbracket&gt;&lt;closebracket&gt;*&lt;backslash&gt;&lt;backslash&gt;?:&lt;apos&gt;,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none&lt;comma&gt;fme_no_geom&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point&lt;comma&gt;xlsx_point&lt;comma&gt;fme_point&lt;comma&gt;xlsx_none&lt;comma&gt;fme_line&lt;comma&gt;xlsx_none&lt;comma&gt;fme_polygon&lt;comma&gt;xlsx_none&lt;comma&gt;fme_text&lt;comma&gt;xlsx_none&lt;comma&gt;fme_ellipse&lt;comma&gt;xlsx_none&lt;comma&gt;fme_arc&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_collection&lt;comma&gt;xlsx_none&lt;comma&gt;fme_surface&lt;comma&gt;xlsx_none&lt;comma&gt;fme_solid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_raster&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point_cloud&lt;comma&gt;xlsx_none&lt;comma&gt;fme_voxel_grid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;xlsx_layer_group&lt;space&gt;xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group&lt;space&gt;Sheet&lt;space&gt;Settings&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_truncate_group&lt;space&gt;xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet&lt;space&gt;Drop&lt;solidus&gt;Truncate&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_rowcolumn_group&lt;space&gt;xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Position&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVEDISCLOSUREGROUP&lt;space&gt;xlsx_protect_sheet&lt;space&gt;xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions&lt;space&gt;Protect&lt;space&gt;Sheet&lt;quote&gt;&lt;comma&gt;NO&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_template_group&lt;space&gt;xlsx_template_sheet%xlsx_remove_unchanged_template_sheet&lt;space&gt;Template&lt;space&gt;Options&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_advanced_group&lt;space&gt;xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_drop_sheet&lt;space&gt;Yes%No&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_trunc_sheet&lt;space&gt;Yes%No&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_sheet_order&lt;space&gt;1%MAX&lt;space&gt;Sheet&lt;space&gt;Order&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_freeze_end_row&lt;space&gt;1%MAX&lt;space&gt;Freeze&lt;space&gt;First&lt;space&gt;Row&lt;openparen&gt;s&lt;closeparen&gt;&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_field_names_out&lt;space&gt;Yes%No&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;++xlsx_field_names_formatting+No&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_field_names_formatting&lt;space&gt;Yes%No&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_names_are_positions&lt;space&gt;Yes%No&lt;space&gt;Use&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;As&lt;space&gt;Column&lt;space&gt;Positions:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_start_col&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_start_row&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_offset_col&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_raster_type&lt;space&gt;BMP%JPEG%PNG&lt;space&gt;Raster&lt;space&gt;Format:&lt;quote&gt;&lt;comma&gt;PNG&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;PASSWORD_ENCODED&lt;space&gt;xlsx_protect_sheet_password&lt;space&gt;Password:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;xlsx_protect_sheet_level&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_DEFAULT&lt;comma&gt;xlsx_protect_sheet_permissions%View&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_ALL&lt;comma&gt;xlsx_protect_sheet_permissions%Specific&lt;lt&gt;space&lt;gt&gt;Permissions&lt;space&gt;Protection&lt;space&gt;Level:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;LOOKUP_LISTBOX&lt;space&gt;xlsx_protect_sheet_permissions&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;locked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_LOCKED_CELLS%Select&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_UNLOCKED_CELLS%Format&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_FORMAT_CELLS%Format&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_FORMAT_COLUMNS%Format&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_FORMAT_ROWS%Insert&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_INSERT_COLUMNS%Insert&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_INSERT_ROWS%Add&lt;lt&gt;space&lt;gt&gt;hyperlinks&lt;lt&gt;space&lt;gt&gt;to&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_INSERT_HYPERLINKS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_DELETE_COLUMNS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_DELETE_ROWS%Sort&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;solidus&gt;rows&lt;solidus&gt;columns&lt;comma&gt;PROT_SORT%Use&lt;lt&gt;space&lt;gt&gt;Autofilter&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_AUTOFILTER%Use&lt;lt&gt;space&lt;gt&gt;PivotTable&lt;lt&gt;space&lt;gt&gt;&lt;amp&gt;&lt;lt&gt;space&lt;gt&gt;PivotChart&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_PIVOTTABLES%Edit&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;objects&lt;comma&gt;PROT_OBJECTS%Edit&lt;lt&gt;space&lt;gt&gt;unprotected&lt;lt&gt;space&lt;gt&gt;scenarios&lt;comma&gt;PROT_SCENARIOS&lt;space&gt;Specific&lt;space&gt;Permissions:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_table_writer_mode&lt;space&gt;Insert&lt;comma&gt;+xlsx_row_id_column+&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;%Update&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id%Delete&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id&lt;space&gt;Writer&lt;space&gt;Mode:&lt;quote&gt;&lt;comma&gt;Insert&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ATTR&lt;space&gt;xlsx_row_id_column&lt;space&gt;ALLOW_NEW&lt;space&gt;Row&lt;space&gt;Number&lt;space&gt;Attribute:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT_EDIT&lt;space&gt;xlsx_template_sheet&lt;space&gt;Template&lt;space&gt;Sheet:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_remove_unchanged_template_sheet&lt;space&gt;Yes%No&lt;space&gt;Remove&lt;space&gt;Template&lt;space&gt;Sheet&lt;space&gt;if&lt;space&gt;Unchanged:&lt;quote&gt;&lt;comma&gt;No,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;NO&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;No&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,DEFAULT_READER&lt;comma&gt;XLSXR&lt;comma&gt;ALLOW_DATASET_CONFLICT&lt;comma&gt;YES&lt;comma&gt;MIME_TYPE&lt;comma&gt;&lt;quote&gt;application&lt;solidus&gt;vnd.openxmlformats-officedocument.spreadsheetml.sheet&lt;space&gt;ADD_DISPOSITION&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;DEFAULT_ATTR_TYPE&lt;comma&gt;auto&lt;comma&gt;USER_ATTRIBUTES_COLUMNS&lt;comma&gt;&lt;quote&gt;Width&lt;comma&gt;Cell&lt;space&gt;Width%Precision&lt;comma&gt;Formatting&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Sheet&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Sheet1&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;name&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Microsoft&lt;space&gt;Excel&lt;space&gt;file&lt;quote&gt;,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter_2"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_CUSTOM_NUMBER_FORMATTING" PARM_VALUE="ENABLE_NATIVE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DROP_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_FORMATTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_OUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_INSERT_IGNORE_DB_OP" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_MULTIPLE_TEMPLATE_SHEETS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXW_OVERWRITE_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_PROTECT_SHEET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_RASTER_FORMAT" PARM_VALUE="PNG"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_TRUNCATE_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_WRITER_MODE" PARM_VALUE="Insert"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="37"
#!   SOURCE_NODE="34"
#!   TARGET_NODE="36"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="46"
#!   SOURCE_NODE="36"
#!   TARGET_NODE="45"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="43"
#!   SOURCE_NODE="38"
#!   TARGET_NODE="42"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="48"
#!   SOURCE_NODE="39"
#!   TARGET_NODE="47"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="44"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="39"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="50"
#!   SOURCE_NODE="45"
#!   TARGET_NODE="49"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="51"
#!   SOURCE_NODE="47"
#!   TARGET_NODE="49"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 Output00"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="61"
#!   SOURCE_NODE="55"
#!   TARGET_NODE="56"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="62"
#!   SOURCE_NODE="56"
#!   TARGET_NODE="57"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="68"
#!   SOURCE_NODE="57"
#!   TARGET_NODE="67"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="77"
#!   SOURCE_NODE="64"
#!   TARGET_NODE="66"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="78"
#!   SOURCE_NODE="66"
#!   TARGET_NODE="73"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="70"
#!   SOURCE_NODE="67"
#!   TARGET_NODE="69"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="72"
#!   SOURCE_NODE="69"
#!   TARGET_NODE="71"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="35"
#!   SOURCE_NODE="71"
#!   TARGET_NODE="34"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="79"
#!   SOURCE_NODE="73"
#!   TARGET_NODE="74"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="80"
#!   SOURCE_NODE="74"
#!   TARGET_NODE="75"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="81"
#!   SOURCE_NODE="75"
#!   TARGET_NODE="76"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="86"
#!   SOURCE_NODE="76"
#!   TARGET_NODE="83"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="85"
#!   SOURCE_NODE="83"
#!   TARGET_NODE="84"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="88"
#!   SOURCE_NODE="84"
#!   TARGET_NODE="87"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="41"
#!   SOURCE_NODE="87"
#!   TARGET_NODE="38"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="65"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="55"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;u70b9&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="53"
#!   SOURCE_NODE="34"
#!   TARGET_NODE="52"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;REJECTED&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 &lt;lt&gt;lt&lt;gt&gt;Rejected&lt;lt&gt;gt&lt;gt&gt;"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="54"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="52"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;REJECTED&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 1 &lt;lt&gt;lt&lt;gt&gt;Rejected&lt;lt&gt;gt&lt;gt&gt;00"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="82"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="64"
#!   SOURCE_PORT_DESC="fo 2 &lt;lt&gt;u7ebf&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO file C:\Users\<USER>\Desktop\经纬度坐标转点、线图层(2).xlsx

DEFAULT_MACRO DEF_VAL 

DEFAULT_MACRO DEF_VAL_2 

DEFAULT_MACRO DEF_VAL_3 

DEFAULT_MACRO PARAMETER 

# ============================================================================
INCLUDE [ if {{$(file$encode)} == {}} { puts_real {Parameter 'file' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)经纬度坐标转点、线图层.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 经纬度坐标转点、线图层
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED <u70b9> <u7ebf>
MACRO FeatureReader_DIRECTIVES ADVANCED,,ALLOW_DOLLAR_SIGNS,YES,APPLY_FILTERS,No,CASE_SENSITIVE_FEATURE_TYPES,YES,CONFIGURATION_DATASET,C:<backslash>Users<backslash>djz<backslash>Desktop<backslash><u7ecf><u7eac><u5ea6><u5750><u6807><u8f6c><u70b9><u3001><u7ebf><u56fe><u5c42><openparen>2<closeparen>.xlsx,CREATE_FEATURE_TABLES_FROM_DATA,Yes,EXCEL_COL_NAMES,YES,EXPAND_MERGED_CELLS,Yes,EXPOSE_ATTRS_GROUP,,FORCE_DATETIME,NO,NETWORK_AUTHENTICATION,,QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,Yes,READ_BLANK_AS,Missing,READ_FORM_CONTROLS,NONE,READ_RASTER_MODE,None,REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,YES,SCAN_FOR_GEOMETRIC_TYPES,Yes,SCAN_MAX_FEATURES,1000,SCHEMA,<lt>u70b9<gt><comma>0<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>14<lt>comma<gt><lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><lt>comma<gt>char<lt>comma<gt>4<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u7ba1<gt><lt>u5f84<gt><lt>comma<gt>char<lt>comma<gt>12<lt>comma<gt><lt>comma<gt><lt>comma<gt>3<lt>comma<gt>B<lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt>L<lt>comma<gt>char<lt>comma<gt>17<lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt>C:<lt>lt<gt>backslash<lt>gt<gt>Users<lt>lt<gt>backslash<lt>gt<gt>djz<lt>lt<gt>backslash<lt>gt<gt>Desktop<lt>lt<gt>backslash<lt>gt<gt><lt>lt<gt>u7ecf<lt>gt<gt><lt>lt<gt>u7eac<lt>gt<gt><lt>lt<gt>u5ea6<lt>gt<gt><lt>lt<gt>u5750<lt>gt<gt><lt>lt<gt>u6807<lt>gt<gt><lt>lt<gt>u8f6c<lt>gt<gt><lt>lt<gt>u70b9<lt>gt<gt><lt>lt<gt>u3001<lt>gt<gt><lt>lt<gt>u7ebf<lt>gt<gt><lt>lt<gt>u56fe<lt>gt<gt><lt>lt<gt>u5c42<lt>gt<gt><lt>lt<gt>openparen<lt>gt<gt>2<lt>lt<gt>closeparen<lt>gt<gt>.xlsx<lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>4<lt>comma<gt>17<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma><lt>u7ebf<gt><comma>0<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>9<lt>comma<gt><lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><lt>comma<gt>char<lt>comma<gt>4<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u957f<gt><lt>u5ea6<gt><lt>comma<gt>number<lt>comma<gt>4<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>3<lt>comma<gt>B1<lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt>L1<lt>comma<gt>char<lt>comma<gt>17<lt>comma<gt><lt>comma<gt><lt>comma<gt>5<lt>comma<gt>B2<lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt>L2<lt>comma<gt>char<lt>comma<gt>17<lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt><lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>6<lt>comma<gt>45<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO,SCHEMA_HANDLING_REVISION,2,SKIP_EMPTY_ROWS,YES,STRIP_SHEETNAME_SPACES,YES,TABLELIST,<u70b9><space><u7ebf>,TRIM_ATTR_NAME_CHARACTERS,,TRIM_ATTR_NAME_WHITESPACE,Yes,USE_CUSTOM_SCHEMA,NO,XLSXR_EXPOSE_FORMAT_ATTRS,
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(file$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { XLSXR  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "<u70b9><space><u7ebf>" }    READER_DIRECTIVES   { METAFILE,XLSXR }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT <u70b9> FEATURE_TYPE FeatureReader_<u70b9>    OUTPUT <u7ebf> FEATURE_TYPE FeatureReader_<u7ebf>
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer }    INPUT  FEATURE_TYPE FeatureReader_<u70b9>    USE_REGEX { NO }    CASE_SENSITIVE { NO }    SOURCE_ATTRIBUTES { B,L }    FIND_TEXT { "<u00b0>" }    REPLACE_TEXT { "<space>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer_2 }    INPUT  FEATURE_TYPE StringReplacer_OUTPUT    USE_REGEX { NO }    CASE_SENSITIVE { NO }    SOURCE_ATTRIBUTES { B,L }    FIND_TEXT { "<u2032>" }    REPLACE_TEXT { "<space>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_2_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer_3 }    INPUT  FEATURE_TYPE StringReplacer_2_OUTPUT    USE_REGEX { NO }    CASE_SENSITIVE { NO }    SOURCE_ATTRIBUTES { B,L }    FIND_TEXT { "<u2033>" }    REPLACE_TEXT { "<space>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_3_OUTPUT          }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_19_837de1ba_9013_47df_9f77_422d5b46f2750} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {<space>}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {B}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {B}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter_19 }    INPUT  FEATURE_TYPE StringReplacer_3_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_19_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_20_5beae216_880f_4679_9c45_aea02785022c0} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {<space>}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {L}]];       set attrNum 0;       set listName [FME_DecodeText {_list1}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {L}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list1}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter_20 }    INPUT  FEATURE_TYPE AttributeSplitter_19_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_20_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_7 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeSplitter_20_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u8f6c><u6362><u540e><u7ecf><u5ea6>" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_list<opencurly>0<closecurly><closeparen>+<at>Value<openparen>_list<opencurly>1<closecurly><closeparen><solidus>60+<at>Value<openparen>_list<opencurly>2<closecurly><closeparen><solidus>3600<closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u8f6c><u6362><u540e><u7eac><u5ea6>" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_list1<opencurly>0<closecurly><closeparen>+<at>Value<openparen>_list1<opencurly>1<closecurly><closeparen><solidus>60+<at>Value<openparen>_list1<opencurly>2<closecurly><closeparen><solidus>3600<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_7_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} VertexCreatorFactory    FACTORY_NAME { VertexCreator }    INPUT  FEATURE_TYPE AttributeCreator_7_OUTPUT    MODE { ADD }    INDEX { "<Unused>" }    CONTINUE_ON_ERROR YES    XVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u8f6c><u6362><u540e><u7ecf><u5ea6><closeparen>,VertexCreator)" }    YVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u8f6c><u6362><u540e><u7eac><u5ea6><closeparen>,VertexCreator)" }    ZVAL { "" }    USE_EXISTING_Z YES    ALLOW_DUPLICATES { "YES" }    CLOSE_LINES { "Yes" }    ADD_MODE_VERSION 5    MISSING_VAL_MODE { COMPUTE_NANS }    COMPUTE_MEASURES_MODE { CONTINUOUS }    COMMAND_PARM_EVALUATION SINGLE_PASS    OUTPUT { OUTPUT FEATURE_TYPE VertexCreator_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE VertexCreator_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE VertexCreator_OUTPUT    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("EPSG:4490","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Reprojector_REPROJECTED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u5e8f><u53f7>" "SET_TO" "" "varchar<openparen>6<closeparen>" }      ATTR_ACTION { "" "<u662f><u5426><u8be5><u9879><u76ee><u5de5><u7a0b>" "SET_TO" "" "varchar<openparen>21<closeparen>" }      ATTR_ACTION { "" "<u6240><u5c5e><u9879><u76ee><u540d><u79f0>" "SET_TO" "$(DEF_VAL)" "varchar<openparen>18<closeparen>" }      ATTR_ACTION { "" "<u9879><u76ee><u7f16><u53f7>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u533a><u53bf>" "SET_TO" "$(DEF_VAL_2)" "varchar<openparen>6<closeparen>" }      ATTR_ACTION { "" "<u4e61><u9547>" "SET_TO" "$(DEF_VAL_3)" "varchar<openparen>6<closeparen>" }      ATTR_ACTION { "" "<u8c03><u67e5><u7f16><u53f7>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u539f><u5de5><u7a0b><u7f16><u53f7>" "SET_TO" "<at>Value<openparen><u5de5><u7a0b><u7f16><u53f7><closeparen>" "varchar<openparen>15<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u540d><u79f0>" "SET_TO" "<at>Value<openparen><u5de5><u7a0b><u540d><u79f0><closeparen>" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u5b8c><u5de5><u65f6><u95f4>" "SET_TO" "" "varchar<openparen>18<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u5927><u7c7b>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u7c7b><u522b>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u7c7b><u578b>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u957f><u5ea6><uff08><u7c73><uff09>" "SET_TO" "" "varchar<openparen>21<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u5bbd><u5ea6><uff08><u7c73><uff09>" "SET_TO" "" "varchar<openparen>21<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u9762><u79ef><uff08><u5e73><u65b9><u7c73><uff09>" "SET_TO" "" "varchar<openparen>27<closeparen>" }      ATTR_ACTION { "" "<u7ba1><u5f84><u5927><u5c0f>" "SET_TO" "<at>Value<openparen><u7ba1><u5f84><closeparen>" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5750><u843d><u5355><u4f4d>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u6743><u5c5e><uff08><u7ba1><u62a4><u4e3b><u4f53><uff09>" "SET_TO" "" "varchar<openparen>30<closeparen>" }      ATTR_ACTION { "" "<u7ba1><u62a4><u5b9e><u65bd><u4e3b><u4f53>" "SET_TO" "" "varchar<openparen>18<closeparen>" }      ATTR_ACTION { "" "<u7ba1><u62a4><u8d23><u4efb><u4eba>" "SET_TO" "" "varchar<openparen>15<closeparen>" }      ATTR_ACTION { "" "<u662f><u5426><u7b7e><u8ba2><u7ba1><u62a4><u534f><u8bae>" "SET_TO" "" "varchar<openparen>24<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u8bbe><u65bd><u8fd0><u884c><u72b6><u6001>" "SET_TO" "" "varchar<openparen>24<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u8bbe><u65bd><u4f7f><u7528><u72b6><u6001>" "SET_TO" "" "varchar<openparen>24<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u4f4d><u7f6e><uff08>wkt<uff09>" "SET_TO" "" "varchar<openparen>21<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer_4 }    INPUT  FEATURE_TYPE FeatureReader_<u7ebf>    USE_REGEX { NO }    CASE_SENSITIVE { NO }    SOURCE_ATTRIBUTES { B1,B2,L1,L2 }    FIND_TEXT { "<u00b0>" }    REPLACE_TEXT { "<space>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_4_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer_5 }    INPUT  FEATURE_TYPE StringReplacer_4_OUTPUT    USE_REGEX { NO }    CASE_SENSITIVE { NO }    SOURCE_ATTRIBUTES { B1,B2,L1,L2 }    FIND_TEXT { "<u2032>" }    REPLACE_TEXT { "<space>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_5_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer_6 }    INPUT  FEATURE_TYPE StringReplacer_5_OUTPUT    USE_REGEX { NO }    CASE_SENSITIVE { NO }    SOURCE_ATTRIBUTES { B1,B2,L1,L2 }    FIND_TEXT { "<u2033>" }    REPLACE_TEXT { "<space>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_6_OUTPUT          }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_21_c304c44a_d847_400f_942e_b8df95e50e0e0} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {<space>}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {B1}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {B1}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter_21 }    INPUT  FEATURE_TYPE StringReplacer_6_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_21_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_22_d5bfa06b_28c4_4e78_8d35_c7e9ac76e95d0} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {<space>}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {L1}]];       set attrNum 0;       set listName [FME_DecodeText {_list1}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {L1}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list1}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter_22 }    INPUT  FEATURE_TYPE AttributeSplitter_21_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_22_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_8 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeSplitter_22_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u8f6c><u6362><u540e><u7ecf><u5ea6>1" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_list<opencurly>0<closecurly><closeparen>+<at>Value<openparen>_list<opencurly>1<closecurly><closeparen><solidus>60+<at>Value<openparen>_list<opencurly>2<closecurly><closeparen><solidus>3600<closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u8f6c><u6362><u540e><u7eac><u5ea6>1" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_list1<opencurly>0<closecurly><closeparen>+<at>Value<openparen>_list1<opencurly>1<closecurly><closeparen><solidus>60+<at>Value<openparen>_list1<opencurly>2<closecurly><closeparen><solidus>3600<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_8_OUTPUT        }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_23_64bb34d9_bf2d_46a1_8126_df77569fd5530} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {<space>}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {B2}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {B2}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter_23 }    INPUT  FEATURE_TYPE AttributeCreator_8_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_23_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_24_c356cde7_6b31_412e_881c_5cbf9698015c0} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {<space>}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {L2}]];       set attrNum 0;       set listName [FME_DecodeText {_list1}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {L2}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list1}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter_24 }    INPUT  FEATURE_TYPE AttributeSplitter_23_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_24_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_9 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeSplitter_24_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u8f6c><u6362><u540e><u7ecf><u5ea6>2" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_list<opencurly>0<closecurly><closeparen>+<at>Value<openparen>_list<opencurly>1<closecurly><closeparen><solidus>60+<at>Value<openparen>_list<opencurly>2<closecurly><closeparen><solidus>3600<closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u8f6c><u6362><u540e><u7eac><u5ea6>2" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_list1<opencurly>0<closecurly><closeparen>+<at>Value<openparen>_list1<opencurly>1<closecurly><closeparen><solidus>60+<at>Value<openparen>_list1<opencurly>2<closecurly><closeparen><solidus>3600<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_9_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} VertexCreatorFactory    FACTORY_NAME { VertexCreator_2 }    INPUT  FEATURE_TYPE AttributeCreator_9_OUTPUT    MODE { ADD }    INDEX { "<Unused>" }    CONTINUE_ON_ERROR YES    XVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u8f6c><u6362><u540e><u7ecf><u5ea6>1<closeparen>,VertexCreator_2)" }    YVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u8f6c><u6362><u540e><u7eac><u5ea6>1<closeparen>,VertexCreator_2)" }    ZVAL { "" }    USE_EXISTING_Z YES    ALLOW_DUPLICATES { "YES" }    CLOSE_LINES { "Yes" }    ADD_MODE_VERSION 5    MISSING_VAL_MODE { COMPUTE_NANS }    COMPUTE_MEASURES_MODE { CONTINUOUS }    COMMAND_PARM_EVALUATION SINGLE_PASS    OUTPUT { OUTPUT FEATURE_TYPE VertexCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} VertexCreatorFactory    FACTORY_NAME { VertexCreator_3 }    INPUT  FEATURE_TYPE VertexCreator_2_OUTPUT    MODE { ADD }    INDEX { "<Unused>" }    CONTINUE_ON_ERROR YES    XVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u8f6c><u6362><u540e><u7ecf><u5ea6>2<closeparen>,VertexCreator_3)" }    YVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u8f6c><u6362><u540e><u7eac><u5ea6>2<closeparen>,VertexCreator_3)" }    ZVAL { "" }    USE_EXISTING_Z YES    ALLOW_DUPLICATES { "YES" }    CLOSE_LINES { "Yes" }    ADD_MODE_VERSION 5    MISSING_VAL_MODE { COMPUTE_NANS }    COMPUTE_MEASURES_MODE { CONTINUOUS }    COMMAND_PARM_EVALUATION SINGLE_PASS    OUTPUT { OUTPUT FEATURE_TYPE VertexCreator_3_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE VertexCreator_3_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector_2 }    INPUT  FEATURE_TYPE VertexCreator_3_OUTPUT    OUTPUT { FEATURE_TYPE Reprojector_2_REPROJECTED         @Reproject("EPSG:4490","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector_2,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Reprojector_2_REPROJECTED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u5e8f><u53f7>" "SET_TO" "" "varchar<openparen>6<closeparen>" }      ATTR_ACTION { "" "<u662f><u5426><u8be5><u9879><u76ee><u5de5><u7a0b>" "SET_TO" "" "varchar<openparen>21<closeparen>" }      ATTR_ACTION { "" "<u6240><u5c5e><u9879><u76ee><u540d><u79f0>" "SET_TO" "$(DEF_VAL)" "varchar<openparen>18<closeparen>" }      ATTR_ACTION { "" "<u9879><u76ee><u7f16><u53f7>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u533a><u53bf>" "SET_TO" "$(DEF_VAL_2)" "varchar<openparen>6<closeparen>" }      ATTR_ACTION { "" "<u4e61><u9547>" "SET_TO" "$(DEF_VAL_3)" "varchar<openparen>6<closeparen>" }      ATTR_ACTION { "" "<u8c03><u67e5><u7f16><u53f7>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u539f><u5de5><u7a0b><u7f16><u53f7>" "SET_TO" "<at>Value<openparen><u5de5><u7a0b><u7f16><u53f7><closeparen>" "varchar<openparen>15<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u540d><u79f0>" "SET_TO" "<at>Value<openparen><u5de5><u7a0b><u540d><u79f0><closeparen>" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u5b8c><u5de5><u65f6><u95f4>" "SET_TO" "" "varchar<openparen>18<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u5927><u7c7b>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u7c7b><u522b>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u7c7b><u578b>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u957f><u5ea6><uff08><u7c73><uff09>" "SET_TO" "<at>Value<openparen><u957f><u5ea6><closeparen>" "varchar<openparen>21<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u5bbd><u5ea6><uff08><u7c73><uff09>" "SET_TO" "" "varchar<openparen>21<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u9762><u79ef><uff08><u5e73><u65b9><u7c73><uff09>" "SET_TO" "" "varchar<openparen>27<closeparen>" }      ATTR_ACTION { "" "<u7ba1><u5f84><u5927><u5c0f>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5750><u843d><u5355><u4f4d>" "SET_TO" "" "varchar<openparen>12<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u6743><u5c5e><uff08><u7ba1><u62a4><u4e3b><u4f53><uff09>" "SET_TO" "" "varchar<openparen>30<closeparen>" }      ATTR_ACTION { "" "<u7ba1><u62a4><u5b9e><u65bd><u4e3b><u4f53>" "SET_TO" "" "varchar<openparen>18<closeparen>" }      ATTR_ACTION { "" "<u7ba1><u62a4><u8d23><u4efb><u4eba>" "SET_TO" "" "varchar<openparen>15<closeparen>" }      ATTR_ACTION { "" "<u662f><u5426><u7b7e><u8ba2><u7ba1><u62a4><u534f><u8bae>" "SET_TO" "" "varchar<openparen>24<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u8bbe><u65bd><u8fd0><u884c><u72b6><u6001>" "SET_TO" "" "varchar<openparen>24<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u8bbe><u65bd><u4f7f><u7528><u72b6><u6001>" "SET_TO" "" "varchar<openparen>24<closeparen>" }      ATTR_ACTION { "" "<u5de5><u7a0b><u4f4d><u7f6e><uff08>wkt<uff09>" "SET_TO" "" "varchar<openparen>21<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode)<backslash><u8f6c><u6362><u540e>.gdb,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { GEODATABASE_FILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_GEODB<comma>NO<comma>DATASET_TEMPLATE<comma><comma>IMPORT_XML_TEMPLATE_GROUP<comma>NO<comma>TEMPLATEFILE<comma><lt>Unused<gt><comma>IMPORT_KIND<comma><lt>Unused<gt><comma>TRANSACTION_TYPE<comma>TRANSACTIONS<comma>FEATURE_DATASET_HANDLING<comma>WRITE<comma>SIMPLIFY_GEOM<comma>No<comma>HAS_Z_VALUES<comma>auto_detect<comma>X_ORIGIN<comma>0<comma>Y_ORIGIN<comma>0<comma>XY_SCALE<comma>0<comma>Z_ORIGIN<comma>0<comma>Z_SCALE<comma>0<comma>GRID_1<comma>0<comma>GEODB_SHARED_WRT_ADV_PARM_GROUP<comma><comma>REQUESTED_GEODATABASE_VERSION<comma>CURRENT<comma>DEFAULT_Z_VALUE<comma>0<comma>TRANSACTION<comma>0<comma>TRANSACTION_INTERVAL<comma>1000<comma>IGNORE_FAILED_FEATURE_ENTRY<comma>no<comma>MAX_NUMBER_FAILED_FEATURES<comma>-1<comma>DUMP_FAILED_FEATURES<comma>no<comma>FFS_DUMP_FILE<comma><comma>ANNOTATION_UNITS<comma>unknown_units<comma>HAS_MEASURES<comma>no<comma>COMPRESS_AT_END<comma>no<comma>ENABLE_FAST_DELETES<comma>yes<comma>PRESERVE_GLOBALID<comma>no<comma>ENABLE_LOAD_ONLY_MODE<comma>no<comma>VALIDATE_FEATURES<comma>no<comma>SIMPLIFY_NETWORK_FEATURES<comma>no<comma>BEGIN_SQL<opencurly>0<closecurly><comma><comma>END_SQL<opencurly>0<closecurly><comma><comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE_TYPE,METAFILE,GEODATABASE_FILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.<space>%-#<openbracket><closebracket><quote><openparen><closeparen>!?*<apos><amp>+<backslash><solidus><opencurly><closecurly>|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>char<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>char<openparen>2048<closeparen><comma>fme_buffer<comma>char<openparen>2048<closeparen><comma>fme_xml<comma>char<openparen>2048<closeparen><comma>fme_json<comma>blob<comma>fme_binarybuffer<comma>blob<comma>fme_varbinary<openparen>width<closeparen><comma>blob<comma>fme_binary<openparen>width<closeparen><comma>globalid<comma>fme_buffer<comma>guid<comma>fme_buffer<comma>date<comma>fme_datetime<comma>timestamp_offset<comma>fme_datetime<comma>date_only<comma>fme_date<comma>time_only<comma>fme_time<comma>integer<comma>fme_int32<comma>integer<comma>fme_uint16<comma>smallint<comma>fme_int16<comma>smallint<comma>fme_int8<comma>smallint<comma>fme_uint8<comma>bigint<comma>fme_int64<comma>float<comma>fme_real32<comma>double<comma>fme_real64<comma>double<comma>fme_uint32<comma>char<openparen>20<closeparen><comma>fme_uint64<comma>boolean<comma>fme_boolean<comma><quote>double<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma>subtype<openparen>stringset<closeparen><comma>fme_char<openparen>width<closeparen><comma>subtype_codes<openparen>stringmap<closeparen><comma>fme_char<openparen>width<closeparen><comma>range_domain<openparen>range_domain<closeparen><comma>fme_char<openparen>width<closeparen><comma>coded_domain<openparen>coded_domain<closeparen><comma>fme_char<openparen>width<closeparen>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,<backslash><backslash><quote>:?*<lt><gt>|<openbracket>%#<space><apos><amp>+-<closebracket>.^~<dollar><comma><closeparen><openparen>,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point<comma>fme_point<comma>geodb_polygon<comma>fme_polygon<comma>geodb_polygon<comma>fme_rounded_rectangle<comma>geodb_polyline<comma>fme_line<comma>geodb_multipoint<comma>fme_point<comma>geodb_table<comma>fme_no_geom<comma>geodb_table<comma>fme_collection<comma>geodb_arc<comma>fme_arc<comma>geodb_ellipse<comma>fme_ellipse<comma>geodb_polygon<comma>fme_rectangle<comma>geodb_annotation<comma>fme_text<comma>geodb_pro_annotation<comma>fme_text<comma>geodb_dimension<comma>fme_point<comma>geodb_simple_junction<comma>fme_point<comma>geodb_simple_edge<comma>fme_line<comma>geodb_complex_edge<comma>fme_line<comma>geodb_attributed_relationship<comma>fme_no_geom<comma>geodb_relationship<comma>fme_no_geom<comma>geodb_undefined<comma>fme_no_geom<comma>geodb_metadata<comma>fme_polygon<comma>geodb_multipatch<comma>fme_surface<comma>geodb_multipatch<comma>fme_solid<comma>geodb_polygon<comma>fme_raster<comma>geodb_polygon<comma>fme_point_cloud<comma>geodb_polygon<comma>fme_voxel_grid<comma>geodb_table<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>fme_configuration_group<space>fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group<space>Table<quote><comma><comma><quote>GUI<space>NAMEDGROUP<space>fme_configuration_common_group<space>fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group<space>General<quote><comma><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_feature_operation<space>Insert<comma>INSERT<comma>fme_update_geometry<comma>fme_selection_group<comma>mie_pack%Update<comma>UPDATE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert<comma>UPSERT<comma>fme_where_builder_clause<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Delete<comma>DELETE<comma>++fme_table_handling+USE_EXISTING<comma>fme_update_geometry<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<comma>fme_spatial_group<comma>fme_advanced_group<comma>oracle_sequenced_cols%<lt>at<gt>Value<lt>openparen<gt>fme_db_operation<lt>closeparen<gt><comma>MULTIPLE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<space>Feature<space>Operation<quote><comma>INSERT<comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_table_handling<space>Use<lt>space<gt>Existing<comma>USE_EXISTING<comma>fme_table_creation_group%Create<lt>space<gt>If<lt>space<gt>Needed<comma>CREATE_IF_MISSING%Drop<lt>space<gt>and<lt>space<gt>Create<comma>DROP_CREATE%Truncate<lt>space<gt>Existing<comma>TRUNCATE_EXISTING<comma>fme_table_creation_group<space>Table<space>Handling<quote><comma>CREATE_IF_MISSING<comma><quote>GUI<space>WHOLE_LINE<space>LOOKUP_CHOICE<space>fme_update_geometry<space>Yes<comma>YES%No<comma>NO<space>Update<space>Spatial<space>Column<openparen>s<closeparen><quote><comma>YES<comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_selection_group<space>fme_selection_method<space>Row<space>Selection<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>RADIOPARAMETERGROUP<space>fme_selection_method<space>fme_match_columns<comma>MATCH_COLUMNS%fme_where_builder_clause<comma>BUILDER<space>Row<space>Selection<space>Method<quote><comma>MATCH_COLUMNS<comma><quote>GUI<space>WHOLE_LINE<space>ATTRLIST_COMMAS<space>fme_match_columns<space>Match<space>Columns<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>TEXT_EDIT_SQL_CFG_OR_ATTR<space>fme_where_builder_clause<space>MODE<comma>WHERE<space>WHERE<space>Clause<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_table_creation_group<space>FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP<space>Table<space>Creation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ORIGIN_GROUP<space>GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP<space>Origin<space>and<space>Scale<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ANNO_GROUP<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ADVANCED_GROUP<space>GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID<opencurly>1<closecurly>%GEODB_AVG_NUM_POINTS<space>Advanced<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_XY_GROUP<space>GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE<space>X<solidus>Y<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_Z_GROUP<space>GEODB_ZORIGIN%GEODB_ZSCALE<space>Z<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_MEASURES_GROUP<space>GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE<space>Measures<quote><comma><comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_NAME<space>Object<space>ID<space>Field<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_ALIAS<space>Object<space>ID<space>Alias<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_NAME<space>Shape<space>Field<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_ALIAS<space>Shape<space>Alias<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_CONFIG_KEYWORD<space>Configuration<space>Keyword<quote><comma>DEFAULTS<comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_GRID<opencurly>1<closecurly><space>Grid<space>1<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>GEODB_AVG_NUM_POINTS<space>Average<space>Number<space>of<space>Points<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XORIGIN<space>X<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_YORIGIN<space>Y<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XYSCALE<space>X<solidus>Y<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_Z_VALUES<space>yes%no<space>Contains<space>Z<space>Values<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZORIGIN<space>Z<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZSCALE<space>Z<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_MEASURES<space>yes%no<space>Contains<space>Measures<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_ORIGIN<space>Measure<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_SCALE<space>Measure<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<space>Reference<space>Scale<quote><comma>,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>geodb_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>GEODB_UPDATE_KEY_COLUMNS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_DROP_TABLE<comma>NO<comma>GEODB_TRUNCATE_TABLE<comma>NO<comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_selection_method<comma>MATCH_COLUMNS<comma>fme_match_columns<comma><quote><quote><quote><quote><quote><quote><comma>fme_where_builder_clause<comma><quote><quote><quote><quote><quote><quote><comma>fme_update_geometry<comma>YES<comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_FEATURE_DATASET<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_GRID<opencurly>1<closecurly><comma><quote><quote><quote><quote><quote><quote><comma>GEODB_AVG_NUM_POINTS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_Z_VALUES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_MEASURES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_ANNO_REFERENCE_SCALE<comma><quote><quote><quote><quote><quote><quote>,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS<comma><quote>_GEODBOverwriteGEODB<space>_GEODBOutTransactionType<space>_GEODBOutSimplifyGeometry<space>_GEODBOutXOrigin<space>_GEODBOutYOrigin<space>_GEODBOutScale<space>_GEODBOutZOrigin<space>_GEODBOutZScale<space>_GEODBOutGrid1<space>_TRANSLATE_SPATIAL_DATA_ONLY<space>_GEODBInResolveDomains<space>_GEODBInResolveSubtypeNames<space>_GEODBInIgnoreNetworkInfo<space>_GEODBInIgnoreRelationshipInfo<space>_GEODBInSplitComplexEdges<space>_GEODBInWhereClause<space>NULL_IN_SPLIT_COMPLEX_ANNOS<space>NULL_IN_CACHE_MULTIPATCH_TEXTURES<space>NULL_IN_SEARCH_METHOD<space>NULL_IN_SEARCH_ORDER<space>NULL_IN_SEARCH_FEATURE<space>NULL_IN_CHECK_SIMPLE_GEOM<space>NULL_IN_MERGE_FEAT_LINKED_ANNOS<space>NULL_IN_READ_THREE_POINT_ARCS<space>NULL_IN_BEGIN_SQL<opencurly>0<closecurly><space>NULL_IN_END_SQL<opencurly>0<closecurly><space>NULL_IN_SIMPLE_DONUT_GEOMETRY<space>_GEODB_IN_ALIAS_MODE<space>GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION<space>GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE<space>GEODATABASE_FILE_OUT_WRITER_MODE<space>GEODATABASE_FILE_OUT_TRANSACTION<space>GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL<space>GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY<space>GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_FFS_DUMP_FILE<space>GEODATABASE_FILE_OUT_ANNOTATION_UNITS<space>GEODATABASE_FILE_OUT_HAS_MEASURES<space>GEODATABASE_FILE_OUT_MEASURES_ORIGIN<space>GEODATABASE_FILE_OUT_MEASURES_SCALE<space>GEODATABASE_FILE_OUT_COMPRESS_AT_END<space>GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES<space>GEODATABASE_FILE_OUT_PRESERVE_GLOBALID<space>GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE<space>GEODATABASE_FILE_OUT_VALIDATE_FEATURES<space>GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES<space>GEODATABASE_FILE_OUT_BEGIN_SQL<opencurly>0<closecurly><space>GEODATABASE_FILE_OUT_END_SQL<opencurly>0<closecurly><quote><comma>PARAMS_TO_NOT_PROPAGATE_ON_INSPECT<comma><quote>BEGIN_SQL<opencurly>0<closecurly><space>END_SQL<opencurly>0<closecurly><quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>ATTRIBUTE_READING_HISTORIC<comma>ALL<comma>FEATURE_TYPE_NAME<comma><quote>Feature<space>Class<space>or<space>Table<quote><comma>FEATURE_TYPE_DEFAULT_NAME<comma>FeatureClass1<comma>SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME<comma>NO<comma>READER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>SQL_EXECUTE_DIRECTIVES<comma>INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<u70b9>:Output,ftp_feature_type_name,<u70b9>,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_point,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u5e8f<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u662f<gt><lt>u5426<gt><lt>u8be5<gt><lt>u9879<gt><lt>u76ee<gt><lt>u5de5<gt><lt>u7a0b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u6240<gt><lt>u5c5e<gt><lt>u9879<gt><lt>u76ee<gt><lt>u540d<gt><lt>u79f0<gt><comma>char<lt>openparen<gt>128<lt>closeparen<gt><comma><lt>u9879<gt><lt>u76ee<gt><lt>u7f16<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u533a<gt><lt>u53bf<gt><comma>char<lt>openparen<gt>10<lt>closeparen<gt><comma><lt>u4e61<gt><lt>u9547<gt><comma>char<lt>openparen<gt>10<lt>closeparen<gt><comma><lt>u8c03<gt><lt>u67e5<gt><lt>u7f16<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u539f<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><comma>char<lt>openparen<gt>128<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u5b8c<gt><lt>u5de5<gt><lt>u65f6<gt><lt>u95f4<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u5927<gt><lt>u7c7b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u7c7b<gt><lt>u522b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u7c7b<gt><lt>u578b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u957f<gt><lt>u5ea6<gt><lt>uff08<gt><lt>u7c73<gt><lt>uff09<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u5bbd<gt><lt>u5ea6<gt><lt>uff08<gt><lt>u7c73<gt><lt>uff09<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u5e73<gt><lt>u65b9<gt><lt>u7c73<gt><lt>uff09<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u5f84<gt><lt>u5927<gt><lt>u5c0f<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5750<gt><lt>u843d<gt><lt>u5355<gt><lt>u4f4d<gt><comma>char<lt>openparen<gt>100<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u6743<gt><lt>u5c5e<gt><lt>uff08<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u4e3b<gt><lt>u4f53<gt><lt>uff09<gt><comma>char<lt>openparen<gt>100<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u62a4<gt><lt>u5b9e<gt><lt>u65bd<gt><lt>u4e3b<gt><lt>u4f53<gt><comma>char<lt>openparen<gt>100<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u62a4<gt><lt>u8d23<gt><lt>u4efb<gt><lt>u4eba<gt><comma>char<lt>openparen<gt>15<lt>closeparen<gt><comma><lt>u662f<gt><lt>u5426<gt><lt>u7b7e<gt><lt>u8ba2<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u534f<gt><lt>u8bae<gt><comma>char<lt>openparen<gt>23<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u8fd0<gt><lt>u884c<gt><lt>u72b6<gt><lt>u6001<gt><comma>char<lt>openparen<gt>24<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u4f7f<gt><lt>u7528<gt><lt>u72b6<gt><lt>u6001<gt><comma>char<lt>openparen<gt>24<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u4f4d<gt><lt>u7f6e<gt><lt>uff08<gt>wkt<lt>uff09<gt><comma>char<lt>openparen<gt>1280<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>GEODB_ORIGIN_GROUP<comma><comma>GEODB_ANNO_GROUP<comma><comma>GEODB_ADVANCED_GROUP<comma><comma>GEODB_XY_GROUP<comma><comma>GEODB_Z_GROUP<comma><comma>GEODB_MEASURES_GROUP<comma><comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_GRID<opencurly>1<closecurly><comma><comma>GEODB_AVG_NUM_POINTS<comma><comma>GEODB_XORIGIN<comma><comma>GEODB_YORIGIN<comma><comma>GEODB_XYSCALE<comma><comma>GEODB_HAS_Z_VALUES<comma><comma>GEODB_ZORIGIN<comma><comma>GEODB_ZSCALE<comma><comma>GEODB_HAS_MEASURES<comma><comma>GEODB_MEASURES_ORIGIN<comma><comma>GEODB_MEASURES_SCALE<comma><comma>GEODB_ANNO_REFERENCE_SCALE<comma>;<u7ebf>:Output00,ftp_feature_type_name,<u7ebf>,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_polyline,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u5e8f<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u662f<gt><lt>u5426<gt><lt>u8be5<gt><lt>u9879<gt><lt>u76ee<gt><lt>u5de5<gt><lt>u7a0b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u6240<gt><lt>u5c5e<gt><lt>u9879<gt><lt>u76ee<gt><lt>u540d<gt><lt>u79f0<gt><comma>char<lt>openparen<gt>128<lt>closeparen<gt><comma><lt>u9879<gt><lt>u76ee<gt><lt>u7f16<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u533a<gt><lt>u53bf<gt><comma>char<lt>openparen<gt>10<lt>closeparen<gt><comma><lt>u4e61<gt><lt>u9547<gt><comma>char<lt>openparen<gt>10<lt>closeparen<gt><comma><lt>u8c03<gt><lt>u67e5<gt><lt>u7f16<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u539f<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><comma>char<lt>openparen<gt>128<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u5b8c<gt><lt>u5de5<gt><lt>u65f6<gt><lt>u95f4<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u5927<gt><lt>u7c7b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u7c7b<gt><lt>u522b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u7c7b<gt><lt>u578b<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u957f<gt><lt>u5ea6<gt><lt>uff08<gt><lt>u7c73<gt><lt>uff09<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u5bbd<gt><lt>u5ea6<gt><lt>uff08<gt><lt>u7c73<gt><lt>uff09<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u5e73<gt><lt>u65b9<gt><lt>u7c73<gt><lt>uff09<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u5f84<gt><lt>u5927<gt><lt>u5c0f<gt><comma>char<lt>openparen<gt>20<lt>closeparen<gt><comma><lt>u5750<gt><lt>u843d<gt><lt>u5355<gt><lt>u4f4d<gt><comma>char<lt>openparen<gt>100<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u6743<gt><lt>u5c5e<gt><lt>uff08<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u4e3b<gt><lt>u4f53<gt><lt>uff09<gt><comma>char<lt>openparen<gt>100<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u62a4<gt><lt>u5b9e<gt><lt>u65bd<gt><lt>u4e3b<gt><lt>u4f53<gt><comma>char<lt>openparen<gt>100<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u62a4<gt><lt>u8d23<gt><lt>u4efb<gt><lt>u4eba<gt><comma>char<lt>openparen<gt>15<lt>closeparen<gt><comma><lt>u662f<gt><lt>u5426<gt><lt>u7b7e<gt><lt>u8ba2<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u534f<gt><lt>u8bae<gt><comma>char<lt>openparen<gt>23<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u8fd0<gt><lt>u884c<gt><lt>u72b6<gt><lt>u6001<gt><comma>char<lt>openparen<gt>24<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u4f7f<gt><lt>u7528<gt><lt>u72b6<gt><lt>u6001<gt><comma>char<lt>openparen<gt>24<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u4f4d<gt><lt>u7f6e<gt><lt>uff08<gt>wkt<lt>uff09<gt><comma>char<lt>openparen<gt>1280<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>GEODB_ORIGIN_GROUP<comma><comma>GEODB_ANNO_GROUP<comma><comma>GEODB_ADVANCED_GROUP<comma><comma>GEODB_XY_GROUP<comma><comma>GEODB_Z_GROUP<comma><comma>GEODB_MEASURES_GROUP<comma><comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_GRID<opencurly>1<closecurly><comma><comma>GEODB_AVG_NUM_POINTS<comma><comma>GEODB_XORIGIN<comma><comma>GEODB_YORIGIN<comma><comma>GEODB_XYSCALE<comma><comma>GEODB_HAS_Z_VALUES<comma><comma>GEODB_ZORIGIN<comma><comma>GEODB_ZSCALE<comma><comma>GEODB_HAS_MEASURES<comma><comma>GEODB_MEASURES_ORIGIN<comma><comma>GEODB_MEASURES_SCALE<comma><comma>GEODB_ANNO_REFERENCE_SCALE<comma>" }    WRITER_PARAMS { "ANNOTATION_UNITS,unknown_units,BEGIN_SQL{0},,COMPRESS_AT_END,no,COORDINATE_SYSTEM_GRANULARITY,FEATURE_TYPE,DEFAULT_Z_VALUE,0,DESTINATION_DATASETTYPE_VALIDATION,Yes,DUMP_FAILED_FEATURES,no,ENABLE_FAST_DELETES,yes,ENABLE_LOAD_ONLY_MODE,no,END_SQL{0},,FEATURE_DATASET_HANDLING,WRITE,FFS_DUMP_FILE,,GEODB_SHARED_WRT_ADV_PARM_GROUP,,GRID_1,0,HAS_MEASURES,no,HAS_Z_VALUES,auto_detect,IGNORE_FAILED_FEATURE_ENTRY,no,IMPORT_XML_TEMPLATE_GROUP,NO,MAX_NUMBER_FAILED_FEATURES,-1,OVERWRITE_GEODB,NO,PRESERVE_GLOBALID,no,REQUESTED_GEODATABASE_VERSION,CURRENT,SIMPLIFY_GEOM,No,SIMPLIFY_NETWORK_FEATURES,no,TRANSACTION,0,TRANSACTION_INTERVAL,1000,TRANSACTION_TYPE,TRANSACTIONS,VALIDATE_FEATURES,no,X_ORIGIN,0,XY_SCALE,0,Y_ORIGIN,0,Z_ORIGIN,0,Z_SCALE,0" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE AttributeCreator_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u70b9>,FeatureWriter))    INPUT Output00 FEATURE_TYPE AttributeCreator_2_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u7ebf>,FeatureWriter))
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode)<backslash><u8f6c><u6362><u9519><u8bef>.xlsx,FeatureWriter_2)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter_2 }    WRITER_TYPE { XLSXW }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter_2)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_FILE<comma>No<comma>TEMPLATEFILE<comma><comma>TEMPLATE_SHEET<comma><comma>REMOVE_UNCHANGED_TEMPLATE_SHEET<comma>No<comma>MULTIPLE_TEMPLATE_SHEETS<comma>Yes<comma>INSERT_IGNORE_DB_OP<comma>Yes<comma>DROP_TABLE<comma>No<comma>TRUNCATE_TABLE<comma>No<comma>FIELD_NAMES_OUT<comma>Yes<comma>FIELD_NAMES_FORMATTING<comma>Yes<comma>WRITER_MODE<comma>Insert<comma>RASTER_FORMAT<comma>PNG<comma>PROTECT_SHEET<comma>NO<comma>PROTECT_SHEET_PASSWORD<comma><lt>Unused<gt><comma>PROTECT_SHEET_LEVEL<comma><lt>Unused<gt><comma>PROTECT_SHEET_PERMISSIONS<comma><lt>Unused<gt><comma>STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING<comma>yes<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE<comma>CUSTOM_NUMBER_FORMATTING<comma>ENABLE_NATIVE<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,XLSXW" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,<quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varchar<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varbinary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_char<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binarybuffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_xml<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_json<comma><quote>auto<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>datetime<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_datetime<comma><quote>time<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_time<comma><quote>date<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_date<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int8<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint8<comma><quote>boolean<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<openbracket><closebracket>*<backslash><backslash>?:<apos>,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none<comma>fme_no_geom<comma>xlsx_none<comma>fme_point<comma>xlsx_point<comma>fme_point<comma>xlsx_none<comma>fme_line<comma>xlsx_none<comma>fme_polygon<comma>xlsx_none<comma>fme_text<comma>xlsx_none<comma>fme_ellipse<comma>xlsx_none<comma>fme_arc<comma>xlsx_none<comma>fme_rectangle<comma>xlsx_none<comma>fme_rounded_rectangle<comma>xlsx_none<comma>fme_collection<comma>xlsx_none<comma>fme_surface<comma>xlsx_none<comma>fme_solid<comma>xlsx_none<comma>fme_raster<comma>xlsx_none<comma>fme_point_cloud<comma>xlsx_none<comma>fme_voxel_grid<comma>xlsx_none<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>xlsx_layer_group<space>xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group<space>Sheet<space>Settings<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_truncate_group<space>xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet<space>Drop<solidus>Truncate<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_rowcolumn_group<space>xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row<space>Start<space>Position<quote><comma><comma><quote>GUI<space>ACTIVEDISCLOSUREGROUP<space>xlsx_protect_sheet<space>xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions<space>Protect<space>Sheet<quote><comma>NO<comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_template_group<space>xlsx_template_sheet%xlsx_remove_unchanged_template_sheet<space>Template<space>Options<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_advanced_group<space>xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type<space>Advanced<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_drop_sheet<space>Yes%No<space>Drop<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>CHOICE<space>xlsx_trunc_sheet<space>Yes%No<space>Truncate<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_sheet_order<space>1%MAX<space>Sheet<space>Order<space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_freeze_end_row<space>1%MAX<space>Freeze<space>First<space>Row<openparen>s<closeparen><space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_field_names_out<space>Yes%No<comma>xlsx_field_names_formatting<comma>++xlsx_field_names_formatting+No<space>Output<space>Field<space>Names:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_field_names_formatting<space>Yes%No<space>Format<space>Field<space>Names<space>Row:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_names_are_positions<space>Yes%No<space>Use<space>Attribute<space>Names<space>As<space>Column<space>Positions:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_start_col<space>Named<space>Range<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_start_row<space>Named<space>Range<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_offset_col<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_offset_row<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_raster_type<space>BMP%JPEG%PNG<space>Raster<space>Format:<quote><comma>PNG<comma><quote>GUI<space>OPTIONAL<space>PASSWORD_ENCODED<space>xlsx_protect_sheet_password<space>Password:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>xlsx_protect_sheet_level<space>Select<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_DEFAULT<comma>xlsx_protect_sheet_permissions%View<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_ALL<comma>xlsx_protect_sheet_permissions%Specific<lt>space<gt>Permissions<space>Protection<space>Level:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>OPTIONAL<space>LOOKUP_LISTBOX<space>xlsx_protect_sheet_permissions<space>Select<lt>space<gt>locked<lt>space<gt>cells<comma>PROT_SEL_LOCKED_CELLS%Select<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_SEL_UNLOCKED_CELLS%Format<lt>space<gt>cells<comma>PROT_FORMAT_CELLS%Format<lt>space<gt>columns<comma>PROT_FORMAT_COLUMNS%Format<lt>space<gt>rows<comma>PROT_FORMAT_ROWS%Insert<lt>space<gt>columns<comma>PROT_INSERT_COLUMNS%Insert<lt>space<gt>rows<comma>PROT_INSERT_ROWS%Add<lt>space<gt>hyperlinks<lt>space<gt>to<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_INSERT_HYPERLINKS%Delete<lt>space<gt>unlocked<lt>space<gt>columns<comma>PROT_DELETE_COLUMNS%Delete<lt>space<gt>unlocked<lt>space<gt>rows<comma>PROT_DELETE_ROWS%Sort<lt>space<gt>unlocked<lt>space<gt>cells<solidus>rows<solidus>columns<comma>PROT_SORT%Use<lt>space<gt>Autofilter<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_AUTOFILTER%Use<lt>space<gt>PivotTable<lt>space<gt><amp><lt>space<gt>PivotChart<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_PIVOTTABLES%Edit<lt>space<gt>unlocked<lt>space<gt>objects<comma>PROT_OBJECTS%Edit<lt>space<gt>unprotected<lt>space<gt>scenarios<comma>PROT_SCENARIOS<space>Specific<space>Permissions:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_table_writer_mode<space>Insert<comma>+xlsx_row_id_column+<quote><quote><quote><quote>%Update<comma>+xlsx_row_id_column+xlsx_row_id%Delete<comma>+xlsx_row_id_column+xlsx_row_id<space>Writer<space>Mode:<quote><comma>Insert<comma><quote>GUI<space>OPTIONAL<space>ATTR<space>xlsx_row_id_column<space>ALLOW_NEW<space>Row<space>Number<space>Attribute:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT_EDIT<space>xlsx_template_sheet<space>Template<space>Sheet:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_remove_unchanged_template_sheet<space>Yes%No<space>Remove<space>Template<space>Sheet<space>if<space>Unchanged:<quote><comma>No,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_freeze_end_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_names_are_positions<comma>No<comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_start_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_start_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_raster_type<comma>PNG<comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_protect_sheet<comma><quote><quote><quote>NO<quote><quote><quote><comma>xlsx_protect_sheet_level<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_password<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_permissions<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_template_sheet<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_remove_unchanged_template_sheet<comma><quote><quote><quote>No<quote><quote><quote>,WRITER_FORMAT_PARAMETER,DEFAULT_READER<comma>XLSXR<comma>ALLOW_DATASET_CONFLICT<comma>YES<comma>MIME_TYPE<comma><quote>application<solidus>vnd.openxmlformats-officedocument.spreadsheetml.sheet<space>ADD_DISPOSITION<quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>DEFAULT_ATTR_TYPE<comma>auto<comma>USER_ATTRIBUTES_COLUMNS<comma><quote>Width<comma>Cell<space>Width%Precision<comma>Formatting<quote><comma>FEATURE_TYPE_NAME<comma>Sheet<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Sheet1<comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>name<space>for<space>the<space>Microsoft<space>Excel<space>file<quote>,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "point_Rejected:<lt>Rejected<gt>,ftp_feature_type_name,point_Rejected,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>14<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><comma>string<lt>openparen<gt>4<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u5f84<gt><comma>string<lt>openparen<gt>12<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>B<comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>L<comma>string<lt>openparen<gt>17<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>fme_rejection_code<comma>string<lt>openparen<gt>17<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma><comma><comma>,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No;line_Rejected:<lt>Rejected<gt>00,ftp_feature_type_name,line_Rejected,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>9<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><comma>string<lt>openparen<gt>4<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u957f<gt><lt>u5ea6<gt><comma>number<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>B1<comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>L1<comma>string<lt>openparen<gt>17<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>B2<comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>L2<comma>string<lt>openparen<gt>17<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma>fme_rejection_code<comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No" }    WRITER_PARAMS { "COORDINATE_SYSTEM_GRANULARITY,FEATURE,CUSTOM_NUMBER_FORMATTING,ENABLE_NATIVE,DESTINATION_DATASETTYPE_VALIDATION,Yes,DROP_TABLE,No,FIELD_NAMES_FORMATTING,Yes,FIELD_NAMES_OUT,Yes,INSERT_IGNORE_DB_OP,Yes,MULTIPLE_TEMPLATE_SHEETS,Yes,NETWORK_AUTHENTICATION,,OVERWRITE_FILE,No,PROTECT_SHEET,NO,RASTER_FORMAT,PNG,STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,yes,TRUNCATE_TABLE,No,WRITER_MODE,Insert" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT <lt>Rejected<gt> FEATURE_TYPE VertexCreator_<REJECTED>  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,point_Rejected,FeatureWriter_2))    INPUT <lt>Rejected<gt>00 FEATURE_TYPE VertexCreator_3_<REJECTED>  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,line_Rejected,FeatureWriter_2))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

