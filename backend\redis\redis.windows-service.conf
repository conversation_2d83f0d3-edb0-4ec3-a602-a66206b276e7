# Redis配置文件

# 基本配置
port 6379
bind 127.0.0.1
timeout 300
tcp-keepalive 60

# 内存管理
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
appendonly no
appendfilename "appendonly.aof"

# 日志配置
loglevel notice
logfile "redis.log"

# 数据库配置
databases 16

# 安全配置
requirepass "geostream123"

# 性能配置
maxclients 10000
maxmemory-samples 5

# 连接配置
tcp-backlog 511
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit slave 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Windows特定配置
pidfile "redis.pid"
dir "./"
daemonize no 