import{d as V,r as k,M as q,ar as S,as as T,W as j,o as B,c as r,e as m,f as h,w as i,at as F,b as e,F as u,H as $,I as w,A as b,n as D,C as E,ac as G,l,_ as M,a5 as H,s as L,k as W}from"./index-CdoJMuEX.js";const J={class:"performance-panel"},K={class:"card-header"},Q={class:"performance-content"},U={class:"section"},X={class:"metrics"},Y={class:"metric-item"},Z={class:"value"},ee={class:"route-metrics"},te={class:"route"},se={class:"time"},ae={class:"section"},ne={class:"preload-status"},oe={class:"route"},le={class:"section"},ie={class:"suggestions"},ce=V({__name:"NavigationPerformancePanel",setup(z){const g=k({}),R={"/":"首页","/tools":"工具运行","/market":"工具市场","/quality":"数据质检","/coordinate":"坐标转换","/cad2gis":"CAD转GIS","/requirement":"需求提交","/layer-preview":"图层预览","/user-management":"用户管理","/settings":"设置","/about":"关于"},_=a=>R[a]||a,C=q(()=>{const a=g.value.navigation||{},t={};return Object.keys(a).forEach(d=>{d!=="average"&&(t[d]=a[d])}),t}),y=q(()=>{var p;const a=[],t=parseFloat(((p=g.value.navigation)==null?void 0:p.average)||"0");t>500?a.push({id:"slow-navigation",text:"导航速度较慢，建议检查网络连接或组件大小",type:"warning",icon:S}):t>0&&a.push({id:"good-navigation",text:"导航性能良好",type:"success",icon:T});const d=g.value.preload||{},f=Object.values(d).filter(Boolean).length,v=Object.keys(d).length;return f<v/2?a.push({id:"low-preload",text:"建议预加载更多常用组件以提升用户体验",type:"info",icon:j}):a.push({id:"good-preload",text:"组件预加载状态良好",type:"success",icon:T}),a}),N=()=>{g.value=F.getPerformanceReport()};return B(()=>{N(),setInterval(N,5e3)}),(a,t)=>{const d=h("el-button"),f=h("el-tag"),v=h("el-icon"),p=h("el-card");return l(),r("div",J,[m(p,{class:"performance-card"},{header:i(()=>[e("div",K,[t[1]||(t[1]=e("span",null,"导航性能监控",-1)),m(d,{type:"primary",size:"small",onClick:N},{default:i(()=>t[0]||(t[0]=[b("刷新")])),_:1})])]),default:i(()=>{var x;return[e("div",Q,[e("div",U,[t[3]||(t[3]=e("h4",null,"导航性能",-1)),e("div",X,[e("div",Y,[t[2]||(t[2]=e("span",{class:"label"},"平均导航时间:",-1)),e("span",Z,u(((x=g.value.navigation)==null?void 0:x.average)||"0ms"),1)])]),e("div",ee,[(l(!0),r($,null,w(C.value,(c,n)=>(l(),r("div",{key:n,class:"route-metric"},[e("span",te,u(_(n))+":",1),e("span",se,u(c),1)]))),128))])]),e("div",ae,[t[4]||(t[4]=e("h4",null,"组件预加载状态",-1)),e("div",ne,[(l(!0),r($,null,w(g.value.preload,(c,n)=>(l(),r("div",{key:n,class:"preload-item"},[e("span",oe,u(_(n))+":",1),m(f,{type:c?"success":"info",size:"small"},{default:i(()=>[b(u(c?"已预加载":"未预加载"),1)]),_:2},1032,["type"])]))),128))])]),e("div",le,[t[5]||(t[5]=e("h4",null,"优化建议",-1)),e("div",ie,[(l(!0),r($,null,w(y.value,c=>(l(),r("div",{key:c.id,class:"suggestion-item"},[m(v,{class:D(c.type)},{default:i(()=>[(l(),E(G(c.icon)))]),_:2},1032,["class"]),e("span",null,u(c.text),1)]))),128))])])])]}),_:1})])}}}),re=M(ce,[["__scopeId","data-v-34fbe3f9"]]),ue={class:"navigation-test"},de={class:"test-content"},ve={class:"quick-nav"},pe={class:"nav-status"},me={class:"status-item"},_e={class:"status-item"},ge={class:"animation-demo"},fe={class:"demo-nav"},he=["onClick"],ye={class:"demo-icon"},ke={class:"demo-text"},Ce={key:0,class:"demo-loading"},Ne=V({__name:"NavigationTestView",setup(z){const g=H(),R=W(),_=k(!1),C=k(""),y=q(()=>g.path),N=[{path:"/",name:"首页"},{path:"/tools",name:"工具运行"},{path:"/market",name:"工具市场"},{path:"/quality",name:"数据质检"},{path:"/coordinate",name:"坐标转换"}],a={"/":"首页","/tools":"工具运行","/market":"工具市场","/quality":"数据质检","/coordinate":"坐标转换","/cad2gis":"CAD转GIS","/requirement":"需求提交","/layer-preview":"图层预览","/user-management":"用户管理","/settings":"设置","/about":"关于","/navigation-test":"导航测试"},t=()=>a[y.value]||y.value,d=async n=>{if(!(_.value||y.value===n)){_.value=!0,C.value=n;try{await R.push(n)}catch(s){console.error("导航失败:",s)}finally{setTimeout(()=>{_.value=!1,C.value=""},300)}}},f=k("home"),v=k(!1),p=k(""),x=[{id:"home",name:"首页",icon:"🏠"},{id:"tools",name:"工具",icon:"🔧"},{id:"market",name:"市场",icon:"🛒"},{id:"quality",name:"质检",icon:"✅"},{id:"coordinate",name:"坐标",icon:"📍"}],c=async n=>{v.value||f.value===n||(v.value=!0,p.value=n,await new Promise(s=>setTimeout(s,800)),f.value=n,v.value=!1,p.value="")};return B(()=>{console.log("导航测试页面已加载")}),(n,s)=>{const A=h("el-button"),I=h("el-tag"),P=h("el-card");return l(),r("div",ue,[s[5]||(s[5]=e("div",{class:"test-header"},[e("h2",null,"导航性能测试"),e("p",null,"测试动画优先的导航切换效果")],-1)),e("div",de,[m(P,{class:"test-section"},{header:i(()=>s[0]||(s[0]=[e("span",null,"快速导航测试",-1)])),default:i(()=>[e("div",ve,[(l(),r($,null,w(N,o=>m(A,{key:o.path,type:y.value===o.path?"primary":"default",onClick:O=>d(o.path),loading:_.value&&C.value===o.path},{default:i(()=>[b(u(o.name),1)]),_:2},1032,["type","onClick","loading"])),64))]),e("div",pe,[e("div",me,[s[1]||(s[1]=e("span",null,"当前页面:",-1)),m(I,null,{default:i(()=>[b(u(t()),1)]),_:1})]),e("div",_e,[s[2]||(s[2]=e("span",null,"导航状态:",-1)),m(I,{type:_.value?"warning":"success"},{default:i(()=>[b(u(_.value?"导航中...":"就绪"),1)]),_:1},8,["type"])])])]),_:1}),m(re),m(P,{class:"test-section"},{header:i(()=>s[3]||(s[3]=[e("span",null,"动画效果演示",-1)])),default:i(()=>[e("div",ge,[e("div",fe,[(l(),r($,null,w(x,o=>e("div",{key:o.id,class:D(["demo-nav-item",f.value===o.id?"active":"",v.value&&p.value===o.id?"navigating":"",v.value&&p.value!==o.id?"dimmed":""]),onClick:O=>c(o.id)},[e("span",ye,u(o.icon),1),e("span",ke,u(o.name),1),v.value&&p.value===o.id?(l(),r("div",Ce)):L("",!0)],10,he)),64))]),s[4]||(s[4]=e("div",{class:"demo-description"},[e("p",null,"这个演示展示了动画优先的导航体验："),e("ul",null,[e("li",null,"点击时立即显示视觉反馈"),e("li",null,"导航过程中显示加载状态"),e("li",null,"其他项目变暗以突出当前操作"),e("li",null,"流畅的过渡动画")])],-1))])]),_:1})])])}}}),we=M(Ne,[["__scopeId","data-v-7df1931c"]]);export{we as default};
