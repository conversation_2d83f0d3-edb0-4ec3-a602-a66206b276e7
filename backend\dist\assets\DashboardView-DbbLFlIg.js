import{d as w,r as d,o as C,a as y,c as B,e,w as o,f as n,b as s,F as i,A as f,m as k,U as x,l as V,_ as G}from"./index-B7WNRWO3.js";const I={class:"dashboard"},D={class:"card-header"},N={class:"stats-grid"},T={class:"stat-item"},U={class:"stat-value"},q={class:"number"},S={class:"stat-item"},z={class:"stat-value"},A={class:"number"},E={class:"stat-item"},F={class:"stat-value"},M={class:"number"},P={class:"card-header"},j={class:"server-stats"},H={class:"server-stat-item"},J={class:"server-stat-item"},K={class:"server-stat-item"},L=w({__name:"DashboardView",setup(O){const c=d("--"),u=d("--"),_=d("--"),p=async()=>{try{const a=await api.get("/api/tools/count");a.data.success&&(c.value=a.data.count)}catch(a){console.error("获取工具总数失败:",a)}},v=async()=>{try{const a=await api.get("/api/task/status/count");a.data.success&&(u.value=a.data.data.queue_count,_.value=a.data.data.running_count)}catch(a){console.error("获取任务状态数量失败:",a)}};let l;return C(()=>{p(),v(),l=window.setInterval(()=>{p(),v()},5e3)}),y(()=>{l&&clearInterval(l)}),(a,t)=>{const g=n("el-icon"),h=n("el-button"),m=n("el-card"),b=n("el-tag"),r=n("el-progress");return V(),B("div",I,[e(m,{class:"dashboard-card"},{header:o(()=>[s("div",D,[t[1]||(t[1]=s("span",null,"工具情况",-1)),e(h,{type:"primary",link:""},{default:o(()=>[t[0]||(t[0]=f(" 查看详情 ")),e(g,{class:"el-icon--right"},{default:o(()=>[e(k(x))]),_:1})]),_:1})])]),default:o(()=>[s("div",N,[s("div",T,[t[3]||(t[3]=s("div",{class:"stat-title"},"工具总数",-1)),s("div",U,[s("span",q,i(c.value),1),t[2]||(t[2]=s("span",{class:"unit"},"个",-1))])]),s("div",S,[t[5]||(t[5]=s("div",{class:"stat-title"},"当前队列数量",-1)),s("div",z,[s("span",A,i(u.value),1),t[4]||(t[4]=s("span",{class:"unit"},"个",-1))])]),s("div",E,[t[7]||(t[7]=s("div",{class:"stat-title"},"当前运行数量",-1)),s("div",F,[s("span",M,i(_.value),1),t[6]||(t[6]=s("span",{class:"unit"},"个",-1))])])])]),_:1}),e(m,{class:"dashboard-card"},{header:o(()=>[s("div",P,[t[9]||(t[9]=s("span",null,"服务器信息",-1)),e(b,{size:"small",type:"success",effect:"light"},{default:o(()=>t[8]||(t[8]=[f("正常运行")])),_:1})])]),default:o(()=>[s("div",j,[s("div",H,[t[10]||(t[10]=s("div",{class:"stat-header"},[s("span",{class:"stat-title"},"CPU使用率"),s("span",{class:"stat-value"},"--%")],-1)),e(r,{percentage:0,format:()=>""})]),s("div",J,[t[11]||(t[11]=s("div",{class:"stat-header"},[s("span",{class:"stat-title"},"内存使用情况"),s("span",{class:"stat-value"},"--GB / --GB")],-1)),e(r,{percentage:0,format:()=>""})]),s("div",K,[t[12]||(t[12]=s("div",{class:"stat-header"},[s("span",{class:"stat-title"},"磁盘使用情况"),s("span",{class:"stat-value"},"--GB / --GB")],-1)),e(r,{percentage:0,format:()=>""})])])]),_:1})])}}}),R=G(L,[["__scopeId","data-v-4b5d6866"]]);export{R as default};
