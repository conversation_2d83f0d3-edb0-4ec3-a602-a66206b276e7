#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2025\0115\19年后项目区擦除19年前项目区并统计.fmw
#          --dir "$(FME_MF_DIR)19年后擦19年前\张家港市\2019年之前"
#          --dir_2 "$(FME_MF_DIR)19年后擦19年前\张家港市\2019年之后"
#          --PARAMETER_2 "C:\Users\<USER>\Desktop\新建文件夹 (22)"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="11043.7 1372.61"
#!   DOC_TOP_LEFT="-2210.25 -1624.44"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="1da7b139-6f5d-4a70-a49d-121c8fe80edf"
#!   FME_DOCUMENT_PRIORGUID="d5e49116-1f17-4029-8f6f-cd9f1bb9e6a9,9a7fc706-0caa-4b03-9bac-c732fcaba5fc"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-01-15T14:51:04"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="4681.23 -593.44"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="99"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_3"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_3"
#!   DATASET="@Value(path_windows)"
#! />
#! <DATASET
#!   NAME="FeatureReader_4"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_4"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="@Value(bclj)"
#! />
#! <DATASET
#!   NAME="FeatureWriter_2"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter_2"
#!   DATASET="FeatureWriter_2/&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u7edf&gt;&lt;u8ba1&gt;.xlsx"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 19年前文件夹"
#!   DEFAULT_VALUE="$(FME_MF_DIR)19年后擦19年前\张家港市\2019年之前"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 19年后文件夹"
#!   DEFAULT_VALUE="$(FME_MF_DIR)19年后擦19年前\张家港市\2019年之后"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (22)"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJhY2Nlc3NNb2RlIjoicmVhZCIsImRlZmF1bHRWYWx1ZSI6IiQoRk1FX01GX0RJUikxOeW5tOWQjuaTpjE55bm05YmNXFzlvKDlrrbmuK/luIJcXDIwMTnlubTkuYvliY0iLCJpbmNsdWRlV2ViQnJvd3NlciI6dHJ1ZSwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiZGlyIiwicHJvbXB0IjoiMTnlubTliY3mlofku7blpLkiLCJyZXF1aXJlZCI6dHJ1ZSwic2VsZWN0TXVsdGlwbGUiOnRydWUsInNob3dQcm9tcHQiOnRydWUsInN1cHBvcnRlZFZhbHVlVHlwZXMiOlsiZXhwcmVzc2lvbiIsImdsb2JhbFBhcmFtZXRlciJdLCJ0eXBlIjoiZmlsZSIsInZhbGlkYXRlRXhpc3RlbmNlIjpmYWxzZSwidmFsdWVUeXBlIjoic3RyaW5nIn0seyJhY2Nlc3NNb2RlIjoicmVhZCIsImRlZmF1bHRWYWx1ZSI6IiQoRk1FX01GX0RJUikxOeW5tOWQjuaTpjE55bm05YmNXFzlvKDlrrbmuK/luIJcXDIwMTnlubTkuYvlkI4iLCJpbmNsdWRlV2ViQnJvd3NlciI6dHJ1ZSwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiZGlyXzIiLCJwcm9tcHQiOiIxOeW5tOWQjuaWh+S7tuWkuSIsInJlcXVpcmVkIjp0cnVlLCJzZWxlY3RNdWx0aXBsZSI6dHJ1ZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOmZhbHNlLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifSx7ImFjY2Vzc01vZGUiOiJ3cml0ZSIsImFsbG93VVJMIjpmYWxzZSwiZGVmYXVsdFZhbHVlIjoiQzpcXFVzZXJzXFxkanpcXERlc2t0b3BcXOaWsOW7uuaWh+S7tuWkuSAoMjIpIiwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiUEFSQU1FVEVSXzIiLCJwcm9tcHQiOiLkv53lrZjot6/lvoQiLCJyZXF1aXJlZCI6dHJ1ZSwic2VsZWN0TXVsdGlwbGUiOmZhbHNlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6dHJ1ZSwidmFsdWVUeXBlIjoic3RyaW5nIn1dfQ=="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)19年后擦19年前\张家港市\2019年之前"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 19年前文件夹"
#! />
#!     <INFO NAME="dir_2" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)19年后擦19年前\张家港市\2019年之后"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 19年后文件夹"
#! />
#!     <INFO NAME="PARAMETER_2" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (22)"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="937.50937509375103 -784.38284382843813"
#!   BOUNDING_RECT="937.50937509375103 -784.38284382843813 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1462.5146251462518 -671.88171881718802"
#!   BOUNDING_RECT="1462.5146251462518 -671.88171881718802 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-2210.2493752210239 -1553.4361331662769"
#!   BOUNDING_RECT="-2210.2493752210239 -1553.4361331662769 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="6"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-1685.2441251685232 -1447.1850706556522"
#!   BOUNDING_RECT="-1685.2441251685232 -1447.1850706556522 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir_2)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="8"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="2006.2700627006279 -856.25856258562612"
#!   BOUNDING_RECT="2006.2700627006279 -856.25856258562612 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="AND"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp&#10;CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;path_rootname&lt;closeparen&gt; BEGINS_WITH XM"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="10"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="2528.1502815028148 -856.25856258562612"
#!   BOUNDING_RECT="2528.1502815028148 -856.25856258562612 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt; CONTAINS &lt;u9879&gt;&lt;u76ee&gt;&lt;u7ae3&gt;&lt;u5de5&gt;&lt;u9a8c&gt;&lt;u6536&gt;&lt;u7b49&gt;&lt;u8d44&gt;&lt;u6599&gt;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="30.398031253041609 -1553.4361331662769"
#!   BOUNDING_RECT="30.398031253041609 -1553.4361331662769 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; open_path SET_TO $(dir_2) varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="13"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-1083.0483030827384 -1553.4361331662769"
#!   BOUNDING_RECT="-1083.0483030827384 -1553.4361331662769 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="AND"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp&#10;CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;path_rootname&lt;closeparen&gt; BEGINS_WITH XM"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="14"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-520.54267802648906 -1553.4361331662769"
#!   BOUNDING_RECT="-520.54267802648906 -1553.4361331662769 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt; CONTAINS &lt;u9879&gt;&lt;u76ee&gt;&lt;u7ae3&gt;&lt;u5de5&gt;&lt;u9a8c&gt;&lt;u6536&gt;&lt;u7b49&gt;&lt;u8d44&gt;&lt;u6599&gt;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="2468.4905939968476 -1436.7328206786067"
#!   BOUNDING_RECT="2468.4905939968476 -1436.7328206786067 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="20"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="3094.4109635993805 -1513.6630081622995"
#!   BOUNDING_RECT="3094.4109635993805 -1513.6630081622995 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="GGSJBZL,buffer,JCMJ,buffer,JGRQ,buffer,JSDD,buffer,JSGGSSPT,buffer,JSMJ,buffer,LXNF,buffer,NTPSBZ,buffer,open_path,buffer,path_directory_unix,buffer,path_directory_windows,buffer,path_extension,buffer,path_filename,buffer,path_rootname,buffer,path_type,buffer,path_unix,buffer,path_windows,buffer,SDXLPTCD,buffer,Shape_Area,buffer,Shape_Leng,buffer,shapefile_type,buffer,SJXZQHDM,buffer,SJXZQHMC,buffer,SXZQHDM,buffer,SXZQHMC,buffer,XJXZQHDM,buffer,XJXZQHMC,buffer,XMDM,buffer,XMJD,buffer,XMLX,buffer,XMMC,buffer,XMTZJE,buffer,XMZGBM,buffer,YSRQ,buffer"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="22"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="3094.4109635993805 -789.50949360613595"
#!   BOUNDING_RECT="3094.4109635993805 -789.50949360613595 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE="XMMC"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="24"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="8403.4931258403503 -1095.2881187971682"
#!   BOUNDING_RECT="8403.4931258403503 -1095.2881187971682 430 71"
#!   ORDER="500000000000054"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;bclj&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;fme-system&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;FME_DISCLOSURE_OPEN&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Compressed&lt;space&gt;Shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Split&lt;space&gt;Dataset&lt;space&gt;into&lt;space&gt;2GB&lt;space&gt;Files,SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;space&gt;Type&lt;space&gt;Storage,SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;Output&lt;space&gt;Dimension,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Elevation&lt;space&gt;as&lt;space&gt;Measures,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;Surface&lt;space&gt;and&lt;space&gt;Solid&lt;space&gt;Storage,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Spatial&lt;space&gt;Index:,SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-system"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_rootname&lt;closeparen&gt;:Output,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;path_rootname&lt;closeparen&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,SXZQHDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SXZQHMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SJXZQHDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SJXZQHMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQHDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQHMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMZGBM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMLX&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMJD&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LXNF&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JSDD&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JGRQ&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;YSRQ&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XMTZJE&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JSMJ&lt;comma&gt;double&lt;comma&gt;JCMJ&lt;comma&gt;double&lt;comma&gt;GGSJBZL&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;JSGGSSPT&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SDXLPTCD&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;NTPSBZ&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;Shape_Leng&lt;comma&gt;double&lt;comma&gt;Shape_Area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="25"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="6968.1568027801477 -1215.2881187971682"
#!   BOUNDING_RECT="6968.1568027801477 -1215.2881187971682 430 71"
#!   ORDER="500000000000055"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="Shape_Area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="26"
#!   TYPE="LengthCalculator"
#!   VERSION="2"
#!   POSITION="7692.0276778525358 -1180.6406958146761"
#!   BOUNDING_RECT="7692.0276778525358 -1180.6406958146761 430 71"
#!   ORDER="500000000000056"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DIM" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="LEN_ATTR" PARM_VALUE="Shape_Leng"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="LengthCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="29"
#!   TYPE="Clipper"
#!   VERSION="16"
#!   POSITION="4942.0948754942092 -789.50949360613595"
#!   BOUNDING_RECT="4942.0948754942092 -789.50949360613595 430 71"
#!   ORDER="500000000000057"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="INSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="19年前项目{}.19年前项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="OUTSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="19年前项目" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ADD_NODATA" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BOUNDARY_CASES" PARM_VALUE="INSIDE"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CLIPPED_ATTR" PARM_VALUE="_clipped"/>
#!     <XFORM_PARM PARM_NAME="CLIPPERS_FIRST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="CLIPPER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INVALID_PARTS_HANDLING" PARM_VALUE="REJECT_WHOLE"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="19&lt;u5e74&gt;&lt;u524d&gt;&lt;u9879&gt;&lt;u76ee&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="SELECTED"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="19年前项目"/>
#!     <XFORM_PARM PARM_NAME="MEASURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MISSING_M_MODE" PARM_VALUE="Linear Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MISSING_Z_MODE" PARM_VALUE="Planar Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MULTIPLE_CLIPPERS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="M_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVERLAPPING_CLIPPERS" PARM_VALUE="CLIP_OUTSIDE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_RASTER_EXTENTS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_MODE" PARM_VALUE="CENTER"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Clipper"/>
#!     <XFORM_PARM PARM_NAME="Z_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Z_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="33"
#!   TYPE="StringReplacer"
#!   VERSION="5"
#!   POSITION="556.82375005568247 -1553.4361331662769"
#!   BOUNDING_RECT="556.82375005568247 -1553.4361331662769 430 71"
#!   ORDER="500000000000058"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CASE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FIND_TEXT" PARM_VALUE="&lt;solidus&gt;"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH" PARM_VALUE="_FME_NO_OP_"/>
#!     <XFORM_PARM PARM_NAME="NO_MATCH_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REGEXP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REPLACE_TEXT" PARM_VALUE="&lt;backslash&gt;"/>
#!     <XFORM_PARM PARM_NAME="SRC_ATTRS" PARM_VALUE="open_path"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StringReplacer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="36"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="1104.9371875536935 -1553.4361331662769"
#!   BOUNDING_RECT="1104.9371875536935 -1553.4361331662769 430 71"
#!   ORDER="500000000000059"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="path_directory_windows"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;open_path&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="35"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="1730.1309376730126 -1553.4361331662769"
#!   BOUNDING_RECT="1730.1309376730126 -1553.4361331662769 430 71"
#!   ORDER="500000000000060"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; bclj SET_TO $(PARAMETER_2)&lt;solidus&gt;&lt;u64e6&gt;&lt;u9664&gt;&lt;u540e&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;solidus&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="40"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="5497.2140630497179 -332.38968753323877"
#!   BOUNDING_RECT="5497.2140630497179 -332.38968753323877 430 71"
#!   ORDER="500000000000061"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="重叠项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="19年前项目{}.19年前项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="42"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="6099.492813109945 -371.82250004318178"
#!   BOUNDING_RECT="6099.492813109945 -371.82250004318178 430 71"
#!   ORDER="500000000000062"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="重叠项目" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="19年前项目{}.19年前项目" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="_area,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="XMMC"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="44"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="3778.1627816278151 -856.25856258562612"
#!   BOUNDING_RECT="3778.1627816278151 -856.25856258562612 430 71"
#!   ORDER="500000000000063"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="19年前项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; 19&lt;u5e74&gt;&lt;u524d&gt;&lt;u9879&gt;&lt;u76ee&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;XMMC&lt;closeparen&gt; buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="47"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="4972.5547450371932 -332.38968753323877"
#!   BOUNDING_RECT="4972.5547450371932 -332.38968753323877 430 71"
#!   ORDER="500000000000063"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="重叠项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="19年前项目{}.19年前项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;19&lt;u5e74&gt;&lt;u524d&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;opencurly&gt;0&lt;closecurly&gt;.19&lt;u5e74&gt;&lt;u524d&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="50"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="7315.7537880735617 -371.82250004318178"
#!   BOUNDING_RECT="7315.7537880735617 -371.82250004318178 430 71"
#!   ORDER="500000000000065"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="19年后项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="与19年之前重叠面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="涉及19年前项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="文本" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.重叠项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; 19&lt;u5e74&gt;&lt;u540e&gt;&lt;u9879&gt;&lt;u76ee&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;XMMC&lt;closeparen&gt; buffer  &lt;u4e0e&gt;19&lt;u5e74&gt;&lt;u4e4b&gt;&lt;u524d&gt;&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt; SET_TO &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area.sum&lt;closeparen&gt;*0.0015&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u6d89&gt;&lt;u53ca&gt;19&lt;u5e74&gt;&lt;u524d&gt;&lt;u9879&gt;&lt;u76ee&gt; SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;10&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;9&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;9&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;8&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;7&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;6&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;5&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;9&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;8&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;8&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;7&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;6&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;5&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;8&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;7&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;7&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;6&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;5&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;7&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;6&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;6&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;5&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;6&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;5&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;5&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;5&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;4&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;3&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;2&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; = _FME_BLANK_STRING_;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;uff0c&gt;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS10___&quot; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u6587&gt;&lt;u672c&gt; SET_TO &lt;u8be5&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u6d89&gt;&lt;u53ca&gt;&lt;u4e0e&gt;2019&lt;u5e74&gt;&lt;u4e4b&gt;&lt;u524d&gt;&lt;u7684&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u3010&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u6d89&gt;&lt;u53ca&gt;19&lt;u5e74&gt;&lt;u524d&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;closeparen&gt;&lt;u3011&gt;&lt;u91cd&gt;&lt;u53e0&gt;&lt;uff0c&gt;&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u4e0e&gt;19&lt;u5e74&gt;&lt;u4e4b&gt;&lt;u524d&gt;&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt;&lt;closeparen&gt;&lt;u4ea9&gt;&lt;u3002&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="52"
#!   TYPE="ListBuilder"
#!   VERSION="3"
#!   POSITION="6715.9762506715942 -371.82250004318178"
#!   BOUNDING_RECT="6715.9762506715942 -371.82250004318178 430 71"
#!   ORDER="500000000000067"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.重叠项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="XMMC _area.sum"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="Selected Attributes"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListBuilder"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="54"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="8403.4931258403503 -251.82250004318178"
#!   BOUNDING_RECT="8403.4931258403503 -251.82250004318178 430 71"
#!   ORDER="500000000000068"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;&lt;u91cd&gt;&lt;u53e0&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u7edf&gt;&lt;u8ba1&gt;.xlsx"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_FILE&lt;comma&gt;No&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;comma&gt;TEMPLATE_SHEET&lt;comma&gt;&lt;comma&gt;REMOVE_UNCHANGED_TEMPLATE_SHEET&lt;comma&gt;No&lt;comma&gt;MULTIPLE_TEMPLATE_SHEETS&lt;comma&gt;Yes&lt;comma&gt;INSERT_IGNORE_DB_OP&lt;comma&gt;Yes&lt;comma&gt;DROP_TABLE&lt;comma&gt;No&lt;comma&gt;TRUNCATE_TABLE&lt;comma&gt;No&lt;comma&gt;FIELD_NAMES_OUT&lt;comma&gt;Yes&lt;comma&gt;FIELD_NAMES_FORMATTING&lt;comma&gt;Yes&lt;comma&gt;WRITER_MODE&lt;comma&gt;Insert&lt;comma&gt;RASTER_FORMAT&lt;comma&gt;PNG&lt;comma&gt;PROTECT_SHEET&lt;comma&gt;NO&lt;comma&gt;PROTECT_SHEET_PASSWORD&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_LEVEL&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_PERMISSIONS&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING&lt;comma&gt;yes&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE&lt;comma&gt;CUSTOM_NUMBER_FORMATTING&lt;comma&gt;ENABLE_NATIVE&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXW_INSERT_IGNORE_DB_OP,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_FIELD_NAMES_FORMATTING,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:,XLSXW_WRITER_MODE,&quot;OPTIONAL CHOICE Insert%Update%Delete&quot;,XLSXW&lt;space&gt;Default&lt;space&gt;Feature&lt;space&gt;Type&lt;space&gt;Writer&lt;space&gt;Mode:,XLSXW_RASTER_FORMAT,&quot;OPTIONAL CHOICE BMP%JPEG%PNG&quot;,XLSXW&lt;space&gt;Raster&lt;space&gt;Format:,XLSXW_DROP_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_CUSTOM_NUMBER_FORMATTING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_MULTIPLE_TEMPLATE_SHEETS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_FIELD_NAMES_OUT,&quot;OPTIONAL ACTIVECHOICE Yes%No,FIELD_NAMES_FORMATTING,++FIELD_NAMES_FORMATTING+No&quot;,XLSXW&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:,XLSXW_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_OVERWRITE_FILE,&quot;OPTIONAL ACTIVECHOICE Yes%No,TEMPLATEFILE,TEMPLATE_SHEET,REMOVE_UNCHANGED_TEMPLATE_SHEET&quot;,XLSXW&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;File:,XLSXW_PROTECT_SHEET,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP PROTECT_SHEET_PASSWORD%PROTECT_SHEET_LEVEL%PROTECT_SHEET_PERMISSIONS&quot;,XLSXW&lt;space&gt;Protect&lt;space&gt;Sheet,XLSXW_TRUNCATE_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXW&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="Sheet1:Sheet1,ftp_feature_type_name,Sheet1,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,19&lt;lt&gt;u5e74&lt;gt&gt;&lt;lt&gt;u540e&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;9&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u4e0e&lt;gt&gt;19&lt;lt&gt;u5e74&lt;gt&gt;&lt;lt&gt;u4e4b&lt;gt&gt;&lt;lt&gt;u524d&lt;gt&gt;&lt;lt&gt;u91cd&lt;gt&gt;&lt;lt&gt;u53e0&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u4ea9&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;9&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6d89&lt;gt&gt;&lt;lt&gt;u53ca&lt;gt&gt;19&lt;lt&gt;u5e74&lt;gt&gt;&lt;lt&gt;u524d&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;30&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;9&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u672c&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;9&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_xml&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_json&lt;comma&gt;&lt;quote&gt;auto&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;datetime&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_datetime&lt;comma&gt;&lt;quote&gt;time&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_time&lt;comma&gt;&lt;quote&gt;date&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_date&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;boolean&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;openbracket&gt;&lt;closebracket&gt;*&lt;backslash&gt;&lt;backslash&gt;?:&lt;apos&gt;,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none&lt;comma&gt;fme_no_geom&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point&lt;comma&gt;xlsx_point&lt;comma&gt;fme_point&lt;comma&gt;xlsx_none&lt;comma&gt;fme_line&lt;comma&gt;xlsx_none&lt;comma&gt;fme_polygon&lt;comma&gt;xlsx_none&lt;comma&gt;fme_text&lt;comma&gt;xlsx_none&lt;comma&gt;fme_ellipse&lt;comma&gt;xlsx_none&lt;comma&gt;fme_arc&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_collection&lt;comma&gt;xlsx_none&lt;comma&gt;fme_surface&lt;comma&gt;xlsx_none&lt;comma&gt;fme_solid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_raster&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point_cloud&lt;comma&gt;xlsx_none&lt;comma&gt;fme_voxel_grid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;xlsx_layer_group&lt;space&gt;xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group&lt;space&gt;Sheet&lt;space&gt;Settings&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_truncate_group&lt;space&gt;xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet&lt;space&gt;Drop&lt;solidus&gt;Truncate&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_rowcolumn_group&lt;space&gt;xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Position&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVEDISCLOSUREGROUP&lt;space&gt;xlsx_protect_sheet&lt;space&gt;xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions&lt;space&gt;Protect&lt;space&gt;Sheet&lt;quote&gt;&lt;comma&gt;NO&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_template_group&lt;space&gt;xlsx_template_sheet%xlsx_remove_unchanged_template_sheet&lt;space&gt;Template&lt;space&gt;Options&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_advanced_group&lt;space&gt;xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_drop_sheet&lt;space&gt;Yes%No&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_trunc_sheet&lt;space&gt;Yes%No&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_sheet_order&lt;space&gt;1%MAX&lt;space&gt;Sheet&lt;space&gt;Order&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_freeze_end_row&lt;space&gt;1%MAX&lt;space&gt;Freeze&lt;space&gt;First&lt;space&gt;Row&lt;openparen&gt;s&lt;closeparen&gt;&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_field_names_out&lt;space&gt;Yes%No&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;++xlsx_field_names_formatting+No&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_field_names_formatting&lt;space&gt;Yes%No&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_names_are_positions&lt;space&gt;Yes%No&lt;space&gt;Use&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;As&lt;space&gt;Column&lt;space&gt;Positions:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_start_col&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_start_row&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_offset_col&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_raster_type&lt;space&gt;BMP%JPEG%PNG&lt;space&gt;Raster&lt;space&gt;Format:&lt;quote&gt;&lt;comma&gt;PNG&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;PASSWORD_ENCODED&lt;space&gt;xlsx_protect_sheet_password&lt;space&gt;Password:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;xlsx_protect_sheet_level&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_DEFAULT&lt;comma&gt;xlsx_protect_sheet_permissions%View&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_ALL&lt;comma&gt;xlsx_protect_sheet_permissions%Specific&lt;lt&gt;space&lt;gt&gt;Permissions&lt;space&gt;Protection&lt;space&gt;Level:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;LOOKUP_LISTBOX&lt;space&gt;xlsx_protect_sheet_permissions&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;locked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_LOCKED_CELLS%Select&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_UNLOCKED_CELLS%Format&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_FORMAT_CELLS%Format&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_FORMAT_COLUMNS%Format&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_FORMAT_ROWS%Insert&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_INSERT_COLUMNS%Insert&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_INSERT_ROWS%Add&lt;lt&gt;space&lt;gt&gt;hyperlinks&lt;lt&gt;space&lt;gt&gt;to&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_INSERT_HYPERLINKS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_DELETE_COLUMNS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_DELETE_ROWS%Sort&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;solidus&gt;rows&lt;solidus&gt;columns&lt;comma&gt;PROT_SORT%Use&lt;lt&gt;space&lt;gt&gt;Autofilter&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_AUTOFILTER%Use&lt;lt&gt;space&lt;gt&gt;PivotTable&lt;lt&gt;space&lt;gt&gt;&lt;amp&gt;&lt;lt&gt;space&lt;gt&gt;PivotChart&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_PIVOTTABLES%Edit&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;objects&lt;comma&gt;PROT_OBJECTS%Edit&lt;lt&gt;space&lt;gt&gt;unprotected&lt;lt&gt;space&lt;gt&gt;scenarios&lt;comma&gt;PROT_SCENARIOS&lt;space&gt;Specific&lt;space&gt;Permissions:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_table_writer_mode&lt;space&gt;Insert&lt;comma&gt;+xlsx_row_id_column+&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;%Update&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id%Delete&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id&lt;space&gt;Writer&lt;space&gt;Mode:&lt;quote&gt;&lt;comma&gt;Insert&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ATTR&lt;space&gt;xlsx_row_id_column&lt;space&gt;ALLOW_NEW&lt;space&gt;Row&lt;space&gt;Number&lt;space&gt;Attribute:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT_EDIT&lt;space&gt;xlsx_template_sheet&lt;space&gt;Template&lt;space&gt;Sheet:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_remove_unchanged_template_sheet&lt;space&gt;Yes%No&lt;space&gt;Remove&lt;space&gt;Template&lt;space&gt;Sheet&lt;space&gt;if&lt;space&gt;Unchanged:&lt;quote&gt;&lt;comma&gt;No,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;NO&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;No&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,DEFAULT_READER&lt;comma&gt;XLSXR&lt;comma&gt;ALLOW_DATASET_CONFLICT&lt;comma&gt;YES&lt;comma&gt;MIME_TYPE&lt;comma&gt;&lt;quote&gt;application&lt;solidus&gt;vnd.openxmlformats-officedocument.spreadsheetml.sheet&lt;space&gt;ADD_DISPOSITION&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;DEFAULT_ATTR_TYPE&lt;comma&gt;auto&lt;comma&gt;USER_ATTRIBUTES_COLUMNS&lt;comma&gt;&lt;quote&gt;Width&lt;comma&gt;Cell&lt;space&gt;Width%Precision&lt;comma&gt;Formatting&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Sheet&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Sheet1&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;name&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Microsoft&lt;space&gt;Excel&lt;space&gt;file&lt;quote&gt;,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter_2"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_CUSTOM_NUMBER_FORMATTING" PARM_VALUE="ENABLE_NATIVE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DROP_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_FORMATTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_OUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_INSERT_IGNORE_DB_OP" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_MULTIPLE_TEMPLATE_SHEETS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXW_OVERWRITE_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_PROTECT_SHEET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_RASTER_FORMAT" PARM_VALUE="PNG"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_TRUNCATE_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_WRITER_MODE" PARM_VALUE="Insert"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="56"
#!   TYPE="ExcelStyler"
#!   VERSION="1"
#!   POSITION="7781.3278132781297 -371.82250004318178"
#!   BOUNDING_RECT="7781.3278132781297 -371.82250004318178 430 71"
#!   ORDER="500000000000069"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ExcelStyled"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="19年后项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="与19年之前重叠面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="涉及19年前项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="文本" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.重叠项目" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="xlsx_row_formatting" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_STYLE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BACKGROUND_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_COLOR" PARM_VALUE="0,0,0"/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_STYLE" PARM_VALUE="BORDERSTYLE_THIN"/>
#!     <XFORM_PARM PARM_NAME="CELL_PROTECTION_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="FILL_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_COLOR" PARM_VALUE="0,0,0"/>
#!     <XFORM_PARM PARM_NAME="FONT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_NAME" PARM_VALUE="&lt;u5b8b&gt;&lt;u4f53&gt;&lt;comma&gt;9"/>
#!     <XFORM_PARM PARM_NAME="HIDDEN" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="HORIZONTAL_ALIGNMENT" PARM_VALUE="center"/>
#!     <XFORM_PARM PARM_NAME="INDENT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="LOCKED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NUMBER_FORMAT_STRING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NUMBER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATTERN_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATTERN_STYLE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ROW_HEIGHT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ROW_OR_CELL" PARM_VALUE="row"/>
#!     <XFORM_PARM PARM_NAME="SELECTION_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEXT_ALIGNMENT_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="TEXT_CONTROL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEXT_ORIENTATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VERTICAL_ALIGNMENT" PARM_VALUE="center"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ExcelStyler"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="55"
#!   TYPE="Aggregator"
#!   VERSION="17"
#!   POSITION="6007.0045144895903 -1079.5562501079553"
#!   BOUNDING_RECT="6007.0045144895903 -1079.5562501079553 430 71"
#!   ORDER="500000000000070"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AGGREGATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="bclj" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="open_path" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GGSJBZL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JCMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JGRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSDD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSGGSSPT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="JSMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LXNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="NTPSBZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SDXLPTCD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQHMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMJD" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMLX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMTZJE" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMZGBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSRQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ACCUM_ATTRS_NAME" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="AGGREGATE_TYPE" PARM_VALUE="Homogeneous Collection (If Possible)"/>
#!     <XFORM_PARM PARM_NAME="ASSEMBLE_HIERARCHY_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="ASSEMBLE_ONE_LEVEL_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_TO_TRAIT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AV" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CHILD_ID_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONCAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="COUNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CYCLE_ID_ERROR_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEOM_NAME_FIELD" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ID_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ID_SELECTION" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="Geometry - Assemble One Level"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARENT_ID_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_ID" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SEP" PARM_VALUE="&lt;comma&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUM" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WEIGHT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Aggregator"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="7"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="6"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="8"
#!   TARGET_NODE="10"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="34"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="33"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="13"
#!   TARGET_NODE="14"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="30"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="29"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 CANDIDATE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="27"
#!   SOURCE_NODE="25"
#!   TARGET_NODE="26"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="28"
#!   SOURCE_NODE="26"
#!   TARGET_NODE="24"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="48"
#!   SOURCE_NODE="29"
#!   TARGET_NODE="47"
#!   SOURCE_PORT_DESC="fo 0 INSIDE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="37"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="36"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="39"
#!   SOURCE_NODE="35"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="38"
#!   SOURCE_NODE="36"
#!   TARGET_NODE="35"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="43"
#!   SOURCE_NODE="40"
#!   TARGET_NODE="42"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="46"
#!   SOURCE_NODE="44"
#!   TARGET_NODE="29"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 CLIPPER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="49"
#!   SOURCE_NODE="47"
#!   TARGET_NODE="40"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="57"
#!   SOURCE_NODE="50"
#!   TARGET_NODE="56"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="51"
#!   SOURCE_NODE="52"
#!   TARGET_NODE="50"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="60"
#!   SOURCE_NODE="55"
#!   TARGET_NODE="25"
#!   SOURCE_PORT_DESC="fo 0 AGGREGATE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="58"
#!   SOURCE_NODE="56"
#!   TARGET_NODE="54"
#!   SOURCE_PORT_DESC="fo 0 ExcelStyled"
#!   TARGET_PORT_DESC="fi 0 Sheet1"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="9"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="8"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="16"
#!   SOURCE_NODE="6"
#!   TARGET_NODE="13"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="23"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="22"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="17"
#!   SOURCE_NODE="14"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="21"
#!   SOURCE_NODE="18"
#!   TARGET_NODE="20"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="45"
#!   SOURCE_NODE="22"
#!   TARGET_NODE="44"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="59"
#!   SOURCE_NODE="29"
#!   TARGET_NODE="55"
#!   SOURCE_PORT_DESC="fo 1 OUTSIDE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="53"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="52"
#!   SOURCE_PORT_DESC="fo 1 COMPLETE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO dir $(FME_MF_DIR)19年后擦19年前\张家港市\2019年之前

DEFAULT_MACRO dir_2 $(FME_MF_DIR)19年后擦19年前\张家港市\2019年之后

DEFAULT_MACRO PARAMETER_2 C:\Users\<USER>\Desktop\新建文件夹 (22)

# ============================================================================
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir_2$encode)} == {}} { puts_real {Parameter 'dir_2' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_2$encode)} == {}} { puts_real {Parameter 'PARAMETER_2' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)19年后项目区擦除19年前项目区并统计.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 19年后项目区擦除19年前项目区并统计
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_2_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_2_XML     NOT_ACTIVATED
MACRO Creator_2_CLASSIC NOT_ACTIVATED
MACRO Creator_2_2D3D    2D_GEOMETRY
MACRO Creator_2_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_2_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2_2D3D 3D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_2: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_2_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
FACTORY_DEF {$(Creator_2_XML)} CreationFactory    FACTORY_NAME { Creator_2_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_2_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_2_CLASSIC_Creator }    $(Creator_2_2D3D) { $(Creator_2_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_2_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_2_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_2_CREATED        fme_feature_type Creator_2         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_2_CREATED Brancher -1 7"   INPUT FEATURE_TYPE Creator_2_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_2_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(dir_2$encode),FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Creator_2_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_2_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_2_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_2_<Rejected> INPUT FEATURE_TYPE FeatureReader_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_3 }    INPUT  FEATURE_TYPE FeatureReader_2_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester_3)" = shp ENCODED } CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_rootname<closeparen>,Tester_3)" BEGINS_WITH XM ENCODED }    BOOLEAN_OPERATOR { AND }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_3_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_4 }    INPUT  FEATURE_TYPE Tester_3_PASSED    CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,Tester_4)" CONTAINS <u9879><u76ee><u7ae3><u5de5><u9a8c><u6536><u7b49><u8d44><u6599> ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_4_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Tester_4_FAILED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "open_path" "SET_TO" "$(dir_2$encode)" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StringReplacerFactory    FACTORY_NAME { StringReplacer }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    USE_REGEX { NO }    CASE_SENSITIVE { NO }    SOURCE_ATTRIBUTES { open_path }    FIND_TEXT { "<solidus>" }    REPLACE_TEXT { "<backslash>" }    REPLACE_NO_MATCH { "_FME_NO_OP_" }    OUTPUT { OUTPUT FEATURE_TYPE StringReplacer_OUTPUT          }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_d94366e2_235f_417c_9f21_e216a884befc3} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>open_path<closeparen>,AttributeSplitter)}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {path_directory_windows}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {path_directory_windows}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter }    INPUT  FEATURE_TYPE StringReplacer_OUTPUT    OUTPUT { FEATURE_TYPE AttributeSplitter_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeSplitter_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "bclj" "SET_TO" "$(PARAMETER_2$encode)<solidus><u64e6><u9664><u540e><u9879><u76ee><solidus><at>Value<openparen>_list<opencurly>1<closecurly><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_3_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE AttributeCreator_2_OUTPUT    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_3_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_3_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_3_<Rejected> INPUT FEATURE_TYPE FeatureReader_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer }    INPUT  FEATURE_TYPE FeatureReader_3_<OTHER>    OUTPUT { FEATURE_TYPE AttributeExposer_OUTPUT          }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = shp ENCODED } CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_rootname<closeparen>,Tester)" BEGINS_WITH XM ENCODED }    BOOLEAN_OPERATOR { AND }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE Tester_PASSED    CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,Tester_2)" CONTAINS <u9879><u76ee><u7ae3><u5de5><u9a8c><u6536><u7b49><u8d44><u6599> ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_2_FAILED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_4_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_4_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_4_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_4_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_4_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_4 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_4)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_4 }    INPUT  FEATURE_TYPE Tester_2_FAILED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_4_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_4)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_4_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_4_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_4_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_4_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_4_<Rejected> INPUT FEATURE_TYPE FeatureReader_4_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_4<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE FeatureReader_4_<OTHER>    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "19<u5e74><u524d><u9879><u76ee>" "SET_TO" "<at>Value<openparen>XMMC<closeparen>" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ClipperFactory    FACTORY_NAME { Clipper }    INPUT CLIPPER FEATURE_TYPE AttributeCreator_3_OUTPUT    INPUT CANDIDATE FEATURE_TYPE AttributeExposer_OUTPUT    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    MULTIPLE_CLIPPERS { YES }    CLIPPERS_FIRST { NO }    OVERLAPPING_CLIPPERS { CLIP_OUTSIDE }    INVALID_PARTS_HANDLING { REJECT_WHOLE }    ADD_NODATA { YES }    Z_VAL_SOURCE { CANDIDATE }    MISSING_Z_MODE { PLANAR }    CONNECT_Z_MODE { <Unused> }    M_VAL_SOURCE { CANDIDATE }    MISSING_M_MODE { LINEAR }    CLEANING_TOLERANCE { AUTO }    CANDIDATE_ON_BOUNDARY { INSIDE }    PRESERVE_RASTER_EXTENTS { NO }    RASTER_CELL_MODE { CENTER }    CLIPPED_INDICATOR_ATTR { "_clipped" }    MERGE_CLIPPER_ATTRIBUTES { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    CLIPPER_PREFIX { "<Unused>" }    LIST_NAME { "19年前项目" }    LIST_ATTRS_TO_INCLUDE { 19<u5e74><u524d><u9879><u76ee> }    LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { INSIDE FEATURE_TYPE Clipper_INSIDE         }    OUTPUT { OUTSIDE FEATURE_TYPE Clipper_OUTSIDE         }    OUTPUT { REJECTED FEATURE_TYPE Clipper_<REJECTED>         }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Clipper_<Rejected> INPUT FEATURE_TYPE Clipper_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Clipper<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_4 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Clipper_INSIDE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u91cd><u53e0><u9879><u76ee>" "SET_TO" "<at>Value<openparen>19<u5e74><u524d><u9879><u76ee><opencurly>0<closecurly>.19<u5e74><u524d><u9879><u76ee><closeparen>" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_4_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_2_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_2_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_2_AreaCalculatorInput }    INPUT  FEATURE_TYPE AttributeCreator_4_OUTPUT    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_2_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $(AreaCalculator_2_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_2_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_2_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator }    INPUT  FEATURE_TYPE AreaCalculator_2_OUTPUT    GROUP_BY { XMMC }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { _area,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { <u91cd><u53e0><u9879><u76ee>,buffer,bclj,varchar<openparen>200<closeparen>,open_path,varchar<openparen>200<closeparen>,path_unix,buffer,path_windows,buffer,path_rootname,buffer,path_filename,buffer,path_extension,buffer,path_directory_unix,buffer,path_directory_windows,buffer,path_type,varchar<openparen>10<closeparen>,fme_geometry<opencurly>0<closecurly>,fme_no_geom,GGSJBZL,buffer,JCMJ,buffer,JGRQ,buffer,JSDD,buffer,JSGGSSPT,buffer,JSMJ,buffer,LXNF,buffer,NTPSBZ,buffer,SDXLPTCD,buffer,Shape_Area,buffer,Shape_Leng,buffer,shapefile_type,buffer,SJXZQHDM,buffer,SJXZQHMC,buffer,SXZQHDM,buffer,SXZQHMC,buffer,XJXZQHDM,buffer,XJXZQHMC,buffer,XMDM,buffer,XMJD,buffer,XMLX,buffer,XMMC,buffer,XMTZJE,buffer,XMZGBM,buffer,YSRQ,buffer,_clipped,char<openparen>3<closeparen>,_area,real64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { COMPLETE FEATURE_TYPE StatisticsCalculator_COMPLETE        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { ListBuilder_fme_type_remover }    INPUT  FEATURE_TYPE StatisticsCalculator_COMPLETE    OUTPUT { FEATURE_TYPE ListBuilder_no_fme_type       @RemoveAttributes(fme_type,fme_geometry) }
FACTORY_DEF {*} ListFactory    FACTORY_NAME { ListBuilder }    INPUT { FEATURE_TYPE ListBuilder_no_fme_type }    LIST_NAME { "_list{}" }    LIST_ATTRS_TO_INCLUDE { <u91cd><u53e0><u9879><u76ee> }    LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    GROUP_BY { XMMC _area.sum }    FLUSH_WHEN_GROUPS_CHANGE { No }    OUTPUT { LIST FEATURE_TYPE ListBuilder_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_5 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE ListBuilder_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "19<u5e74><u540e><u9879><u76ee>" "SET_TO" "<at>Value<openparen>XMMC<closeparen>" "buffer" }      ATTR_ACTION { "" "<u4e0e>19<u5e74><u4e4b><u524d><u91cd><u53e0><u9762><u79ef><uff08><u4ea9><uff09>" "SET_TO" "<at>Format<openparen>%.4f<comma><at>Evaluate<openparen><at>Value<openparen>_area.sum<closeparen>*0.0015<closeparen><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u6d89><u53ca>19<u5e74><u524d><u9879><u76ee>" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'<at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>10<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>9<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>9<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>8<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>7<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>6<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>5<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>9<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>8<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>8<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>7<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>6<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>5<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>8<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>7<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>7<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>6<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>5<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>7<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>6<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>6<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>5<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>6<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>5<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>5<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>5<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>4<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>3<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'BOOL_OP;AND;COMPOSITE_TEST;1 AND 2;TEST <at>Value<openparen>_list<opencurly>2<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> = _FME_BLANK_STRING_;TEST <at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>1<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen><uff0c><at>Value<openparen>_list<opencurly>0<closecurly>.<u91cd><u53e0><u9879><u76ee><closeparen>'FME_NUM_CONDITIONS10___" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u6587><u672c>" "SET_TO" "<u8be5><u9879><u76ee><u6d89><u53ca><u4e0e>2019<u5e74><u4e4b><u524d><u7684><u9879><u76ee><u3010><at>Value<openparen><u6d89><u53ca>19<u5e74><u524d><u9879><u76ee><closeparen><u3011><u91cd><u53e0><uff0c><u91cd><u53e0><u9762><u79ef><at>Value<openparen><u4e0e>19<u5e74><u4e4b><u524d><u91cd><u53e0><u9762><u79ef><uff08><u4ea9><uff09><closeparen><u4ea9><u3002>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_5_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ExcelStyleFactory    FACTORY_NAME { ExcelStyler_Styler }    INPUT  FEATURE_TYPE AttributeCreator_5_OUTPUT    ROW_OR_CELL { "row" }    NUMBER_FORMAT_STRING { "" }    FONT_NAME { "<u5b8b><u4f53><comma>9" }    FONT_COLOR { "0,0,0" }    PATTERN_COLOR { "" }    BACKGROUND_COLOR { "" }    PATTERN_STYLE { "" }    HORIZONTAL_ALIGNMENT { "center" }    VERTICAL_ALIGNMENT { "center" }    INDENT { "" }    TEXT_ORIENTATION { "" }    TEXT_CONTROL { "" }    CELL_BORDER_COLOR { "0,0,0" }    CELL_BORDER_STYLE { "BORDERSTYLE_THIN" }    ROW_HEIGHT { "" }    ATTRS_TO_STYLE { <Unused> }    HIDDEN { "" }    LOCKED { "" }    OUTPUT { ExcelStyled FEATURE_TYPE ExcelStyler_ExcelStyled        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash><u91cd><u53e0><u9879><u76ee><u7edf><u8ba1>.xlsx,FeatureWriter_2)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter_2 }    WRITER_TYPE { XLSXW }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter_2)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_FILE<comma>No<comma>TEMPLATEFILE<comma><comma>TEMPLATE_SHEET<comma><comma>REMOVE_UNCHANGED_TEMPLATE_SHEET<comma>No<comma>MULTIPLE_TEMPLATE_SHEETS<comma>Yes<comma>INSERT_IGNORE_DB_OP<comma>Yes<comma>DROP_TABLE<comma>No<comma>TRUNCATE_TABLE<comma>No<comma>FIELD_NAMES_OUT<comma>Yes<comma>FIELD_NAMES_FORMATTING<comma>Yes<comma>WRITER_MODE<comma>Insert<comma>RASTER_FORMAT<comma>PNG<comma>PROTECT_SHEET<comma>NO<comma>PROTECT_SHEET_PASSWORD<comma><lt>Unused<gt><comma>PROTECT_SHEET_LEVEL<comma><lt>Unused<gt><comma>PROTECT_SHEET_PERMISSIONS<comma><lt>Unused<gt><comma>STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING<comma>yes<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE<comma>CUSTOM_NUMBER_FORMATTING<comma>ENABLE_NATIVE<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,XLSXW" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,<quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varchar<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varbinary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_char<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binarybuffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_xml<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_json<comma><quote>auto<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>datetime<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_datetime<comma><quote>time<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_time<comma><quote>date<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_date<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int8<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint8<comma><quote>boolean<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<openbracket><closebracket>*<backslash><backslash>?:<apos>,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none<comma>fme_no_geom<comma>xlsx_none<comma>fme_point<comma>xlsx_point<comma>fme_point<comma>xlsx_none<comma>fme_line<comma>xlsx_none<comma>fme_polygon<comma>xlsx_none<comma>fme_text<comma>xlsx_none<comma>fme_ellipse<comma>xlsx_none<comma>fme_arc<comma>xlsx_none<comma>fme_rectangle<comma>xlsx_none<comma>fme_rounded_rectangle<comma>xlsx_none<comma>fme_collection<comma>xlsx_none<comma>fme_surface<comma>xlsx_none<comma>fme_solid<comma>xlsx_none<comma>fme_raster<comma>xlsx_none<comma>fme_point_cloud<comma>xlsx_none<comma>fme_voxel_grid<comma>xlsx_none<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>xlsx_layer_group<space>xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group<space>Sheet<space>Settings<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_truncate_group<space>xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet<space>Drop<solidus>Truncate<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_rowcolumn_group<space>xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row<space>Start<space>Position<quote><comma><comma><quote>GUI<space>ACTIVEDISCLOSUREGROUP<space>xlsx_protect_sheet<space>xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions<space>Protect<space>Sheet<quote><comma>NO<comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_template_group<space>xlsx_template_sheet%xlsx_remove_unchanged_template_sheet<space>Template<space>Options<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_advanced_group<space>xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type<space>Advanced<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_drop_sheet<space>Yes%No<space>Drop<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>CHOICE<space>xlsx_trunc_sheet<space>Yes%No<space>Truncate<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_sheet_order<space>1%MAX<space>Sheet<space>Order<space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_freeze_end_row<space>1%MAX<space>Freeze<space>First<space>Row<openparen>s<closeparen><space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_field_names_out<space>Yes%No<comma>xlsx_field_names_formatting<comma>++xlsx_field_names_formatting+No<space>Output<space>Field<space>Names:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_field_names_formatting<space>Yes%No<space>Format<space>Field<space>Names<space>Row:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_names_are_positions<space>Yes%No<space>Use<space>Attribute<space>Names<space>As<space>Column<space>Positions:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_start_col<space>Named<space>Range<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_start_row<space>Named<space>Range<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_offset_col<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_offset_row<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_raster_type<space>BMP%JPEG%PNG<space>Raster<space>Format:<quote><comma>PNG<comma><quote>GUI<space>OPTIONAL<space>PASSWORD_ENCODED<space>xlsx_protect_sheet_password<space>Password:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>xlsx_protect_sheet_level<space>Select<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_DEFAULT<comma>xlsx_protect_sheet_permissions%View<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_ALL<comma>xlsx_protect_sheet_permissions%Specific<lt>space<gt>Permissions<space>Protection<space>Level:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>OPTIONAL<space>LOOKUP_LISTBOX<space>xlsx_protect_sheet_permissions<space>Select<lt>space<gt>locked<lt>space<gt>cells<comma>PROT_SEL_LOCKED_CELLS%Select<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_SEL_UNLOCKED_CELLS%Format<lt>space<gt>cells<comma>PROT_FORMAT_CELLS%Format<lt>space<gt>columns<comma>PROT_FORMAT_COLUMNS%Format<lt>space<gt>rows<comma>PROT_FORMAT_ROWS%Insert<lt>space<gt>columns<comma>PROT_INSERT_COLUMNS%Insert<lt>space<gt>rows<comma>PROT_INSERT_ROWS%Add<lt>space<gt>hyperlinks<lt>space<gt>to<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_INSERT_HYPERLINKS%Delete<lt>space<gt>unlocked<lt>space<gt>columns<comma>PROT_DELETE_COLUMNS%Delete<lt>space<gt>unlocked<lt>space<gt>rows<comma>PROT_DELETE_ROWS%Sort<lt>space<gt>unlocked<lt>space<gt>cells<solidus>rows<solidus>columns<comma>PROT_SORT%Use<lt>space<gt>Autofilter<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_AUTOFILTER%Use<lt>space<gt>PivotTable<lt>space<gt><amp><lt>space<gt>PivotChart<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_PIVOTTABLES%Edit<lt>space<gt>unlocked<lt>space<gt>objects<comma>PROT_OBJECTS%Edit<lt>space<gt>unprotected<lt>space<gt>scenarios<comma>PROT_SCENARIOS<space>Specific<space>Permissions:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_table_writer_mode<space>Insert<comma>+xlsx_row_id_column+<quote><quote><quote><quote>%Update<comma>+xlsx_row_id_column+xlsx_row_id%Delete<comma>+xlsx_row_id_column+xlsx_row_id<space>Writer<space>Mode:<quote><comma>Insert<comma><quote>GUI<space>OPTIONAL<space>ATTR<space>xlsx_row_id_column<space>ALLOW_NEW<space>Row<space>Number<space>Attribute:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT_EDIT<space>xlsx_template_sheet<space>Template<space>Sheet:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_remove_unchanged_template_sheet<space>Yes%No<space>Remove<space>Template<space>Sheet<space>if<space>Unchanged:<quote><comma>No,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_freeze_end_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_names_are_positions<comma>No<comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_start_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_start_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_raster_type<comma>PNG<comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_protect_sheet<comma><quote><quote><quote>NO<quote><quote><quote><comma>xlsx_protect_sheet_level<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_password<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_permissions<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_template_sheet<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_remove_unchanged_template_sheet<comma><quote><quote><quote>No<quote><quote><quote>,WRITER_FORMAT_PARAMETER,DEFAULT_READER<comma>XLSXR<comma>ALLOW_DATASET_CONFLICT<comma>YES<comma>MIME_TYPE<comma><quote>application<solidus>vnd.openxmlformats-officedocument.spreadsheetml.sheet<space>ADD_DISPOSITION<quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>DEFAULT_ATTR_TYPE<comma>auto<comma>USER_ATTRIBUTES_COLUMNS<comma><quote>Width<comma>Cell<space>Width%Precision<comma>Formatting<quote><comma>FEATURE_TYPE_NAME<comma>Sheet<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Sheet1<comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>name<space>for<space>the<space>Microsoft<space>Excel<space>file<quote>,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "Sheet1:Sheet1,ftp_feature_type_name,Sheet1,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,19<lt>u5e74<gt><lt>u540e<gt><lt>u9879<gt><lt>u76ee<gt><comma>string<lt>openparen<gt>50<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>9<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u4e0e<gt>19<lt>u5e74<gt><lt>u4e4b<gt><lt>u524d<gt><lt>u91cd<gt><lt>u53e0<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u4ea9<gt><lt>uff09<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>9<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u6d89<gt><lt>u53ca<gt>19<lt>u5e74<gt><lt>u524d<gt><lt>u9879<gt><lt>u76ee<gt><comma>string<lt>openparen<gt>30<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>9<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u6587<gt><lt>u672c<gt><comma>string<lt>openparen<gt>200<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>9<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma>,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No" }    WRITER_PARAMS { "COORDINATE_SYSTEM_GRANULARITY,FEATURE,CUSTOM_NUMBER_FORMATTING,ENABLE_NATIVE,DESTINATION_DATASETTYPE_VALIDATION,Yes,DROP_TABLE,No,FIELD_NAMES_FORMATTING,Yes,FIELD_NAMES_OUT,Yes,INSERT_IGNORE_DB_OP,Yes,MULTIPLE_TEMPLATE_SHEETS,Yes,NETWORK_AUTHENTICATION,,OVERWRITE_FILE,No,PROTECT_SHEET,NO,RASTER_FORMAT,PNG,STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,yes,TRUNCATE_TABLE,No,WRITER_MODE,Insert" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Sheet1 FEATURE_TYPE ExcelStyler_ExcelStyled  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,Sheet1,FeatureWriter_2))
# -------------------------------------------------------------------------
FACTORY_DEF {*} AggregateFactory    FACTORY_NAME { Aggregator }    INPUT  FEATURE_TYPE Clipper_OUTSIDE    MODE { ONE_LEVEL }    GROUP_BY { fme_feature_type }    FLUSH_WHEN_GROUPS_CHANGE { No }    REMOVE_GEOMETRY { NO }    ACCUMULATE_ATTRIBUTES { One }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    SEPARATOR { <comma> }    PRODUCE_MULTIS { YES }    ALL_ATTRS_LIST_GROUP_BY_MODE ADD    OUTPUT { AGGREGATE FEATURE_TYPE Aggregator_AGGREGATE          }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE Aggregator_AGGREGATE    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, Shape_Area, $(AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { LengthCalculator_LengthCalculatorInput }    INPUT  FEATURE_TYPE AreaCalculator_OUTPUT    OUTPUT { FEATURE_TYPE ___TOLENGTHCALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { LengthCalculator_LengthCalculator }    INPUT FEATURE_TYPE ___TOLENGTHCALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, Shape_Leng, @Length(REJECTABLE,2,"1")) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { LengthCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE LengthCalculator_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>bclj<closeparen>,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>fme-system<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma>FME_DISCLOSURE_OPEN<comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<at>Value<openparen>path_rootname<closeparen>:Output,ftp_feature_type_name_exp,<at>Value<openparen>path_rootname<closeparen>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,SXZQHDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SXZQHMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SJXZQHDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SJXZQHMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XJXZQHDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XJXZQHMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMZGBM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMLX<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMJD<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>LXNF<comma>varchar<lt>openparen<gt>20<lt>closeparen<gt><comma>XMDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMMC<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>JSDD<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>JGRQ<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>YSRQ<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XMTZJE<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>JSMJ<comma>double<comma>JCMJ<comma>double<comma>GGSJBZL<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>JSGGSSPT<comma>varchar<lt>openparen<gt>20<lt>closeparen<gt><comma>SDXLPTCD<comma>varchar<lt>openparen<gt>20<lt>closeparen<gt><comma>NTPSBZ<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>Shape_Leng<comma>double<comma>Shape_Area<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,FME_DISCLOSURE_OPEN,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,fme-system,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE LengthCalculator_OUTPUT  @SupplyAttributes(ENCODED,fme_template_feature_type,Output)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_rootname<closeparen>,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

