import{d as p,r as _,o as v,c,e,w as o,f as r,b as s,ag as m,l as d,ah as g,a8 as y,ad as w,ai as V,t as b,y as a,af as S,V as x,A as h,k as C,_ as A}from"./index-Dn7OnccA.js";const G={class:"about-container"},I={class:"card-header"},N={class:"features-grid"},k={class:"feature-item"},B={class:"feature-item"},D={class:"feature-item"},T={class:"feature-item"},E={class:"card-header"},L={class:"tech-content"},z={class:"tech-section"},M={class:"tech-tags"},P={class:"tech-section"},j={class:"tech-tags"},q={class:"copyright-content"},F={class:"version-info"},H=p({__name:"AboutView",setup(J){const u=_("");return v(()=>{u.value=new Date().toLocaleString("zh-CN")}),(K,t)=>{const i=r("el-card"),n=r("el-icon"),l=r("el-tag"),f=r("el-divider");return C(),c("div",G,[e(i,{class:"intro-card",shadow:"hover"},{header:o(()=>t[0]||(t[0]=[s("div",{class:"card-header"},[s("div",{class:"logo-section"},[s("img",{src:m,alt:"GeoStream Logo",class:"logo"}),s("div",{class:"title-section"},[s("h1",null,"GeoStream Integration"),s("p",{class:"subtitle"},"专业的地理信息数据处理平台")])])],-1)])),default:o(()=>[t[1]||(t[1]=s("div",{class:"intro-content"},[s("p",{class:"description"}," 欢迎使用 GeoStream Integration 平台。这是一个专业的地理信息数据处理平台， 致力于为用户提供高效、便捷的数据处理、坐标转换、格式转换等一站式解决方案。 ")],-1))]),_:1}),e(i,{class:"features-card",shadow:"hover"},{header:o(()=>[s("div",I,[e(n,{class:"header-icon"},{default:o(()=>[e(d(b))]),_:1}),t[2]||(t[2]=s("span",null,"核心功能",-1))])]),default:o(()=>[s("div",N,[s("div",k,[e(n,{class:"feature-icon"},{default:o(()=>[e(d(g))]),_:1}),t[3]||(t[3]=s("h3",null,"坐标转换",-1)),t[4]||(t[4]=s("p",null,"支持多种坐标系统间的精确转换",-1))]),s("div",B,[e(n,{class:"feature-icon"},{default:o(()=>[e(d(y))]),_:1}),t[5]||(t[5]=s("h3",null,"格式转换",-1)),t[6]||(t[6]=s("p",null,"CAD转GIS等多种数据格式转换",-1))]),s("div",D,[e(n,{class:"feature-icon"},{default:o(()=>[e(d(w))]),_:1}),t[7]||(t[7]=s("h3",null,"数据质检",-1)),t[8]||(t[8]=s("p",null,"全面的数据质量检查与验证",-1))]),s("div",T,[e(n,{class:"feature-icon"},{default:o(()=>[e(d(V))]),_:1}),t[9]||(t[9]=s("h3",null,"工具市场",-1)),t[10]||(t[10]=s("p",null,"丰富的处理工具生态系统",-1))])])]),_:1}),e(i,{class:"tech-card",shadow:"hover"},{header:o(()=>[s("div",E,[e(n,{class:"header-icon"},{default:o(()=>[e(d(S))]),_:1}),t[11]||(t[11]=s("span",null,"技术架构",-1))])]),default:o(()=>[s("div",L,[s("div",z,[t[16]||(t[16]=s("h4",null,"前端技术",-1)),s("div",M,[e(l,{type:"primary"},{default:o(()=>t[12]||(t[12]=[a("Vue 3")])),_:1}),e(l,{type:"success"},{default:o(()=>t[13]||(t[13]=[a("TypeScript")])),_:1}),e(l,{type:"info"},{default:o(()=>t[14]||(t[14]=[a("Element Plus")])),_:1}),e(l,{type:"warning"},{default:o(()=>t[15]||(t[15]=[a("Vite")])),_:1})])]),e(f),s("div",P,[t[21]||(t[21]=s("h4",null,"核心特性",-1)),s("div",j,[e(l,{type:"primary"},{default:o(()=>t[17]||(t[17]=[a("响应式设计")])),_:1}),e(l,{type:"success"},{default:o(()=>t[18]||(t[18]=[a("模块化架构")])),_:1}),e(l,{type:"info"},{default:o(()=>t[19]||(t[19]=[a("实时数据")])),_:1}),e(l,{type:"warning"},{default:o(()=>t[20]||(t[20]=[a("高性能")])),_:1})])])])]),_:1}),e(i,{class:"copyright-card",shadow:"never"},{default:o(()=>[s("div",q,[e(f,null,{default:o(()=>[e(n,null,{default:o(()=>[e(d(x))]),_:1})]),_:1}),t[22]||(t[22]=s("p",{class:"copyright-text"}," © 2025 园测信息科技股份有限公司 技术创新小组 版权所有 ",-1)),s("p",F," Version 1.0.0 | 构建时间："+h(u.value),1)])]),_:1})])}}}),Q=A(H,[["__scopeId","data-v-ba0c38c1"]]);export{Q as default};
