{"version": 3, "file": "basic-month-table.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/basic-month-table.vue"], "sourcesContent": ["<template>\n  <table\n    role=\"grid\"\n    :aria-label=\"t('el.datepicker.monthTablePrompt')\"\n    :class=\"ns.b()\"\n    @click=\"handleMonthTableClick\"\n    @mousemove=\"handleMouseMove\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr v-for=\"(row, key) in rows\" :key=\"key\">\n        <td\n          v-for=\"(cell, key_) in row\"\n          :key=\"key_\"\n          :ref=\"(el) => isSelectedCell(cell) && (currentCellRef = el as HTMLElement)\"\n          :class=\"getCellStyle(cell)\"\n          :aria-selected=\"`${isSelectedCell(cell)}`\"\n          :aria-label=\"t(`el.datepicker.month${+cell.text + 1}`)\"\n          :tabindex=\"isSelectedCell(cell) ? 0 : -1\"\n          @keydown.space.prevent.stop=\"handleMonthTableClick\"\n          @keydown.enter.prevent.stop=\"handleMonthTableClick\"\n        >\n          <el-date-picker-cell\n            :cell=\"{\n              ...cell,\n              renderText: t('el.datepicker.months.' + months[cell.text]),\n            }\"\n          />\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, ref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { castArray, hasClass } from '@element-plus/utils'\nimport { basicMonthTableProps } from '../props/basic-month-table'\nimport { datesInMonth, getValidDateOfMonth } from '../utils'\nimport ElDatePickerCell from './basic-cell-render'\n\ntype MonthCell = {\n  column: number\n  row: number\n  disabled: boolean\n  start: boolean\n  end: boolean\n  text: number\n  type: 'normal' | 'today'\n  inRange: boolean\n}\n\nconst props = defineProps(basicMonthTableProps)\nconst emit = defineEmits(['changerange', 'pick', 'select'])\n\nconst ns = useNamespace('month-table')\n\nconst { t, lang } = useLocale()\nconst tbodyRef = ref<HTMLElement>()\nconst currentCellRef = ref<HTMLElement>()\nconst months = ref(\n  props.date\n    .locale('en')\n    .localeData()\n    .monthsShort()\n    .map((_) => _.toLowerCase())\n)\nconst tableRows = ref<MonthCell[][]>([\n  [] as MonthCell[],\n  [] as MonthCell[],\n  [] as MonthCell[],\n])\nconst lastRow = ref<number>()\nconst lastColumn = ref<number>()\nconst rows = computed<MonthCell[][]>(() => {\n  const rows = tableRows.value\n\n  const now = dayjs().locale(lang.value).startOf('month')\n\n  for (let i = 0; i < 3; i++) {\n    const row = rows[i]\n    for (let j = 0; j < 4; j++) {\n      const cell = (row[j] ||= {\n        row: i,\n        column: j,\n        type: 'normal',\n        inRange: false,\n        start: false,\n        end: false,\n        text: -1,\n        disabled: false,\n      })\n\n      cell.type = 'normal'\n\n      const index = i * 4 + j\n      const calTime = props.date.startOf('year').month(index)\n\n      const calEndDate =\n        props.rangeState.endDate ||\n        props.maxDate ||\n        (props.rangeState.selecting && props.minDate) ||\n        null\n\n      cell.inRange =\n        !!(\n          props.minDate &&\n          calTime.isSameOrAfter(props.minDate, 'month') &&\n          calEndDate &&\n          calTime.isSameOrBefore(calEndDate, 'month')\n        ) ||\n        !!(\n          props.minDate &&\n          calTime.isSameOrBefore(props.minDate, 'month') &&\n          calEndDate &&\n          calTime.isSameOrAfter(calEndDate, 'month')\n        )\n\n      if (props.minDate?.isSameOrAfter(calEndDate)) {\n        cell.start = !!(calEndDate && calTime.isSame(calEndDate, 'month'))\n        cell.end = props.minDate && calTime.isSame(props.minDate, 'month')\n      } else {\n        cell.start = !!(props.minDate && calTime.isSame(props.minDate, 'month'))\n        cell.end = !!(calEndDate && calTime.isSame(calEndDate, 'month'))\n      }\n\n      const isToday = now.isSame(calTime)\n      if (isToday) {\n        cell.type = 'today'\n      }\n\n      cell.text = index\n      cell.disabled = props.disabledDate?.(calTime.toDate()) || false\n    }\n  }\n  return rows\n})\n\nconst focus = () => {\n  currentCellRef.value?.focus()\n}\n\nconst getCellStyle = (cell: MonthCell) => {\n  const style = {} as any\n  const year = props.date.year()\n  const today = new Date()\n  const month = cell.text\n\n  style.disabled = props.disabledDate\n    ? datesInMonth(year, month, lang.value).every(props.disabledDate)\n    : false\n  style.current =\n    castArray(props.parsedValue).findIndex(\n      (date) =>\n        dayjs.isDayjs(date) && date.year() === year && date.month() === month\n    ) >= 0\n  style.today = today.getFullYear() === year && today.getMonth() === month\n\n  if (cell.inRange) {\n    style['in-range'] = true\n\n    if (cell.start) {\n      style['start-date'] = true\n    }\n\n    if (cell.end) {\n      style['end-date'] = true\n    }\n  }\n  return style\n}\n\nconst isSelectedCell = (cell: MonthCell) => {\n  const year = props.date.year()\n  const month = cell.text\n  return (\n    castArray(props.date).findIndex(\n      (date) => date.year() === year && date.month() === month\n    ) >= 0\n  )\n}\n\nconst handleMouseMove = (event: MouseEvent) => {\n  if (!props.rangeState.selecting) return\n\n  let target = event.target as HTMLElement\n  if (target.tagName === 'SPAN') {\n    target = target.parentNode?.parentNode as HTMLElement\n  }\n  if (target.tagName === 'DIV') {\n    target = target.parentNode as HTMLElement\n  }\n  if (target.tagName !== 'TD') return\n\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const column = (target as HTMLTableCellElement).cellIndex\n  // can not select disabled date\n  if (rows.value[row][column].disabled) return\n\n  // only update rangeState when mouse moves to a new cell\n  // this avoids frequent Date object creation and improves performance\n  if (row !== lastRow.value || column !== lastColumn.value) {\n    lastRow.value = row\n    lastColumn.value = column\n    emit('changerange', {\n      selecting: true,\n      endDate: props.date.startOf('year').month(row * 4 + column),\n    })\n  }\n}\nconst handleMonthTableClick = (event: MouseEvent | KeyboardEvent) => {\n  const target = (event.target as HTMLElement)?.closest(\n    'td'\n  ) as HTMLTableCellElement\n  if (target?.tagName !== 'TD') return\n  if (hasClass(target, 'disabled')) return\n  const column = target.cellIndex\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const month = row * 4 + column\n  const newDate = props.date.startOf('year').month(month)\n  if (props.selectionMode === 'months') {\n    if (event.type === 'keydown') {\n      emit('pick', castArray(props.parsedValue), false)\n      return\n    }\n    const newMonth = getValidDateOfMonth(\n      props.date.year(),\n      month,\n      lang.value,\n      props.disabledDate\n    )\n    const newValue = hasClass(target, 'current')\n      ? castArray(props.parsedValue).filter(\n          (d) =>\n            // Filter out the selected month only when both year and month match\n            // This allows remove same months from different years #20019\n            d?.year() !== newMonth.year() || d?.month() !== newMonth.month()\n        )\n      : castArray(props.parsedValue).concat([dayjs(newMonth)])\n    emit('pick', newValue)\n  } else if (props.selectionMode === 'range') {\n    if (!props.rangeState.selecting) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (props.minDate && newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  } else {\n    emit('pick', month)\n  }\n}\n\nwatch(\n  () => props.date,\n  async () => {\n    if (tbodyRef.value?.contains(document.activeElement)) {\n      await nextTick()\n      currentCellRef.value?.focus()\n    }\n  }\n)\n\ndefineExpose({\n  /**\n   * @description focus current cell\n   */\n  focus,\n})\n</script>\n"], "names": ["rows", "_openBlock", "_createElementBlock", "_unref", "_normalizeClass", "_createElementVNode", "_Fragment", "_renderList"], "mappings": ";;;;;;;;;;;;;;;;;AAwDA,IAAM,MAAA,EAAA,GAAK,aAAa,aAAa,CAAA,CAAA;AAErC,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAC9B,IAAA,MAAM,WAAW,GAAiB,EAAA,CAAA;AAClC,IAAA,MAAM,iBAAiB,GAAiB,EAAA,CAAA;AACxC,IAAA,MAAM,MAAS,GAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,UAAA,EAAA,CAAA,WAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACP,SACI,GAAA,GAAA,CAAA;AAGmB,MAC/B,EAAA;AACA,MAAA,EAAA;AAAqC,MACnC,EAAC;AAAA,KAAA,CACD,CAAC;AAAA,IAAA,MACA,OAAA,GAAA,GAAA,EAAA,CAAA;AAAA,IACH,MAAC,UAAA,GAAA,GAAA,EAAA,CAAA;AACD,IAAA,MAAM,eAAsB,CAAA,MAAA;AAC5B,MAAA,IAAM;AACN,MAAM,MAAA,KAAO,YAA8B,CAAA,KAAA,CAAA;AACzC,MAAA,MAAMA,WAAiB,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AAEvB,MAAM,KAAA,IAAA,CAAA,UAAc,CAAA,EAAA,CAAA,EAAA;AAEpB,QAAA,MAAA,GAAa,GAAA,KAAO,CAAA,CAAA,CAAA,CAAG;AACrB,QAAM,KAAA,IAAA,CAAA,UAAY,CAAA,EAAA,CAAA,EAAA,EAAA;AAClB,UAAA,MAAA,IAAa,GAAA,GAAO,CAAA,CAAA,CAAA,KAAQ,GAAA,CAAA,CAAA,CAAA,GAAA;AAC1B,YAAM,GAAA,EAAA,CAAA;AAAmB,YACvB,MAAK,EAAA,CAAA;AAAA,YACL,IAAQ,EAAA,QAAA;AAAA,YACR,OAAM,EAAA,KAAA;AAAA,YACN,KAAS,EAAA,KAAA;AAAA,YACT,GAAO,EAAA,KAAA;AAAA,YACP,IAAK,EAAA,CAAA,CAAA;AAAA,YACL,QAAM,EAAA,KAAA;AAAA,WAAA,CACN,CAAU;AAAA,UACZ,IAAA,CAAA,IAAA,GAAA,QAAA,CAAA;AAEA,UAAA,MAAY,KAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;AAEZ,UAAM,MAAA,OAAA,QAAgB,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACtB,UAAA,MAAM,UAAU,GAAM,KAAA,CAAA,UAAa,CAAM,OAAE,SAAW,CAAA,OAAA,IAAA,KAAA,CAAA,UAAA,CAAA,SAAA,IAAA,KAAA,CAAA,OAAA,IAAA,IAAA,CAAA;AAEtD,UAAM,IAAA,CAAA,OAAA,GAAA,CAAA,EAAA,KACE,CAAA,OAAA,IAAA,OACN,CAAA,2BACO,EAAA,OAAA,CAAA,IAAwB,UAAA,IAAA,OAC/B,CAAA,cAAA,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA,IAAA,CAAA,EAAA,KAAA,CAAA,OAAA,IAAA,OAAA,CAAA,cAAA,CAAA,KAAA,CAAA,OAAA,EAAA,OAAA,CAAA,IAAA,UAAA,IAAA,OAAA,CAAA,aAAA,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AAEF,UAAA,IAAA,CAAK,EACH,GAAA,KAAA,CAAC,OACC,KACA,IAAA,GAAA,KAAA,CAAA,GAAsB,EAAA,CAAA,aAAA,CAAA,UAAe,CAAA,EAAA;AAWzC,YAAA,IAAU,CAAA,KAAA,GAAA,CAAA,EAAA,UAAuB,IAAA,OAAA,CAAA,MAAa,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AAC5C,YAAA,IAAA,CAAK,WAAW,mBAAsB,CAAA,MAAA,CAAA,eAA0B,OAAA,CAAA,CAAA;AAChE,WAAA,MAAK;AAA4D,YAC5D,IAAA,CAAA,KAAA,GAAA,CAAA,EAAA,KAAA,CAAA,OAAA,IAAA,OAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACL,YAAK,IAAA,CAAA,GAAA,GAAA,CAAA,EAAS,qBAA2B,CAAA,MAAA,CAAA,UAAa,EAAA,OAAA,CAAS,CAAO,CAAA;AACtE,WAAA;AAA8D,UAChE,MAAA,OAAA,GAAA,GAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA;AAEA,UAAM,IAAA,OAAA,EAAA;AACN,YAAA,IAAa,CAAA,IAAA,GAAA,OAAA,CAAA;AACX,WAAA;AAAY,UACd,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA;AAEA,UAAA,IAAA,CAAK,QAAO,GAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,CAAA,KAAA,KAAA,CAAA;AACZ,SAAA;AAA0D,OAC5D;AAAA,MACF,OAAA,KAAA,CAAA;AACA,KAAOA,CAAAA,CAAAA;AAAA,IACT,MAAC,KAAA,GAAA,MAAA;AAED,MAAA,IAAM;AACJ,MAAA,CAAA,EAAA,GAAA,oBAA4B,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAAA,KAC9B,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAC,IAAoB,KAAA;AACxC,MAAA,MAAM,QAAQ,EAAC,CAAA;AACf,MAAM,MAAA,IAAA,GAAO,KAAM,CAAA,IAAA,CAAK,IAAK,EAAA,CAAA;AAC7B,MAAM,MAAA,KAAA,uBAAY,IAAK,EAAA,CAAA;AACvB,MAAA,MAAM,QAAQ,IAAK,CAAA,IAAA,CAAA;AAEnB,MAAA,KAAA,CAAM,QAAW,GAAA,KAAA,CAAM,YACnB,GAAA,YAAA,CAAa,IAAM,EAAA,KAAA,EAAO,IAAK,CAAA,KAAK,CAAE,CAAA,KAAA,CAAM,KAAM,CAAA,YAAY,CAC9D,GAAA,KAAA,CAAA;AACJ,MAAA,KAAA,CAAM,OACJ,GAAA,SAAA,CAAU,KAAM,CAAA,WAAW,CAAE,CAAA,SAAA,CAAA,CAAA,IAAA,KAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,IAAA,IAAA,CAAA,KAAA,EAAA,KAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MAAA,KAC1B,CAAA,KAAA,GACO,KAAA,CAAA,WAAY,EAAA,KAAU,IAAA,IAAW,KAAA,CAAA,QAAa,EAAA,KAAA,KAAY,CAAA;AAAA,MACpE,IAAK,IAAA,CAAA,OAAA,EAAA;AACP,QAAA,gBAAoB,CAAA,GAAA,IAAA,CAAA;AAEpB,QAAA,QAAkB,CAAA,KAAA,EAAA;AAChB,UAAA,kBAAoB,CAAA,GAAA,IAAA,CAAA;AAEpB,SAAA;AACE,QAAA,IAAA,IAAM;AAAgB,UACxB,KAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA;AAEA,SAAA;AACE,OAAA;AAAoB,MACtB,OAAA,KAAA,CAAA;AAAA,KACF,CAAA;AACA,IAAO,MAAA,cAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACT,MAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA;AAEA,MAAM,MAAA,KAAA,GAAA,IAAA,CAAA,IAAsC,CAAA;AAC1C,MAAM,OAAA,SAAa,CAAA,KAAA,CAAA,IAAU,CAAA,CAAA,SAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,EAAA,KAAA,IAAA,IAAA,IAAA,CAAA,KAAA,EAAA,KAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAC7B,KAAA,CAAA;AACA,IACE,MAAA,eAAgB,GAAA,CAAA,KAAM,KAAA;AAAA,MACpB;AAAmD,MACrD,IAAK,CAAA,KAAA,CAAA,UAAA,CAAA,SAAA;AAAA,QAET,OAAA;AAEA,MAAM,IAAA,MAAA,GAAA,KAAA,CAAA,MAAyC,CAAA;AAC7C,MAAI,IAAA,MAAO,CAAA,OAAA,KAAsB,MAAA,EAAA;AAEjC,QAAA,YAAmB,GAAA,MAAA,CAAA,UAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AACnB,OAAI;AACF,MAAA,IAAA,MAAA,CAAS,OAAO,KAAY,KAAA,EAAA;AAAA,QAC9B,MAAA,GAAA,MAAA,CAAA,UAAA,CAAA;AACA,OAAI;AACF,MAAA,IAAA,MAAA,CAAS,OAAO,KAAA,IAAA;AAAA,QAClB,OAAA;AACA,MAAI,MAAA,GAAA,oBAAyB,CAAA,QAAA,CAAA;AAE7B,MAAM,MAAA,MAAO,SAA0C,CAAA,SAAA,CAAA;AACvD,MAAA,IAAA,WAAgB,GAAgC,CAAA,CAAA,MAAA,CAAA,CAAA,QAAA;AAEhD,QAAA,OAAS;AAIT,MAAA,IAAI,GAAQ,KAAA,OAAA,CAAQ,KAAS,IAAA,MAAA,KAAW,WAAW,KAAO,EAAA;AACxD,QAAA,OAAA,CAAQ,KAAQ,GAAA,GAAA,CAAA;AAChB,QAAA,UAAA,CAAW,KAAQ,GAAA,MAAA,CAAA;AACnB,QAAA,IAAA,CAAK,aAAe,EAAA;AAAA,UAClB,SAAW,EAAA,IAAA;AAAA,UACX,OAAA,EAAS,MAAM,IAAK,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,KAAA,CAAM,GAAM,GAAA,CAAA,GAAI,MAAM,CAAA;AAAA,SAC3D,CAAA,CAAA;AAAA,OACH;AAAA,KACF,CAAA;AACA,IAAM,MAAA,qBAAA,GAAwB,CAAC,KAAsC,KAAA;AACnE,MAAM,IAAA,EAAA,CAAA;AAAwC,MAC5C,MAAA,MAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACF,IAAA,CAAA,MAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,MAAA,CAAA,OAAA,MAAA,IAAA;AACA,QAAI,OAAA;AACJ,MAAI,IAAA,QAAA,CAAS,MAAQ,EAAA,UAAU,CAAG;AAClC,QAAA;AACA,MAAM,MAAA,MAAO,SAA0C,CAAA,SAAA,CAAA;AACvD,MAAM,MAAA,GAAA,GAAA,iBAAkB,CAAA,QAAA,CAAA;AACxB,MAAA,MAAM,WAAgB,GAAA,CAAA,GAAA;AACtB,MAAI,MAAA,4BAAkC,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACpC,MAAI,IAAA,KAAA,CAAA,aAA0B,KAAA,QAAA,EAAA;AAC5B,QAAA,IAAA,KAAa,CAAA,IAAA,KAAA,SAAgB,EAAA;AAC7B,UAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AAAA,UACF,OAAA;AACA,SAAA;AAAiB,QACf,MAAA,QAAgB,GAAA,mBAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,EAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,YAAA,CAAA,CAAA;AAAA,QAChB,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA,EAAA,SAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,EAAA,MAAA,QAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,KAAA,EAAA,MAAA,QAAA,CAAA,KAAA,EAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QAAA,IACK,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;AAAA,OAAA,MACC,IAAA,KAAA,CAAA,aAAA,KAAA,OAAA,EAAA;AAAA,QACR,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA;AACA,UAAM,IAAA,CAAA,MAAA,EAAA,EAAW,SAAS,OAAQ,EAAA,OAAA,MACpB,EAAA,CAAA,CAAA;AAAmB,UAC3B,IAAC,CAAA,QAAA,EAAA,IAAA,CAAA,CAAA;AAAA,SAAA,MAAA;AAAA,UAAA,IAAA,KAAA,CAAA,OAAA,IAAA,OAAA,IAAA,KAAA,CAAA,OAAA,EAAA;AAAA,YAGC,IAAQ,CAAA,MAAA,EAAA,EAAe,OAAA,EAAA,aAAa,EAAA,OAAY,EAAA,OAAA,EAAS,CAAM,CAAA;AAAA,WAAA,MAAA;AAAA,YAEnE,IAAU,CAAA,MAAA,EAAA,EAAA,OAAiB,EAAA,gBAAU,EAAA,KAAc,CAAA,OAAE,EAAA,CAAA,CAAA;AACzD,WAAA;AAAqB,UACvB,IAAA,CAAA,QAAiB,EAAA,KAAA,CAAA,CAAA;AACf,SAAI;AACF,OAAA,MAAA;AACA,QAAA,IAAA,CAAA,aAAmB,CAAA,CAAA;AAAA,OAAA;AAEnB,KAAA,CAAA;AACE,IAAA,KAAA,CAAA,MAAA,UAAe,EAAA;AAA0C,MAAA,IAC3D,EAAO,EAAA,EAAA,CAAA;AACL,MAAA,IAAA,CAAA,EAAA,GAAA,SAAa,KAAE,KAAA,YAA2B,CAAA,GAAA,EAAA,CAAA,iBAAe,CAAA,aAAA,CAAA,EAAA;AAAA,QAC3D,MAAA,QAAA,EAAA,CAAA;AACA,QAAA,CAAA,EAAA,GAAA,cAAoB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAAA,OACtB;AAAA,KAAA,CACF,CAAO;AACL,IAAA,MAAA,CAAA;AAAkB,MACpB,KAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACE,OAAYC,SAAA,EAAA,EAAAC,kBAAA,CAAA,OAAA,EAAA;AAAA,QACA,IAAA,EAAA,MAAA;AACV,QAAA,YAAa,EAAAC,KAAO,CAAS,CAAA,CAAA,CAAA,gCAAyB,CAAA;AACpD,QAAA,KAAA,EAAAC,cAAe,CAAAD,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACf,QAAA,OAAA,EAAA,qBAA4B;AAAA,QAC9B,WAAA,EAAA,eAAA;AAAA,OACF,EAAA;AAAA,QACFE,kBAAA,CAAA,OAAA,EAAA;AAEA,UAAa,OAAA,EAAA,UAAA;AAAA,UAAA,GAAA,EAAA,QAAA;AAAA,SAAA,EAAA;AAAA,WAAAJ,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAI,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAAJ,KAAA,CAAA,IAAA,CAAA,EAAA,CAAA,GAAA,EAAA,GAAA,KAAA;AAAA,YAIX,OAAAF,SAAA,EAAA,EAAAC,kBAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,EAAA;AAAA,eACDD,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAI,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,IAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}