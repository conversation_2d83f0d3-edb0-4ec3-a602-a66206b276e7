2025-05-21 10:56:15|   0.0|  0.0|INFORM|Current FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-05-21 10:56:15|   0.0|  0.0|INFORM|System Time: 20250521025616 UTC
2025-05-21 10:56:15|   0.0|  0.0|INFORM|Workspace was last saved in FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME_HOME is 'C:\Program Files\FME\'
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME ESRI ArcGIS Server Edition (floating)
2025-05-21 10:56:15|   0.0|  0.0|INFORM|Permanent License.
2025-05-21 10:56:15|   0.0|  0.0|INFORM|Machine host name is: DESKTOP-9BLU554
2025-05-21 10:56:15|   0.0|  0.0|INFORM|OS Locale Name     : zh_CN
2025-05-21 10:56:15|   0.0|  0.0|INFORM|OS Locale Encoding : GBK
2025-05-21 10:56:15|   0.0|  0.0|INFORM|Process Encoding   : UTF-8
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME API version: '4.0 20230426'
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME Configuration: FME_BASE is 'no'
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME Configuration: FME_MF_DIR is 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU/'
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME Configuration: FME_MF_NAME is 'GIS数据通用质检软件1204.fmw'
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME Configuration: FME_PRODUCT_NAME is 'FME(R) 2023.1.0.0'
2025-05-21 10:56:15|   0.0|  0.0|INFORM|Operating System: Microsoft Windows 10 64-bit  (Build 19045)
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME Platform: WIN64
2025-05-21 10:56:15|   0.0|  0.0|INFORM|System Status: 156.03 GB of disk space available in the FME temporary folder (C:\Users\<USER>\AppData\Local\Temp)
2025-05-21 10:56:15|   0.0|  0.0|INFORM|System Status: 15.74 GB of physical memory available
2025-05-21 10:56:15|   0.0|  0.0|INFORM|System Status: 62.96 GB of virtual memory available
2025-05-21 10:56:15|   0.0|  0.0|INFORM|START - ProcessID: 18880, peak process memory usage: 90288 kB, current process memory usage: 89844 kB
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME Configuration: Command line arguments are `C:\Program Files\FME\fme.exe' `E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\GIS数据通用质检软件1204.fmw' `--X输出类型_config' `['问题数据库', '数据问题清单', '质量检查记录', '检查日志']' `--X输出路径_config' `E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3'
2025-05-21 10:56:15|   0.0|  0.0|INFORM|FME Configuration: Connection Storage: 'C:\Users\<USER>\AppData\Roaming\Safe Software\FME\'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Shared folders for formats are : C:\Program Files\FME\datasources;C:\Users\<USER>\Documents\FME\Formats
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Shared folders for transformers are : C:\Users\<USER>\AppData\Roaming\Safe Software\FME\Packages\23619-win64\transformers;C:\Program Files\FME\transformers;C:\Users\<USER>\AppData\Roaming\Safe Software\FME\FME Store\Transformers
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Shared folders for coordinate systems are : C:\Users\<USER>\Documents\FME\CoordinateSystems
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Shared folders for coordinate system exceptions are : C:\Users\<USER>\Documents\FME\CoordinateSystemExceptions
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Shared folders for coordinate system grid overrides are : C:\Users\<USER>\Documents\FME\CoordinateSystemGridOverrides
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Shared folders for CS-MAP transformation exceptions are : C:\Users\<USER>\Documents\FME\CsmapTransformationExceptions
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Shared folders for transformer categories are : C:\Users\<USER>\Documents\FME\TransformerCategories
2025-05-21 10:56:16|   0.0|  0.0|INFORM|Mapping File Identifier is: GIS数据通用质检软件1.0
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Reader Keyword is `MULTI_READER'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Writer Keyword is `MULTI_DEST'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Writer Group Definition Keyword is `MULTI_DEST_DEF'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Reader type is `MULTI_READER'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Writer type is `MULTI_WRITER'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Current working folder is `E:\GeoStream_Integration\frontend\backend'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Temporary folder is `C:\Users\<USER>\AppData\Local\Temp', set from environment variable `TEMP'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Cache folder is 'C:\Users\<USER>\AppData\Local\Temp'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: FME_HOME is `C:\Program Files\FME\'
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Start freeing memory when the process exceeds 47.22 GB
2025-05-21 10:56:16|   0.0|  0.0|INFORM|FME Configuration: Stop freeing memory when the process is below 35.42 GB
2025-05-21 10:56:16|   0.1|  0.1|INFORM|Creating writer for format: 
2025-05-21 10:56:16|   0.1|  0.0|INFORM|Creating reader for format: 
2025-05-21 10:56:16|   0.1|  0.0|INFORM|MULTI_READER(MULTI_READER): Will fail with first member reader failure
2025-05-21 10:56:16|   0.1|  0.0|INFORM|Using Multi Reader with keyword `MULTI_READER' to read multiple datasets
2025-05-21 10:56:16|   0.1|  0.0|INFORM|Using MultiWriter with keyword `MULTI_DEST' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-21 10:56:16|   0.1|  0.0|INFORM|Loaded module 'Geometry_func' from file 'C:\Program Files\FME\plugins/Geometry_func.dll'
2025-05-21 10:56:16|   0.1|  0.0|INFORM|FME API version of module 'Geometry_func' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.1|  0.0|INFORM|Loaded module 'ExcelStyleFactory' from file 'C:\Program Files\FME\plugins/ExcelStyleFactory.dll'
2025-05-21 10:56:16|   0.1|  0.0|INFORM|FME API version of module 'ExcelStyleFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.1|  0.0|INFORM|Loaded module 'StringReplacerFactory' from file 'C:\Program Files\FME\plugins/StringReplacerFactory.dll'
2025-05-21 10:56:16|   0.1|  0.0|INFORM|FME API version of module 'StringReplacerFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.1|  0.0|INFORM|Loaded module 'QueryFactory' from file 'C:\Program Files\FME\plugins/QueryFactory.dll'
2025-05-21 10:56:16|   0.1|  0.0|INFORM|FME API version of module 'QueryFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.2|  0.1|INFORM|Loaded module 'DuplicateRemoverFactory' from file 'C:\Program Files\FME\plugins/DuplicateRemoverFactory.dll'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|FME API version of module 'DuplicateRemoverFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.2|  0.0|INFORM|Loaded module 'StatisticsCalculatorFactory' from file 'C:\Program Files\FME\plugins/StatisticsCalculatorFactory.dll'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|FME API version of module 'StatisticsCalculatorFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.2|  0.0|INFORM|统计数据集、数据表个数_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|B_C统计要素个数_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'fme_feature_type.total_count'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|Loaded module 'AttributeKeeperFactory' from file 'C:\Program Files\FME\plugins/AttributeKeeperFactory.dll'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|FME API version of module 'AttributeKeeperFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.2|  0.0|INFORM|Loaded module 'CounterFactory' from file 'C:\Program Files\FME\plugins/CounterFactory.dll'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|FME API version of module 'CounterFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.2|  0.0|INFORM|StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated 'path_windows.total_count'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|Loaded module 'FeatureJoinerFactory' from file 'C:\Program Files\FME\plugins/FeatureJoinerFactory.dll'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|FME API version of module 'FeatureJoinerFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.2|  0.0|INFORM|Loaded module 'PythonFactory' from file 'C:\Program Files\FME\plugins/PythonFactory.dll'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|FME API version of module 'PythonFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:16|   0.2|  0.0|INFORM|Using Python interpreter from `C:\Program Files\ArcGIS\Pro\bin\Python\envs\arcgispro-py3\python39.dll' with PYTHONHOME `C:\Program Files\ArcGIS\Pro\bin\Python\envs\arcgispro-py3'
2025-05-21 10:56:16|   0.2|  0.0|INFORM|Python version 3.9 loaded successfully
2025-05-21 10:56:17|   0.7|  0.5|INFORM|ArcGIS for the Python interpreter initialized in 3.7274709 seconds
2025-05-21 10:56:17|   0.7|  0.0|INFORM|Adding folder `E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\' to the python path
2025-05-21 10:56:17|   1.1|  0.4|INFORM|... Last line repeated 2 times ...
2025-05-21 10:56:17|   1.1|  0.0|INFORM|Loaded module 'GeometryFilterFactory' from file 'C:\Program Files\FME\plugins/GeometryFilterFactory.dll'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|FME API version of module 'GeometryFilterFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.1|  0.0|INFORM|FME Configuration: Using FME Reprojection Engine
2025-05-21 10:56:17|   1.1|  0.0|INFORM|Loaded module 'ClipperFactory' from file 'C:\Program Files\FME\plugins/ClipperFactory.dll'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|FME API version of module 'ClipperFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C01空间基础检查_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'fme_feature_type.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020201_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_length.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020301_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_length.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|Loaded module 'UUID_func' from file 'C:\Program Files\FME\plugins/UUID_func.dll'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|FME API version of module 'UUID_func' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020302_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'angle.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020303_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '检查图层.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020401_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_count.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020401_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '检查图层.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|Loaded module 'GeometryValidationFactory' from file 'C:\Program Files\FME\plugins/GeometryValidationFactory.dll'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|FME API version of module 'GeometryValidationFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.1|  0.0|INFORM|Loaded module 'GQueryFactory' from file 'C:\Program Files\FME\plugins/GQueryFactory.dll'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|FME API version of module 'GQueryFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.1|  0.0|INFORM|Loaded module 'GeneralizeFactory' from file 'C:\Program Files\FME\plugins/GeneralizeFactory.dll'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|FME API version of module 'GeneralizeFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020402_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '检查图层.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C02空间几何检查_C020501_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '检查图层.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030101（点）必须不重合_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.sum'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030102（点）必须为单一部件_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030103（点-点）必须与其他图层的点重合_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'fme_feature_type.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030104（点-线）必须被线的端点覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'fme_feature_type.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030105（点-线）必须被线覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'fme_feature_type.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030106（点-面）必须位于面的边界上_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'fme_feature_type.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030107（点-面）必须位于面的内部_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated 'fme_feature_type.total_count'
2025-05-21 10:56:17|   1.1|  0.0|INFORM|C03空间拓扑_C030201（线）不能重叠_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.total_count'
2025-05-21 10:56:17|   1.2|  0.1|INFORM|C03空间拓扑_C030202（线）不能相交_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030202（线）不能相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030203（线）不能有悬挂点_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|Loaded module 'VertexCreatorFactory' from file 'C:\Program Files\FME\plugins/VertexCreatorFactory.dll'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|FME API version of module 'VertexCreatorFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_vertex_number.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030206（线）不能自重叠_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030207（线）不能自相交_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_overlaps.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030207（线）不能自相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030208（线）必须为单一部件_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.2|  0.0|INFORM|C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.1|INFORM|C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|Loaded module 'NetworkFactory' from file 'C:\Program Files\FME\plugins/NetworkFactory.dll'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|FME API version of module 'NetworkFactory' matches current internal version (4.0 20230426)
2025-05-21 10:56:17|   1.3|  0.0|INFORM|C03空间拓扑_C030213（线-线）不能与其他图层的线相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|C03空间拓扑_C030213（线-线）不能与其他图层的线相交_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|C03空间拓扑_C030215（线-面）必须被面的边界覆盖_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|C03空间拓扑_C030216（线-面）必须位于面的内部_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|C03空间拓扑_C030301（面）不能有空隙_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|C03空间拓扑_C030302（面）不能重叠_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:17|   1.3|  0.0|INFORM|C03空间拓扑_C030303（面）不能自相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_vertex_number.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030305（面）不能有孔洞_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030306（面）必须为单一部件_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030307（面-点）必须包含点_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030308（面-点）必须包含一个点_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030309（面-线）边界必须被线覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '_list.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|StatisticsCalculator_3 (StatisticsCalculatorFactory): The following statistics will be calculated 'JCXDM.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|StatisticsCalculator_4 (StatisticsCalculatorFactory): The following statistics will be calculated 'JCXDM.total_count'
2025-05-21 10:56:18|   1.3|  0.0|INFORM|StatisticsCalculator_5 (StatisticsCalculatorFactory): The following statistics will be calculated 'JCXDM.total_count'
2025-05-21 10:56:18|   1.4|  0.1|INFORM|StatisticsCalculator_6 (StatisticsCalculatorFactory): The following statistics will be calculated '问题数量.sum'
2025-05-21 10:56:18|   1.4|  0.0|INFORM|StatisticsCalculator (StatisticsCalculatorFactory): The following statistics will be calculated '检查代码.total_count'
2025-05-21 10:56:18|   1.4|  0.0|INFORM|Emptying factory pipeline
2025-05-21 10:56:18|   1.5|  0.1|STATS |参数传入_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |参数传入_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |参数传入_CREATED Brancher -1 141 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator CREATED Splitter (TeeFactory): Cloned 1 input feature(s) into 3 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_CREATED_0_GJAgmIF9G6c= Brancher -1 448 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_CREATED_1_Exx7idfjvCA= Brancher -1 445 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_CREATED_2_goANeUpBIDA= Brancher -1 451 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_2_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_2_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_2_CREATED Brancher -1 436 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_3_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_3_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_3_CREATED Brancher -1 439 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_4_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_4_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_4_CREATED Brancher -1 442 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_7_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_7_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_7_CREATED Brancher -1 454 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_8_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_8_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_8_CREATED Brancher -1 457 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Creating writer for format: 
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Using MultiWriter with keyword `FeatureWriter_4' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Creating writer for format: Microsoft Excel
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Trying to find a DYNAMIC plugin for writer named `XLSXW'
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Loaded module 'XLSXW' from file 'C:\Program Files\FME\plugins/xlsx.dll'
2025-05-21 10:56:18|   1.5|  0.0|INFORM|FME API version of module 'xlsx' matches current internal version (4.0 20230426)
2025-05-21 10:56:18|   1.5|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Writer `FeatureWriter_4_0' of type `XLSXW' using group definition keyword `FeatureWriter_4_0_DEF'
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Row(s) from 1 to 1 will be frozen
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Use Attribute Names As Column Positions is set to 'no' for sheet '_log' in dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\检查日志_**************.xlsx' 
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Opening dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\检查日志_**************.xlsx'...
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Reading existing workbook formatting. Workbook contains 1 fonts and 1 cell formats
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Sheet '_log' has writer mode 'INSERT'
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Sheet '_log' has Output field names set to 'yes'
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Sheet '_log' will start writing to row '1' and column 'A' relative to its start location. Sheets have a starting location of row 1, column 1, named range starting locations can be specified
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Outputting field names for sheet '_log'
2025-05-21 10:56:18|   1.5|  0.0|STATS |LogMessageStreamer_LOGFILE Brancher -1 168 (BranchingFactory): Branched 137 input features -- 137 features routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_5_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_5_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_5_CREATED Brancher -1 502 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_6_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_6_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_6_CREATED Brancher -1 504 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_9_XML_Creator (CreationFactory): Created 1 features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_9_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_9 CREATED Splitter (TeeFactory): Cloned 1 input feature(s) into 4 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_9_CREATED_0_G2t7NV/eQVk= Brancher -1 510 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_9_CREATED_1_XcIZYHY94x0= Brancher -1 511 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_9_CREATED_2_tbLxo5uTBEM= Brancher -1 512 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |Creator_9_CREATED_3_8Uw4g6P6Exs= Brancher -1 513 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-21 10:56:18|   1.5|  0.0|STATS |_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 152 input feature(s) into 152 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |VariableRetriever_4 (TeeFactory): Cloned 137 input feature(s) into 137 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |VariableRetriever_3 (TeeFactory): Cloned 137 input feature(s) into 137 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_13 (TestFactory): Tested 137 input feature(s) -- 137 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Saving changes to dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\检查日志_**************.xlsx'
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Workbook contains 2 fonts and 4 cell formats after writing
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Excel Writer: Closing dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\检查日志_**************.xlsx'...
2025-05-21 10:56:18|   1.5|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:18|   1.5|  0.0|INFORM|Feature output statistics for `XLSXW' writer using keyword `FeatureWriter_4_0':
2025-05-21 10:56:18|   1.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:18|   1.5|  0.0|STATS |                               Features Written
2025-05-21 10:56:18|   1.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:18|   1.5|  0.0|STATS |_log                                                                       137
2025-05-21 10:56:18|   1.5|  0.0|STATS |==============================================================================
2025-05-21 10:56:18|   1.5|  0.0|STATS |Total Features Written                                                     137
2025-05-21 10:56:18|   1.5|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_34 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_7 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_33 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |FeatureReader_9 <SCHEMA> Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |FeatureReader_9 <OTHER> Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_4 PASSED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |统计数据集、数据表个数 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |统计数据集、数据表个数_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |统计数据集、数据表个数_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |统计数据集、数据表个数_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |统计数据集、数据表个数_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:18|   1.5|  0.0|INFORM|统计数据集、数据表个数_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:18|   1.5|  0.0|STATS |统计数据集、数据表个数_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |统计数据集、数据表个数 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_24 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Junction_7 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Junction_7 Output Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B_C统计要素个数 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B_C统计要素个数_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B_C统计要素个数_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|INFORM|B_C统计要素个数_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:18|   1.5|  0.0|STATS |B_C统计要素个数_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B_C统计要素个数 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_29 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_22 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |FeatureReader_8 <SCHEMA> Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式_AttributeExposer_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式_无法识别的表类型1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库模板转为配置表形式 无法识别的表类型 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_18 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |ListExploder_2 (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_20 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_28 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_21 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_36 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_AttributeSplitter_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_ListElementCounter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_AttributeCreator_3 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_Tester_8 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_FeatureMerger (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_Tester_9 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A文件夹转换为配置表形式 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_9 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_27 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_23 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester FAILED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_35 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_35 PASSED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|INFORM|StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_12 (TestFactory): Tested 1 input feature(s) -- 1 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_31 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_8 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Junction (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查 模板 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查 待检目录 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_模板1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_待检目录1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_AttributeSplitter_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_ListElementCounter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Junction_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Junction_3 Output Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_FeatureJoiner JOINED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Junction (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_AttributeExposer_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_AttributeExposer_3 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_AttributeKeeper_3 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Junction_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_FeatureMerger (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_A010103_缺失必选目录1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_A010102_冗余目录1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_A010101_层级错误1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查_A010104_父级错误1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查 A010101_层级错误 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查 A010102_冗余目录 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查 A010103_缺失必选目录 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |A目录规范性检查 A010104_父级错误 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Junction_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_30 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_10 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_17 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |Junction_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 待检数据库 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 模板 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_待检数据库1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_模板1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_AttributeExposer_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_AttributeExposer_3 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_AttributeExposer_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_AttributeCreator_19 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_Tester_24 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_FeatureJoiner UNJOINED_LEFT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_Tester_2 (Disabled) Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_FeatureJoiner_2 JOINED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_Tester_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_B010101_数据集冗余1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_B010102_缺失必选数据集1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_B010201_数据表冗余1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_B010202_缺少必选数据表1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_B010203_数据表类型错误1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查_B010204_表与数据集的组织结构异常1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 B010101_数据集冗余 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 B010102_缺失必选数据集 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 B010201_数据表冗余 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 B010202_缺少必选数据表 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 B010203_数据表类型错误 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B01数据库分层检查 B010204_表与数据集的组织结构异常 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Junction_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Tester_19 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构 配置表 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构 数据库 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_配置表1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_数据库1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_StringSearcher (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_DuplicateFilter UNIQUE Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_处理后1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_无法识别的字段类型1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构_字段名重复1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构 字段名重复 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构 无法识别的字段类型 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |处理数据表结构 处理后 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |Junction_5 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范 待检数据库 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范 模板 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_待检数据库1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_模板1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_AttributeExposer OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Tester_15 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Tester_15 PASSED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Python读取字段值 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Python读取字段值_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Python读取字段值_Tester_27 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeCreator_6 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:18|   1.5|  0.0|STATS |B02数据表规范_Python读取字段值_Tester_25 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  3.1|WARN  |B02数据表规范_Python读取字段值_PythonCaller (PythonFactory): PythonFactory script changed the current FME process locale from 'Chinese (Simplified)_China.utf8' to 'Chinese (Simplified)_China.936' and FME has restored it back to 'Chinese (Simplified)_China.utf8'. It is undefined behavior to change locale and doing so may cause unexpected errors. For more information, visit http://fme.ly/PythonLocaleError
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_ListExploder_3 (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeSplitter_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_Tester_26 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeExposer_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_ListExploder_2 (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeSplitter_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_AttributeSplitter_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_值域待检查1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值_要素唯一性待检查1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值 要素唯一性待检查 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段值 值域待检查 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_FeatureMerger_2 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_FeatureMerger_2 MERGED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_TestFilter_INPUT (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_TestFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_9 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_AttributeSplitter_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_12 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_11 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_13 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_14 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_10 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_DuplicateFilter_2 UNIQUE Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_AttributeExposer_2 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_StringSearcher (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_AttributeKeeper_2 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_FeatureMerger_3 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Junction_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Junction_2 Output Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Junction (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_FeatureMerger_4 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_FeatureJoiner_2 JOINED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_7 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名_AttributeExposer_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名_ListExploder_2 (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名_AttributeSplitter_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名_AttributeSplitter_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Python读取字段别名 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_FeatureMerger (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_Tester_8 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_B020101_表名与数据表规范不符1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_B020102_字段名与数据表规范不符1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_B020103_字段类型与数据表规范不符1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_B020104_字段长度与数据表规范不符1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_B020107_数据表的字段别名错误1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_B010205_字段值不唯一1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范_B010206_字段值与数据表规范值域不符1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范 B020101_表名与数据表规范不符 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范 B020102_字段名与数据表规范不符 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范 B020103_字段类型与数据表规范不符 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范 B020104_字段长度与数据表规范不符 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范 B010205_字段值不唯一 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范 B010206_字段值与数据表规范值域不符 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |B02数据表规范 B020107_数据表的字段别名错误 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |Junction_6 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |Tester_32 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |Tester_11 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查 配置表 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查 待检查数据 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_配置表1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_待检查数据1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_AttributeExposer_2 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_GeometryFilter_3 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_Reprojector_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_CoordinateSystemExtractor (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_CoordinateSystemDescriptionConverter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_Reprojector_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_AttributeExposer OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_AttributeSplitter_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|INFORM|C01空间基础检查_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_GeometryFilter_2 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_VariableRetriever_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_FeatureMerger (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_ListBuilder_fme_type_remover (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_ListBuilder (ListFactory): Combined 0 input feature(s) into 0 output feature(s), including 0 singleton(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_VariableRetriever (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_C010101_图层要素超出工作范围1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查_C010102_图层不符合坐标系统要求1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查 C010101_图层要素超出工作范围 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C01空间基础检查 C010102_图层不符合坐标系统要求 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |Junction_8 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |Tester_26 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查 待检查 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查 配置表 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_配置表1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_待检查1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_Junction_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_Junction_2 Output Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_Junction (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_Junction Output Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_FeatureMerger (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter Point Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter Area Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_LengthCalculator_LengthCalculatorInput (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_LengthCalculator_LengthCalculator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_LengthCalculator_Rejector (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|INFORM|C02空间几何检查_C020201_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020201 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_FeatureMerger_3 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_2 Point Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_2 Curve Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_2 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020301 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020301_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:23|   4.6|  0.0|STATS |C02空间几何检查_C020301_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_Counter OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_Chopper (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_LengthCalculator_LengthCalculatorInput (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_LengthCalculator_LengthCalculator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_LengthCalculator_Rejector (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|INFORM|C02空间几何检查_C020301_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_DuplicateFilter DUPLICATE Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_FeatureJoiner (Disabled) Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_FeatureMerger_4 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_3 Point Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_3 Curve Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_3 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator INPUT_Polygons Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_INPUT_Polygons1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_TestFilter_INPUT (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_TestFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Junction_15 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Junction_15 Output Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Orientor (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Orientor_Rejector (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Chopper_7 (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Intersector_4_ALL (IntersectionFactory): Processed 0 input feature(s), resulting in 0 segment(s) and 0 point(s) output (%4 overlapping segment(s) merged)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_ListSorter_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Junction_16 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_AttributeRemover (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_AttributeRemover OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_TestFilter_6_INPUT (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_TestFilter_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_TestFilter_6 Small<space>Anglepoints Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_TestFilter_6 <lt>UNFILTERED<gt> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_FeatureMerger_2 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_Junction (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_OUTPUT_Angle_Points1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_OUTPUT_Small_Angle_Points1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_OUTPUT_Polygons_with_small_Angles1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator_OUTPUT_Rejected1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator OUTPUT_Angle_Points Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator OUTPUT_Small_Angle_Points Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator OUTPUT_Polygons_with_small_Angles Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_AngleCalculator OUTPUT_Rejected Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|INFORM|C02空间几何检查_C020302_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_Tester_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_FeatureMerger_5 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_4 Point Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_4 Curve Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_4 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_AreaCalculator_AreaCalculatorInput (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_AreaCalculator_AreaCalculator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_AreaCalculator_Rejector (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|INFORM|C02空间几何检查_C020303_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_Tester_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_FeatureMerger_6 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_5 Point Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_5 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_Counter OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_Chopper (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.6|  0.0|INFORM|C02空间几何检查_C020401_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_FeatureMerger (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   4.6|  0.0|INFORM|C02空间几何检查_C020401_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_Tester_7 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_FeatureMerger_2 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_6 Point Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_6 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_Tester FAILED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_Counter_2 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_PathSplitter (DeaggregateFactory): Split 0 input path feature(s) into 0 segments
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_Counter_3 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_AttributeExposer_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|INFORM|C02空间几何检查_C020402_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_StatisticsCalculator_2 COMPLETE Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_Tester_8 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_FeatureMerger_7 (ReferenceFactory): Total Results: 0 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_GeometryFilter_7 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501 输入 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_输入1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_TESTZDEFAULT (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_TESTMODE (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_LIST (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_SPECIFIC (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_SPECIFIC_X (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_SPECIFIC_Y (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_SPECIFIC_Z_ROUTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_CoordinateExtractor_SPECIFIC_Z (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.6|  0.0|INFORM|C02空间几何检查_C020501_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_输出1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501 输出 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020201_存在碎线1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020301_存在超短边1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020302_存在尖锐角1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020303_存在微小面1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020401_节点数不符合要求1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020402_存在圆弧1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020501_包含Z值1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查_C020502_包含M值1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查 C020201_存在碎线 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.6|  0.0|STATS |C02空间几何检查 C020301_存在超短边 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.1|STATS |C02空间几何检查 C020302_存在尖锐角 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C02空间几何检查 C020303_存在微小面 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C02空间几何检查 C020401_节点数不符合要求 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C02空间几何检查 C020402_存在圆弧 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C02空间几何检查 C020501_包含Z值 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C02空间几何检查 C020502_包含M值 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |Junction_9 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |Tester_25 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑 配置表 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_配置表1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_处理打开图层名 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_处理打开图层名_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_处理打开图层名_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_处理打开图层名_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_处理打开图层名 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_TestFilter_INPUT (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_TestFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_TestFilter <u65e0><u6cd5><u88ab><u8bc6><u522b><u7684><u68c0><u67e5><u89c4><u5219> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_PointOnPointOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_PointOnPointOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030101（点）必须不重合_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030101（点）必须不重合 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030102（点）必须为单一部件_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030102（点）必须为单一部件 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_PointOnPointOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_PointOnPointOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030103（点-点）必须与其他图层的点重合_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030103（点-点）必须与其他图层的点重合 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_GeometryFilter Curve Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Counter OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_AreaOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_AreaOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Snipper_2_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_PointOnPointOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_PointOnPointOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030104（点-线）必须被线的端点覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030104（点-线）必须被线的端点覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030105（点-线）必须被线覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030105（点-线）必须被线覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_Chopper (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030106（点-面）必须位于面的边界上_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030106（点-面）必须位于面的边界上 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_AttributeKeeper OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_PointOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_PointOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_Chopper (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030107（点-面）必须位于面的内部_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030107（点-面）必须位于面的内部 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030201（线）不能重叠_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030201（线）不能重叠 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_Tester PASSED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030202（线）不能相交_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030202（线）不能相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_point1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交_line1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交 point Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030202（线）不能相交 line Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_GeometryFilter Curve Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Snipper_2_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030203（线）不能有悬挂点_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030203（线）不能有悬挂点 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点） Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_GeometryFilter_2 Arc Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_GeometryFilter_2 OrientedArc Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer INPUT Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_INPUT1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AttributeValueMapper (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_GEOMETRYFILTER_InputPassThrough (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_GEOMETRYFILTER_RouterPrepper (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_GEOMETRYFILTER_Router (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COUNTER_3_Rejector (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATECOUNTER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Liner (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Arear (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Arcer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Texter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Ellipser (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Nuller (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LoopTester (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_PASSED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_FAILED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_NON-LINEAR1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter PASSED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter FAILED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter NON-LINEAR Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_ATTRIBUTERENAMER_6 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_CHOPPER (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COUNTER_Rejector (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_GeometryFilter_InputPassThrough (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_GeometryFilter_RouterPrepper (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_GeometryFilter_Router (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Liner (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Arear (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Arcer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Texter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Ellipser (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Nuller (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LoopTester (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_PASSED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_FAILED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_NON-LINEAR1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter PASSED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter FAILED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter NON-LINEAR Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LOGGER_2_Logger (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LOGGER_2_Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_COORDINATEFETCHER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_COORDINATEFETCHER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_6 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_TESTER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_5 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_ATTRIBUTEREMOVER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_Not_Line1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_AZIMUTH1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator Not_Line Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator AZIMUTH Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LENGTHCALCULATOR_LengthCalculatorInput (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LENGTHCALCULATOR_LengthCalculator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LENGTHCALCULATOR_Rejector (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom INPUT Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_INPUT1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GEOMETRYCOERCER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GEOMETRYCOERCER_2_AreaFixer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GEOMETRYCOERCER_2_Renamer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Liner (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Arear (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Arcer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Texter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Ellipser (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Nuller (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LoopTester (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_PASSED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_FAILED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_NON-LINEAR1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 PASSED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 FAILED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 NON-LINEAR Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_COORDINATECOUNTER_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_TESTZDEFAULT (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_TESTMODE (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_LIST (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_X (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_Y (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_Z_ROUTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_Z (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GeometryRemover (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_AttributeManager OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTEREMOVER_5 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_Output_Labels_Test_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTECREATOR_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTESETTER_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_TESTER_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_TESTER_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_TESTER_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_StrokeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_Liner (LabelFactory): Processed 0 input feature(s).  Output 0 Point feature(s) and a total of 0 features
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_PipCreator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_PipSplitter (PIPComponentsFactory): Split 0 point and polygon features into separate points and polygons
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTEREMOVER_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_POINT1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABEL1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom POINT Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom LABEL Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COUNTER_2_Rejector (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_POINTONLINEOVERLAYER (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_POINTONLINEOVERLAYER (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.7|  0.0|INFORM|C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_MATCHER (MatchingFactory): Upper bound on the number of comparisons is 0. Fewer comparisons may be made, and the worst case will not exceed this upper bound
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_MATCHER (MatchingFactory): Found 0 matching group(s) of features in 0 input feature(s). 0 feature(s) did not have any matches
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTELEMENTCOUNTER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTSORTER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_TESTER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_TESTER_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_5_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_5_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.7|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_6_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.1|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_6_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_2_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_2_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_EXPRESSIONEVALUATOR_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AttributeRemover_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_EXPRESSIONEVALUATOR (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_EXPRESSIONEVALUATOR_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_TESTZDEFAULT (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_TESTMODE (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_LIST (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_X (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_Y (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_Z_ROUTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_Z (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_ATTRIBUTEREMOVER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AttributeRemover (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_Vertex1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_<Rejected>1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer Vertex Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer <Rejected> Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点）_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030204（线）不能有伪结点（检查单一要素的多余节点） Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性） Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_GeometryFilter Curve Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Snipper_2_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_PointOnLineOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_PointOnLineOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_TESTZDEFAULT (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_TESTMODE (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_LIST (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_SPECIFIC (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_SPECIFIC_X (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_SPECIFIC_Y (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_SPECIFIC_Z_ROUTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_CoordinateExtractor_SPECIFIC_Z (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_ListExploder_2 (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性）_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030205（线）不能有伪结点（检查要素连通性） Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030206（线）不能自重叠_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030206（线）不能自重叠 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_Counter OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030207（线）不能自相交_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030207（线）不能自相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_point1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交_line1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交 point Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030207（线）不能自相交 line Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030208（线）必须为单一部件_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030208（线）必须为单一部件 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_GeometryFilter Curve Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_modeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_vertexFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_locationVerifier (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_DeaggFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_Aggfilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_Typefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_DeaggInput (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_MultiTypefilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_DeaggMultis (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_DeaggNullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_Input (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_SplitRemnants (DeaggregateFactory): Split 0 input feature(s) into 0 point-in-polygon feature(s), 0 donut polygon(s), 0 polygon(s), 0 line(s), 0 point(s), and 0 aggregate(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_NullNuker (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Snipper_2_Outputter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_PointOnPointOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_PointOnPointOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030210（线-点）端点必须被另一图层的点覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_LineOnLineOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_LineOnLineOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030211（线-线）必须被其他图层的线覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_LineOnLineOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_LineOnLineOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.8|  0.0|INFORM|C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030212（线-线）不能与其他图层的线重叠 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.8|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.1|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Aggregator (AggregateFactory): Combined 0 input feature(s) into 0 output feature(s), including 0 singleton(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Aggregator AGGREGATE Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_LineOnLineOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_LineOnLineOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_LineOnLineOverlayer_2 LINE Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Tester_3 PASSED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030213（线-线）不能与其他图层的线相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030213（线-线）不能与其他图层的线相交_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_point1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交_line1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交 point Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030213（线-线）不能与其他图层的线相交 line Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_Chopper (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_LineOnLineOverlayer_3 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_LineOnLineOverlayer_3 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_LineOnLineOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_LineOnLineOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030215（线-面）必须被面的边界覆盖_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030215（线-面）必须被面的边界覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_LineOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_LineOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030216（线-面）必须位于面的内部_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030216（线-面）必须位于面的内部 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030301（面）不能有空隙_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030301（面）不能有空隙 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_AreaOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_AreaOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030302（面）不能重叠_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030302（面）不能重叠 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030303（面）不能自相交_StatisticsCalculator_2 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交_point1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030303（面）不能自相交 point Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点） Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_Reprojector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer INPUT Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_INPUT1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AttributeValueMapper (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_GEOMETRYFILTER_InputPassThrough (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_GEOMETRYFILTER_RouterPrepper (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_GEOMETRYFILTER_Router (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COUNTER_3_Rejector (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATECOUNTER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Liner (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Arear (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Arcer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Texter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Ellipser (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LineFilter_Nuller (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_LoopTester (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_PASSED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_FAILED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter_NON-LINEAR1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter PASSED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter FAILED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LoopFilter NON-LINEAR Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_ATTRIBUTERENAMER_6 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_CHOPPER (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COUNTER_Rejector (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_GeometryFilter_InputPassThrough (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_GeometryFilter_RouterPrepper (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_GeometryFilter_Router (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Liner (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Arear (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Arcer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Texter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Ellipser (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LineFilter_Nuller (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_LoopTester (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_PASSED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_FAILED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter_NON-LINEAR1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter PASSED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter FAILED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LoopFilter NON-LINEAR Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LOGGER_2_Logger (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_LOGGER_2_Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_COORDINATEFETCHER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_COORDINATEFETCHER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_6 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_TESTER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_5 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_EXPRESSIONEVALUATOR_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_ATTRIBUTEREMOVER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_Not_Line1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator_AZIMUTH1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator Not_Line Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AzimuthCalculator AZIMUTH Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LENGTHCALCULATOR_LengthCalculatorInput (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LENGTHCALCULATOR_LengthCalculator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LENGTHCALCULATOR_Rejector (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom INPUT Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_INPUT1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GEOMETRYCOERCER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GEOMETRYCOERCER_2_AreaFixer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GEOMETRYCOERCER_2_Renamer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 LINE Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LINE1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Liner (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Arear (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Arcer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Texter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Ellipser (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LineFilter_Nuller (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_LoopTester (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_PASSED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_FAILED1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2_NON-LINEAR1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 PASSED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 FAILED Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LoopFilter_2 NON-LINEAR Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_COORDINATECOUNTER_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_TESTZDEFAULT (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_TESTMODE (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_LIST (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_X (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_Y (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_Z_ROUTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_CoordinateExtractor_SPECIFIC_Z (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_GeometryRemover (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_AttributeManager OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTEREMOVER_5 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_Output_Labels_Test_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTECREATOR_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTESETTER_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_TESTER_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_TESTER_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_TESTER_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_StrokeFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_Pointer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_Liner (LabelFactory): Processed 0 input feature(s).  Output 0 Point feature(s) and a total of 0 features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_PipCreator (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABELPOINTREPLACER_2_PipSplitter (PIPComponentsFactory): Split 0 point and polygon features into separate points and polygons
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_ATTRIBUTEREMOVER_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_POINT1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom_LABEL1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom POINT Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_VertexCounter_Custom LABEL Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COUNTER_2_Rejector (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_POINTONLINEOVERLAYER (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_POINTONLINEOVERLAYER (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_MATCHER (MatchingFactory): Upper bound on the number of comparisons is 0. Fewer comparisons may be made, and the worst case will not exceed this upper bound
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_MATCHER (MatchingFactory): Found 0 matching group(s) of features in 0 input feature(s). 0 feature(s) did not have any matches
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTELEMENTCOUNTER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTSORTER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_TESTER_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_TESTER_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_5_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_5_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_6_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_6_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_2_IntTester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_LISTINDEXER_2_ListIndexer (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_EXPRESSIONEVALUATOR_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AttributeRemover_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_TESTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_EXPRESSIONEVALUATOR (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_EXPRESSIONEVALUATOR_4 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_TESTZDEFAULT (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_TESTMODE (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_LIST (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_X (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_Y (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_Z_ROUTER (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_COORDINATEFETCHER_SPECIFIC_Z (ExecuteFunctionFactory): Executed FME Functions on 0 input feature(s) -- 0 feature(s) completed and 0 feature(s) rejected
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_ATTRIBUTEREMOVER (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_AttributeRemover (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_Vertex1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer_<Rejected>1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer Vertex Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_PolylineAnalyzer <Rejected> Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点）_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030304（面）不能有伪结点（检查单一要素的多余节点） Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030305（面）不能有孔洞_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030305（面）不能有孔洞 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030306（面）必须为单一部件_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030306（面）必须为单一部件 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_GeometryFilter Area Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_PointOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_PointOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030307（面-点）必须包含点_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030307（面-点）必须包含点 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_GeometryFilter Area Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_PointOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_PointOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_PointOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_PointOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030308（面-点）必须包含一个点_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030308（面-点）必须包含一个点 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_Chopper (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   4.9|  0.0|INFORM|C03空间拓扑_C030309（面-线）边界必须被线覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030309（面-线）边界必须被线覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   4.9|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.1|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_AreaOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_AreaOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_AreaOnAreaOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_AreaOnAreaOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|INFORM|C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310（面-面）必须被其他图层的面覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_AreaOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_AreaOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_AreaOnAreaOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_AreaOnAreaOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|INFORM|C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312（面-面）必须与其他图层的面相互覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_AreaOnAreaOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_AreaOnAreaOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_AreaOnAreaOverlayer_2 (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_AreaOnAreaOverlayer_2 (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|INFORM|C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313（面-面）不得与其他图层的面重叠 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖 Input Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_Input1747792825 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_AttributeSplitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_ListExploder (ElementFactory): Split out 0 list elements from 0 input features
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_Chopper (ChoppingFactory): Processed 0 input feature(s), of which 0 feature(s) were chopped up into 0 piece(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_LineOnLineOverlayer (OverlayFactory): Input Summary:  0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_LineOnLineOverlayer (OverlayFactory): Output Summary: 0 area(s), 0 point(s), and 0 line(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|INFORM|C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖_Output1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314（面-面）边界必须被其他面图层的边界覆盖 Output Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030101_重合点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030102_多部件点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030103_点与其他点图层不重合1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030104_点未被线图层的端点覆盖1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030105_点未被线图层覆盖1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030106_点不在面图层的边界上1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030107_点不在面图层的内部1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030201_重叠线1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030202_相交线_交点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030202_相交线_重叠部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030203_线的悬挂点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030204_线的伪节点_多余节点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030205_线的伪节点_连通性较差1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030206_自重叠线_自重叠部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030207_自相交线_交点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030207_自相交线_重叠部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030208_多部件线1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030210_线的端点未被点图层覆盖1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030211_线未被其他线图层覆盖_超出部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030212_线与其他线图层重叠_重叠部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030213_线与其他线图层相交_交点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030213_线与其他线图层相交_重叠部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030215_线未被面图层的边界覆盖_超出部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030216_线超出面图层的内部_超出部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030301_面存在空隙1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030302_重叠面1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030303_面的边界自相交_边界交点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030304_面的伪节点_多余节点1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030305_孔洞多边形1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030306_多部件多边形1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030307_面的内部未覆盖点图层1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030308_面的内部未覆盖或覆盖多个点图层的要素1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030309_面的边界未被线图层覆盖_超出边界1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030310_面未被其他面图层覆盖_超出部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030312_面与其他面图层不互相覆盖_差异部分并集1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030313_面与其他面图层重叠_重叠部分1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑_C030314_面的边界未被其他面图层的边界覆盖_超出边界1747792825 Output Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030101_重合点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030102_多部件点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030103_点与其他点图层不重合 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030104_点未被线图层的端点覆盖 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030105_点未被线图层覆盖 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030106_点不在面图层的边界上 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030107_点不在面图层的内部 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030201_重叠线 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030202_相交线_交点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030202_相交线_重叠部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030203_线的悬挂点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030204_线的伪节点_多余节点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030205_线的伪节点_连通性较差 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030206_自重叠线_自重叠部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030207_自相交线_交点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030207_自相交线_重叠部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030208_多部件线 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030210_线的端点未被点图层覆盖 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030211_线未被其他线图层覆盖_超出部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030212_线与其他线图层重叠_重叠部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030213_线与其他线图层相交_交点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030213_线与其他线图层相交_重叠部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030215_线未被面图层的边界覆盖_超出部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030216_线超出面图层的内部_超出部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030301_面存在空隙 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030302_重叠面 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030303_面的边界自相交_边界交点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030304_面的伪节点_多余节点 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030305_孔洞多边形 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030306_多部件多边形 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030307_面的内部未覆盖点图层 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030308_面的内部未覆盖或覆盖多个点图层的要素 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030309_面的边界未被线图层覆盖_超出边界 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030310_面未被其他面图层覆盖_超出部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030312_面与其他面图层不互相覆盖_差异部分并集 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030313_面与其他面图层重叠_重叠部分 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |C03空间拓扑 C030314_面的边界未被其他面图层的边界覆盖_超出边界 Output Renamer/Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |Junction_10 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |Tester_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|STATS |VariableRetriever_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |VariableRetriever (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |AttributeCreator_4 OUTPUT Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |Tester_16 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|STATS |Sorter_3 (SortFactory): Finished sorting a total of 0 features.
2025-05-21 10:56:24|   5.0|  0.0|STATS |Tester_15 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|STATS |Sorter_2 (SortFactory): Finished sorting a total of 0 features.
2025-05-21 10:56:24|   5.0|  0.0|STATS |Sorter_2 SORTED Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter_INPUT (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter_3_INPUT (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter_3 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|StatisticsCalculator_3 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|INFORM|StatisticsCalculator_4 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|INFORM|StatisticsCalculator_5 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|STATS |FeatureMerger_2 (ReferenceFactory): Total Results: 1 Complete Feature(s), 3 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |FeatureMerger (ReferenceFactory): Total Results: 1 Complete Feature(s), 3 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |AttributeManager OUTPUT Splitter (TeeFactory): Cloned 4 input feature(s) into 8 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|StatisticsCalculator_6 (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Sorter_4 (SortFactory): Starting sorting 5 feature(s)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Sorter_4 (SortFactory): Done sorting 5 feature(s)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Creating writer for format: 
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Using MultiWriter with keyword `FeatureWriter_3' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Creating writer for format: Microsoft Excel
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for writer named `XLSXW'
2025-05-21 10:56:24|   5.0|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Writer `FeatureWriter_3_0' of type `XLSXW' using group definition keyword `FeatureWriter_3_0_DEF'
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Use Attribute Names As Column Positions is set to 'no' for sheet '总体检查情况' in dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\质量检查记录_**************.xlsx' 
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Use Attribute Names As Column Positions is set to 'no' for sheet '成果完整性检查情况' in dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\质量检查记录_**************.xlsx' 
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Use Attribute Names As Column Positions is set to 'no' for sheet '属性专题检查情况' in dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\质量检查记录_**************.xlsx' 
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Use Attribute Names As Column Positions is set to 'no' for sheet '空间专题检查情况' in dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\质量检查记录_**************.xlsx' 
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Opening dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\质量检查记录_**************.xlsx'...
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Reading existing workbook formatting. Workbook contains 1 fonts and 1 cell formats
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Sheet '总体检查情况' has writer mode 'INSERT'
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Sheet '总体检查情况' has Output field names set to 'yes'
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Sheet '总体检查情况' will start writing to row '1' and column 'A' relative to its start location. Sheets have a starting location of row 1, column 1, named range starting locations can be specified
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Outputting field names for sheet '总体检查情况'
2025-05-21 10:56:24|   5.0|  0.0|STATS |Sorter_4 (SortFactory): Finished sorting a total of 5 features.
2025-05-21 10:56:24|   5.0|  0.0|STATS |VariableRetriever_10 (TeeFactory): Cloned 5 input feature(s) into 5 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |VariableRetriever_9 (TeeFactory): Cloned 5 input feature(s) into 5 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|StatisticsCalculator (StatisticsCalculatorFactory): Processing complete
2025-05-21 10:56:24|   5.0|  0.0|STATS |Tester_14 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|STATS |Sorter (SortFactory): Finished sorting a total of 0 features.
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter_2_INPUT (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter_2 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-05-21 10:56:24|   5.0|  0.0|STATS |TestFilter_2 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Saving changes to dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\质量检查记录_**************.xlsx'
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Workbook contains 3 fonts and 4 cell formats after writing
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Excel Writer: Closing dataset 'E:\GeoStream_Integration\frontend\backend\models\Wo7BobdPztr2JotwuMcoiRvvXKqU2NupNkx4e6DU\output\task_1747796168128_oxt8vkai3\GIS数据质量检查结果_**************\质量检查记录_**************.xlsx'...
2025-05-21 10:56:24|   5.0|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Feature output statistics for `XLSXW' writer using keyword `FeatureWriter_3_0':
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |                               Features Written
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |总体检查情况                                                           5
2025-05-21 10:56:24|   5.0|  0.0|STATS |==============================================================================
2025-05-21 10:56:24|   5.0|  0.0|STATS |Total Features Written                                                       5
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理 输入 Input Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理_输入1747792826 Input Splitter (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理_StringReplacer OUTPUT Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理_AttributeSplitter (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理_AttributeSplitter_2 (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理_输出1747792826 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理_c1747792826 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理 输出 Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理 c Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |输出结果命名变量处理 输出 Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |VariableSetter OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |VariableSetter_2 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |VariableSetter_3 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|STATS |Destination Feature Type Routing Correlator (RoutingFactory): Tested 0 input feature(s), wrote 0 output feature(s): 0 matched merge filters, 0 were routed to output, 0 could not be routed.
2025-05-21 10:56:24|   5.0|  0.0|STATS |Final Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|INFORM|                     'GlobalVariable' Final State Summary
2025-05-21 10:56:24|   5.0|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|INFORM|a                                                               **************
2025-05-21 10:56:24|   5.0|  0.0|INFORM|b                                                                     <Unused>
2025-05-21 10:56:24|   5.0|  0.0|INFORM|c                                                                     <Unused>
2025-05-21 10:56:24|   5.0|  0.0|INFORM|==============================================================================
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Total Number of Global Variables:                                            3
2025-05-21 10:56:24|   5.0|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |                            Features Read Summary
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |==============================================================================
2025-05-21 10:56:24|   5.0|  0.0|STATS |Total Features Read                                                          0
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |                           Features Written Summary
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|STATS |==============================================================================
2025-05-21 10:56:24|   5.0|  0.0|STATS |Total Features Written                                                       0
2025-05-21 10:56:24|   5.0|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-21 10:56:24|   5.0|  0.0|INFORM|Translation was SUCCESSFUL with 1 warning(s) (0 feature(s) output)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|FME Session Duration: 8.5 seconds. (CPU: 3.3s user, 1.7s system)
2025-05-21 10:56:24|   5.0|  0.0|INFORM|END - ProcessID: 18880, peak process memory usage: 329300 kB, current process memory usage: 272424 kB
