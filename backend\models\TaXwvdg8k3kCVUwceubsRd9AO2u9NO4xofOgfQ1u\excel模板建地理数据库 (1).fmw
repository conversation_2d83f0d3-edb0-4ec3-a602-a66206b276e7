#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" "C:\Users\<USER>\Desktop\建库 - 高标准农田工程设施.fmw"
#          --dir "$(FME_MF_DIR)0208建库"
#          --图层名 ""
#          --DEST "EPSG:4528"
#          --PARAMETER "gdb"
#          --PARAMETER_3 ""
#          --PARAMETER_2 "$(FME_MF_DIR)新建文件夹 (5)"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="12946.4 2551.16"
#!   DOC_TOP_LEFT="-5433.04 -2414.77"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="eba6b69b-31dc-4dc6-9522-d6b9bcee8871"
#!   FME_DOCUMENT_PRIORGUID="7e959428-fa6f-4bb5-a43e-f5e4472e65f5,a5826982-80a1-43ef-b1d3-ec08d81f2138,17b624eb-a9f7-40dd-abb3-dded026555ae,179e5a88-2195-4074-a505-cbf3850deb5c,4d08d1c5-720f-4322-b19d-0647cbbc5c02,f7e5a89e-3397-44b9-b357-832e87360ac7,6b2cda76-795b-4142-bc2a-d00d779b589d,05a79d73-d4af-4a1c-bc4b-1e9dde8e9a1f,db6e6911-d148-4331-9230-cc0429e94a10,22b01692-a0a6-4675-97a8-68bdf1b89453,ebcb518d-35f5-4874-8821-61af3cb7f363,1466f879-0463-4616-9a11-cc2b19814866,7be0a603-4961-4f23-a8bd-504af89c835a,d4ebc557-f2cb-4b82-9ccf-92722b4cb494,f60b5942-2f99-4b75-8a5b-dc9a1602fbd1,a7ee3c2a-89a3-4f51-a2ab-ef11bc6bfab1"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-02-08T11:01:44"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="2643.78 384.379"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader"
#!   DATASET="@Value(path_windows)"
#! />
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="FeatureReader_2/320507相城区_CSJC2023.gdb"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/.gdb"
#! />
#! <DATASET
#!   NAME="FeatureWriter_2"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter_2"
#!   DATASET="FeatureWriter_2/&lt;u5efa&gt;&lt;u5e93&gt;"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER excel模板压缩包"
#!   DEFAULT_VALUE="$(FME_MF_DIR)0208建库"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI STRING_OR_ATTR 图层名 图层名（区县）"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI COORDSYS_OR_ATTR DEST 定义坐标系"
#!   DEFAULT_VALUE="EPSG:4528"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI CHOICE_OR_ATTR PARAMETER gdb%shp 数据库类型"
#!   DEFAULT_VALUE="gdb"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL STRING_OR_ATTR PARAMETER_3 gdb库文件名"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#!   DEFAULT_VALUE="$(FME_MF_DIR)新建文件夹 (5)"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="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"
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)0208建库"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER excel模板压缩包"
#! />
#!     <INFO NAME="图层名" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI STRING_OR_ATTR 图层名 图层名（区县）"
#! />
#!     <INFO NAME="DEST" 
#!   DEFAULT_VALUE="EPSG:4528"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI COORDSYS_OR_ATTR DEST 定义坐标系"
#! />
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE="gdb"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI CHOICE_OR_ATTR PARAMETER gdb%shp 数据库类型"
#! />
#!     <INFO NAME="PARAMETER_3" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL STRING_OR_ATTR PARAMETER_3 gdb库文件名"
#! />
#!     <INFO NAME="PARAMETER_2" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)新建文件夹 (5)"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! <BOOKMARK
#!   IDENTIFIER="30"
#!   NAME="定义坐标系投影"
#!   DESCRIPTION=""
#!   TOP_LEFT="4433.9202192021912 -265.00375003750014"
#!   ORDER="500000000000033"
#!   PALETTE_COLOR="Color1"
#!   BOTTOM_RIGHT="5025.9202192021912 -541.00375003750014"
#!   BOUNDING_RECT="4433.9202192021912 -265.00375003750014 592 276"
#!   STICKY="true"
#!   COLOUR="0.59999999999999998,0.80000000000000004,0.80000000000000004,1"
#!   CONTENTS="33 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="36"
#!   NAME="读取excel"
#!   DESCRIPTION=""
#!   TOP_LEFT="-3438.2140176957328 -129.83591669250023"
#!   ORDER="500000000000034"
#!   PALETTE_COLOR="Color2"
#!   BOTTOM_RIGHT="-2234.9577051326069 -724.33591669250018"
#!   BOUNDING_RECT="-3438.2140176957328 -129.83591669250023 1203.2563125631257 594.5"
#!   STICKY="true"
#!   COLOUR="0.63529411764705879,0.80000000000000004,0.59999999999999998,1"
#!   CONTENTS="2 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="37"
#!   NAME="映射字段类型长度"
#!   DESCRIPTION=""
#!   TOP_LEFT="-1993.5746113016678 -232.2269444916671"
#!   ORDER="500000000000035"
#!   PALETTE_COLOR="Color3"
#!   BOTTOM_RIGHT="-1369.5735430439383 -508.2269444916671"
#!   BOUNDING_RECT="-1993.5746113016678 -232.2269444916671 624.00106825772946 276"
#!   STICKY="true"
#!   COLOUR="1,0.85490196078431369,0.59999999999999998,1"
#!   CONTENTS="5 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="38"
#!   NAME="映射图层几何类型"
#!   DESCRIPTION=""
#!   TOP_LEFT="-943.75943759437587 136.38915278041665"
#!   ORDER="500000000000036"
#!   PALETTE_COLOR="Color4"
#!   BOTTOM_RIGHT="920.14809036979204 -788.20232646770899"
#!   BOUNDING_RECT="-943.75943759437587 136.38915278041665 1863.9075279641679 924.59147924812567"
#!   STICKY="true"
#!   COLOUR="0.85098039215686272,0.92941176470588238,0.60392156862745094,1"
#!   CONTENTS="58 54 61 64 12 57 55 65 66 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="40"
#!   NAME="构建Schema"
#!   DESCRIPTION=""
#!   TOP_LEFT="2229.0370592594804 -186.0868542018753"
#!   ORDER="500000000000040"
#!   PALETTE_COLOR="Color8"
#!   BOTTOM_RIGHT="4086.6747156360443 -518.33741670750044"
#!   BOUNDING_RECT="2229.0370592594804 -186.0868542018753 1857.6376563765639 332.25056250562517"
#!   STICKY="true"
#!   COLOUR="0.62352941176470589,0.5725490196078431,0.8784313725490196,1"
#!   CONTENTS="15 14 16 39 22 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="41"
#!   NAME="输出数据库gdb"
#!   DESCRIPTION=""
#!   TOP_LEFT="6069.0060145045891 56.901461792395779"
#!   ORDER="500000000000041"
#!   PALETTE_COLOR="Color9"
#!   BOTTOM_RIGHT="7513.3893583380259 -895.2280505299484"
#!   BOUNDING_RECT="6069.0060145045891 56.901461792395779 1444.3833438334368 952.12951232234423"
#!   STICKY="true"
#!   COLOUR="0.81568627450980391,0.5607843137254902,0.85882352941176465,1"
#!   CONTENTS="18 47 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="45"
#!   NAME="映射字段别名"
#!   DESCRIPTION=""
#!   TOP_LEFT="1411.3205487610421 -242.08685420187533"
#!   ORDER="500000000000043"
#!   PALETTE_COLOR="Color1"
#!   BOTTOM_RIGHT="2003.3205487610421 -518.08685420187533"
#!   BOUNDING_RECT="1411.3205487610421 -242.08685420187533 592 276"
#!   STICKY="true"
#!   COLOUR="0.59999999999999998,0.80000000000000004,0.80000000000000004,1"
#!   CONTENTS="42 "
#! >
#! </BOOKMARK>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-3037.8776009982321 -232.2269444916671"
#!   BOUNDING_RECT="-3037.8776009982321 -232.2269444916671 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="Sheet1"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="Sheet1,&quot;图层名,图层类型,字段名,字段别名,类型,长度&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="Sheet1,&quot;varchar(40),varchar(3),varchar(8),varchar(24),varchar(9),int16&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%Sheet1"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="Sheet1"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="Sheet1,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXR_SKIP_EMPTY_ROWS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_EXCEL_COL_NAMES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP XLSXR_EXPOSE_FORMAT_ATTRS&quot;,XLSXR&lt;space&gt;Schema&lt;space&gt;Attributes,XLSXR_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXR&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,XLSXR_USE_CUSTOM_SCHEMA,&quot;OPTIONAL RADIO_GROUP 2%Automatic,NO%Manual,YES&quot;,XLSXR&lt;space&gt;Attribute&lt;space&gt;Definition,XLSXR_TRIM_ATTR_NAME_CHARACTERS,&quot;OPTIONAL TEXT_EDIT_ENCODED FME_INCLUDEBROWSE%NO&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Characters&lt;space&gt;from&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_CREATE_FEATURE_TABLES_FROM_DATA,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_ALLOW_DOLLAR_SIGNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP APPLY_FILTERS%SCAN_MAX_FEATURES%TRIM_ATTR_NAME_WHITESPACE%TRIM_ATTR_NAME_CHARACTERS%READ_BLANK_AS%EXPAND_MERGED_CELLS%READ_RASTER_MODE%READ_FORM_CONTROLS%SCAN_FOR_GEOMETRIC_TYPES&quot;,XLSXR&lt;space&gt;Advanced,XLSXR_TABLELIST,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS XLSXR%Source&quot;,XLSXR&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,XLSXR_SCAN_MAX_FEATURES,&quot;OPTIONAL RANGE_SLIDER 0%MAX%0&quot;,XLSXR&lt;space&gt;Maximum&lt;space&gt;Rows&lt;space&gt;to&lt;space&gt;Scan:,XLSXR_SCAN_FOR_GEOMETRIC_TYPES,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Scan&lt;space&gt;For&lt;space&gt;Geometric&lt;space&gt;Types:,XLSXR_FORCE_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_STRIP_SHEETNAME_SPACES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_READ_FORM_CONTROLS,&quot;OPTIONAL CHECKBOX CELL_VALUE%NONE&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Form&lt;space&gt;Control&lt;space&gt;as&lt;space&gt;Cell&lt;space&gt;Values:,XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_CONFIGURATION_DATASET,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_APPLY_FILTERS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Apply&lt;space&gt;Filter&lt;openparen&gt;s&lt;closeparen&gt;:,XLSXR_TRIM_ATTR_NAME_WHITESPACE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Whitespace&lt;space&gt;From&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_EXPAND_MERGED_CELLS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Expand&lt;space&gt;Merged&lt;space&gt;Cells:,XLSXR_SCHEMA_HANDLING_REVISION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_READ_RASTER_MODE,&quot;OPTIONAL LOOKUP_CHOICE None%Attribute%Geometry&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Embedded&lt;space&gt;Images&lt;space&gt;As:,XLSXR_CASE_SENSITIVE_FEATURE_TYPES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SCHEMA,&quot;OPTIONAL STRING&quot;,XLSXR&lt;space&gt;To&lt;space&gt;be&lt;space&gt;populated,XLSXR_READ_BLANK_AS,&quot;OPTIONAL CHOICE Missing%Null&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Cells&lt;space&gt;As:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ALLOW_DOLLAR_SIGNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_APPLY_FILTERS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CASE_SENSITIVE_FEATURE_TYPES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CONFIGURATION_DATASET" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CREATE_FEATURE_TABLES_FROM_DATA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXCEL_COL_NAMES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPAND_MERGED_CELLS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_FORCE_DATETIME" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_BLANK_AS" PARM_VALUE="Missing"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_FORM_CONTROLS" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_RASTER_MODE" PARM_VALUE="None"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_FOR_GEOMETRIC_TYPES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_MAX_FEATURES" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA_HANDLING_REVISION" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SKIP_EMPTY_ROWS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_STRIP_SHEETNAME_SPACES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TABLELIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_CHARACTERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_WHITESPACE" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_USE_CUSTOM_SCHEMA" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-5433.0404415155253 -423.61534726458359"
#!   BOUNDING_RECT="-5433.0404415155253 -423.61534726458359 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="AttributeValueMapper"
#!   VERSION="7"
#!   POSITION="-1899.3245488010427 -342.22694449166704"
#!   BOUNDING_RECT="-1899.3245488010427 -342.22694449166704 486.00106825772946 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DEFAULT_VALUE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="LUT" PARM_VALUE="&lt;u5b57&gt;&lt;u7b26&gt;&lt;u4e32&gt;,fme_varchar&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;closeparen&gt;&lt;closeparen&gt;,&lt;u53cc&gt;&lt;u7cbe&gt;&lt;u5ea6&gt;,fme_real64,&lt;u5355&gt;&lt;u7cbe&gt;&lt;u5ea6&gt;,fme_real32"/>
#!     <XFORM_PARM PARM_NAME="MAPPING_DIRECTION" PARM_VALUE="FORWARD"/>
#!     <XFORM_PARM PARM_NAME="SRC" PARM_VALUE="&lt;u7c7b&gt;&lt;u578b&gt;"/>
#!     <XFORM_PARM PARM_NAME="VALUE_MAP_TABLE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VAL_ATTR" PARM_VALUE="&lt;u7c7b&gt;&lt;u578b&gt;&lt;u957f&gt;&lt;u5ea6&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeValueMapper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="-634.72856950791697 -20.460197935312635"
#!   BOUNDING_RECT="-634.72856950791697 -20.460197935312635 454 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u7c7b&gt;&lt;u578b&gt; SET_TO fme_point buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="14"
#!   TYPE="ListBuilder"
#!   VERSION="3"
#!   POSITION="2935.7932468213562 -296.08685420187533"
#!   BOUNDING_RECT="2935.7932468213562 -296.08685420187533 454 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="图层名"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u522b&gt;&lt;u540d&gt; &lt;u5b57&gt;&lt;u6bb5&gt;&lt;u540d&gt; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt; &lt;u7c7b&gt;&lt;u578b&gt;&lt;u957f&gt;&lt;u5ea6&gt; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u7c7b&gt;&lt;u578b&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="Selected Attributes"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListBuilder"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="16"
#!   TYPE="PythonCaller"
#!   VERSION="4"
#!   POSITION="3567.0495593844821 -329.00375003750014"
#!   BOUNDING_RECT="3567.0495593844821 -329.00375003750014 454 71"
#!   ORDER="500000000000015"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="HIDE_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NEW_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PYTHONSOURCE" PARM_VALUE="import&lt;space&gt;fme&lt;lf&gt;import&lt;space&gt;fmeobjects&lt;lf&gt;&lt;lf&gt;def&lt;space&gt;processFeature&lt;openparen&gt;feature&lt;closeparen&gt;:&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;pass&lt;lf&gt;&lt;lf&gt;class&lt;space&gt;FeatureProcessor&lt;openparen&gt;object&lt;closeparen&gt;:&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;def&lt;space&gt;__init__&lt;openparen&gt;self&lt;closeparen&gt;:&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;pass&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;def&lt;space&gt;input&lt;openparen&gt;self&lt;comma&gt;feature&lt;closeparen&gt;:&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;file_name_list&lt;space&gt;=&lt;space&gt;feature.getAttribute&lt;openparen&gt;&lt;apos&gt;_list&lt;opencurly&gt;&lt;closecurly&gt;.&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u540d&gt;&lt;apos&gt;&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;file_type_list&lt;space&gt;=&lt;space&gt;feature.getAttribute&lt;openparen&gt;&lt;apos&gt;_list&lt;opencurly&gt;&lt;closecurly&gt;.&lt;u7c7b&gt;&lt;u578b&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;apos&gt;&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;file_alias_list&lt;space&gt;=&lt;space&gt;feature.getAttribute&lt;openparen&gt;&lt;apos&gt;_list&lt;opencurly&gt;&lt;closecurly&gt;.&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u522b&gt;&lt;u540d&gt;&lt;apos&gt;&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;feature.setAttribute&lt;openparen&gt;&lt;apos&gt;attribute&lt;opencurly&gt;&lt;closecurly&gt;.name&lt;apos&gt;&lt;comma&gt;file_name_list&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;feature.setAttribute&lt;openparen&gt;&lt;apos&gt;attribute&lt;opencurly&gt;&lt;closecurly&gt;.fme_data_type&lt;apos&gt;&lt;comma&gt;file_type_list&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;for&lt;space&gt;i&lt;space&gt;in&lt;space&gt;range&lt;openparen&gt;len&lt;openparen&gt;file_name_list&lt;closeparen&gt;&lt;closeparen&gt;:&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;name&lt;space&gt;=&lt;space&gt;file_name_list&lt;openbracket&gt;i&lt;closebracket&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;alias&lt;space&gt;=&lt;space&gt;file_alias_list&lt;openbracket&gt;i&lt;closebracket&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;feature.setAttribute&lt;openparen&gt;name&lt;space&gt;+&lt;space&gt;&lt;apos&gt;_alias&lt;apos&gt;&lt;comma&gt;&lt;space&gt;alias&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;layer_type&lt;space&gt;=&lt;space&gt;feature.getAttribute&lt;openparen&gt;&lt;apos&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u56fe&gt;&lt;u5c42&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;apos&gt;&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;feature.setAttribute&lt;openparen&gt;&lt;apos&gt;fme_geometry&lt;opencurly&gt;&lt;closecurly&gt;&lt;apos&gt;&lt;comma&gt;&lt;openbracket&gt;layer_type&lt;closebracket&gt;&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;self.pyoutput&lt;openparen&gt;feature&lt;closeparen&gt;&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;def&lt;space&gt;close&lt;openparen&gt;self&lt;closeparen&gt;:&lt;lf&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;&lt;space&gt;pass"/>
#!     <XFORM_PARM PARM_NAME="PYTHONSYMBOL" PARM_VALUE="FeatureProcessor"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="PythonCaller"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="6494.8566152328176 -149.00375003750014"
#!   BOUNDING_RECT="6494.8566152328176 -149.00375003750014 430 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;$(PARAMETER_3).gdb"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_GEODB&lt;comma&gt;NO&lt;comma&gt;DATASET_TEMPLATE&lt;comma&gt;&lt;comma&gt;IMPORT_XML_TEMPLATE_GROUP&lt;comma&gt;NO&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;IMPORT_KIND&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;TRANSACTION_TYPE&lt;comma&gt;TRANSACTIONS&lt;comma&gt;FEATURE_DATASET_HANDLING&lt;comma&gt;WRITE&lt;comma&gt;SIMPLIFY_GEOM&lt;comma&gt;No&lt;comma&gt;HAS_Z_VALUES&lt;comma&gt;auto_detect&lt;comma&gt;X_ORIGIN&lt;comma&gt;0&lt;comma&gt;Y_ORIGIN&lt;comma&gt;0&lt;comma&gt;XY_SCALE&lt;comma&gt;0&lt;comma&gt;Z_ORIGIN&lt;comma&gt;0&lt;comma&gt;Z_SCALE&lt;comma&gt;0&lt;comma&gt;GRID_1&lt;comma&gt;0&lt;comma&gt;GEODB_SHARED_WRT_ADV_PARM_GROUP&lt;comma&gt;&lt;comma&gt;REQUESTED_GEODATABASE_VERSION&lt;comma&gt;CURRENT&lt;comma&gt;DEFAULT_Z_VALUE&lt;comma&gt;0&lt;comma&gt;TRANSACTION&lt;comma&gt;0&lt;comma&gt;TRANSACTION_INTERVAL&lt;comma&gt;1000&lt;comma&gt;IGNORE_FAILED_FEATURE_ENTRY&lt;comma&gt;no&lt;comma&gt;MAX_NUMBER_FAILED_FEATURES&lt;comma&gt;-1&lt;comma&gt;DUMP_FAILED_FEATURES&lt;comma&gt;no&lt;comma&gt;FFS_DUMP_FILE&lt;comma&gt;&lt;comma&gt;ANNOTATION_UNITS&lt;comma&gt;unknown_units&lt;comma&gt;HAS_MEASURES&lt;comma&gt;no&lt;comma&gt;COMPRESS_AT_END&lt;comma&gt;no&lt;comma&gt;ENABLE_FAST_DELETES&lt;comma&gt;yes&lt;comma&gt;PRESERVE_GLOBALID&lt;comma&gt;no&lt;comma&gt;ENABLE_LOAD_ONLY_MODE&lt;comma&gt;no&lt;comma&gt;VALIDATE_FEATURES&lt;comma&gt;no&lt;comma&gt;SIMPLIFY_NETWORK_FEATURES&lt;comma&gt;no&lt;comma&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE_TYPE,METAFILE,GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="GEODATABASE_FILE_DUMP_FAILED_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Dump&lt;space&gt;Failed&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;File:,GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION,&quot;OPTIONAL LOOKUP_CHOICE Current,CURRENT%10.0%9.3&quot;,GEODATABASE_FILE&lt;space&gt;Geodatabase&lt;space&gt;Version:,GEODATABASE_FILE_COMPRESS_AT_END,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Compact&lt;space&gt;Database&lt;space&gt;When&lt;space&gt;Done:,GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_X_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_XY_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_TRANSACTION,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Number:,GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Load&lt;space&gt;Only&lt;space&gt;Mode:,GEODATABASE_FILE_GRID_1,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_FFS_DUMP_FILE,&quot;OPTIONAL FILENAME FME_Feature_Store_Files(*.ffs)|*.ffs|All_files(*)|*&quot;,GEODATABASE_FILE&lt;space&gt;Failed&lt;space&gt;Feature&lt;space&gt;Dump&lt;space&gt;filename:,GEODATABASE_FILE_Y_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_FEATURE_DATASET_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Write&lt;space&gt;Feature&lt;space&gt;Dataset,WRITE%Warn&lt;space&gt;and&lt;space&gt;Ignore&lt;space&gt;Feature&lt;space&gt;Dataset,IGNORE%Error&lt;space&gt;and&lt;space&gt;End&lt;space&gt;Translation,END&quot;,GEODATABASE_FILE&lt;space&gt;Feature&lt;space&gt;Dataset&lt;space&gt;Handling:,GEODATABASE_FILE_Z_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP TEMPLATEFILE%IMPORT_KIND%++YES+OVERWRITE_GEODB+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Import&lt;space&gt;XML&lt;space&gt;Workspace&lt;space&gt;Document,GEODATABASE_FILE_BEGIN_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;Before&lt;space&gt;Write,GEODATABASE_FILE_ENABLE_FAST_DELETES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Fast&lt;space&gt;Deletes:,GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Network&lt;space&gt;Features:,GEODATABASE_FILE_DEFAULT_Z_VALUE,&quot;OPTIONAL FLOAT&quot;,GEODATABASE_FILE&lt;space&gt;Default&lt;space&gt;Z&lt;space&gt;Value:,GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_SIMPLIFY_GEOM,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Geometry:,GEODATABASE_FILE_HAS_Z_VALUES,&quot;OPTIONAL LOOKUP_CHOICE Yes,yes%No,no%Auto&lt;space&gt;Detect,auto_detect&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values:,GEODATABASE_FILE_OVERWRITE_GEODB,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_OPEN%DATASET_TEMPLATE%++YES+IMPORT_XML_TEMPLATE_GROUP+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;Geodatabase,GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Max&lt;space&gt;number&lt;space&gt;of&lt;space&gt;features&lt;space&gt;to&lt;space&gt;ignore:,GEODATABASE_FILE_ANNOTATION_UNITS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Unknown Units&quot;&quot;,unknown_units%&quot;&quot;Decimal Degrees&quot;&quot;,decimal_degrees%Inches,inches%Points,points%Feet,feet%Yards,yards%Miles,miles%&quot;&quot;Nautical Miles&quot;&quot;,nautical_miles%Millimeters,millimeters%Centimeters,centimeters%Meters,meters%Kilometers,kilometers%Decimeters,decimeters&quot;,GEODATABASE_FILE&lt;space&gt;Annotation&lt;space&gt;Units:,GEODATABASE_FILE_PRESERVE_GLOBALID,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Preserve&lt;space&gt;GlobalID:,GEODATABASE_FILE_VALIDATE_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Validate&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;Write:,GEODATABASE_FILE_Z_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP,&quot;OPTIONAL DISCLOSUREGROUP REQUESTED_GEODATABASE_VERSION%DEFAULT_Z_VALUE%TRANSACTION%TRANSACTION_INTERVAL%IGNORE_FAILED_FEATURE_ENTRY%MAX_NUMBER_FAILED_FEATURES%DUMP_FAILED_FEATURES%FFS_DUMP_FILE%ANNOTATION_UNITS%HAS_MEASURES%MEASURES_ORIGIN%MEASURES_SCALE%COMPRESS_AT_END%ENABLE_FAST_DELETES%PRESERVE_GLOBALID%ENABLE_LOAD_ONLY_MODE%VALIDATE_FEATURES%SIMPLIFY_NETWORK_FEATURES%BEGIN_SQL{0}%END_SQL{0}&quot;,GEODATABASE_FILE&lt;space&gt;Advanced,GEODATABASE_FILE_END_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;After&lt;space&gt;Write,GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Failed&lt;space&gt;Features:,GEODATABASE_FILE_TRANSACTION_INTERVAL,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Features&lt;space&gt;Per&lt;space&gt;Transaction,GEODATABASE_FILE_TRANSACTION_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Edit&lt;space&gt;Session,EDIT_SESSION%Transactions,TRANSACTIONS%None,NONE&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Type:,GEODATABASE_FILE_HAS_MEASURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Measures"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ANNOTATION_UNITS" PARM_VALUE="unknown_units"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_BEGIN_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COMPRESS_AT_END" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE_TYPE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DEFAULT_Z_VALUE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DUMP_FAILED_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_FAST_DELETES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_END_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FEATURE_DATASET_HANDLING" PARM_VALUE="WRITE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FFS_DUMP_FILE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GRID_1" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_MEASURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_Z_VALUES" PARM_VALUE="auto_detect"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES" PARM_VALUE="-1"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_OVERWRITE_GEODB" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_PRESERVE_GLOBALID" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION" PARM_VALUE="CURRENT"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_GEOM" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_INTERVAL" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_TYPE" PARM_VALUE="TRANSACTIONS"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_VALIDATE_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_XY_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_X_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Y_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="图层名"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="Output &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PER_EACH_INPUT"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt;:Output,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt;,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_table,ftp_dynamic_schema,yes,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_FROM_ATTRIBUTE,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry,from_schema_definition,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,SCHEMA_FROM_FIRST_FEATURE,ftp_attribute_source,2,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;GEODB_ORIGIN_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ADVANCED_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_XY_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_Z_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;comma&gt;GEODB_XORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_YORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_XYSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;comma&gt;GEODB_ZORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_ZSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_ORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_SCALE&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.&lt;space&gt;%-#&lt;openbracket&gt;&lt;closebracket&gt;&lt;quote&gt;&lt;openparen&gt;&lt;closeparen&gt;!?*&lt;apos&gt;&lt;amp&gt;+&lt;backslash&gt;&lt;solidus&gt;&lt;opencurly&gt;&lt;closecurly&gt;|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;blob&lt;comma&gt;fme_binarybuffer&lt;comma&gt;blob&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;blob&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;globalid&lt;comma&gt;fme_buffer&lt;comma&gt;guid&lt;comma&gt;fme_buffer&lt;comma&gt;date&lt;comma&gt;fme_datetime&lt;comma&gt;timestamp_offset&lt;comma&gt;fme_datetime&lt;comma&gt;date_only&lt;comma&gt;fme_date&lt;comma&gt;time_only&lt;comma&gt;fme_time&lt;comma&gt;integer&lt;comma&gt;fme_int32&lt;comma&gt;integer&lt;comma&gt;fme_uint16&lt;comma&gt;smallint&lt;comma&gt;fme_int16&lt;comma&gt;smallint&lt;comma&gt;fme_int8&lt;comma&gt;smallint&lt;comma&gt;fme_uint8&lt;comma&gt;bigint&lt;comma&gt;fme_int64&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;char&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;boolean&lt;comma&gt;fme_boolean&lt;comma&gt;&lt;quote&gt;double&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;subtype&lt;openparen&gt;stringset&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;subtype_codes&lt;openparen&gt;stringmap&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;range_domain&lt;openparen&gt;range_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;coded_domain&lt;openparen&gt;coded_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,&lt;backslash&gt;&lt;backslash&gt;&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|&lt;openbracket&gt;%#&lt;space&gt;&lt;apos&gt;&lt;amp&gt;+-&lt;closebracket&gt;.^~&lt;dollar&gt;&lt;comma&gt;&lt;closeparen&gt;&lt;openparen&gt;,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point&lt;comma&gt;fme_point&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_line&lt;comma&gt;geodb_multipoint&lt;comma&gt;fme_point&lt;comma&gt;geodb_table&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_table&lt;comma&gt;fme_collection&lt;comma&gt;geodb_arc&lt;comma&gt;fme_arc&lt;comma&gt;geodb_ellipse&lt;comma&gt;fme_ellipse&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;geodb_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_pro_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_dimension&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_junction&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_complex_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_attributed_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_undefined&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_metadata&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_raster&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;geodb_table&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_group&lt;space&gt;fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_common_group&lt;space&gt;fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group&lt;space&gt;General&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_feature_operation&lt;space&gt;Insert&lt;comma&gt;INSERT&lt;comma&gt;fme_update_geometry&lt;comma&gt;fme_selection_group&lt;comma&gt;mie_pack%Update&lt;comma&gt;UPDATE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert&lt;comma&gt;UPSERT&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Delete&lt;comma&gt;DELETE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;fme_update_geometry&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;comma&gt;fme_spatial_group&lt;comma&gt;fme_advanced_group&lt;comma&gt;oracle_sequenced_cols%&lt;lt&gt;at&lt;gt&gt;Value&lt;lt&gt;openparen&lt;gt&gt;fme_db_operation&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;MULTIPLE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;space&gt;Feature&lt;space&gt;Operation&lt;quote&gt;&lt;comma&gt;INSERT&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_table_handling&lt;space&gt;Use&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;USE_EXISTING&lt;comma&gt;fme_table_creation_group%Create&lt;lt&gt;space&lt;gt&gt;If&lt;lt&gt;space&lt;gt&gt;Needed&lt;comma&gt;CREATE_IF_MISSING%Drop&lt;lt&gt;space&lt;gt&gt;and&lt;lt&gt;space&lt;gt&gt;Create&lt;comma&gt;DROP_CREATE%Truncate&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;TRUNCATE_EXISTING&lt;comma&gt;fme_table_creation_group&lt;space&gt;Table&lt;space&gt;Handling&lt;quote&gt;&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;fme_update_geometry&lt;space&gt;Yes&lt;comma&gt;YES%No&lt;comma&gt;NO&lt;space&gt;Update&lt;space&gt;Spatial&lt;space&gt;Column&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;YES&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_selection_group&lt;space&gt;fme_selection_method&lt;space&gt;Row&lt;space&gt;Selection&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;RADIOPARAMETERGROUP&lt;space&gt;fme_selection_method&lt;space&gt;fme_match_columns&lt;comma&gt;MATCH_COLUMNS%fme_where_builder_clause&lt;comma&gt;BUILDER&lt;space&gt;Row&lt;space&gt;Selection&lt;space&gt;Method&lt;quote&gt;&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;ATTRLIST_COMMAS&lt;space&gt;fme_match_columns&lt;space&gt;Match&lt;space&gt;Columns&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;TEXT_EDIT_SQL_CFG_OR_ATTR&lt;space&gt;fme_where_builder_clause&lt;space&gt;MODE&lt;comma&gt;WHERE&lt;space&gt;WHERE&lt;space&gt;Clause&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_table_creation_group&lt;space&gt;FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP&lt;space&gt;Table&lt;space&gt;Creation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ORIGIN_GROUP&lt;space&gt;GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP&lt;space&gt;Origin&lt;space&gt;and&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ANNO_GROUP&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ADVANCED_GROUP&lt;space&gt;GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;%GEODB_AVG_NUM_POINTS&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_XY_GROUP&lt;space&gt;GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_Z_GROUP&lt;space&gt;GEODB_ZORIGIN%GEODB_ZSCALE&lt;space&gt;Z&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_MEASURES_GROUP&lt;space&gt;GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_NAME&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_ALIAS&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_NAME&lt;space&gt;Shape&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_ALIAS&lt;space&gt;Shape&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_CONFIG_KEYWORD&lt;space&gt;Configuration&lt;space&gt;Keyword&lt;quote&gt;&lt;comma&gt;DEFAULTS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;space&gt;Grid&lt;space&gt;1&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;GEODB_AVG_NUM_POINTS&lt;space&gt;Average&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Points&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XORIGIN&lt;space&gt;X&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_YORIGIN&lt;space&gt;Y&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_Z_VALUES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZORIGIN&lt;space&gt;Z&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZSCALE&lt;space&gt;Z&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_MEASURES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_ORIGIN&lt;space&gt;Measure&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_SCALE&lt;space&gt;Measure&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;space&gt;Reference&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;geodb_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;GEODB_UPDATE_KEY_COLUMNS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_DROP_TABLE&lt;comma&gt;NO&lt;comma&gt;GEODB_TRUNCATE_TABLE&lt;comma&gt;NO&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_selection_method&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_update_geometry&lt;comma&gt;YES&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_FEATURE_DATASET&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;_GEODBOverwriteGEODB&lt;space&gt;_GEODBOutTransactionType&lt;space&gt;_GEODBOutSimplifyGeometry&lt;space&gt;_GEODBOutXOrigin&lt;space&gt;_GEODBOutYOrigin&lt;space&gt;_GEODBOutScale&lt;space&gt;_GEODBOutZOrigin&lt;space&gt;_GEODBOutZScale&lt;space&gt;_GEODBOutGrid1&lt;space&gt;_TRANSLATE_SPATIAL_DATA_ONLY&lt;space&gt;_GEODBInResolveDomains&lt;space&gt;_GEODBInResolveSubtypeNames&lt;space&gt;_GEODBInIgnoreNetworkInfo&lt;space&gt;_GEODBInIgnoreRelationshipInfo&lt;space&gt;_GEODBInSplitComplexEdges&lt;space&gt;_GEODBInWhereClause&lt;space&gt;NULL_IN_SPLIT_COMPLEX_ANNOS&lt;space&gt;NULL_IN_CACHE_MULTIPATCH_TEXTURES&lt;space&gt;NULL_IN_SEARCH_METHOD&lt;space&gt;NULL_IN_SEARCH_ORDER&lt;space&gt;NULL_IN_SEARCH_FEATURE&lt;space&gt;NULL_IN_CHECK_SIMPLE_GEOM&lt;space&gt;NULL_IN_MERGE_FEAT_LINKED_ANNOS&lt;space&gt;NULL_IN_READ_THREE_POINT_ARCS&lt;space&gt;NULL_IN_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_SIMPLE_DONUT_GEOMETRY&lt;space&gt;_GEODB_IN_ALIAS_MODE&lt;space&gt;GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION&lt;space&gt;GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE&lt;space&gt;GEODATABASE_FILE_OUT_WRITER_MODE&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL&lt;space&gt;GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY&lt;space&gt;GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_FFS_DUMP_FILE&lt;space&gt;GEODATABASE_FILE_OUT_ANNOTATION_UNITS&lt;space&gt;GEODATABASE_FILE_OUT_HAS_MEASURES&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_ORIGIN&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_SCALE&lt;space&gt;GEODATABASE_FILE_OUT_COMPRESS_AT_END&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES&lt;space&gt;GEODATABASE_FILE_OUT_PRESERVE_GLOBALID&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE&lt;space&gt;GEODATABASE_FILE_OUT_VALIDATE_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;GEODATABASE_FILE_OUT_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;PARAMS_TO_NOT_PROPAGATE_ON_INSPECT&lt;comma&gt;&lt;quote&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;ATTRIBUTE_READING_HISTORIC&lt;comma&gt;ALL&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;&lt;quote&gt;Feature&lt;space&gt;Class&lt;space&gt;or&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;FeatureClass1&lt;comma&gt;SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME&lt;comma&gt;NO&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;SQL_EXECUTE_DIRECTIVES&lt;comma&gt;INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="22"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="2317.0370592594804 -296.08685420187533"
#!   BOUNDING_RECT="2317.0370592594804 -296.08685420187533 454 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u7c7b&gt;&lt;u578b&gt;&lt;u957f&gt;&lt;u5ea6&gt;,&lt;u56fe&gt;&lt;u5c42&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u7c7b&gt;&lt;u578b&gt;,&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;,&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u522b&gt;&lt;u540d&gt;,&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u540d&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="33"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="4521.9202192021912 -375.00375003750014"
#!   BOUNDING_RECT="4521.9202192021912 -375.00375003750014 454 71"
#!   ORDER="500000000000032"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="$(DEST)"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="42"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="1499.3205487610421 -352.08685420187533"
#!   BOUNDING_RECT="1499.3205487610421 -352.08685420187533 454 71"
#!   ORDER="500000000000042"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u5b57&gt;&lt;u6bb5&gt;&lt;u522b&gt;&lt;u540d&gt; SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;&lt;at&gt;Value&lt;openparen&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u540d&gt;&lt;closeparen&gt;&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u522b&gt;&lt;u540d&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u522b&gt;&lt;u540d&gt;&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS2___&quot; varchar&lt;openparen&gt;24&lt;closeparen&gt;  &lt;at&gt;Value&lt;openparen&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u540d&gt;&lt;closeparen&gt;_alias SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u522b&gt;&lt;u540d&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;24&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="706.25706257062598 -1825.0182501825016"
#!   BOUNDING_RECT="706.25706257062598 -1825.0182501825016 442.00106825772946 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="false"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="CQJCFWA"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;BZSSA"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;CQNFWJZA"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="HEIGHT" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="FAREA" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GBAREA" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SETS" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GBAREA_SPZF" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SETS_SPZF" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GBAREA_BZXZF" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SETS_BZXZF" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;CSNLJSL"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;CSNLJSP"/>
#!     <FEAT_COLLAPSED COLLAPSED="5"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;CZZZA"/>
#!     <FEAT_COLLAPSED COLLAPSED="6"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="IFRENT" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;DLTBBHA"/>
#!     <FEAT_COLLAPSED COLLAPSED="7"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;FLJGA"/>
#!     <FEAT_COLLAPSED COLLAPSED="8"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;FLJGP"/>
#!     <FEAT_COLLAPSED COLLAPSED="9"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;GYSSA"/>
#!     <FEAT_COLLAPSED COLLAPSED="10"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="RMK" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;GYSSP"/>
#!     <FEAT_COLLAPSED COLLAPSED="11"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="RMK" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="11" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;GYYLDA"/>
#!     <FEAT_COLLAPSED COLLAPSED="12"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="12" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;JTFWCZA"/>
#!     <FEAT_COLLAPSED COLLAPSED="13"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="13" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;JTFWCZL"/>
#!     <FEAT_COLLAPSED COLLAPSED="14"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="14" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;SDZA"/>
#!     <FEAT_COLLAPSED COLLAPSED="15"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="15" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;TYHDA"/>
#!     <FEAT_COLLAPSED COLLAPSED="16"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="IFFF" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="16" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;TYHDP"/>
#!     <FEAT_COLLAPSED COLLAPSED="17"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <XFORM_ATTR ATTR_NAME="IFFF" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="17" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;WHHDA"/>
#!     <FEAT_COLLAPSED COLLAPSED="18"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="18" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;WHHDP"/>
#!     <FEAT_COLLAPSED COLLAPSED="19"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="19" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="19" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="19" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="19" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="19" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="19" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="19" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;WYCGA"/>
#!     <FEAT_COLLAPSED COLLAPSED="20"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="20" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;WYCGP"/>
#!     <FEAT_COLLAPSED COLLAPSED="21"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="21" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="21" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="21" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="21" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="21" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="21" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="21" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;XXA"/>
#!     <FEAT_COLLAPSED COLLAPSED="22"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="IFTC" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="22" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;XXP"/>
#!     <FEAT_COLLAPSED COLLAPSED="23"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="IFTC" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="23" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;YJBNA"/>
#!     <FEAT_COLLAPSED COLLAPSED="24"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="24" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="24" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="24" />
#!     <XFORM_ATTR ATTR_NAME="PRCTAG" IS_USER_CREATED="false" FEAT_INDEX="24" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="24" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="24" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="24" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;YLJGA"/>
#!     <FEAT_COLLAPSED COLLAPSED="25"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="GRADE" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="IFCH" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="IFIDH" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="25" />
#!     <OUTPUT_FEAT NAME="CtyDataset&lt;solidus&gt;YLJGP"/>
#!     <FEAT_COLLAPSED COLLAPSED="26"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="CC" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="CCN" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="NAME" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="TYPE" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="GRADE" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="IFCH" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="IFIDH" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="FEATID" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <XFORM_ATTR ATTR_NAME="AREACODE" IS_USER_CREATED="false" FEAT_INDEX="26" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="27"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="28"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="28" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="28" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="29"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="29" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="29" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="CQJCFWA,&quot;OBJECTID,AREACODE,XZQDM,XZQMC,Shape_Length,Shape_Area&quot;,CtyDataset/BZSSA,&quot;OBJECTID,DLBM,DLMC,NAME,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/CQNFWJZA,&quot;OBJECTID,HEIGHT,FAREA,GBAREA,SETS,FEATID,GBAREA_SPZF,SETS_SPZF,GBAREA_BZXZF,SETS_BZXZF,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/CSNLJSL,&quot;OBJECTID,FEATID,AREACODE,Shape_Length&quot;,CtyDataset/CSNLJSP,&quot;OBJECTID,FEATID,AREACODE&quot;,CtyDataset/CZZZA,&quot;OBJECTID,DLBM,DLMC,CC,CCN,IFRENT,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/DLTBBHA,&quot;OBJECTID,DLBM,DLMC,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/FLJGA,&quot;OBJECTID,DLBM,DLMC,NAME,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/FLJGP,&quot;OBJECTID,DLBM,DLMC,NAME,FEATID,AREACODE&quot;,CtyDataset/GYSSA,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,TYPE,RMK,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/GYSSP,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,TYPE,RMK,FEATID,AREACODE&quot;,CtyDataset/GYYLDA,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/JTFWCZA,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/JTFWCZL,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,FEATID,AREACODE,Shape_Length&quot;,CtyDataset/SDZA,&quot;OBJECTID,DLBM,DLMC,NAME,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/TYHDA,&quot;OBJECTID,DLBM,DLMC,NAME,IFFF,TYPE,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/TYHDP,&quot;OBJECTID,DLBM,DLMC,NAME,IFFF,TYPE,FEATID,AREACODE&quot;,CtyDataset/WHHDA,&quot;OBJECTID,DLBM,DLMC,NAME,TYPE,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/WHHDP,&quot;OBJECTID,DLBM,DLMC,NAME,TYPE,FEATID,AREACODE&quot;,CtyDataset/WYCGA,&quot;OBJECTID,DLBM,DLMC,NAME,TYPE,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/WYCGP,&quot;OBJECTID,DLBM,DLMC,NAME,TYPE,FEATID,AREACODE&quot;,CtyDataset/XXA,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,TYPE,IFTC,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/XXP,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,TYPE,IFTC,FEATID,AREACODE&quot;,CtyDataset/YJBNA,&quot;OBJECTID,NAME,FEATID,PRCTAG,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/YLJGA,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,TYPE,GRADE,IFCH,IFIDH,FEATID,AREACODE,Shape_Length,Shape_Area&quot;,CtyDataset/YLJGP,&quot;OBJECTID,DLBM,DLMC,CC,CCN,NAME,TYPE,GRADE,IFCH,IFIDH,FEATID,AREACODE&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="CQJCFWA,&quot;int32,varchar(16),varchar(6),varchar(100),real64,real64&quot;,CtyDataset/BZSSA,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/CQNFWJZA,&quot;int32,real64,real64,real64,varchar(16),varchar(16),real64,varchar(16),real64,varchar(16),varchar(16),real64,real64&quot;,CtyDataset/CSNLJSL,&quot;int32,varchar(16),varchar(16),real64&quot;,CtyDataset/CSNLJSP,&quot;int32,varchar(16),varchar(16)&quot;,CtyDataset/CZZZA,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(32),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/DLTBBHA,&quot;int32,varchar(10),varchar(60),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/FLJGA,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/FLJGP,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(16),varchar(16)&quot;,CtyDataset/GYSSA,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(64),varchar(64),varchar(255),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/GYSSP,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(64),varchar(64),varchar(255),varchar(16),varchar(16)&quot;,CtyDataset/GYYLDA,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(255),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/JTFWCZA,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(255),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/JTFWCZL,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(255),varchar(16),varchar(16),real64&quot;,CtyDataset/SDZA,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/TYHDA,&quot;int32,varchar(10),varchar(60),varchar(64),varchar(32),varchar(64),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/TYHDP,&quot;int32,varchar(10),varchar(60),varchar(64),varchar(32),varchar(64),varchar(16),varchar(16)&quot;,CtyDataset/WHHDA,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(64),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/WHHDP,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(64),varchar(16),varchar(16)&quot;,CtyDataset/WYCGA,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(64),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/WYCGP,&quot;int32,varchar(10),varchar(60),varchar(255),varchar(64),varchar(16),varchar(16)&quot;,CtyDataset/XXA,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(255),varchar(64),varchar(64),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/XXP,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(255),varchar(64),varchar(64),varchar(16),varchar(16)&quot;,CtyDataset/YJBNA,&quot;int32,varchar(255),varchar(16),varchar(4),varchar(16),real64,real64&quot;,CtyDataset/YLJGA,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(255),varchar(64),varchar(32),varchar(32),varchar(32),varchar(16),varchar(16),real64,real64&quot;,CtyDataset/YLJGP,&quot;int32,varchar(10),varchar(60),varchar(10),varchar(32),varchar(255),varchar(64),varchar(32),varchar(32),varchar(32),varchar(16),varchar(16)&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%CQJCFWA%CtyDataset/BZSSA%CtyDataset/CQNFWJZA%CtyDataset/CSNLJSL%CtyDataset/CSNLJSP%CtyDataset/CZZZA%CtyDataset/DLTBBHA%CtyDataset/FLJGA%CtyDataset/FLJGP%CtyDataset/GYSSA%CtyDataset/GYSSP%CtyDataset/GYYLDA%CtyDataset/JTFWCZA%CtyDataset/JTFWCZL%CtyDataset/SDZA%CtyDataset/TYHDA%CtyDataset/TYHDP%CtyDataset/WHHDA%CtyDataset/WHHDP%CtyDataset/WYCGA%CtyDataset/WYCGP%CtyDataset/XXA%CtyDataset/XXP%CtyDataset/YJBNA%CtyDataset/YLJGA%CtyDataset/YLJGP"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="E:&lt;backslash&gt;YC&lt;backslash&gt;&lt;u56fd&gt;&lt;u7a7a&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u8d28&gt;&lt;u68c0&gt;&lt;backslash&gt;test&lt;backslash&gt;&lt;u672c&gt;&lt;u5e74&gt;&lt;u5ea6&gt;&lt;u5f85&gt;&lt;u68c0&gt;&lt;u67e5&gt;&lt;backslash&gt;DLGGC&lt;backslash&gt;DLG320500&lt;backslash&gt;320507&lt;u76f8&gt;&lt;u57ce&gt;&lt;u533a&gt;_CSJC2023.gdb"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_BEGIN_SQL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_END_SQL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_FILEGDB_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_GEOMETRY" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_REMOVE_FEATURE_DATASET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_SIMPLE_DONUT_GEOMETRY" PARM_VALUE="simple"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_STRIP_GUID_GLOBALID_BRACES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_TABLELIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="FILEGDB"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="CQJCFWA,,CtyDataset/BZSSA,,CtyDataset/CQNFWJZA,,CtyDataset/CSNLJSL,,CtyDataset/CSNLJSP,,CtyDataset/CZZZA,,CtyDataset/DLTBBHA,,CtyDataset/FLJGA,,CtyDataset/FLJGP,,CtyDataset/GYSSA,,CtyDataset/GYSSP,,CtyDataset/GYYLDA,,CtyDataset/JTFWCZA,,CtyDataset/JTFWCZL,,CtyDataset/SDZA,,CtyDataset/TYHDA,,CtyDataset/TYHDP,,CtyDataset/WHHDA,,CtyDataset/WHHDP,,CtyDataset/WYCGA,,CtyDataset/WYCGP,,CtyDataset/XXA,,CtyDataset/XXP,,CtyDataset/YJBNA,,CtyDataset/YLJGA,,CtyDataset/YLJGP,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,FILEGDB"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="FILEGDB_GEOMETRY,&quot;OPTIONAL DISCLOSUREGROUP SIMPLE_DONUT_GEOMETRY&quot;,FILEGDB&lt;space&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;,FILEGDB_SIMPLE_DONUT_GEOMETRY,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,simple%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,complex&quot;,FILEGDB&lt;space&gt;&lt;u73af&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u53d1&gt;&lt;u73b0&gt;,FILEGDB_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,FILEGDB&lt;space&gt;,FILEGDB_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP BEGIN_SQL%END_SQL%GEOMETRY%STRIP_GUID_GLOBALID_BRACES&quot;,FILEGDB&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,FILEGDB_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,FILEGDB&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u641c&gt;&lt;u7d22&gt;&lt;u8303&gt;&lt;u56f4&gt;,FILEGDB_END_SQL,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,FILEGDB&quot;,FILEGDB&lt;space&gt;&lt;u8bfb&gt;&lt;u53d6&gt;&lt;u540e&gt;&lt;u6267&gt;&lt;u884c&gt;&lt;u7684&gt;SQL,FILEGDB_BEGIN_SQL,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,FILEGDB&quot;,FILEGDB&lt;space&gt;&lt;u8bfb&gt;&lt;u53d6&gt;&lt;u524d&gt;&lt;u6267&gt;&lt;u884c&gt;&lt;u7684&gt;SQL,FILEGDB_TABLELIST,&quot;IGNORE TEXT&quot;,FILEGDB&lt;space&gt;&lt;u8868&gt;:,FILEGDB_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP FILEGDB_EXPOSE_FORMAT_ATTRS&quot;,FILEGDB&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,FILEGDB_REMOVE_FEATURE_DATASET,&quot;OPTIONAL ACTIVECHECK YES%NO&quot;,FILEGDB&lt;space&gt;Remove&lt;space&gt;Feature&lt;space&gt;Dataset:,FILEGDB_FILEGDB_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS FILEGDB%Source&quot;,FILEGDB&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,FILEGDB_STRIP_GUID_GLOBALID_BRACES,&quot;OPTIONAL CHOICE yes%no&quot;,FILEGDB&lt;space&gt;&lt;u4ece&gt;GlobalID&lt;u548c&gt;GUID&lt;u4e2d&gt;&lt;u53bb&gt;&lt;u6389&gt;&lt;u5927&gt;&lt;u62ec&gt;&lt;u53f7&gt;:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="8"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="81.250812508125364 -1890.6439064390638"
#!   BOUNDING_RECT="81.250812508125364 -1890.6439064390638 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="false"
#!   ENABLED="false"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="10"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="1703.1420314203147 -2343.7734377343772"
#!   BOUNDING_RECT="1703.1420314203147 -2343.7734377343772 454 71"
#!   ORDER="500000000000012"
#!   PARMS_EDITED="true"
#!   ENABLED="false"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="native_data_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="attribute{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="32"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-4868.104236597921 -361.11472225833364"
#!   BOUNDING_RECT="-4868.104236597921 -361.11472225833364 430 71"
#!   ORDER="500000000000044"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="48"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-4211.8476740322949 -472.22694449166698"
#!   BOUNDING_RECT="-4211.8476740322949 -472.22694449166698 430 71"
#!   ORDER="500000000000045"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = xlsx"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="46"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="5458.3879172125035 -269.00375003750014"
#!   BOUNDING_RECT="5458.3879172125035 -269.00375003750014 430 71"
#!   ORDER="500000000000046"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段别名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.类型长度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST $(PARAMETER) = gdb"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="47"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="6486.4540724942817 -513.11697314960986"
#!   BOUNDING_RECT="6486.4540724942817 -513.11697314960986 430 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}.类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;&lt;u5efa&gt;&lt;u5e93&gt;"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;utf-8&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Compressed&lt;space&gt;Shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;Output&lt;space&gt;Dimension,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Elevation&lt;space&gt;as&lt;space&gt;Measures,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;space&gt;Type&lt;space&gt;Storage,SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Split&lt;space&gt;Dataset&lt;space&gt;into&lt;space&gt;2GB&lt;space&gt;Files,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Spatial&lt;space&gt;Index:,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;Surface&lt;space&gt;and&lt;space&gt;Solid&lt;space&gt;Storage,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;Advanced"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="图层名"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="Output &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PER_EACH_INPUT"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="utf-8"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt;:Output,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,yes,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_FROM_ATTRIBUTE,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry,from_schema_definition,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,SCHEMA_FROM_FIRST_FEATURE,ftp_attribute_source,2,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="54"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="-634.72856950791697 -287.57342017864619"
#!   BOUNDING_RECT="-634.72856950791697 -287.57342017864619 454 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u7c7b&gt;&lt;u578b&gt; SET_TO fme_line buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="57"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="191.25317077999892 -20.460197935312635"
#!   BOUNDING_RECT="191.25317077999892 -20.460197935312635 430 71"
#!   ORDER="500000000000049"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;1 SET_TO $(图层名) varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;1&lt;closeparen&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;_&lt;u70b9&gt; varchar&lt;openparen&gt;40&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="55"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="191.25317077999892 -287.57342017864619"
#!   BOUNDING_RECT="191.25317077999892 -287.57342017864619 430 71"
#!   ORDER="500000000000049"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;1 SET_TO $(图层名) varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;1&lt;closeparen&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;_&lt;u7ebf&gt; varchar&lt;openparen&gt;40&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="61"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="-602.00331563916734 -523.88101593770921"
#!   BOUNDING_RECT="-602.00331563916734 -523.88101593770921 454 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u7c7b&gt;&lt;u578b&gt; SET_TO fme_polygon buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="65"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="219.37845203281154 -523.88101593770921"
#!   BOUNDING_RECT="219.37845203281154 -523.88101593770921 430 71"
#!   ORDER="500000000000049"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图层名1" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层几何类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段别名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="类型长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;1 SET_TO $(图层名) varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;1&lt;closeparen&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;_&lt;u9762&gt; varchar&lt;openparen&gt;40&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_7"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="35"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="32"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="9"
#!   SOURCE_NODE="8"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="13"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="56"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="54"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="63"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="61"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="2"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="10"
#!   SOURCE_PORT_DESC="fo 0 &lt;lt&gt;SCHEMA&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="58"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="57"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="39"
#!   SOURCE_NODE="14"
#!   TARGET_NODE="16"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="34"
#!   SOURCE_NODE="16"
#!   TARGET_NODE="33"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="22"
#!   TARGET_NODE="14"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="51"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="46"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="44"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="22"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="52"
#!   SOURCE_NODE="46"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="50"
#!   SOURCE_NODE="48"
#!   TARGET_NODE="2"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="64"
#!   SOURCE_NODE="54"
#!   TARGET_NODE="55"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="62"
#!   SOURCE_NODE="55"
#!   TARGET_NODE="42"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="59"
#!   SOURCE_NODE="57"
#!   TARGET_NODE="42"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="66"
#!   SOURCE_NODE="61"
#!   TARGET_NODE="65"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="67"
#!   SOURCE_NODE="65"
#!   TARGET_NODE="42"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="60"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 1 Sheet1"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="49"
#!   SOURCE_NODE="32"
#!   TARGET_NODE="48"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="53"
#!   SOURCE_NODE="46"
#!   TARGET_NODE="47"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO dir $(FME_MF_DIR)0208建库

DEFAULT_MACRO 图层名 

DEFAULT_MACRO DEST EPSG:4528

DEFAULT_MACRO PARAMETER gdb

DEFAULT_MACRO PARAMETER_3 

DEFAULT_MACRO PARAMETER_2 $(FME_MF_DIR)新建文件夹 (5)

# ============================================================================
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(图层名$encode)} == {}} { puts_real {Parameter '图层名' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(DEST$encode)} == {}} { puts_real {Parameter 'DEST' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_2$encode)} == {}} { puts_real {Parameter 'PARAMETER_2' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)建库 - 高标准农田工程设施.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 建库 - 高标准农田工程设施
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 35"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_2_CREATED Brancher -1 9"   INPUT FEATURE_TYPE Creator_2_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "FeatureReader_2 (Disabled) Nuker"   INPUT FEATURE_TYPE Creator_2_CREATED
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_2_<Rejected> INPUT FEATURE_TYPE FeatureReader_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "ListExploder (Disabled) Nuker"   INPUT FEATURE_TYPE FeatureReader_2_<SCHEMA>
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME ListExploder_<Rejected> INPUT FEATURE_TYPE ListExploder_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, ListExploder<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_3_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_3_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_3_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_3_<Rejected> INPUT FEATURE_TYPE FeatureReader_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_3_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = xlsx ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED Sheet1
MACRO FeatureReader_DIRECTIVES ADVANCED,,ALLOW_DOLLAR_SIGNS,YES,APPLY_FILTERS,No,CASE_SENSITIVE_FEATURE_TYPES,YES,CONFIGURATION_DATASET,,CREATE_FEATURE_TABLES_FROM_DATA,Yes,EXCEL_COL_NAMES,YES,EXPAND_MERGED_CELLS,Yes,EXPOSE_ATTRS_GROUP,,FORCE_DATETIME,NO,NETWORK_AUTHENTICATION,,QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,Yes,READ_BLANK_AS,Missing,READ_FORM_CONTROLS,NONE,READ_RASTER_MODE,None,REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,YES,SCAN_FOR_GEOMETRIC_TYPES,Yes,SCAN_MAX_FEATURES,1000,SCHEMA,,SCHEMA_HANDLING_REVISION,2,SKIP_EMPTY_ROWS,YES,STRIP_SHEETNAME_SPACES,YES,TABLELIST,,TRIM_ATTR_NAME_CHARACTERS,,TRIM_ATTR_NAME_WHITESPACE,Yes,USE_CUSTOM_SCHEMA,NO,XLSXR_EXPOSE_FORMAT_ATTRS,
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Tester_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { XLSXR  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "Sheet1" }    READER_DIRECTIVES   { METAFILE,XLSXR }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }    OUTPUT Sheet1 FEATURE_TYPE FeatureReader_Sheet1
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
Lookup AttributeValueMapper_LOOKUP_TABLE   <u5b57><u7b26><u4e32> @EvaluateExpression(FDIV,STRING_ENCODED,fme_varchar<openparen><at>Value<openparen><u957f><u5ea6><closeparen><closeparen>,AttributeValueMapper)   <u53cc><u7cbe><u5ea6> fme_real64   <u5355><u7cbe><u5ea6> fme_real32   ""              ""   ENCODED_SUPPORTUNICODE
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeValueMapper }    INPUT  FEATURE_TYPE FeatureReader_Sheet1    OUTPUT { FEATURE_TYPE AttributeValueMapper_OUTPUT         @Lookup(AttributeValueMapper_LOOKUP_TABLE,"<u7c7b><u578b>",FORWARD|ENCODED_ATTR|REAL_NULL_SUPPORT, <u7c7b><u578b><u957f><u5ea6>)          }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AttributeValueMapper OUTPUT Splitter"   INPUT FEATURE_TYPE AttributeValueMapper_OUTPUT   OUTPUT FEATURE_TYPE AttributeValueMapper_OUTPUT_0_QInrT6iEB8o=   OUTPUT FEATURE_TYPE AttributeValueMapper_OUTPUT_1_fKNhKDmasv0=   OUTPUT FEATURE_TYPE AttributeValueMapper_OUTPUT_2_//3f/dokNHg=
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeValueMapper_OUTPUT_0_QInrT6iEB8o=    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5c42><u51e0><u4f55><u7c7b><u578b>" "SET_TO" "fme_point" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_4 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5c42><u540d>1" "SET_TO" "$(图层名$encode)" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u56fe><u5c42><u540d>" "SET_TO" "<at>Value<openparen><u56fe><u5c42><u540d>1<closeparen><u5de5><u7a0b><u8bbe><u65bd>_<u70b9>" "varchar<openparen>40<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_4_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeValueMapper_OUTPUT_1_fKNhKDmasv0=    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5c42><u51e0><u4f55><u7c7b><u578b>" "SET_TO" "fme_line" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_6 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeCreator_3_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5c42><u540d>1" "SET_TO" "$(图层名$encode)" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u56fe><u5c42><u540d>" "SET_TO" "<at>Value<openparen><u56fe><u5c42><u540d>1<closeparen><u5de5><u7a0b><u8bbe><u65bd>_<u7ebf>" "varchar<openparen>40<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_6_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_5 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeValueMapper_OUTPUT_2_//3f/dokNHg=    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5c42><u51e0><u4f55><u7c7b><u578b>" "SET_TO" "fme_polygon" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_5_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_7 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeCreator_5_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5c42><u540d>1" "SET_TO" "$(图层名$encode)" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u56fe><u5c42><u540d>" "SET_TO" "<at>Value<openparen><u56fe><u5c42><u540d>1<closeparen><u5de5><u7a0b><u8bbe><u65bd>_<u9762>" "varchar<openparen>40<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_7_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeCreator_6_OUTPUT    INPUT  FEATURE_TYPE AttributeCreator_4_OUTPUT    INPUT  FEATURE_TYPE AttributeCreator_7_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u5b57><u6bb5><u522b><u540d>" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'<at>Value<openparen><u5b57><u6bb5><u540d><closeparen>'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Value<openparen><u5b57><u6bb5><u522b><u540d><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen><u5b57><u6bb5><u522b><u540d><closeparen>'FME_NUM_CONDITIONS2___" "varchar<openparen>24<closeparen>" }      ATTR_ACTION { "" "<at>Value<openparen><u5b57><u6bb5><u540d><closeparen>_alias" "SET_TO" "<at>Value<openparen><u5b57><u6bb5><u522b><u540d><closeparen>" "varchar<openparen>24<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper }    INPUT  FEATURE_TYPE AttributeCreator_2_OUTPUT    KEEP_ATTRS { <u7c7b><u578b><u957f><u5ea6>,<u56fe><u5c42><u51e0><u4f55><u7c7b><u578b>,<u56fe><u5c42><u540d>,<u5b57><u6bb5><u522b><u540d>,<u5b57><u6bb5><u540d> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { ListBuilder_fme_type_remover }    INPUT  FEATURE_TYPE AttributeKeeper_OUTPUT    OUTPUT { FEATURE_TYPE ListBuilder_no_fme_type       @RemoveAttributes(fme_type,fme_geometry) }
FACTORY_DEF {*} ListFactory    FACTORY_NAME { ListBuilder }    INPUT { FEATURE_TYPE ListBuilder_no_fme_type }    LIST_NAME { "_list{}" }    LIST_ATTRS_TO_INCLUDE { <u5b57><u6bb5><u522b><u540d> <u5b57><u6bb5><u540d> <u56fe><u5c42><u540d> <u7c7b><u578b><u957f><u5ea6> <u56fe><u5c42><u51e0><u4f55><u7c7b><u578b> }    LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    GROUP_BY { 图层名 }    FLUSH_WHEN_GROUPS_CHANGE { No }    OUTPUT { LIST FEATURE_TYPE ListBuilder_OUTPUT          }
# -------------------------------------------------------------------------
FME_PYTHON_PATH "$(FME_MF_DIR)"
FACTORY_DEF {*} PythonFactory    FACTORY_NAME { PythonCaller }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    INPUT  FEATURE_TYPE ListBuilder_OUTPUT    SYMBOL_NAME { FeatureProcessor }    SOURCE_CODE { import<space>fme<lf>import<space>fmeobjects<lf><lf>def<space>processFeature<openparen>feature<closeparen>:<lf><space><space><space><space>pass<lf><lf>class<space>FeatureProcessor<openparen>object<closeparen>:<lf><space><space><space><space>def<space>__init__<openparen>self<closeparen>:<lf><space><space><space><space><space><space><space><space>pass<lf><space><space><space><space>def<space>input<openparen>self<comma>feature<closeparen>:<lf><space><space><space><space><space><space><space><space>file_name_list<space>=<space>feature.getAttribute<openparen><apos>_list<opencurly><closecurly>.<u5b57><u6bb5><u540d><apos><closeparen><lf><space><space><space><space><space><space><space><space>file_type_list<space>=<space>feature.getAttribute<openparen><apos>_list<opencurly><closecurly>.<u7c7b><u578b><u957f><u5ea6><apos><closeparen><lf><space><space><space><space><space><space><space><space>file_alias_list<space>=<space>feature.getAttribute<openparen><apos>_list<opencurly><closecurly>.<u5b57><u6bb5><u522b><u540d><apos><closeparen><lf><space><space><space><space><space><space><space><space>feature.setAttribute<openparen><apos>attribute<opencurly><closecurly>.name<apos><comma>file_name_list<closeparen><lf><space><space><space><space><space><space><space><space>feature.setAttribute<openparen><apos>attribute<opencurly><closecurly>.fme_data_type<apos><comma>file_type_list<closeparen><lf><space><space><space><space><space><space><space><space>for<space>i<space>in<space>range<openparen>len<openparen>file_name_list<closeparen><closeparen>:<lf><space><space><space><space><space><space><space><space><space><space><space><space>name<space>=<space>file_name_list<openbracket>i<closebracket><lf><space><space><space><space><space><space><space><space><space><space><space><space>alias<space>=<space>file_alias_list<openbracket>i<closebracket><lf><space><space><space><space><space><space><space><space><space><space><space><space>feature.setAttribute<openparen>name<space>+<space><apos>_alias<apos><comma><space>alias<closeparen><lf><space><space><space><space><space><space><space><space><space><space><space><space><lf><space><space><space><space><space><space><space><space>layer_type<space>=<space>feature.getAttribute<openparen><apos>_list<opencurly>0<closecurly>.<u56fe><u5c42><u51e0><u4f55><u7c7b><u578b><apos><closeparen><lf><space><space><space><space><space><space><space><space>feature.setAttribute<openparen><apos>fme_geometry<opencurly><closecurly><apos><comma><openbracket>layer_type<closebracket><closeparen><lf><space><space><space><space><space><space><space><space>self.pyoutput<openparen>feature<closeparen><lf><space><space><space><space>def<space>close<openparen>self<closeparen>:<lf><space><space><space><space><space><space><space><space>pass }    OUTPUT { PYOUTPUT FEATURE_TYPE PythonCaller_OUTPUT         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE PythonCaller_OUTPUT    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","@EvaluateExpression(FDIV,STRING,$(DEST$encode),Reprojector)",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE Reprojector_REPROJECTED    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode),Tester_2)" = gdb ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }    OUTPUT { FAILED FEATURE_TYPE Tester_2_FAILED         }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash>$(PARAMETER_3$encode).gdb,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    GROUP_BY { 图层名 }    FLUSH_WHEN_GROUPS_CHANGE { No }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { GEODATABASE_FILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_GEODB<comma>NO<comma>DATASET_TEMPLATE<comma><comma>IMPORT_XML_TEMPLATE_GROUP<comma>NO<comma>TEMPLATEFILE<comma><lt>Unused<gt><comma>IMPORT_KIND<comma><lt>Unused<gt><comma>TRANSACTION_TYPE<comma>TRANSACTIONS<comma>FEATURE_DATASET_HANDLING<comma>WRITE<comma>SIMPLIFY_GEOM<comma>No<comma>HAS_Z_VALUES<comma>auto_detect<comma>X_ORIGIN<comma>0<comma>Y_ORIGIN<comma>0<comma>XY_SCALE<comma>0<comma>Z_ORIGIN<comma>0<comma>Z_SCALE<comma>0<comma>GRID_1<comma>0<comma>GEODB_SHARED_WRT_ADV_PARM_GROUP<comma><comma>REQUESTED_GEODATABASE_VERSION<comma>CURRENT<comma>DEFAULT_Z_VALUE<comma>0<comma>TRANSACTION<comma>0<comma>TRANSACTION_INTERVAL<comma>1000<comma>IGNORE_FAILED_FEATURE_ENTRY<comma>no<comma>MAX_NUMBER_FAILED_FEATURES<comma>-1<comma>DUMP_FAILED_FEATURES<comma>no<comma>FFS_DUMP_FILE<comma><comma>ANNOTATION_UNITS<comma>unknown_units<comma>HAS_MEASURES<comma>no<comma>COMPRESS_AT_END<comma>no<comma>ENABLE_FAST_DELETES<comma>yes<comma>PRESERVE_GLOBALID<comma>no<comma>ENABLE_LOAD_ONLY_MODE<comma>no<comma>VALIDATE_FEATURES<comma>no<comma>SIMPLIFY_NETWORK_FEATURES<comma>no<comma>BEGIN_SQL<opencurly>0<closecurly><comma><comma>END_SQL<opencurly>0<closecurly><comma><comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE_TYPE,METAFILE,GEODATABASE_FILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.<space>%-#<openbracket><closebracket><quote><openparen><closeparen>!?*<apos><amp>+<backslash><solidus><opencurly><closecurly>|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>char<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>char<openparen>2048<closeparen><comma>fme_buffer<comma>char<openparen>2048<closeparen><comma>fme_xml<comma>char<openparen>2048<closeparen><comma>fme_json<comma>blob<comma>fme_binarybuffer<comma>blob<comma>fme_varbinary<openparen>width<closeparen><comma>blob<comma>fme_binary<openparen>width<closeparen><comma>globalid<comma>fme_buffer<comma>guid<comma>fme_buffer<comma>date<comma>fme_datetime<comma>timestamp_offset<comma>fme_datetime<comma>date_only<comma>fme_date<comma>time_only<comma>fme_time<comma>integer<comma>fme_int32<comma>integer<comma>fme_uint16<comma>smallint<comma>fme_int16<comma>smallint<comma>fme_int8<comma>smallint<comma>fme_uint8<comma>bigint<comma>fme_int64<comma>float<comma>fme_real32<comma>double<comma>fme_real64<comma>double<comma>fme_uint32<comma>char<openparen>20<closeparen><comma>fme_uint64<comma>boolean<comma>fme_boolean<comma><quote>double<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma>subtype<openparen>stringset<closeparen><comma>fme_char<openparen>width<closeparen><comma>subtype_codes<openparen>stringmap<closeparen><comma>fme_char<openparen>width<closeparen><comma>range_domain<openparen>range_domain<closeparen><comma>fme_char<openparen>width<closeparen><comma>coded_domain<openparen>coded_domain<closeparen><comma>fme_char<openparen>width<closeparen>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,<backslash><backslash><quote>:?*<lt><gt>|<openbracket>%#<space><apos><amp>+-<closebracket>.^~<dollar><comma><closeparen><openparen>,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point<comma>fme_point<comma>geodb_polygon<comma>fme_polygon<comma>geodb_polygon<comma>fme_rounded_rectangle<comma>geodb_polyline<comma>fme_line<comma>geodb_multipoint<comma>fme_point<comma>geodb_table<comma>fme_no_geom<comma>geodb_table<comma>fme_collection<comma>geodb_arc<comma>fme_arc<comma>geodb_ellipse<comma>fme_ellipse<comma>geodb_polygon<comma>fme_rectangle<comma>geodb_annotation<comma>fme_text<comma>geodb_pro_annotation<comma>fme_text<comma>geodb_dimension<comma>fme_point<comma>geodb_simple_junction<comma>fme_point<comma>geodb_simple_edge<comma>fme_line<comma>geodb_complex_edge<comma>fme_line<comma>geodb_attributed_relationship<comma>fme_no_geom<comma>geodb_relationship<comma>fme_no_geom<comma>geodb_undefined<comma>fme_no_geom<comma>geodb_metadata<comma>fme_polygon<comma>geodb_multipatch<comma>fme_surface<comma>geodb_multipatch<comma>fme_solid<comma>geodb_polygon<comma>fme_raster<comma>geodb_polygon<comma>fme_point_cloud<comma>geodb_polygon<comma>fme_voxel_grid<comma>geodb_table<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>fme_configuration_group<space>fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group<space>Table<quote><comma><comma><quote>GUI<space>NAMEDGROUP<space>fme_configuration_common_group<space>fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group<space>General<quote><comma><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_feature_operation<space>Insert<comma>INSERT<comma>fme_update_geometry<comma>fme_selection_group<comma>mie_pack%Update<comma>UPDATE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert<comma>UPSERT<comma>fme_where_builder_clause<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Delete<comma>DELETE<comma>++fme_table_handling+USE_EXISTING<comma>fme_update_geometry<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<comma>fme_spatial_group<comma>fme_advanced_group<comma>oracle_sequenced_cols%<lt>at<gt>Value<lt>openparen<gt>fme_db_operation<lt>closeparen<gt><comma>MULTIPLE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<space>Feature<space>Operation<quote><comma>INSERT<comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_table_handling<space>Use<lt>space<gt>Existing<comma>USE_EXISTING<comma>fme_table_creation_group%Create<lt>space<gt>If<lt>space<gt>Needed<comma>CREATE_IF_MISSING%Drop<lt>space<gt>and<lt>space<gt>Create<comma>DROP_CREATE%Truncate<lt>space<gt>Existing<comma>TRUNCATE_EXISTING<comma>fme_table_creation_group<space>Table<space>Handling<quote><comma>CREATE_IF_MISSING<comma><quote>GUI<space>WHOLE_LINE<space>LOOKUP_CHOICE<space>fme_update_geometry<space>Yes<comma>YES%No<comma>NO<space>Update<space>Spatial<space>Column<openparen>s<closeparen><quote><comma>YES<comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_selection_group<space>fme_selection_method<space>Row<space>Selection<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>RADIOPARAMETERGROUP<space>fme_selection_method<space>fme_match_columns<comma>MATCH_COLUMNS%fme_where_builder_clause<comma>BUILDER<space>Row<space>Selection<space>Method<quote><comma>MATCH_COLUMNS<comma><quote>GUI<space>WHOLE_LINE<space>ATTRLIST_COMMAS<space>fme_match_columns<space>Match<space>Columns<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>TEXT_EDIT_SQL_CFG_OR_ATTR<space>fme_where_builder_clause<space>MODE<comma>WHERE<space>WHERE<space>Clause<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_table_creation_group<space>FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP<space>Table<space>Creation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ORIGIN_GROUP<space>GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP<space>Origin<space>and<space>Scale<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ANNO_GROUP<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ADVANCED_GROUP<space>GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID<opencurly>1<closecurly>%GEODB_AVG_NUM_POINTS<space>Advanced<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_XY_GROUP<space>GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE<space>X<solidus>Y<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_Z_GROUP<space>GEODB_ZORIGIN%GEODB_ZSCALE<space>Z<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_MEASURES_GROUP<space>GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE<space>Measures<quote><comma><comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_NAME<space>Object<space>ID<space>Field<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_ALIAS<space>Object<space>ID<space>Alias<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_NAME<space>Shape<space>Field<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_ALIAS<space>Shape<space>Alias<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_CONFIG_KEYWORD<space>Configuration<space>Keyword<quote><comma>DEFAULTS<comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_GRID<opencurly>1<closecurly><space>Grid<space>1<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>GEODB_AVG_NUM_POINTS<space>Average<space>Number<space>of<space>Points<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XORIGIN<space>X<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_YORIGIN<space>Y<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XYSCALE<space>X<solidus>Y<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_Z_VALUES<space>yes%no<space>Contains<space>Z<space>Values<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZORIGIN<space>Z<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZSCALE<space>Z<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_MEASURES<space>yes%no<space>Contains<space>Measures<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_ORIGIN<space>Measure<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_SCALE<space>Measure<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<space>Reference<space>Scale<quote><comma>,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>geodb_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>GEODB_UPDATE_KEY_COLUMNS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_DROP_TABLE<comma>NO<comma>GEODB_TRUNCATE_TABLE<comma>NO<comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_selection_method<comma>MATCH_COLUMNS<comma>fme_match_columns<comma><quote><quote><quote><quote><quote><quote><comma>fme_where_builder_clause<comma><quote><quote><quote><quote><quote><quote><comma>fme_update_geometry<comma>YES<comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_FEATURE_DATASET<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_GRID<opencurly>1<closecurly><comma><quote><quote><quote><quote><quote><quote><comma>GEODB_AVG_NUM_POINTS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_Z_VALUES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_MEASURES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_ANNO_REFERENCE_SCALE<comma><quote><quote><quote><quote><quote><quote>,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS<comma><quote>_GEODBOverwriteGEODB<space>_GEODBOutTransactionType<space>_GEODBOutSimplifyGeometry<space>_GEODBOutXOrigin<space>_GEODBOutYOrigin<space>_GEODBOutScale<space>_GEODBOutZOrigin<space>_GEODBOutZScale<space>_GEODBOutGrid1<space>_TRANSLATE_SPATIAL_DATA_ONLY<space>_GEODBInResolveDomains<space>_GEODBInResolveSubtypeNames<space>_GEODBInIgnoreNetworkInfo<space>_GEODBInIgnoreRelationshipInfo<space>_GEODBInSplitComplexEdges<space>_GEODBInWhereClause<space>NULL_IN_SPLIT_COMPLEX_ANNOS<space>NULL_IN_CACHE_MULTIPATCH_TEXTURES<space>NULL_IN_SEARCH_METHOD<space>NULL_IN_SEARCH_ORDER<space>NULL_IN_SEARCH_FEATURE<space>NULL_IN_CHECK_SIMPLE_GEOM<space>NULL_IN_MERGE_FEAT_LINKED_ANNOS<space>NULL_IN_READ_THREE_POINT_ARCS<space>NULL_IN_BEGIN_SQL<opencurly>0<closecurly><space>NULL_IN_END_SQL<opencurly>0<closecurly><space>NULL_IN_SIMPLE_DONUT_GEOMETRY<space>_GEODB_IN_ALIAS_MODE<space>GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION<space>GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE<space>GEODATABASE_FILE_OUT_WRITER_MODE<space>GEODATABASE_FILE_OUT_TRANSACTION<space>GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL<space>GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY<space>GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_FFS_DUMP_FILE<space>GEODATABASE_FILE_OUT_ANNOTATION_UNITS<space>GEODATABASE_FILE_OUT_HAS_MEASURES<space>GEODATABASE_FILE_OUT_MEASURES_ORIGIN<space>GEODATABASE_FILE_OUT_MEASURES_SCALE<space>GEODATABASE_FILE_OUT_COMPRESS_AT_END<space>GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES<space>GEODATABASE_FILE_OUT_PRESERVE_GLOBALID<space>GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE<space>GEODATABASE_FILE_OUT_VALIDATE_FEATURES<space>GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES<space>GEODATABASE_FILE_OUT_BEGIN_SQL<opencurly>0<closecurly><space>GEODATABASE_FILE_OUT_END_SQL<opencurly>0<closecurly><quote><comma>PARAMS_TO_NOT_PROPAGATE_ON_INSPECT<comma><quote>BEGIN_SQL<opencurly>0<closecurly><space>END_SQL<opencurly>0<closecurly><quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>ATTRIBUTE_READING_HISTORIC<comma>ALL<comma>FEATURE_TYPE_NAME<comma><quote>Feature<space>Class<space>or<space>Table<quote><comma>FEATURE_TYPE_DEFAULT_NAME<comma>FeatureClass1<comma>SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME<comma>NO<comma>READER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>SQL_EXECUTE_DIRECTIVES<comma>INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<at>Value<openparen><u56fe><u5c42><u540d><closeparen>:Output,ftp_feature_type_name_exp,<at>Value<openparen><u56fe><u5c42><u540d><closeparen>,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_table,ftp_dynamic_schema,yes,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_FROM_ATTRIBUTE,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry,from_schema_definition,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,SCHEMA_FROM_FIRST_FEATURE,ftp_attribute_source,2,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>GEODB_ORIGIN_GROUP<comma><comma>GEODB_ANNO_GROUP<comma><comma>GEODB_ADVANCED_GROUP<comma><comma>GEODB_XY_GROUP<comma><comma>GEODB_Z_GROUP<comma><comma>GEODB_MEASURES_GROUP<comma><comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_GRID<opencurly>1<closecurly><comma><comma>GEODB_AVG_NUM_POINTS<comma><comma>GEODB_XORIGIN<comma><comma>GEODB_YORIGIN<comma><comma>GEODB_XYSCALE<comma><comma>GEODB_HAS_Z_VALUES<comma><comma>GEODB_ZORIGIN<comma><comma>GEODB_ZSCALE<comma><comma>GEODB_HAS_MEASURES<comma><comma>GEODB_MEASURES_ORIGIN<comma><comma>GEODB_MEASURES_SCALE<comma><comma>GEODB_ANNO_REFERENCE_SCALE<comma>" }    WRITER_PARAMS { "ANNOTATION_UNITS,unknown_units,BEGIN_SQL{0},,COMPRESS_AT_END,no,COORDINATE_SYSTEM_GRANULARITY,FEATURE_TYPE,DEFAULT_Z_VALUE,0,DESTINATION_DATASETTYPE_VALIDATION,Yes,DUMP_FAILED_FEATURES,no,ENABLE_FAST_DELETES,yes,ENABLE_LOAD_ONLY_MODE,no,END_SQL{0},,FEATURE_DATASET_HANDLING,WRITE,FFS_DUMP_FILE,,GEODB_SHARED_WRT_ADV_PARM_GROUP,,GRID_1,0,HAS_MEASURES,no,HAS_Z_VALUES,auto_detect,IGNORE_FAILED_FEATURE_ENTRY,no,IMPORT_XML_TEMPLATE_GROUP,NO,MAX_NUMBER_FAILED_FEATURES,-1,OVERWRITE_GEODB,NO,PRESERVE_GLOBALID,no,REQUESTED_GEODATABASE_VERSION,CURRENT,SIMPLIFY_GEOM,No,SIMPLIFY_NETWORK_FEATURES,no,TRANSACTION,0,TRANSACTION_INTERVAL,1000,TRANSACTION_TYPE,TRANSACTIONS,VALIDATE_FEATURES,no,X_ORIGIN,0,XY_SCALE,0,Y_ORIGIN,0,Z_ORIGIN,0,Z_SCALE,0" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { Output <at>Value<openparen><u56fe><u5c42><u540d><closeparen> }    INPUT Output FEATURE_TYPE Tester_2_PASSED  @SupplyAttributes(ENCODED,fme_template_feature_type,Output)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u56fe><u5c42><u540d><closeparen>,FeatureWriter))
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash><u5efa><u5e93>,FeatureWriter_2)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    GROUP_BY { 图层名 }    FLUSH_WHEN_GROUPS_CHANGE { No }    FACTORY_NAME { FeatureWriter_2 }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter_2)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>utf-8<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma><comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<at>Value<openparen><u56fe><u5c42><u540d><closeparen>:Output,ftp_feature_type_name_exp,<at>Value<openparen><u56fe><u5c42><u540d><closeparen>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,yes,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_FROM_ATTRIBUTE,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry,from_schema_definition,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,SCHEMA_FROM_FIRST_FEATURE,ftp_attribute_source,2,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,utf-8,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { Output <at>Value<openparen><u56fe><u5c42><u540d><closeparen> }    INPUT Output FEATURE_TYPE Tester_2_FAILED  @SupplyAttributes(ENCODED,fme_template_feature_type,Output)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u56fe><u5c42><u540d><closeparen>,FeatureWriter_2))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

