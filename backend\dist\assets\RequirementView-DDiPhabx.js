import{d as X,r as _,N as S,o as H,c as T,e as o,w as r,m as x,S as J,f as p,i as D,E as d,H as W,I as K,A as y,C as R,s as Q,b as V,a4 as ee,T as te,l as z,_ as oe}from"./index-B7WNRWO3.js";import{u as ae}from"./user-DIpGcr2j.js";const re={class:"requirement-container"},le=X({__name:"RequirementView",setup(se){const w=ae(),q=_(),m=_([]),k=_([]),U=_(0),h=_(!1),g=_(""),j=t=>window.location.pathname.startsWith("/gsi/")?`/gsi${t}`:`${D.defaults.baseURL}${t}`,a=S({toolName:"",project:"",approvals:"",deliveryDate:"",runMode:"online",testData:"",description:"",contact:"",phone:""}),$=async()=>{try{const t=await D.get("/api/approvers");t.data.success?k.value=t.data.data:d.error("获取审批人员列表失败")}catch(t){console.error("获取审批人员列表失败:",t),d.error("获取审批人员列表失败")}};H(()=>{$()});const F=S({toolName:[{required:!0,message:"请输入工具名称",trigger:["blur","input"]},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:["blur","input"]}],project:[{required:!0,message:"请输入所属项目",trigger:["blur","input"]}],approvals:[{required:!0,message:"请选择审批人员",trigger:["change","blur"]}],deliveryDate:[{required:!0,message:"请选择预计交接时间",trigger:["change","blur"]}],runMode:[{required:!0,message:"请选择运行方式",trigger:["change","blur"]}],description:[{required:!0,message:"请输入需求描述",trigger:["blur","input"]},{min:10,message:"描述不能少于10个字符",trigger:["blur","input"]}],contact:[{required:!0,message:"请输入联系人",trigger:["blur","input"]}],phone:[{required:!0,message:"请输入联系电话",trigger:["blur","input"]},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:["blur","input"]}]}),A=t=>{var l;const e=[".zip",".rar",".7z"],i=t.name.substring(t.name.lastIndexOf(".")).toLowerCase();if(!e.includes(i)){d.error("只能上传ZIP、RAR、7Z格式的压缩包"),m.value=[];return}const n=10*1024*1024*1024;if(t.size&&t.size>n){d.error("文件大小不能超过10GB"),m.value=[];return}if(t.size===0){d.error("文件不能为空"),m.value=[];return}console.log("文件验证通过:",{name:t.name,size:t.size,type:(l=t.raw)==null?void 0:l.type}),m.value=[t],a.testData=t.name},E=t=>{h.value=!0,U.value=Math.round(t.percent),g.value="",t.percent===100&&(setTimeout(()=>{h.value=!1,U.value=0,g.value="success"},500),setTimeout(()=>{g.value=""},1500))},C=()=>{h.value=!1,g.value="exception",setTimeout(()=>{g.value="",U.value=0},1500)},I=t=>{t.success?(d.success("文件上传成功"),a.testData=t.data.path):(d.error(t.message||"文件上传失败"),C())},P=async t=>{var u,f;const{file:e,onProgress:i,onSuccess:n,onError:l}=t;if(!e){l(new Error("没有选择文件"));return}console.log("开始上传文件:",{name:e.name,size:e.size,type:e.type});const b=new FormData;b.append("file",e);try{const c=await D.post("/api/upload",b,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${w.token}`,"X-Username":w.user.username},onUploadProgress:v=>{if(v.total){const M=Math.round(v.loaded*100/v.total);i({percent:M})}},timeout:3e5});console.log("上传响应:",c.data),c.data.success?n(c.data):l(new Error(c.data.message||"上传失败"))}catch(c){console.error("上传错误:",c);const v=((f=(u=c.response)==null?void 0:u.data)==null?void 0:f.message)||c.message||"上传失败";l(new Error(v))}},O=async t=>{t&&await t.validate(async(e,i)=>{var n;if(e)try{await te.confirm("确认提交工具需求申请吗？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const l=sessionStorage.getItem("user");if(!l){d.error("请先登录");return}if(!JSON.parse(l).username){d.error("请先登录");return}const u=new FormData;if(u.append("title",a.toolName),u.append("description",a.description),u.append("category",a.project),u.append("priority",a.runMode),u.append("contact",a.contact),u.append("phone",a.phone),u.append("delivery_date",a.deliveryDate),u.append("approvals",a.approvals),m.value.length>0){const c=m.value[0].raw;c&&u.append("files",c)}const f=await D.post("/api/requirements/submit",u,{headers:{"Content-Type":"multipart/form-data"}});f.data.success?(d.success("需求提交成功"),N(t)):d.error(f.data.message||"提交失败")}catch(l){if(l!=null&&l.toString().includes("cancel"))return;console.error("提交失败:",l),((n=l.response)==null?void 0:n.status)===401?d.error("请先登录"):d.error("提交失败，请稍后重试")}else if(console.log("表单验证失败:",i),i){const l=Object.values(i)[0];l&&l[0]&&d.error(l[0].message)}})},N=t=>{t&&(t.resetFields(),m.value=[])};return(t,e)=>{const i=p("el-input"),n=p("el-form-item"),l=p("el-option"),b=p("el-select"),u=p("el-date-picker"),f=p("el-radio"),c=p("el-radio-group"),v=p("el-icon"),M=p("el-upload"),G=p("el-progress"),B=p("el-button"),L=p("el-form"),Y=p("el-card"),Z=p("el-config-provider");return z(),T("div",re,[o(Z,{locale:x(J)},{default:r(()=>[o(Y,{class:"requirement-form"},{header:r(()=>e[10]||(e[10]=[V("div",{class:"card-header"},[V("h2",null,"工具需求申请")],-1)])),default:r(()=>[o(L,{ref_key:"formRef",ref:q,model:a,rules:F,"label-width":"120px","label-position":"right",class:"form-content"},{default:r(()=>[o(n,{label:"工具名称",prop:"toolName"},{default:r(()=>[o(i,{modelValue:a.toolName,"onUpdate:modelValue":e[0]||(e[0]=s=>a.toolName=s),placeholder:"请输入工具名称"},null,8,["modelValue"])]),_:1}),o(n,{label:"所属项目",prop:"project"},{default:r(()=>[o(i,{modelValue:a.project,"onUpdate:modelValue":e[1]||(e[1]=s=>a.project=s),placeholder:"请输入所属项目名称"},null,8,["modelValue"])]),_:1}),o(n,{label:"审批人员",prop:"approvals"},{default:r(()=>[o(b,{modelValue:a.approvals,"onUpdate:modelValue":e[2]||(e[2]=s=>a.approvals=s),placeholder:"请选择审批人员",style:{width:"100%"}},{default:r(()=>[(z(!0),T(W,null,K(k.value,s=>(z(),R(l,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"预计交接时间",prop:"deliveryDate"},{default:r(()=>[o(u,{modelValue:a.deliveryDate,"onUpdate:modelValue":e[3]||(e[3]=s=>a.deliveryDate=s),type:"date",placeholder:"请选择预计交接时间",style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),o(n,{label:"运行方式",prop:"runMode"},{default:r(()=>[o(c,{modelValue:a.runMode,"onUpdate:modelValue":e[4]||(e[4]=s=>a.runMode=s)},{default:r(()=>[o(f,{value:"online"},{default:r(()=>e[11]||(e[11]=[y("在线")])),_:1}),o(f,{value:"offline"},{default:r(()=>e[12]||(e[12]=[y("离线")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"测试数据",prop:"testData"},{default:r(()=>[o(M,{class:"upload-demo",drag:"",action:j("/api/upload"),"auto-upload":!0,"http-request":P,"on-change":A,"on-progress":E,"on-success":I,"on-error":C,"file-list":m.value,accept:".zip,.rar,.7z",headers:{Authorization:`Bearer ${x(w).token}`,"X-Username":x(w).user.username}},{tip:r(()=>e[13]||(e[13]=[V("div",{class:"el-upload__tip"}," 支持上传ZIP、RAR、7Z格式的压缩包，单个文件不超过10GB ",-1)])),default:r(()=>[o(v,{class:"el-icon--upload"},{default:r(()=>[o(x(ee))]),_:1}),e[14]||(e[14]=V("div",{class:"el-upload__text"},[y(" 将压缩包拖到此处，或"),V("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","headers"]),h.value||g.value?(z(),R(G,{key:0,percentage:U.value,status:g.value,format:()=>"",style:{"margin-top":"10px"}},null,8,["percentage","status"])):Q("",!0)]),_:1}),o(n,{label:"需求描述",prop:"description"},{default:r(()=>[o(i,{modelValue:a.description,"onUpdate:modelValue":e[5]||(e[5]=s=>a.description=s),type:"textarea",rows:4,placeholder:"请详细描述工具的功能需求、使用场景等"},null,8,["modelValue"])]),_:1}),o(n,{label:"联系人",prop:"contact"},{default:r(()=>[o(i,{modelValue:a.contact,"onUpdate:modelValue":e[6]||(e[6]=s=>a.contact=s),placeholder:"请输入联系人姓名"},null,8,["modelValue"])]),_:1}),o(n,{label:"联系电话",prop:"phone"},{default:r(()=>[o(i,{modelValue:a.phone,"onUpdate:modelValue":e[7]||(e[7]=s=>a.phone=s),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),o(n,null,{default:r(()=>[o(B,{type:"primary",onClick:e[8]||(e[8]=s=>O(q.value))},{default:r(()=>e[15]||(e[15]=[y("提交申请")])),_:1}),o(B,{onClick:e[9]||(e[9]=s=>N(q.value))},{default:r(()=>e[16]||(e[16]=[y("重置")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1},8,["locale"])])}}}),pe=oe(le,[["__scopeId","data-v-83d69a1b"]]);export{pe as default};
