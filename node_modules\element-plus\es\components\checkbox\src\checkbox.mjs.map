{"version": 3, "file": "checkbox.mjs", "sources": ["../../../../../../packages/components/checkbox/src/checkbox.ts"], "sourcesContent": ["import { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport { isBoolean, isNumber, isString } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Checkbox from './checkbox.vue'\n\nexport type CheckboxValueType = string | number | boolean\n\nexport const checkboxProps = {\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [Number, String, Boolean],\n    default: undefined,\n  },\n  /**\n   * @description label of the Checkbox when used inside a `checkbox-group`\n   */\n  label: {\n    type: [String, Boolean, Number, Object],\n    default: undefined,\n  },\n  /**\n   * @description value of the Checkbox when used inside a `checkbox-group`\n   */\n  value: {\n    type: [String, Boolean, Number, Object],\n    default: undefined,\n  },\n  /**\n   * @description Set indeterminate state, only responsible for style control\n   */\n  indeterminate: Boolean,\n  /**\n   * @description whether the Checkbox is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description if the Checkbox is checked\n   */\n  checked: <PERSON>olean,\n  /**\n   * @description native 'name' attribute\n   */\n  name: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description value of the Checkbox if it's checked\n   */\n  trueValue: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @description value of the Checkbox if it's not checked\n   */\n  falseValue: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @deprecated use `trueValue` instead\n   * @description value of the Checkbox if it's checked\n   */\n  trueLabel: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @deprecated use `falseValue` instead\n   * @description value of the Checkbox if it's not checked\n   */\n  falseLabel: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @description input id\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description whether to add a border around Checkbox\n   */\n  border: Boolean,\n  /**\n   * @description size of the Checkbox\n   */\n  size: useSizeProp,\n  /**\n   * @description input tabindex\n   */\n  tabindex: [String, Number],\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  ...useAriaProps(['ariaControls']),\n}\n\nexport const checkboxEmits = {\n  [UPDATE_MODEL_EVENT]: (val: CheckboxValueType) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n  change: (val: CheckboxValueType) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n}\n\nexport type CheckboxProps = ExtractPropTypes<typeof checkboxProps>\nexport type CheckboxEmits = typeof checkboxEmits\nexport type CheckboxInstance = InstanceType<typeof Checkbox> & unknown\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,aAAa,GAAG;AAC7B,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;AAC3C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;AAC3C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC5B,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAG,YAAY,CAAC,CAAC,cAAc,CAAC,CAAC;AACnC,EAAE;AACU,MAAC,aAAa,GAAG;AAC7B,EAAE,CAAC,kBAAkB,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC;AACjF,EAAE,MAAM,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC;AACnE;;;;"}