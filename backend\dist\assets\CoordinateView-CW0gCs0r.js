import{d as Fa,u as Oa,r as i,M as O,O as re,o as Ae,a as $t,ad as za,c as M,e as t,w as l,f as v,i as x,E as u,Y as et,P as z,Z as B,b as n,J as Pa,A as c,m as w,D as Ee,C as D,a7 as Ia,n as Fe,B as Ma,F as _,ab as tt,a4 as we,a0 as ja,a1 as at,s as ie,R as At,Q as Ga,H as lt,I as st,T as Oe,l as h,a6 as Q,j as Ba,_ as Na}from"./index-CdoJMuEX.js";import{f as Wa}from"./format-CBpsKyOP.js";const qa={class:"tools-container"},Ha={key:"convert"},Xa={class:"steps-progress"},Ya={class:"start-container"},Za={class:"start-content"},Ja={class:"step-content"},Qa={style:{"margin-bottom":"20px","margin-top":"20px","padding-left":"50px","text-align":"left",display:"flex",gap:"12px","align-items":"center"}},Ka=["title"],el=["title"],tl={style:{display:"none"}},al={class:"step-footer"},ll={class:"step-content"},sl={class:"upload-sections"},ol={class:"upload-section"},nl={class:"section-title"},rl={class:"upload-section"},il={class:"section-title"},ul={class:"file-list-table",style:{"margin-top":"20px"}},dl={class:"table-header",style:{display:"flex","justify-content":"flex-start","align-items":"center"}},cl={class:"license-tags",style:{display:"flex","align-items":"center","margin-left":"30px"}},pl=["title"],fl={class:"step-footer"},ml={class:"step-content"},vl={class:"output-settings-container"},_l={class:"info-confirmation"},gl={class:"section-card"},yl={class:"section-card"},hl={class:"output-types"},wl={class:"section-card"},bl={style:{display:"flex","flex-direction":"column",gap:"24px","max-width":"350px"}},Cl={style:{display:"flex","align-items":"center"}},xl={style:{display:"flex","align-items":"center"}},kl={key:0,style:{display:"flex","align-items":"center"}},Vl={class:"step-footer"},Dl={class:"step-content"},Ul={key:"history"},Ll={class:"table-container"},Tl=["title"],Rl={style:{display:"flex","justify-content":"center"}},Sl={key:0,class:"message-content"},$l={key:6,class:"color-picker-wrapper"},Al={class:"color-value"},El={key:1,class:"no-params"},Fl={class:"dialog-footer"},Ol={class:"dialog-footer"},zl={class:"dialog-footer"},Pl={style:{display:"flex","justify-content":"center"}},Il={class:"error-log-content"},Ml={class:"dialog-footer"},jl={class:"error-message"},Gl={class:"error-message"},Bl={class:"error-message"},Nl={class:"error-message"},Wl={class:"error-message"},ql={class:"dialog-footer"},Hl=5e3,Xl=Fa({__name:"CoordinateView",setup(Yl){const b=Oa(),Et=i(!1),K=i([]),ee=i([]),ot=i(""),nt=i(""),te=i("convert"),Ft=i(1),Ot=i(1),zt=i(1),rt=i(10),it=i(10);i(10);const ze=i(0);i(0);const S=i(localStorage.getItem("token")),A={GET_TOOLS_LIST:"/api/Coordinate/tools/list",GET_TOOLS_COUNT:"/api/Coordinate/tools/count",UPLOAD_TOOL:"/api/Coordinate/tools/upload",DELETE_TOOL:"/api/Coordinate/tools/delete",APPLY_TOOL:"/api/Coordinate/tools/apply",APPROVE_TOOL:"/api/Coordinate/tools/approve",REJECT_TOOL:"/api/Coordinate/tools/reject",GET_MY_APPLICATIONS:"/api/tools/my-applications",GET_MY_APPROVALS:"/api/Coordinate/tools/my-approvals",RUN_TOOL:"/api/Coordinate/tools/run",RUN_FME:"/api/Coordinate/run_fme",GET_RUN_RECORDS:"/api/Coordinate/tools/run_records",UPLOAD_FILE:"/api/upload",DOWNLOAD_RESULT:"/api/tools/download_result",UPDATE_USAGE_COUNT:"/api/Coordinate/tools/update_usacount",DELETE_FILE:"/api/Coordinate/delete"},Pt=a=>window.location.pathname.startsWith("/gsi/")?`${window.location.protocol}//${window.location.host}/gsi${a}`:`${window.location.protocol}//${window.location.host}${a}`,It=a=>{a.name==="history"&&_e()},Mt=O(()=>K.value.filter(a=>a.fmw_name.toLowerCase().includes(ot.value.toLowerCase())||(a.user_project||"").toLowerCase().includes(ot.value.toLowerCase()))),jt=O(()=>ee.value.filter(a=>a.fmw_name.toLowerCase().includes(nt.value.toLowerCase())||a.project.toLowerCase().includes(nt.value.toLowerCase())));O(()=>{const a=(zt.value-1)*rt.value,e=a+rt.value;return Mt.value.slice(a,e)}),O(()=>{const a=(Ot.value-1)*it.value,e=a+it.value;return jt.value.slice(a,e)});const be=async()=>{var a;try{if(!((a=b.user)!=null&&a.username)){K.value=[];return}const e=await x.post(A.GET_TOOLS_LIST,{username:b.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});e.data.success?K.value=e.data.data.map(o=>({...o,user_project:o.user_project||o.project||"未指定项目",created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取工具列表失败"),K.value=[])}catch(e){console.error("获取工具列表失败:",e),K.value=[]}},Gt=async()=>{var a;try{if(!((a=b.user)!=null&&a.username)){ee.value=[];return}const e=await x.get("/api/tools/my-applications",{params:{source:"CoordinateView",fmw_id:"coordinatetransformation"},headers:{"X-Username":b.user.username}});e.data.success?ee.value=e.data.data.filter(o=>o.status==="已通过").map(o=>({...o,count:parseInt(o.count)||0,usage_count:parseInt(o.usage_count)||0,remaining_count:(parseInt(o.usage_count)||0)-(parseInt(o.count)||0),user_project:o.user_project||"未指定项目",end_date:o.end_date||null,created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取申请列表失败"),ee.value=[])}catch{ee.value=[]}},Ce=(a,e="yyyy-MM-dd HH:mm")=>{if(!a)return"--";try{return Wa(new Date(a),e)}catch{return"--"}};re(()=>b.user,a=>{a!=null&&a.username?be():(K.value=[],ee.value=[])},{immediate:!0});const ue=i(!1),P=i(null),ae=i([]),g=i({}),R=i(null),Pe=i({}),Bt=i(!1);i(!1);const Ie=i(!1),de=i([]);re(Ie,a=>{a||(Ft.value=1)});const Nt=a=>({running:"warning",success:"success",failed:"danger"})[a]||"info",Wt=a=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[a]||a,ut=a=>{if(!a)return"-";const e=Math.floor(a/60),o=a%60;return`${e}分${o}秒`},qt=async a=>{try{await Oe.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=de.value.findIndex(o=>o.task_id===a.task_id);e!==-1&&(de.value.splice(e,1),ze.value--),x.post("/api/Coordinate/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}}).then(o=>{o.data.success?u.success("删除成功"):(de.value.splice(e,0,a),ze.value++,u.error(o.data.message||"删除失败"))}).catch(o=>{de.value.splice(e,0,a),ze.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&u.error("删除失败，请稍后重试")}};Ae(()=>{be(),H.value=qe(),P.value={fmw_id:"coordinatetransformation",fmw_name:"坐标转换",fmw_path:"tools/coordinatetransformation/coordinatetransformation.fmw"}}),$t(()=>{});const ce=i(!1),pe=i(!1),T=i({fmw_name:"",project:"",description:"",file:null}),N=i({fmw_id:"",file:null}),Ht={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},W=i(null),q=i(null),Xt=()=>{ue.value=!1},Yt=()=>{var a;g.value={},ae.value=[],R.value&&R.value.resetFields(),(a=R.value)!=null&&a.$el&&R.value.$el.querySelectorAll(".el-upload").forEach(o=>{var p;const f=(p=o.__vueParentComponent)==null?void 0:p.ctx;f&&typeof f.clearFiles=="function"&&f.clearFiles()})},Zt=()=>{ce.value=!1},Jt=()=>{j.value&&j.value.resetFields(),T.value={fmw_name:"",project:"",description:"",file:null},W.value&&typeof W.value.clearFiles=="function"&&W.value.clearFiles()},Qt=()=>{pe.value=!1},Kt=()=>{xe.value&&xe.value.resetFields(),N.value={fmw_id:"",file:null},q.value&&typeof q.value.clearFiles=="function"&&q.value.clearFiles()},j=i(),xe=i();re(()=>g.value,a=>{const e={};ae.value.forEach(o=>{je(o)&&Pe.value[o.prop]&&(e[o.prop]=Pe.value[o.prop])}),R.value&&(R.value.clearValidate(),R.value.rules=e)},{deep:!0});const Me=i(!1),ea=async()=>{var a;Me.value=!0;try{if(!P.value){u.error("工具信息不完整"),Me.value=!1;return}const e=ae.value.filter(r=>je(r));console.log("可见的表单项:",e.map(r=>r.prop));for(const r of e)if(r.required&&!g.value[r.prop]){let y="";r.type==="file"||r.type==="upload"?y=`请上传${r.label}`:r.type==="select"||r.type==="dropdown"||r.type==="listbox"?y=`请选择${r.label}`:y=`请填写${r.label}`,u.error(y);return}const f={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:P.value.fmw_id,fmw_name:P.value.fmw_name,fmw_path:P.value.fmw_path,params:{}};for(const r of e){const y=g.value[r.prop];y!=null&&!r.prop.endsWith("_value")&&(r.type==="color"?f.params[r.prop]=g.value[`${r.prop}_value`]:f.params[r.prop]=y)}if(console.log("提交的请求数据:",f),Bt.value)try{const r=await x.post(A.UPDATE_COUNT,{id:P.value.id,username:b.user.username});if(!r.data.success){u.error(r.data.message||"更新使用次数失败");return}}catch(r){console.error("更新使用次数失败:",r),u.error("更新使用次数失败，请稍后重试");return}const p=await x.post(A.RUN_FME,f,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});if(p.data.success){try{const r=await x.post("/api/Coordinate/tools/update-run-times",{},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});r.data.success||console.error("更新运行次数失败:",r.data.message)}catch(r){console.error("更新运行次数失败:",r)}u.success("任务提交成功"),g.value={},ae.value=[],R.value&&R.value.resetFields(),(a=R.value)!=null&&a.$el&&R.value.$el.querySelectorAll(".el-upload").forEach(y=>{var m;const U=(m=y.__vueParentComponent)==null?void 0:m.ctx;U&&typeof U.clearFiles=="function"&&U.clearFiles()}),ue.value=!1,window.location.reload(),await Gt(),te.value="history",yt()}else u.error(p.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),u.error("提交失败，请稍后重试")}finally{Me.value=!1}},ta=async()=>{if(j.value)try{if(await j.value.validate(),!T.value.file){u.error("请上传工具文件");return}const a=new FormData;a.append("file",T.value.file);const e=await x.post(`${A.UPLOAD_FILE}`,a,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const o={fmw_id,fmw_name:T.value.fmw_name,project:T.value.project,description:T.value.description,fmw_path:e.data.data.path,file_path:e.data.data.path,data:new Date().toISOString()},f=await x.post(`${A.UPLOAD_TOOL}`,o,{headers:{Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});f.data.success?(u.success("工具上传成功"),ce.value=!1,T.value={fmw_name:"",project:"",description:"",file:null},j.value&&j.value.resetFields(),W.value&&typeof W.value.clearFiles=="function"&&W.value.clearFiles(),await be()):u.error(f.data.message||"上传失败")}catch(a){console.error("上传工具失败:",a),u.error("参数未填写完整")}},aa=a=>a.name.toLowerCase().endsWith(".fmw")?a.size/1024/1024/1024<10?!0:(u.error("文件大小不能超过10GB"),!1):(u.error("只能上传FMW文件"),!1),la=a=>(N.value.file=a.raw,!1),sa=async()=>{if(!N.value.file){u.error("请选择更新文件");return}try{const a=new FormData;a.append("file",N.value.file);const e=await x.post(`${A.UPLOAD_FILE}`,a,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const o={fmw_id:N.value.fmw_id,file_path:e.data.data.path},f=await x.post(`${A.UPDATE_TOOL}`,o,{headers:{Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});f.data.success?(u.success("工具更新成功"),pe.value=!1,N.value={fmw_id:"",file:null},xe.value&&xe.value.resetFields(),q.value&&typeof q.value.clearFiles=="function"&&q.value.clearFiles(),await be()):u.error(f.data.message||"更新失败")}catch(a){console.error("更新工具失败:",a),u.error("更新失败，请检查网络连接")}},oa=(a,e)=>{if(!e){g.value[a]="rgb(255, 255, 255)";return}g.value[a]=e;const o=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(o){const[,f,p,r]=o;g.value[`${a}_value`]=`${f},${p},${r}`}else g.value[`${a}_value`]="255,255,255"},je=a=>{var o;if(!((o=a.component)!=null&&o.visibility))return!0;const e=a.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const f of e.if){const{condition:p,then:r}=f;let y=!1;if(p.allOf)y=p.allOf.every(U=>{if(U.equals){const{parameter:m,value:I}=U.equals;return g.value[m]===I}else if(U.isEnabled){const{parameter:m}=U.isEnabled;return!!g.value[m]}return!1});else if(p.equals){const{parameter:U,value:m}=p.equals;y=g.value[U]===m}else if(p.isEnabled){const{parameter:U}=p.isEnabled;y=!!g.value[U]}if(y)return r==="visibleEnabled"||r==="visibleDisabled"}return!1},ke=i(!1),dt=i(""),le=i(!1),fe=i(),Ge=i(!1),ct=i([]),k=i({tool_name:"坐标转换",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),na={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},ra=a=>a.getTime()<Date.now()-864e5,ia=()=>{le.value=!0,ua()},ua=async()=>{try{const a=await x.get("/api/admin-users");a.data.success?ct.value=a.data.data:u.error("获取审批人失败")}catch{u.error("获取审批人失败")}},da=async()=>{fe.value&&await fe.value.validate(async a=>{if(a){Ge.value=!0;try{const e=await x.post("/api/Coordinate/tools/apply",{fmw_id:"coordinatetransformation",fmw_name:"坐标转换",applicant:b.user.username,reason:k.value.reason,end_date:k.value.end_date,usage_count:k.value.usage_count,user_project:k.value.user_project,reviewer:k.value.approver},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});e.data.success?(u.success("申请提交成功"),le.value=!1,pt(),await De()):u.error(e.data.message||"申请提交失败")}catch{u.error("申请提交失败")}finally{Ge.value=!1}}})},pt=()=>{fe.value&&fe.value.resetFields(),Object.assign(k.value,{tool_name:"坐标转换",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})},Ve=i([]),Be=i(!1),F=i(null),L=i(0),ft=i(0),Ne=()=>{L.value--},We=()=>{if(L.value,L.value===2&&!ve.value){u.warning("许可次数不足，无法进入下一步");return}L.value++};re(L,a=>{setTimeout(()=>{ft.value=a},300)});const De=async()=>{Be.value=!0;try{const a=await x.get("/api/tools/my-applications",{params:{source:"CoordinateView",fmw_id:"coordinatetransformation"},headers:{Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});if(a.data.success){Ve.value=a.data.data;const e=Ve.value.find(o=>mt(o));e&&(F.value=e.id)}else u.error(a.data.message||"获取许可列表失败")}catch(a){console.error("获取许可列表失败:",a),u.error("获取许可列表失败")}finally{Be.value=!1}};Ae(()=>{De()});function me(a){return{审批中:{type:"info",text:"审批中"},已通过:{type:"success",text:"已通过"},已驳回:{type:"danger",text:"已驳回"},已过期:{type:"warning",text:"已过期"},已耗尽:{type:"warning",text:"已耗尽"},可用:{type:"success",text:"可用"}}[a]||{type:"default",text:a}}const se=i(!1),ca=za(async()=>{if(!se.value){se.value=!0;try{await De()}finally{se.value=!1}}},1e3,{leading:!0,trailing:!1}),pa=a=>{if(me(a.status).text!=="已通过")return;const e=new Date(a.end_date),o=new Date;if(o.setHours(0,0,0,0),e<o){u.warning("该许可已过期");return}if(a.count>=a.usage_count){u.warning("该许可使用次数已达上限");return}if(L.value===2&&V.value.length>0&&(a.usage_count||0)-(a.count||0)-V.value.length<0){u.warning(`当前选择的许可剩余次数不足，无法处理 ${V.value.length} 个文件`);return}F.value=a.id},fa=({row:a})=>{if(me(a.status).text!=="已通过")return"disabled-row";const e=new Date(a.end_date),o=new Date;return o.setHours(0,0,0,0),e<o||a.count>=a.usage_count?"disabled-row":"clickable-row"},mt=a=>{if(me(a.status).text!=="已通过")return!1;const e=new Date(a.end_date),o=new Date;return o.setHours(0,0,0,0),!(e<o||a.count>=a.usage_count)},ma=i([]),H=i(""),qe=()=>{const a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return Array.from({length:40},()=>a.charAt(Math.floor(Math.random()*a.length))).join("")};Ae(()=>{H.value=qe()});const He=async a=>{try{const e=await x.post(A.DELETE_FILE,{folderId:H.value,fileName:a.name},{headers:{"Content-Type":"application/json"}});if(e.data.success){const o=V.value.findIndex(f=>f.name===a.name);o!==-1&&V.value.splice(o,1),u.success("文件删除成功")}else u.error(e.data.message||"文件删除失败")}catch(e){console.error("Delete error:",e),u.error("文件删除失败")}},va=a=>{Oe.confirm(`确定要移除文件 "${a.name}" 吗？`,"移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{He({name:a.name})}).catch(()=>{})},Xe=O(()=>V.value.some(a=>a.type==="DWG")),oe=O(()=>F.value?Ve.value.find(a=>a.id===F.value):null),Ye=O(()=>{var a,e;return(((a=oe.value)==null?void 0:a.usage_count)||0)-(((e=oe.value)==null?void 0:e.count)||0)-V.value.length}),ve=O(()=>Ye.value>=0),vt=i([]),X=i([]),Ze=i(!1),_t=i(1),gt=i(10),Ue=i(0),Le=i(null),Je=i(!1),_a=a=>({running:"warning",success:"success",failed:"danger"})[a]||"info",ga=a=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[a]||a,_e=async(a=(e=>(e=P.value)==null?void 0:e.fmw_id)()||"coordinatetransformation")=>{try{Ze.value=!0;const o=await x.post("/api/Coordinate/tools/run_records",{fmw_id:a,username:b.user.username,page:_t.value,page_size:gt.value},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}});o.data.success?(X.value=o.data.data.records,Ue.value=o.data.data.pagination.total):u.error(o.data.message||"获取历史记录失败")}catch(o){console.error("获取历史记录失败:",o),u.error("获取历史记录失败")}finally{Ze.value=!1}},yt=()=>{Je.value||(Je.value=!0,Le.value=setInterval(async()=>{X.value.some(e=>e.status==="running"||e.status==="pending")?(console.log("检测到运行中的任务，正在更新状态..."),await _e()):(console.log("没有运行中的任务，停止轮询"),ht())},Hl))},ht=()=>{Le.value&&(clearInterval(Le.value),Le.value=null),Je.value=!1},ya=async a=>{try{await Oe.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=X.value.findIndex(o=>o.task_id===a.task_id);e!==-1&&(X.value.splice(e,1),Ue.value--),x.post("/api/Coordinate/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":b.user.username}}).then(o=>{o.data.success?u.success("删除成功"):(X.value.splice(e,0,a),Ue.value++,u.error(o.data.message||"删除失败"))}).catch(o=>{X.value.splice(e,0,a),Ue.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&(console.error("删除历史记录失败:",e),u.error("删除历史记录失败"))}},wt=async a=>{try{const e=`tools/coordinatetransformation/output/${a.task_id}/${a.file_name}`,o=`${A.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`,f=document.createElement("a");f.style.display="none",document.body.appendChild(f),f.href=Pt(o),f.download=a.file_name,f.click(),setTimeout(()=>{document.body.removeChild(f)},100),u.success("开始下载")}catch(e){console.error("下载历史记录失败:",e),u.error("下载失败，请稍后重试")}},bt=async a=>{try{a.error_message?(dt.value=a.error_message,ke.value=!0):u.warning("暂无错误信息")}catch(e){console.error("显示日志失败:",e),u.error("显示日志失败")}};re([_t,gt],()=>{_e()}),Ae(()=>{_e()}),$t(()=>{ht()});const Te=i(null);re(L,async()=>{if(await Ba(),Te.value){const a=Te.value.querySelector('.step-content[v-show="true"]');a&&(Te.value.style.height=`${a.scrollHeight}px`)}}),O(()=>vt.value.length>0);const ha=i([]),wa=i([]),V=i([]),Ct=i(!1),ba=a=>a.name.toLowerCase().endsWith(".dwg")?!0:(u.error("只能上传DWG格式的文件！"),!1),Ca=a=>[".zip",".rar",".7z"].some(o=>a.name.toLowerCase().endsWith(o))?!0:(u.error("只能上传ZIP、RAR、7Z格式的压缩包！"),!1),xa=(a,e)=>{a.success&&Array.isArray(a.files)?(a.files.forEach(o=>{o.name&&o.name.toLowerCase().endsWith(".dwg")&&V.value.push({name:o.name,type:"DWG",size:o.size})}),u.success("DWG文件上传成功")):u.error(a.message||"DWG文件上传失败")},ka=(a,e)=>{a.success&&Array.isArray(a.files)?(a.files.forEach(o=>{if(o.name){let f="";o.name.toLowerCase().endsWith(".dwg")?f="DWG":o.name.toLowerCase().endsWith(".gdb")?f="GDB":o.name.toLowerCase().endsWith(".shp")&&(f="SHP"),f&&V.value.push({name:o.name,type:f,size:o.size})}}),u.success("压缩包解析成功")):u.error(a.message||"压缩包解析失败")},xt=a=>{u.error("文件上传失败")},Y=i(""),Z=i(""),Re=i("Release2007"),Va=O(()=>!(!Y.value||!Z.value||Y.value===Z.value||Xe.value&&!Re.value||!ve.value)),Da=async()=>{try{const a=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,o={trans_form:Array.from(new Set(V.value.map(r=>r.type.toLowerCase()))).join(" "),input_date:`temp/${H.value}`,source_coor:Y.value,target_coor:Z.value,save_path:`tools/coordinatetransformation/output/${a}`,...Xe.value?{VERSION_2:Re.value}:{}},f={task_id:a,fmw_id:"coordinatetransformation",fmw_name:"坐标转换",fmw_path:"tools/coordinatetransformation/coordinatetransformation.fmw",params:o,up_nums:V.value.length},p=sessionStorage.getItem("user");if(!p){u.error("未登录,请先登录");return}try{const r=JSON.parse(p);if(!r.username){u.error("用户信息不完整,请重新登录");return}const y=await x.post(A.RUN_FME,f,{headers:{"Content-Type":"application/json","X-Username":r.username}});if(y.data.success){const U=await x.post(A.UPDATE_USAGE_COUNT,{id:F.value,username:r.username,file_count:V.value.length},{headers:{"Content-Type":"application/json","X-Username":r.username}});U.data.success||console.error("更新使用次数失败:",U.data.message),u.success("任务提交成功"),await new Promise(m=>setTimeout(m,1e3)),await _e("coordinatetransformation"),yt(),L.value=0,F.value=null,V.value=[],vt.value=[],H.value=qe(),ma.value=[],Ct.value=!1,se.value=!1,await De(),te.value="history"}else u.error(y.data.message||"任务提交失败")}catch(r){console.error("解析用户信息失败:",r),u.error("用户信息解析失败,请重新登录")}}catch(a){console.error("提交任务失败:",a),u.error("任务提交失败，请重试")}},Ua=()=>{Oe.confirm("确定要提交该任务吗？","提交确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Da()})};return(a,e)=>{var Rt,St;const o=v("el-step"),f=v("el-steps"),p=v("el-icon"),r=v("el-button"),y=v("el-skeleton-item"),U=v("el-skeleton"),m=v("el-table-column"),I=v("el-tag"),La=v("el-radio"),ge=v("el-table"),Qe=v("ArrowLeft"),ye=v("el-upload"),Se=v("el-descriptions-item"),Ta=v("el-descriptions"),$=v("el-option"),he=v("el-select"),Ra=v("el-alert"),kt=v("el-card"),Vt=v("el-tab-pane"),Sa=v("el-tooltip"),Dt=v("el-button-group"),$a=v("el-tabs"),Ut=v("el-input-number"),Lt=v("el-date-picker"),Aa=v("el-checkbox"),Ea=v("el-color-picker"),J=v("el-input"),E=v("el-form-item"),Ke=v("el-form"),ne=v("el-dialog"),Tt=Ga("loading");return h(),M("div",qa,[t($a,{modelValue:te.value,"onUpdate:modelValue":e[4]||(e[4]=s=>te.value=s),class:"cad-tabs",onTabClick:It},{default:l(()=>[t(Vt,{label:"转换工具",name:"convert"},{default:l(()=>[t(et,{name:"slide-fade",mode:"out-in"},{default:l(()=>[z(n("div",Ha,[t(kt,{class:"license-card",style:{"margin-top":"0"}},{default:l(()=>[t(f,{active:L.value,"finish-status":"success",simple:""},{default:l(()=>[t(o,{title:"开始"}),t(o,{title:"选择许可"}),t(o,{title:"上传文件"}),t(o,{title:"参数选择"})]),_:1},8,["active"]),n("div",Xa,[n("div",{class:"progress-bar",style:Pa({width:`${ft.value/4*100}%`})},null,4)]),n("div",{class:"step-content-container",ref_key:"stepContentRef",ref:Te},[t(et,{name:"step-fade",mode:"out-in"},{default:l(()=>{var s;return[(h(),M("div",{key:L.value},[z(n("div",Ya,[n("div",Za,[t(r,{type:"primary",size:"large",class:"start-button",onClick:We},{default:l(()=>[t(p,null,{default:l(()=>[t(w(Ee))]),_:1}),e[26]||(e[26]=c(" 开始转换 "))]),_:1})])],512),[[B,L.value===0]]),z(n("div",Ja,[n("div",Qa,[t(r,{type:"primary",onClick:ia},{default:l(()=>[t(p,null,{default:l(()=>[t(w(Ia))]),_:1}),e[27]||(e[27]=c(" 申请许可 "))]),_:1}),t(r,{type:"primary",onClick:w(ca),disabled:se.value},{default:l(()=>[t(p,{class:Fe({"refresh-rotate":se.value})},{default:l(()=>[t(w(Ma))]),_:1},8,["class"]),e[28]||(e[28]=c(" 刷新许可 "))]),_:1},8,["onClick","disabled"])]),Be.value?(h(),D(U,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:l(()=>[t(y,{variant:"text",style:{width:"80px","margin-right":"16px"}}),t(y,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(y,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(y,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(y,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(y,{variant:"text",style:{width:"180px","margin-right":"16px"}}),t(y,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(y,{variant:"text",style:{width:"80px"}})]),_:1})):(h(),D(ge,{key:1,data:Ve.value,style:{width:"100%"},onRowClick:pa,"row-class-name":fa,"show-overflow-tooltip":!1},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"user_project",label:"项目","min-width":"150"},{default:l(({row:d})=>[n("span",{title:d.user_project},_(d.user_project),9,Ka)]),_:1}),t(m,{prop:"reason",label:"原因","min-width":"150"},{default:l(({row:d})=>[n("span",{title:d.reason},_(d.reason),9,el)]),_:1}),t(m,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),t(m,{prop:"count",label:"已用次数",width:"100",align:"center"}),t(m,{prop:"end_date",label:"截止时间",width:"180",align:"center"},{default:l(({row:d})=>[c(_(Ce(d.end_date,"yyyy-MM-dd")),1)]),_:1}),t(m,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:d})=>[t(I,{type:me(d.status).type},{default:l(()=>[c(_(me(d.status).text),1)]),_:2},1032,["type"])]),_:1}),t(m,{label:"选择",width:"80",align:"center"},{default:l(({row:d})=>[t(La,{modelValue:F.value,"onUpdate:modelValue":e[0]||(e[0]=G=>F.value=G),label:d.id,disabled:!mt(d),class:"custom-radio"},{default:l(()=>[n("span",tl,_(d.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"])),n("div",al,[t(r,{onClick:Ne},{default:l(()=>[t(p,null,{default:l(()=>[t(Qe)]),_:1}),e[29]||(e[29]=c(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:We,disabled:!F.value},{default:l(()=>[e[30]||(e[30]=c(" 下一步 ")),t(p,null,{default:l(()=>[t(w(Ee))]),_:1})]),_:1},8,["disabled"])])],512),[[B,L.value===1]]),z(n("div",ll,[n("div",sl,[n("div",ol,[n("div",nl,[t(p,null,{default:l(()=>[t(w(tt))]),_:1}),e[31]||(e[31]=n("span",null,"上传DWG文件",-1))]),e[34]||(e[34]=n("div",{class:"section-desc"},"支持直接上传单个或多个DWG文件",-1)),t(ye,{class:"upload-component",drag:"",action:w(x).defaults.baseURL+"/api/Coordinate/upload","auto-upload":!0,"on-success":xa,"on-error":xt,"on-remove":He,"file-list":ha.value,"before-upload":ba,accept:".dwg",multiple:"","show-file-list":!0,data:{folderId:H.value}},{tip:l(()=>e[32]||(e[32]=[n("div",{class:"el-upload__tip"}," 支持 .dwg 格式文件 ",-1)])),default:l(()=>[t(p,{class:"el-icon--upload"},{default:l(()=>[t(w(we))]),_:1}),e[33]||(e[33]=n("div",{class:"el-upload__text"},[c(" 将DWG文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])]),n("div",rl,[n("div",il,[t(p,null,{default:l(()=>[t(w(ja))]),_:1}),e[35]||(e[35]=n("span",null,"上传压缩包",-1))]),e[38]||(e[38]=n("div",{class:"section-desc"},"支持上传包含DWG/GDB/SHP的ZIP/RAR/7Z压缩包，将自动解压并提取文件",-1)),t(ye,{class:"upload-component",drag:"",action:w(x).defaults.baseURL+"/api/Coordinate/upload","auto-upload":!0,"on-success":ka,"on-error":xt,"on-remove":He,"file-list":wa.value,"before-upload":Ca,accept:".zip,.rar,.7z",multiple:"","show-file-list":!0,data:{folderId:H.value,username:(s=w(b).user)==null?void 0:s.username,licenseId:F.value}},{tip:l(()=>e[36]||(e[36]=[n("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包 ",-1)])),default:l(()=>[t(p,{class:"el-icon--upload"},{default:l(()=>[t(w(we))]),_:1}),e[37]||(e[37]=n("div",{class:"el-upload__text"},[c(" 将压缩包拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])])]),n("div",ul,[n("div",dl,[e[39]||(e[39]=n("span",{class:"table-title",style:{"font-size":"18px"}},"待转换文件",-1)),n("div",cl,[t(I,{type:"info",style:{"margin-right":"10px"}},{default:l(()=>{var d;return[c("当前许可次数："+_(((d=oe.value)==null?void 0:d.usage_count)||0),1)]}),_:1}),t(I,{type:"info",style:{"margin-right":"10px"}},{default:l(()=>{var d;return[c("已用次数："+_(((d=oe.value)==null?void 0:d.count)||0),1)]}),_:1}),t(I,{type:"warning",style:{"margin-right":"10px"}},{default:l(()=>[c("消耗次数："+_(V.value.length),1)]),_:1}),t(I,{type:ve.value?"success":"danger",style:{"margin-right":"10px"}},{default:l(()=>[c(" 消耗后剩余次数："+_(Ye.value),1)]),_:1},8,["type"])])]),t(ge,{data:V.value,style:{width:"100%"},border:"",size:"small","show-overflow-tooltip":!1},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"name",label:"文件名","min-width":"200"},{default:l(({row:d})=>[n("span",{title:d.name},_(d.name),9,pl)]),_:1}),t(m,{prop:"type",label:"类型",width:"100",align:"center"}),t(m,{label:"操作",width:"120",align:"center"},{default:l(({row:d})=>[t(r,{type:"danger",size:"small",onClick:G=>va(d),disabled:Ct.value},{default:l(()=>[t(p,null,{default:l(()=>[t(w(at))]),_:1}),e[40]||(e[40]=c(" 移除 "))]),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])]),n("div",fl,[t(r,{onClick:Ne},{default:l(()=>[t(p,null,{default:l(()=>[t(Qe)]),_:1}),e[41]||(e[41]=c(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:We,disabled:V.value.length===0||!ve.value},{default:l(()=>[e[42]||(e[42]=c(" 下一步 ")),t(p,null,{default:l(()=>[t(w(Ee))]),_:1})]),_:1},8,["disabled"])])],512),[[B,L.value===2]]),z(n("div",ml,[n("div",vl,[n("div",_l,[n("div",gl,[e[43]||(e[43]=n("h3",null,"待转换文件",-1)),t(ge,{data:V.value,style:{width:"100%"},size:"small"},{default:l(()=>[t(m,{prop:"name",label:"文件名"}),t(m,{prop:"type",label:"类型",width:"100"})]),_:1},8,["data"])]),n("div",yl,[e[44]||(e[44]=n("h3",null,"许可信息",-1)),t(Ta,{column:1,border:""},{default:l(()=>[t(Se,{label:"当前许可次数"},{default:l(()=>{var d;return[c(_(((d=oe.value)==null?void 0:d.usage_count)||0),1)]}),_:1}),t(Se,{label:"已用次数"},{default:l(()=>{var d;return[c(_(((d=oe.value)==null?void 0:d.count)||0),1)]}),_:1}),t(Se,{label:"消耗次数"},{default:l(()=>[c(_(V.value.length),1)]),_:1}),t(Se,{label:"消耗后剩余次数"},{default:l(()=>[n("span",{class:Fe({"text-danger":!ve.value})},_(Ye.value),3)]),_:1})]),_:1})])]),n("div",hl,[n("div",wl,[e[48]||(e[48]=n("h3",null,"参数选择",-1)),n("div",bl,[n("div",Cl,[e[45]||(e[45]=n("label",{style:{"font-weight":"bold",width:"100px","text-align":"right","margin-right":"16px","white-space":"nowrap"}},"源坐标系：",-1)),t(he,{modelValue:Y.value,"onUpdate:modelValue":e[1]||(e[1]=d=>Y.value=d),placeholder:"请选择源坐标系",style:{width:"200px"}},{default:l(()=>[t($,{label:"苏州独立",value:"苏州独立"}),t($,{label:"EPSG:4528",value:"EPSG:4528"})]),_:1},8,["modelValue"])]),n("div",xl,[e[46]||(e[46]=n("label",{style:{"font-weight":"bold",width:"100px","text-align":"right","margin-right":"16px","white-space":"nowrap"}},"目标坐标系：",-1)),t(he,{modelValue:Z.value,"onUpdate:modelValue":e[2]||(e[2]=d=>Z.value=d),placeholder:"请选择目标坐标系",style:{width:"200px"}},{default:l(()=>[t($,{label:"苏州独立",value:"苏州独立"}),t($,{label:"EPSG:4528",value:"EPSG:4528"})]),_:1},8,["modelValue"])]),Xe.value?(h(),M("div",kl,[e[47]||(e[47]=n("label",{style:{"font-weight":"bold",width:"100px","text-align":"right","margin-right":"16px","white-space":"nowrap"}},"DWG输出版本：",-1)),t(he,{modelValue:Re.value,"onUpdate:modelValue":e[3]||(e[3]=d=>Re.value=d),placeholder:"请选择DWG输出版本",style:{width:"200px"}},{default:l(()=>[t($,{label:"2000",value:"Release2000"}),t($,{label:"2004",value:"Release2004"}),t($,{label:"2007",value:"Release2007"}),t($,{label:"2010",value:"Release2010"}),t($,{label:"2013",value:"Release2013"}),t($,{label:"2018",value:"Release2018"})]),_:1},8,["modelValue"])])):ie("",!0)]),Y.value&&Z.value&&Y.value===Z.value?(h(),D(Ra,{key:0,type:"error","show-icon":"",title:"源坐标系和目标坐标系不能相同",style:{margin:"16px 0 0 0"}})):ie("",!0)])])]),n("div",Vl,[t(r,{onClick:Ne},{default:l(()=>[t(p,null,{default:l(()=>[t(Qe)]),_:1}),e[49]||(e[49]=c(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:Ua,disabled:!Va.value},{default:l(()=>[e[50]||(e[50]=c(" 提交任务 ")),t(p,null,{default:l(()=>[t(w(Ee))]),_:1})]),_:1},8,["disabled"])])],512),[[B,L.value===3]]),z(n("div",Dl,null,512),[[B,L.value===4]])]))]}),_:1})],512)]),_:1})],512),[[B,te.value==="convert"]])]),_:1})]),_:1}),t(Vt,{label:"历史记录",name:"history"},{default:l(()=>[t(et,{name:"slide-fade",mode:"out-in"},{default:l(()=>[z(n("div",Ul,[t(kt,{class:"history-card",style:{"margin-top":"0px"}},{header:l(()=>e[51]||(e[51]=[])),default:l(()=>[n("div",Ll,[z((h(),D(ge,{data:X.value,style:{width:"100%"}},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"submit_time",label:"提交时间",width:"350",align:"center"},{default:l(({row:s})=>[t(Sa,{content:Ce(s.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:l(()=>[n("span",null,_(Ce(s.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(m,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:s})=>[t(I,{type:_a(s.status)},{default:l(()=>[c(_(ga(s.status)),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"time_consuming",label:"运行耗时",width:"250",align:"center"},{default:l(({row:s})=>[c(_(ut(s.time_consuming)),1)]),_:1}),t(m,{prop:"file_size",label:"文件大小",width:"150",align:"center"},{default:l(({row:s})=>[c(_(s.file_size),1)]),_:1}),t(m,{prop:"up_nums",label:"转换文件数量","min-width":"130",align:"center"},{default:l(({row:s})=>[n("span",{title:s.up_nums},_(s.up_nums),9,Tl)]),_:1}),t(m,{label:"操作",width:"300",align:"center",fixed:"right"},{default:l(({row:s})=>[n("div",Rl,[t(Dt,null,{default:l(()=>[s.status==="success"&&s.up_nums>0&&s.file_size!=="0.0MB"?(h(),D(r,{key:0,type:"success",size:"small",onClick:d=>wt(s),disabled:s.status!=="success"},{default:l(()=>[t(p,null,{default:l(()=>[t(w(At))]),_:1}),e[52]||(e[52]=c(" 下载 "))]),_:2},1032,["onClick","disabled"])):ie("",!0),s.error_message?(h(),D(r,{key:1,type:"info",size:"small",onClick:d=>bt(s)},{default:l(()=>[t(p,null,{default:l(()=>[t(w(tt))]),_:1}),e[53]||(e[53]=c(" 日志 "))]),_:2},1032,["onClick"])):ie("",!0),t(r,{type:"danger",size:"small",onClick:d=>ya(s)},{default:l(()=>[t(p,null,{default:l(()=>[t(w(at))]),_:1}),e[54]||(e[54]=c(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Tt,Ze.value]])])]),_:1})],512),[[B,te.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(ne,{modelValue:ue.value,"onUpdate:modelValue":e[6]||(e[6]=s=>ue.value=s),title:`运行工具 - ${(Rt=P.value)==null?void 0:Rt.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:Xt,onAfterClose:Yt},{footer:l(()=>[n("span",Fl,[t(r,{onClick:e[5]||(e[5]=s=>ue.value=!1)},{default:l(()=>e[56]||(e[56]=[c("取消")])),_:1}),t(r,{type:"primary",onClick:ea},{default:l(()=>e[57]||(e[57]=[c("提交任务")])),_:1})])]),default:l(()=>[t(Ke,{ref_key:"formRef",ref:R,model:g.value,rules:Pe.value,"label-width":"200px",size:"small",class:"run-form"},{default:l(()=>[ae.value.length>0?(h(!0),M(lt,{key:0},st(ae.value,s=>z((h(),D(E,{key:s.prop,label:s.type==="message"?"":s.label,prop:s.prop,required:s.required,class:Fe({"message-form-item":s.type==="message"})},{default:l(()=>{var d,G,$e;return[s.type==="message"?(h(),M("div",Sl,_(s.component.content),1)):s.type==="upload"?(h(),D(ye,Q({key:1,ref_for:!0},s.component.props,{class:["upload-area",{"is-error":(($e=(G=(d=R.value)==null?void 0:d.fields)==null?void 0:G.find(C=>C.prop===s.prop))==null?void 0:$e.validateState)==="error"}],drag:""}),{default:l(()=>[t(p,{class:"el-icon--upload"},{default:l(()=>[t(w(we))]),_:1}),e[55]||(e[55]=n("div",{class:"el-upload__text"},[c(" 拖拽文件到此处"),n("br"),c("或"),n("em",null,"点击上传")],-1))]),_:2},1040,["class"])):s.type==="select"?(h(),D(he,Q({key:2,modelValue:g.value[s.prop],"onUpdate:modelValue":C=>g.value[s.prop]=C,ref_for:!0},s.component.props,{style:{width:"100%"}}),{default:l(()=>[(h(!0),M(lt,null,st(s.component.options,C=>(h(),D($,{key:C.value,label:C.label,value:C.value,title:C.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue"])):s.type==="number"?(h(),D(Ut,Q({key:3,modelValue:g.value[s.prop],"onUpdate:modelValue":C=>g.value[s.prop]=C,ref_for:!0},s.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):s.type==="datetime"?(h(),D(Lt,Q({key:4,modelValue:g.value[s.prop],"onUpdate:modelValue":C=>g.value[s.prop]=C,ref_for:!0},s.component.props),null,16,["modelValue","onUpdate:modelValue"])):s.type==="checkbox"?(h(),D(Aa,Q({key:5,modelValue:g.value[s.prop],"onUpdate:modelValue":C=>g.value[s.prop]=C,ref_for:!0},s.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):s.type==="color"?(h(),M("div",$l,[t(Ea,Q({modelValue:g.value[s.prop],"onUpdate:modelValue":C=>g.value[s.prop]=C,ref_for:!0},s.component.props,{onChange:C=>oa(s.prop,C),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),n("span",Al,_(g.value[`${s.prop}_value`]||"255,255,255"),1)])):(h(),D(J,Q({key:7,modelValue:g.value[s.prop],"onUpdate:modelValue":C=>g.value[s.prop]=C,ref_for:!0},s.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[B,je(s)]])),128)):(h(),M("div",El," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),t(ne,{modelValue:ce.value,"onUpdate:modelValue":e[11]||(e[11]=s=>ce.value=s),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:Zt,onAfterClose:Jt},{footer:l(()=>[n("span",Ol,[t(r,{onClick:e[10]||(e[10]=s=>ce.value=!1)},{default:l(()=>e[60]||(e[60]=[c("取消")])),_:1}),t(r,{type:"primary",onClick:ta},{default:l(()=>e[61]||(e[61]=[c("确定")])),_:1})])]),default:l(()=>[t(Ke,{ref_key:"uploadFormRef",ref:j,model:T.value,rules:Ht,"label-width":"100px",size:"small"},{default:l(()=>[t(E,{label:"工具名称",prop:"fmw_name"},{default:l(()=>[t(J,{modelValue:T.value.fmw_name,"onUpdate:modelValue":e[7]||(e[7]=s=>T.value.fmw_name=s),placeholder:""},null,8,["modelValue"])]),_:1}),t(E,{label:"所属项目",prop:"project"},{default:l(()=>[t(J,{modelValue:T.value.project,"onUpdate:modelValue":e[8]||(e[8]=s=>T.value.project=s),placeholder:""},null,8,["modelValue"])]),_:1}),t(E,{label:"工具描述",prop:"description"},{default:l(()=>[t(J,{modelValue:T.value.description,"onUpdate:modelValue":e[9]||(e[9]=s=>T.value.description=s),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),t(E,{label:"工具文件",prop:"file"},{default:l(()=>{var s,d,G;return[t(ye,{ref_key:"uploadRef",ref:W,class:Fe(["upload-demo",{"is-error":((G=(d=(s=j.value)==null?void 0:s.fields)==null?void 0:d.find($e=>$e.prop==="file"))==null?void 0:G.validateState)==="error"}]),drag:"","auto-upload":!1,"on-change":a.handleFileChange,"before-upload":aa,limit:1,accept:".fmw","file-list":[]},{tip:l(()=>e[58]||(e[58]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:l(()=>[t(p,{class:"el-icon--upload"},{default:l(()=>[t(w(we))]),_:1}),e[59]||(e[59]=n("div",{class:"el-upload__text"},[c(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["class","on-change"])]}),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(ne,{modelValue:pe.value,"onUpdate:modelValue":e[13]||(e[13]=s=>pe.value=s),title:"更新工具",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:Qt,onAfterClose:Kt},{footer:l(()=>[n("span",zl,[t(r,{onClick:e[12]||(e[12]=s=>pe.value=!1)},{default:l(()=>e[64]||(e[64]=[c("取消")])),_:1}),t(r,{type:"primary",onClick:sa,disabled:!N.value.file},{default:l(()=>e[65]||(e[65]=[c("确认更新")])),_:1},8,["disabled"])])]),default:l(()=>[t(ye,{ref_key:"updateUploadRef",ref:q,class:"upload-demo","auto-upload":!1,"on-change":la,limit:1,accept:".fmw",drag:""},{tip:l(()=>e[62]||(e[62]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:l(()=>[t(p,{class:"el-icon--upload"},{default:l(()=>[t(w(we))]),_:1}),e[63]||(e[63]=n("div",{class:"el-upload__text"},[c(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},512)]),_:1},8,["modelValue"]),t(ne,{modelValue:Ie.value,"onUpdate:modelValue":e[14]||(e[14]=s=>Ie.value=s),title:`运行成果 - ${(St=P.value)==null?void 0:St.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:l(()=>[z((h(),D(ge,{data:de.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:l(({row:s})=>[c(_(Ce(s.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),t(m,{prop:"status",label:"运行状态",width:"100",align:"center"},{default:l(({row:s})=>[t(I,{type:Nt(s.status)},{default:l(()=>[c(_(Wt(s.status)),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"time_consuming",label:"运行耗时",width:"100",align:"center"},{default:l(({row:s})=>[c(_(ut(s.time_consuming)),1)]),_:1}),t(m,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),t(m,{label:"操作",width:"300",align:"center",fixed:"right"},{default:l(({row:s})=>[n("div",Pl,[t(Dt,null,{default:l(()=>[s.status==="success"&&s.up_nums>0&&s.file_size!=="0.0MB"?(h(),D(r,{key:0,type:"success",size:"small",onClick:d=>wt(s),disabled:s.status!=="success"},{default:l(()=>[t(p,null,{default:l(()=>[t(w(At))]),_:1}),e[66]||(e[66]=c(" 下载 "))]),_:2},1032,["onClick","disabled"])):ie("",!0),s.error_message?(h(),D(r,{key:1,type:"info",size:"small",onClick:d=>bt(s)},{default:l(()=>[t(p,null,{default:l(()=>[t(w(tt))]),_:1}),e[67]||(e[67]=c(" 日志 "))]),_:2},1032,["onClick"])):ie("",!0),t(r,{type:"danger",size:"small",onClick:d=>qt(s)},{default:l(()=>[t(p,null,{default:l(()=>[t(w(at))]),_:1}),e[68]||(e[68]=c(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Tt,Et.value]])]),_:1},8,["modelValue","title"]),t(ne,{modelValue:ke.value,"onUpdate:modelValue":e[16]||(e[16]=s=>ke.value=s),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:l(()=>[n("span",Ml,[t(r,{onClick:e[15]||(e[15]=s=>ke.value=!1)},{default:l(()=>e[69]||(e[69]=[c("关闭")])),_:1})])]),default:l(()=>[n("div",Il,[n("pre",null,_(dt.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"]),t(ne,{modelValue:le.value,"onUpdate:modelValue":e[24]||(e[24]=s=>le.value=s),title:"申请许可-坐标转换",width:"500px","close-on-click-modal":!1,onClose:e[25]||(e[25]=s=>le.value=!1),onAfterClose:pt},{footer:l(()=>[n("span",ql,[t(r,{onClick:e[23]||(e[23]=s=>le.value=!1)},{default:l(()=>e[70]||(e[70]=[c("取消")])),_:1}),t(r,{type:"primary",loading:Ge.value,onClick:da},{default:l(()=>e[71]||(e[71]=[c("提交")])),_:1},8,["loading"])])]),default:l(()=>[t(Ke,{ref_key:"licenseFormRef",ref:fe,model:k.value,rules:na,"label-width":"100px",class:"apply-form",size:"small"},{default:l(()=>[t(E,{label:"工具名称",prop:"tool_name"},{default:l(()=>[t(J,{modelValue:k.value.tool_name,"onUpdate:modelValue":e[17]||(e[17]=s=>k.value.tool_name=s),value:"坐标转换",disabled:""},null,8,["modelValue"])]),_:1}),t(E,{label:"使用项目",prop:"user_project"},{error:l(({error:s})=>[n("span",jl,_(s),1)]),default:l(()=>[t(J,{modelValue:k.value.user_project,"onUpdate:modelValue":e[18]||(e[18]=s=>k.value.user_project=s),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),t(E,{label:"申请原因",prop:"reason"},{error:l(({error:s})=>[n("span",Gl,_(s),1)]),default:l(()=>[t(J,{modelValue:k.value.reason,"onUpdate:modelValue":e[19]||(e[19]=s=>k.value.reason=s),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),t(E,{label:"有效期",prop:"end_date"},{error:l(({error:s})=>[n("span",Bl,_(s),1)]),default:l(()=>[t(Lt,{modelValue:k.value.end_date,"onUpdate:modelValue":e[20]||(e[20]=s=>k.value.end_date=s),type:"date",placeholder:"请选择有效期","disabled-date":ra,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),t(E,{label:"申请次数",prop:"usage_count"},{error:l(({error:s})=>[n("span",Nl,_(s),1)]),default:l(()=>[t(Ut,{modelValue:k.value.usage_count,"onUpdate:modelValue":e[21]||(e[21]=s=>k.value.usage_count=s),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(E,{label:"审批人",prop:"approver"},{error:l(({error:s})=>[n("span",Wl,_(s),1)]),default:l(()=>[t(he,{modelValue:k.value.approver,"onUpdate:modelValue":e[22]||(e[22]=s=>k.value.approver=s),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:l(()=>[(h(!0),M(lt,null,st(ct.value,s=>(h(),D($,{key:s.username,label:s.real_name,value:s.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Kl=Na(Xl,[["__scopeId","data-v-d8864a9c"]]);export{Kl as default};
