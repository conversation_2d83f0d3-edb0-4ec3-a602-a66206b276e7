{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/backtop/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Backtop from './src/backtop.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElBacktop: SFCWithInstall<typeof Backtop> = withInstall(Backtop)\nexport default ElBacktop\n\nexport * from './src/backtop'\nexport type { BacktopInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,SAAS,GAAG,WAAW,CAAC,OAAO;;;;"}