#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2025\02\0213\dgn读取\none2none.fmw
#          --PARAMETER_2 "C:\Users\<USER>\Desktop\新建文件夹 (liujun)\统计"
#          --PARAMETER "C:\Users\<USER>\Desktop\新建文件夹 (1111111)"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="6673.81 772.132"
#!   DOC_TOP_LEFT="-3306.28 -846.008"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="e22b1c9b-3a1b-42db-bf22-2e6178a69ff9"
#!   FME_DOCUMENT_PRIORGUID="1944c80c-1414-4a97-a91f-4e3c28884c86"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-02-13T13:30:30"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="NO"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="-3946.91 365.629"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/line.gdb"
#! />
#! <DATASET
#!   NAME="FeatureWriter_2"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter_2"
#!   DATASET="FeatureWriter_2/point.gdb"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_SRC_OR_ATTR PARAMETER_2 待提取dgn压缩包"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (liujun)\统计"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER 保存路径"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (1111111)"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJhY2Nlc3NNb2RlIjoicmVhZCIsImFsbG93VVJMIjpmYWxzZSwiZGVmYXVsdFZhbHVlIjoiQzpcXFVzZXJzXFxkanpcXERlc2t0b3BcXOaWsOW7uuaWh+S7tuWkuSAobGl1anVuKVxc57uf6K6hIiwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiUEFSQU1FVEVSXzIiLCJwcm9tcHQiOiLlvoXmj5Dlj5ZkZ27ljovnvKnljIUiLCJyZXF1aXJlZCI6dHJ1ZSwic2VsZWN0TXVsdGlwbGUiOmZhbHNlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6dHJ1ZSwidmFsdWVUeXBlIjoic3RyaW5nIn0seyJhY2Nlc3NNb2RlIjoid3JpdGUiLCJhbGxvd1VSTCI6ZmFsc2UsImRlZmF1bHRWYWx1ZSI6IkM6XFxVc2Vyc1xcZGp6XFxEZXNrdG9wXFzmlrDlu7rmlofku7blpLkgKDExMTExMTEpIiwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiUEFSQU1FVEVSIiwicHJvbXB0Ijoi5L+d5a2Y6Lev5b6EIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjpmYWxzZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOnRydWUsInZhbHVlVHlwZSI6InN0cmluZyJ9XX0="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="PARAMETER_2" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (liujun)\统计"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_SRC_OR_ATTR PARAMETER_2 待提取dgn压缩包"
#! />
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\新建文件夹 (1111111)"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-3306.2830628306283 -259.3775937759379"
#!   BOUNDING_RECT="-3306.2830628306283 -259.3775937759379 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-1196.8869688696891 -156.87596875968757"
#!   BOUNDING_RECT="-1196.8869688696891 -156.87596875968757 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="IGDS"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,design"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="IGDS_APPLY_WORLD_FILE,&quot;OPTIONAL CHECKBOX YES%NO&quot;,IGDS&lt;space&gt;Apply&lt;space&gt;World&lt;space&gt;File&lt;space&gt;&lt;openparen&gt;.wld&lt;solidus&gt;.wld3&lt;closeparen&gt;,IGDS_REMOVE_DUPLICATES,&quot;OPTIONAL CHECKBOX YES%NO&quot;,IGDS&lt;space&gt;Remove&lt;space&gt;duplicate&lt;space&gt;points:,IGDS_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,IGDS&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,IGDS_OVERRIDE_GLOBAL_ORIGIN,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_CLOSED%UOR_GLOBAL_ORIGIN_X%UOR_GLOBAL_ORIGIN_Y%UOR_GLOBAL_ORIGIN_Z&quot;,IGDS&lt;space&gt;Override&lt;space&gt;Global&lt;space&gt;Origin,IGDS_TAGS_AS_TEXT,&quot;OPTIONAL CHECKBOX_WITHLABEL YES%NO&quot;,IGDS&lt;space&gt;Output&lt;space&gt;Tags&lt;space&gt;as&lt;space&gt;Text,IGDS_EXPAND_UNNAMED_CELLS,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_CLOSED%PRESERVE_UNNAMEDCELL_INSERTS&quot;,IGDS&lt;space&gt;Expand&lt;space&gt;Unnamed&lt;space&gt;&lt;openparen&gt;Group&lt;space&gt;Hole&lt;closeparen&gt;&lt;space&gt;Cells,IGDS_UOR_SCALE,&quot;OPTIONAL FLOAT&quot;,IGDS&lt;space&gt;UOR&lt;space&gt;to&lt;space&gt;FME&lt;space&gt;units&lt;space&gt;scale&lt;space&gt;factor:,IGDS_READ_XREF_FILES,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_CLOSED%XREF_ELEMENTS_MSG%READ_XREF_NEST_DEPTH%USE_XREF_PARENT_MODEL&quot;,IGDS&lt;space&gt;Read&lt;space&gt;Elements&lt;space&gt;From&lt;space&gt;Reference&lt;space&gt;Files,IGDS_PRESERVE_CELL_INSERTS,&quot;OPTIONAL CHECKBOX_WITHLABEL yes%no&quot;,IGDS&lt;space&gt;Preserve&lt;space&gt;Named&lt;space&gt;Cell&lt;space&gt;Insert&lt;space&gt;Points,IGDS_EXPAND_CELLS,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_CLOSED%PRESERVE_CELL_INSERTS&quot;,IGDS&lt;space&gt;Expand&lt;space&gt;Named&lt;space&gt;Cells,IGDS_IGDS_MSLINKS,&quot;OPTIONAL CHECKBOX_WITHLABEL YES%NO&quot;,IGDS&lt;space&gt;MSLinks,IGDS_UNITS,&quot;OPTIONAL RADIO_GROUP 3%&quot;&quot;Master&quot;&quot;,IGDS_MASTER_UNITS%&quot;&quot;Sub&quot;&quot;,IGDS_SUB_UNITS%&quot;&quot;UOR&quot;&quot;,IGDS_UORS&quot;,IGDS&lt;space&gt;Coordinate&lt;space&gt;Units&lt;space&gt;By,IGDS_CURVE_VERTICES,&quot;OPTIONAL INTEGER&quot;,IGDS&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;interpolated&lt;space&gt;curve&lt;space&gt;vertices,IGDS_READER_META_ATTRIBUTES,&quot;OPTIONAL NO_EDIT TEXT&quot;,IGDS&lt;space&gt;,IGDS_IGDS_FRAMME,&quot;OPTIONAL CHECKBOX_WITHLABEL YES%NO&quot;,IGDS&lt;space&gt;FRAMME,IGDS_APPLY_WORLD_FILE_TO_ATTRS,&quot;OPTIONAL NO_EDIT TEXT&quot;,IGDS&lt;space&gt;,IGDS_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP EXPOSE_ATTRS_MSG%IGDS_EXPOSE_FORMAT_ATTRS&quot;,IGDS&lt;space&gt;Schema&lt;space&gt;Attributes,IGDS_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%SJIS%CP437%CP850%CP852%CP855%CP857%CP860%CP861%CP863%CP864%CP865%CP869%CP874%CP932%CP936%CP950%CP1250%CP1251%CP1252%CP1253%CP1254%CP1255%CP1256%CP1257%ISO8859-1%ISO8859-2%ISO8859-3%ISO8859-4%ISO8859-5%ISO8859-6%ISO8859-7%ISO8859-8%ISO8859-9&quot;,IGDS&lt;space&gt;Character&lt;space&gt;Encoding,IGDS_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,IGDS&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,IGDS_READ_TAGS,&quot;OPTIONAL CHECKBOX_WITHLABEL YES%NO&quot;,IGDS&lt;space&gt;Read&lt;space&gt;Tag&lt;space&gt;Attributes:,IGDS_ELEVATION_SHIFT_FACTOR,&quot;OPTIONAL FLOAT&quot;,IGDS&lt;space&gt;Elevation&lt;space&gt;Shift&lt;space&gt;Factor,IGDS_USE_LEVEL_NAMES,&quot;OPTIONAL NO_EDIT TEXT&quot;,IGDS&lt;space&gt;,IGDS_SUBS_PER_MASTER,&quot;OPTIONAL FLOAT&quot;,IGDS&lt;space&gt;SUBS&lt;space&gt;PER&lt;space&gt;MASTER&lt;space&gt;UNIT:,IGDS_PRESERVE_CURVES,&quot;OPTIONAL CHECKBOX YES%NO&quot;,IGDS&lt;space&gt;Preserve&lt;space&gt;curves,IGDS_XREF_OPTIONS,&quot;OPTIONAL DISCLOSUREGROUP FME_DISCLOSURE_CLOSED%READ_XREF_FILES%READ_XREFS_AS_ELEMENTS&quot;,IGDS&lt;space&gt;External&lt;space&gt;Reference&lt;space&gt;File&lt;space&gt;Options,IGDS_READ_SELECTED_MODELS,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SELECTED_MODELS&quot;,IGDS&lt;space&gt;Read&lt;space&gt;Only&lt;space&gt;Selected&lt;space&gt;Models,IGDS_ASSUME_MATCHING_UNITS,&quot;OPTIONAL CHECKBOX YES%NO&quot;,IGDS&lt;space&gt;Assume&lt;space&gt;master&lt;space&gt;units&lt;space&gt;equal&lt;space&gt;working&lt;space&gt;units:,IGDS_SPLIT_COMPLEX_CHAINS,&quot;OPTIONAL CHECKBOX YES%NO&quot;,IGDS&lt;space&gt;Drop&lt;space&gt;Complex&lt;space&gt;Chains&lt;solidus&gt;Shapes,IGDS_UORS_PER_SUB,&quot;OPTIONAL FLOAT&quot;,IGDS&lt;space&gt;UORS&lt;space&gt;PER&lt;space&gt;SUB&lt;space&gt;UNIT:,IGDS_USE_RICH_ARC_GEOM,&quot;OPTIONAL NO_EDIT TEXT&quot;,IGDS&lt;space&gt;,IGDS_READ_XATTRS_AND_ITEM_SETS,&quot;OPTIONAL CHECKBOX_WITHLABEL YES%NO&quot;,IGDS&lt;space&gt;Read&lt;space&gt;XAttributes&lt;space&gt;and&lt;space&gt;Item&lt;space&gt;Type&lt;space&gt;attributes&lt;space&gt;&lt;openparen&gt;V8&lt;space&gt;only&lt;closeparen&gt;:,IGDS_SPLIT_MULTITEXT,&quot;OPTIONAL CHECKBOX YES%NO&quot;,IGDS&lt;space&gt;Split&lt;space&gt;Multi&lt;space&gt;Text,IGDS_IGDS_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS IGDS%Source&quot;,IGDS&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,IGDS_READ_XREFS_AS_ELEMENTS,&quot;OPTIONAL CHECKBOX_WITHLABEL YES%NO&quot;,IGDS&lt;space&gt;Keep&lt;space&gt;Elements&lt;space&gt;That&lt;space&gt;Represent&lt;space&gt;Reference&lt;space&gt;Files&lt;space&gt;&lt;openparen&gt;V8&lt;space&gt;only&lt;closeparen&gt;,IGDS_EXPLODE_DIMENSION_ELEM,&quot;OPTIONAL CHECKBOX YES%NO&quot;,IGDS&lt;space&gt;Drop&lt;space&gt;Dimensions,IGDS_METAFILE,&quot;OPTIONAL RADIO_GROUP 2%&quot;&quot;Level Numbers&quot;&quot;,design%&quot;&quot;Schema - Level Numbers&quot;&quot;,designScan%&quot;&quot;Level Names&quot;&quot;,designNames%&quot;&quot;Schema - Level Names&quot;&quot;,designScanLevelNames%Geometry,designGeom&quot;,IGDS&lt;space&gt;Group&lt;space&gt;Elements&lt;space&gt;By,IGDS_IGDS_RDR_ADV_PARM_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SPLIT_COMPLEX_CHAINS%SPLIT_MULTITEXT%EXPLODE_DIMENSION_ELEM%APPLY_WORLD_FILE%PRESERVE_CURVES%REMOVE_DUPLICATES%ASSUME_MATCHING_UNITS%UOR_SCALE%UORS_PER_SUB%SUBS_PER_MASTER%ELEVATION_SHIFT_FACTOR%CURVE_VERTICES&quot;,IGDS&lt;space&gt;Advanced"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="IGDS_APPLY_WORLD_FILE" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="IGDS_APPLY_WORLD_FILE_TO_ATTRS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="IGDS_ASSUME_MATCHING_UNITS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_CURVE_VERTICES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_ELEVATION_SHIFT_FACTOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_ENCODING" PARM_VALUE="gbk"/>
#!     <XFORM_PARM PARM_NAME="IGDS_EXPAND_CELLS" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="IGDS_EXPAND_UNNAMED_CELLS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="IGDS_EXPLODE_DIMENSION_ELEM" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="IGDS_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_IGDS_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_IGDS_FRAMME" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_IGDS_MSLINKS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_IGDS_RDR_ADV_PARM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_METAFILE" PARM_VALUE="design"/>
#!     <XFORM_PARM PARM_NAME="IGDS_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_OVERRIDE_GLOBAL_ORIGIN" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="IGDS_PRESERVE_CELL_INSERTS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="IGDS_PRESERVE_CURVES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_READER_META_ATTRIBUTES" PARM_VALUE="fme_dataset"/>
#!     <XFORM_PARM PARM_NAME="IGDS_READ_SELECTED_MODELS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_READ_TAGS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_READ_XATTRS_AND_ITEM_SETS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_READ_XREFS_AS_ELEMENTS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="IGDS_READ_XREF_FILES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_REMOVE_DUPLICATES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_SPLIT_COMPLEX_CHAINS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_SPLIT_MULTITEXT" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="IGDS_SUBS_PER_MASTER" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_TAGS_AS_TEXT" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_UNITS" PARM_VALUE="IGDS_MASTER_UNITS"/>
#!     <XFORM_PARM PARM_NAME="IGDS_UORS_PER_SUB" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_UOR_SCALE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IGDS_USE_LEVEL_NAMES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_USE_RICH_ARC_GEOM" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="IGDS_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGDS_XREF_OPTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="153.12653126531291 -231.25231252312528"
#!   BOUNDING_RECT="153.12653126531291 -231.25231252312528 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;fme_text_string&lt;closeparen&gt; != Pattern&lt;space&gt;Control&lt;space&gt;Element"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="-490.62990629906301 -231.25231252312528"
#!   BOUNDING_RECT="-490.62990629906301 -231.25231252312528 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="fme_feature_type,,fme_text_string,,fme_geometry,,igds_level_name,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="10"
#!   TYPE="GeometryFilter"
#!   VERSION="8"
#!   POSITION="856.2585625856259 -121.87621876218759"
#!   BOUNDING_RECT="856.2585625856259 -121.87621876218759 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Null"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="Point"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="Text"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="Curve"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="Area"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <OUTPUT_FEAT NAME="Surface"/>
#!     <FEAT_COLLAPSED COLLAPSED="5"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <OUTPUT_FEAT NAME="Solid"/>
#!     <FEAT_COLLAPSED COLLAPSED="6"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <OUTPUT_FEAT NAME="Raster"/>
#!     <FEAT_COLLAPSED COLLAPSED="7"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <OUTPUT_FEAT NAME="PointCloud"/>
#!     <FEAT_COLLAPSED COLLAPSED="8"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <OUTPUT_FEAT NAME="VoxelGrid"/>
#!     <FEAT_COLLAPSED COLLAPSED="9"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <OUTPUT_FEAT NAME="&lt;UNFILTERED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="10"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILTER_MODE" PARM_VALUE="SIMPLE"/>
#!     <XFORM_PARM PARM_NAME="FILTER_MULTI" PARM_VALUE="FILTER_TYPES]&quot;Null Point Text Line Area Surface Solid Raster PointCloud VoxelGrid&quot;]FME_CONTROLLER_QUERY_FILE]]FME_CONTROLLER_CHOICE]Simple"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryFilter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="GeometryCoercer"
#!   VERSION="6"
#!   POSITION="1556.2655626556259 -775.00775007750121"
#!   BOUNDING_RECT="1556.2655626556259 -775.00775007750121 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="COERCED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNTOUCHED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="GQUERY" PARM_VALUE="for&lt;space&gt;&lt;dollar&gt;geom&lt;space&gt;in&lt;space&gt;&lt;solidus&gt;&lt;solidus&gt;geometry&lt;lf&gt;where&lt;space&gt;&lt;dollar&gt;geom&lt;solidus&gt;count&lt;openparen&gt;parent::geometry&lt;closeparen&gt;&lt;space&gt;=&lt;space&gt;0&lt;lf&gt;return&lt;space&gt;number&lt;openparen&gt;&lt;dollar&gt;geom&lt;solidus&gt;&lt;at&gt;fme_id&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="GQUERY_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="KIND" PARM_VALUE="fme_line"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryCoercer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="17"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-2693.7769377693771 -96.875968759687623"
#!   BOUNDING_RECT="-2693.7769377693771 -96.875968759687623 484.37984379843783 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="20"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-1987.5198751987523 -231.25231252312528"
#!   BOUNDING_RECT="-1987.5198751987523 -231.25231252312528 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = dgn"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="19"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="2259.3975939759398 -560.00500005000106"
#!   BOUNDING_RECT="2259.3975939759398 -560.00500005000106 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST 1 = 1"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="14"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="2937.5293752937514 -560.00500005000106"
#!   BOUNDING_RECT="2937.5293752937514 -560.00500005000106 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER)&lt;backslash&gt;line.gdb"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_GEODB&lt;comma&gt;NO&lt;comma&gt;DATASET_TEMPLATE&lt;comma&gt;&lt;comma&gt;IMPORT_XML_TEMPLATE_GROUP&lt;comma&gt;NO&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;IMPORT_KIND&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;TRANSACTION_TYPE&lt;comma&gt;TRANSACTIONS&lt;comma&gt;FEATURE_DATASET_HANDLING&lt;comma&gt;WRITE&lt;comma&gt;SIMPLIFY_GEOM&lt;comma&gt;No&lt;comma&gt;HAS_Z_VALUES&lt;comma&gt;auto_detect&lt;comma&gt;X_ORIGIN&lt;comma&gt;0&lt;comma&gt;Y_ORIGIN&lt;comma&gt;0&lt;comma&gt;XY_SCALE&lt;comma&gt;0&lt;comma&gt;Z_ORIGIN&lt;comma&gt;0&lt;comma&gt;Z_SCALE&lt;comma&gt;0&lt;comma&gt;GRID_1&lt;comma&gt;0&lt;comma&gt;GEODB_SHARED_WRT_ADV_PARM_GROUP&lt;comma&gt;&lt;comma&gt;REQUESTED_GEODATABASE_VERSION&lt;comma&gt;CURRENT&lt;comma&gt;DEFAULT_Z_VALUE&lt;comma&gt;0&lt;comma&gt;TRANSACTION&lt;comma&gt;0&lt;comma&gt;TRANSACTION_INTERVAL&lt;comma&gt;1000&lt;comma&gt;IGNORE_FAILED_FEATURE_ENTRY&lt;comma&gt;no&lt;comma&gt;MAX_NUMBER_FAILED_FEATURES&lt;comma&gt;-1&lt;comma&gt;DUMP_FAILED_FEATURES&lt;comma&gt;no&lt;comma&gt;FFS_DUMP_FILE&lt;comma&gt;&lt;comma&gt;ANNOTATION_UNITS&lt;comma&gt;unknown_units&lt;comma&gt;HAS_MEASURES&lt;comma&gt;no&lt;comma&gt;COMPRESS_AT_END&lt;comma&gt;no&lt;comma&gt;ENABLE_FAST_DELETES&lt;comma&gt;yes&lt;comma&gt;PRESERVE_GLOBALID&lt;comma&gt;no&lt;comma&gt;ENABLE_LOAD_ONLY_MODE&lt;comma&gt;no&lt;comma&gt;VALIDATE_FEATURES&lt;comma&gt;no&lt;comma&gt;SIMPLIFY_NETWORK_FEATURES&lt;comma&gt;no&lt;comma&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE_TYPE,METAFILE,GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="GEODATABASE_FILE_TRANSACTION_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Edit&lt;space&gt;Session,EDIT_SESSION%Transactions,TRANSACTIONS%None,NONE&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Type:,GEODATABASE_FILE_COMPRESS_AT_END,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Compact&lt;space&gt;Database&lt;space&gt;When&lt;space&gt;Done:,GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Max&lt;space&gt;number&lt;space&gt;of&lt;space&gt;features&lt;space&gt;to&lt;space&gt;ignore:,GEODATABASE_FILE_ENABLE_FAST_DELETES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Fast&lt;space&gt;Deletes:,GEODATABASE_FILE_Z_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_VALIDATE_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Validate&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;Write:,GEODATABASE_FILE_X_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_ANNOTATION_UNITS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Unknown Units&quot;&quot;,unknown_units%&quot;&quot;Decimal Degrees&quot;&quot;,decimal_degrees%Inches,inches%Points,points%Feet,feet%Yards,yards%Miles,miles%&quot;&quot;Nautical Miles&quot;&quot;,nautical_miles%Millimeters,millimeters%Centimeters,centimeters%Meters,meters%Kilometers,kilometers%Decimeters,decimeters&quot;,GEODATABASE_FILE&lt;space&gt;Annotation&lt;space&gt;Units:,GEODATABASE_FILE_OVERWRITE_GEODB,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_OPEN%DATASET_TEMPLATE%++YES+IMPORT_XML_TEMPLATE_GROUP+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;Geodatabase,GEODATABASE_FILE_TRANSACTION,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Number:,GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Network&lt;space&gt;Features:,GEODATABASE_FILE_Z_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_TRANSACTION_INTERVAL,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Features&lt;space&gt;Per&lt;space&gt;Transaction,GEODATABASE_FILE_HAS_MEASURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Measures,GEODATABASE_FILE_SIMPLIFY_GEOM,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Geometry:,GEODATABASE_FILE_PRESERVE_GLOBALID,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Preserve&lt;space&gt;GlobalID:,GEODATABASE_FILE_END_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;After&lt;space&gt;Write,GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Load&lt;space&gt;Only&lt;space&gt;Mode:,GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Failed&lt;space&gt;Features:,GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION,&quot;OPTIONAL LOOKUP_CHOICE Current,CURRENT%10.0%9.3&quot;,GEODATABASE_FILE&lt;space&gt;Geodatabase&lt;space&gt;Version:,GEODATABASE_FILE_GRID_1,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP,&quot;OPTIONAL DISCLOSUREGROUP REQUESTED_GEODATABASE_VERSION%DEFAULT_Z_VALUE%TRANSACTION%TRANSACTION_INTERVAL%IGNORE_FAILED_FEATURE_ENTRY%MAX_NUMBER_FAILED_FEATURES%DUMP_FAILED_FEATURES%FFS_DUMP_FILE%ANNOTATION_UNITS%HAS_MEASURES%MEASURES_ORIGIN%MEASURES_SCALE%COMPRESS_AT_END%ENABLE_FAST_DELETES%PRESERVE_GLOBALID%ENABLE_LOAD_ONLY_MODE%VALIDATE_FEATURES%SIMPLIFY_NETWORK_FEATURES%BEGIN_SQL{0}%END_SQL{0}&quot;,GEODATABASE_FILE&lt;space&gt;Advanced,GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP TEMPLATEFILE%IMPORT_KIND%++YES+OVERWRITE_GEODB+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Import&lt;space&gt;XML&lt;space&gt;Workspace&lt;space&gt;Document,GEODATABASE_FILE_DEFAULT_Z_VALUE,&quot;OPTIONAL FLOAT&quot;,GEODATABASE_FILE&lt;space&gt;Default&lt;space&gt;Z&lt;space&gt;Value:,GEODATABASE_FILE_Y_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_HAS_Z_VALUES,&quot;OPTIONAL LOOKUP_CHOICE Yes,yes%No,no%Auto&lt;space&gt;Detect,auto_detect&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values:,GEODATABASE_FILE_FFS_DUMP_FILE,&quot;OPTIONAL FILENAME FME_Feature_Store_Files(*.ffs)|*.ffs|All_files(*)|*&quot;,GEODATABASE_FILE&lt;space&gt;Failed&lt;space&gt;Feature&lt;space&gt;Dump&lt;space&gt;filename:,GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_DUMP_FAILED_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Dump&lt;space&gt;Failed&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;File:,GEODATABASE_FILE_FEATURE_DATASET_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Write&lt;space&gt;Feature&lt;space&gt;Dataset,WRITE%Warn&lt;space&gt;and&lt;space&gt;Ignore&lt;space&gt;Feature&lt;space&gt;Dataset,IGNORE%Error&lt;space&gt;and&lt;space&gt;End&lt;space&gt;Translation,END&quot;,GEODATABASE_FILE&lt;space&gt;Feature&lt;space&gt;Dataset&lt;space&gt;Handling:,GEODATABASE_FILE_BEGIN_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;Before&lt;space&gt;Write,GEODATABASE_FILE_XY_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ANNOTATION_UNITS" PARM_VALUE="unknown_units"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_BEGIN_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COMPRESS_AT_END" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE_TYPE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DEFAULT_Z_VALUE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DUMP_FAILED_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_FAST_DELETES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_END_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FEATURE_DATASET_HANDLING" PARM_VALUE="WRITE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FFS_DUMP_FILE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GRID_1" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_MEASURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_Z_VALUES" PARM_VALUE="auto_detect"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES" PARM_VALUE="-1"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_OVERWRITE_GEODB" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_PRESERVE_GLOBALID" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION" PARM_VALUE="CURRENT"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_GEOM" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_INTERVAL" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_TYPE" PARM_VALUE="TRANSACTIONS"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_VALIDATE_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_XY_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_X_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Y_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="line:Coerced,ftp_feature_type_name,line,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_polyline,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,igds_level_name&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;,ftp_format_attributes,fme_feature_type&lt;comma&gt;fme_text_string&lt;comma&gt;fme_geometry,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;GEODB_ORIGIN_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ADVANCED_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_XY_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_Z_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;comma&gt;GEODB_XORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_YORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_XYSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;comma&gt;GEODB_ZORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_ZSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_ORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_SCALE&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.&lt;space&gt;%-#&lt;openbracket&gt;&lt;closebracket&gt;&lt;quote&gt;&lt;openparen&gt;&lt;closeparen&gt;!?*&lt;apos&gt;&lt;amp&gt;+&lt;backslash&gt;&lt;solidus&gt;&lt;opencurly&gt;&lt;closecurly&gt;|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;blob&lt;comma&gt;fme_binarybuffer&lt;comma&gt;blob&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;blob&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;globalid&lt;comma&gt;fme_buffer&lt;comma&gt;guid&lt;comma&gt;fme_buffer&lt;comma&gt;date&lt;comma&gt;fme_datetime&lt;comma&gt;timestamp_offset&lt;comma&gt;fme_datetime&lt;comma&gt;date_only&lt;comma&gt;fme_date&lt;comma&gt;time_only&lt;comma&gt;fme_time&lt;comma&gt;integer&lt;comma&gt;fme_int32&lt;comma&gt;integer&lt;comma&gt;fme_uint16&lt;comma&gt;smallint&lt;comma&gt;fme_int16&lt;comma&gt;smallint&lt;comma&gt;fme_int8&lt;comma&gt;smallint&lt;comma&gt;fme_uint8&lt;comma&gt;bigint&lt;comma&gt;fme_int64&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;char&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;boolean&lt;comma&gt;fme_boolean&lt;comma&gt;&lt;quote&gt;double&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;subtype&lt;openparen&gt;stringset&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;subtype_codes&lt;openparen&gt;stringmap&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;range_domain&lt;openparen&gt;range_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;coded_domain&lt;openparen&gt;coded_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,&lt;backslash&gt;&lt;backslash&gt;&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|&lt;openbracket&gt;%#&lt;space&gt;&lt;apos&gt;&lt;amp&gt;+-&lt;closebracket&gt;.^~&lt;dollar&gt;&lt;comma&gt;&lt;closeparen&gt;&lt;openparen&gt;,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point&lt;comma&gt;fme_point&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_line&lt;comma&gt;geodb_multipoint&lt;comma&gt;fme_point&lt;comma&gt;geodb_table&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_table&lt;comma&gt;fme_collection&lt;comma&gt;geodb_arc&lt;comma&gt;fme_arc&lt;comma&gt;geodb_ellipse&lt;comma&gt;fme_ellipse&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;geodb_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_pro_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_dimension&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_junction&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_complex_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_attributed_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_undefined&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_metadata&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_raster&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;geodb_table&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_group&lt;space&gt;fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_common_group&lt;space&gt;fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group&lt;space&gt;General&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_feature_operation&lt;space&gt;Insert&lt;comma&gt;INSERT&lt;comma&gt;fme_update_geometry&lt;comma&gt;fme_selection_group&lt;comma&gt;mie_pack%Update&lt;comma&gt;UPDATE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert&lt;comma&gt;UPSERT&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Delete&lt;comma&gt;DELETE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;fme_update_geometry&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;comma&gt;fme_spatial_group&lt;comma&gt;fme_advanced_group&lt;comma&gt;oracle_sequenced_cols%&lt;lt&gt;at&lt;gt&gt;Value&lt;lt&gt;openparen&lt;gt&gt;fme_db_operation&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;MULTIPLE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;space&gt;Feature&lt;space&gt;Operation&lt;quote&gt;&lt;comma&gt;INSERT&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_table_handling&lt;space&gt;Use&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;USE_EXISTING&lt;comma&gt;fme_table_creation_group%Create&lt;lt&gt;space&lt;gt&gt;If&lt;lt&gt;space&lt;gt&gt;Needed&lt;comma&gt;CREATE_IF_MISSING%Drop&lt;lt&gt;space&lt;gt&gt;and&lt;lt&gt;space&lt;gt&gt;Create&lt;comma&gt;DROP_CREATE%Truncate&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;TRUNCATE_EXISTING&lt;comma&gt;fme_table_creation_group&lt;space&gt;Table&lt;space&gt;Handling&lt;quote&gt;&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;fme_update_geometry&lt;space&gt;Yes&lt;comma&gt;YES%No&lt;comma&gt;NO&lt;space&gt;Update&lt;space&gt;Spatial&lt;space&gt;Column&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;YES&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_selection_group&lt;space&gt;fme_selection_method&lt;space&gt;Row&lt;space&gt;Selection&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;RADIOPARAMETERGROUP&lt;space&gt;fme_selection_method&lt;space&gt;fme_match_columns&lt;comma&gt;MATCH_COLUMNS%fme_where_builder_clause&lt;comma&gt;BUILDER&lt;space&gt;Row&lt;space&gt;Selection&lt;space&gt;Method&lt;quote&gt;&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;ATTRLIST_COMMAS&lt;space&gt;fme_match_columns&lt;space&gt;Match&lt;space&gt;Columns&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;TEXT_EDIT_SQL_CFG_OR_ATTR&lt;space&gt;fme_where_builder_clause&lt;space&gt;MODE&lt;comma&gt;WHERE&lt;space&gt;WHERE&lt;space&gt;Clause&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_table_creation_group&lt;space&gt;FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP&lt;space&gt;Table&lt;space&gt;Creation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ORIGIN_GROUP&lt;space&gt;GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP&lt;space&gt;Origin&lt;space&gt;and&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ANNO_GROUP&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ADVANCED_GROUP&lt;space&gt;GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;%GEODB_AVG_NUM_POINTS&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_XY_GROUP&lt;space&gt;GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_Z_GROUP&lt;space&gt;GEODB_ZORIGIN%GEODB_ZSCALE&lt;space&gt;Z&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_MEASURES_GROUP&lt;space&gt;GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_NAME&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_ALIAS&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_NAME&lt;space&gt;Shape&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_ALIAS&lt;space&gt;Shape&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_CONFIG_KEYWORD&lt;space&gt;Configuration&lt;space&gt;Keyword&lt;quote&gt;&lt;comma&gt;DEFAULTS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;space&gt;Grid&lt;space&gt;1&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;GEODB_AVG_NUM_POINTS&lt;space&gt;Average&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Points&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XORIGIN&lt;space&gt;X&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_YORIGIN&lt;space&gt;Y&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_Z_VALUES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZORIGIN&lt;space&gt;Z&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZSCALE&lt;space&gt;Z&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_MEASURES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_ORIGIN&lt;space&gt;Measure&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_SCALE&lt;space&gt;Measure&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;space&gt;Reference&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;geodb_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;GEODB_UPDATE_KEY_COLUMNS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_DROP_TABLE&lt;comma&gt;NO&lt;comma&gt;GEODB_TRUNCATE_TABLE&lt;comma&gt;NO&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_selection_method&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_update_geometry&lt;comma&gt;YES&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_FEATURE_DATASET&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;_GEODBOverwriteGEODB&lt;space&gt;_GEODBOutTransactionType&lt;space&gt;_GEODBOutSimplifyGeometry&lt;space&gt;_GEODBOutXOrigin&lt;space&gt;_GEODBOutYOrigin&lt;space&gt;_GEODBOutScale&lt;space&gt;_GEODBOutZOrigin&lt;space&gt;_GEODBOutZScale&lt;space&gt;_GEODBOutGrid1&lt;space&gt;_TRANSLATE_SPATIAL_DATA_ONLY&lt;space&gt;_GEODBInResolveDomains&lt;space&gt;_GEODBInResolveSubtypeNames&lt;space&gt;_GEODBInIgnoreNetworkInfo&lt;space&gt;_GEODBInIgnoreRelationshipInfo&lt;space&gt;_GEODBInSplitComplexEdges&lt;space&gt;_GEODBInWhereClause&lt;space&gt;NULL_IN_SPLIT_COMPLEX_ANNOS&lt;space&gt;NULL_IN_CACHE_MULTIPATCH_TEXTURES&lt;space&gt;NULL_IN_SEARCH_METHOD&lt;space&gt;NULL_IN_SEARCH_ORDER&lt;space&gt;NULL_IN_SEARCH_FEATURE&lt;space&gt;NULL_IN_CHECK_SIMPLE_GEOM&lt;space&gt;NULL_IN_MERGE_FEAT_LINKED_ANNOS&lt;space&gt;NULL_IN_READ_THREE_POINT_ARCS&lt;space&gt;NULL_IN_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_SIMPLE_DONUT_GEOMETRY&lt;space&gt;_GEODB_IN_ALIAS_MODE&lt;space&gt;GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION&lt;space&gt;GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE&lt;space&gt;GEODATABASE_FILE_OUT_WRITER_MODE&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL&lt;space&gt;GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY&lt;space&gt;GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_FFS_DUMP_FILE&lt;space&gt;GEODATABASE_FILE_OUT_ANNOTATION_UNITS&lt;space&gt;GEODATABASE_FILE_OUT_HAS_MEASURES&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_ORIGIN&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_SCALE&lt;space&gt;GEODATABASE_FILE_OUT_COMPRESS_AT_END&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES&lt;space&gt;GEODATABASE_FILE_OUT_PRESERVE_GLOBALID&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE&lt;space&gt;GEODATABASE_FILE_OUT_VALIDATE_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;GEODATABASE_FILE_OUT_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;PARAMS_TO_NOT_PROPAGATE_ON_INSPECT&lt;comma&gt;&lt;quote&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;ATTRIBUTE_READING_HISTORIC&lt;comma&gt;ALL&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;&lt;quote&gt;Feature&lt;space&gt;Class&lt;space&gt;or&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;FeatureClass1&lt;comma&gt;SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME&lt;comma&gt;NO&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;SQL_EXECUTE_DIRECTIVES&lt;comma&gt;INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="24"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="2863.1551275546926 -73.876088928126109"
#!   BOUNDING_RECT="2863.1551275546926 -73.876088928126109 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER)&lt;backslash&gt;point.gdb"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_GEODB&lt;comma&gt;NO&lt;comma&gt;DATASET_TEMPLATE&lt;comma&gt;&lt;comma&gt;IMPORT_XML_TEMPLATE_GROUP&lt;comma&gt;NO&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;IMPORT_KIND&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;TRANSACTION_TYPE&lt;comma&gt;TRANSACTIONS&lt;comma&gt;FEATURE_DATASET_HANDLING&lt;comma&gt;WRITE&lt;comma&gt;SIMPLIFY_GEOM&lt;comma&gt;No&lt;comma&gt;HAS_Z_VALUES&lt;comma&gt;auto_detect&lt;comma&gt;X_ORIGIN&lt;comma&gt;0&lt;comma&gt;Y_ORIGIN&lt;comma&gt;0&lt;comma&gt;XY_SCALE&lt;comma&gt;0&lt;comma&gt;Z_ORIGIN&lt;comma&gt;0&lt;comma&gt;Z_SCALE&lt;comma&gt;0&lt;comma&gt;GRID_1&lt;comma&gt;0&lt;comma&gt;GEODB_SHARED_WRT_ADV_PARM_GROUP&lt;comma&gt;&lt;comma&gt;REQUESTED_GEODATABASE_VERSION&lt;comma&gt;CURRENT&lt;comma&gt;DEFAULT_Z_VALUE&lt;comma&gt;0&lt;comma&gt;TRANSACTION&lt;comma&gt;0&lt;comma&gt;TRANSACTION_INTERVAL&lt;comma&gt;1000&lt;comma&gt;IGNORE_FAILED_FEATURE_ENTRY&lt;comma&gt;no&lt;comma&gt;MAX_NUMBER_FAILED_FEATURES&lt;comma&gt;-1&lt;comma&gt;DUMP_FAILED_FEATURES&lt;comma&gt;no&lt;comma&gt;FFS_DUMP_FILE&lt;comma&gt;&lt;comma&gt;ANNOTATION_UNITS&lt;comma&gt;unknown_units&lt;comma&gt;HAS_MEASURES&lt;comma&gt;no&lt;comma&gt;COMPRESS_AT_END&lt;comma&gt;no&lt;comma&gt;ENABLE_FAST_DELETES&lt;comma&gt;yes&lt;comma&gt;PRESERVE_GLOBALID&lt;comma&gt;no&lt;comma&gt;ENABLE_LOAD_ONLY_MODE&lt;comma&gt;no&lt;comma&gt;VALIDATE_FEATURES&lt;comma&gt;no&lt;comma&gt;SIMPLIFY_NETWORK_FEATURES&lt;comma&gt;no&lt;comma&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE_TYPE,METAFILE,GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="GEODATABASE_FILE_TRANSACTION_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Edit&lt;space&gt;Session,EDIT_SESSION%Transactions,TRANSACTIONS%None,NONE&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Type:,GEODATABASE_FILE_COMPRESS_AT_END,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Compact&lt;space&gt;Database&lt;space&gt;When&lt;space&gt;Done:,GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Max&lt;space&gt;number&lt;space&gt;of&lt;space&gt;features&lt;space&gt;to&lt;space&gt;ignore:,GEODATABASE_FILE_ENABLE_FAST_DELETES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Fast&lt;space&gt;Deletes:,GEODATABASE_FILE_Z_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_VALIDATE_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Validate&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;Write:,GEODATABASE_FILE_X_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_ANNOTATION_UNITS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Unknown Units&quot;&quot;,unknown_units%&quot;&quot;Decimal Degrees&quot;&quot;,decimal_degrees%Inches,inches%Points,points%Feet,feet%Yards,yards%Miles,miles%&quot;&quot;Nautical Miles&quot;&quot;,nautical_miles%Millimeters,millimeters%Centimeters,centimeters%Meters,meters%Kilometers,kilometers%Decimeters,decimeters&quot;,GEODATABASE_FILE&lt;space&gt;Annotation&lt;space&gt;Units:,GEODATABASE_FILE_OVERWRITE_GEODB,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP FME_DISCLOSURE_OPEN%DATASET_TEMPLATE%++YES+IMPORT_XML_TEMPLATE_GROUP+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;Geodatabase,GEODATABASE_FILE_TRANSACTION,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Transaction&lt;space&gt;Number:,GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Network&lt;space&gt;Features:,GEODATABASE_FILE_Z_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_TRANSACTION_INTERVAL,&quot;OPTIONAL INTEGER&quot;,GEODATABASE_FILE&lt;space&gt;Features&lt;space&gt;Per&lt;space&gt;Transaction,GEODATABASE_FILE_HAS_MEASURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Measures,GEODATABASE_FILE_SIMPLIFY_GEOM,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,GEODATABASE_FILE&lt;space&gt;Simplify&lt;space&gt;Geometry:,GEODATABASE_FILE_PRESERVE_GLOBALID,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Preserve&lt;space&gt;GlobalID:,GEODATABASE_FILE_END_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;After&lt;space&gt;Write,GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Enable&lt;space&gt;Load&lt;space&gt;Only&lt;space&gt;Mode:,GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Failed&lt;space&gt;Features:,GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION,&quot;OPTIONAL LOOKUP_CHOICE Current,CURRENT%10.0%9.3&quot;,GEODATABASE_FILE&lt;space&gt;Geodatabase&lt;space&gt;Version:,GEODATABASE_FILE_GRID_1,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP,&quot;OPTIONAL DISCLOSUREGROUP REQUESTED_GEODATABASE_VERSION%DEFAULT_Z_VALUE%TRANSACTION%TRANSACTION_INTERVAL%IGNORE_FAILED_FEATURE_ENTRY%MAX_NUMBER_FAILED_FEATURES%DUMP_FAILED_FEATURES%FFS_DUMP_FILE%ANNOTATION_UNITS%HAS_MEASURES%MEASURES_ORIGIN%MEASURES_SCALE%COMPRESS_AT_END%ENABLE_FAST_DELETES%PRESERVE_GLOBALID%ENABLE_LOAD_ONLY_MODE%VALIDATE_FEATURES%SIMPLIFY_NETWORK_FEATURES%BEGIN_SQL{0}%END_SQL{0}&quot;,GEODATABASE_FILE&lt;space&gt;Advanced,GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP TEMPLATEFILE%IMPORT_KIND%++YES+OVERWRITE_GEODB+disableParameter&quot;,GEODATABASE_FILE&lt;space&gt;Import&lt;space&gt;XML&lt;space&gt;Workspace&lt;space&gt;Document,GEODATABASE_FILE_DEFAULT_Z_VALUE,&quot;OPTIONAL FLOAT&quot;,GEODATABASE_FILE&lt;space&gt;Default&lt;space&gt;Z&lt;space&gt;Value:,GEODATABASE_FILE_Y_ORIGIN,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_HAS_Z_VALUES,&quot;OPTIONAL LOOKUP_CHOICE Yes,yes%No,no%Auto&lt;space&gt;Detect,auto_detect&quot;,GEODATABASE_FILE&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values:,GEODATABASE_FILE_FFS_DUMP_FILE,&quot;OPTIONAL FILENAME FME_Feature_Store_Files(*.ffs)|*.ffs|All_files(*)|*&quot;,GEODATABASE_FILE&lt;space&gt;Failed&lt;space&gt;Feature&lt;space&gt;Dump&lt;space&gt;filename:,GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_DUMP_FAILED_FEATURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Dump&lt;space&gt;Failed&lt;space&gt;Features&lt;space&gt;to&lt;space&gt;File:,GEODATABASE_FILE_FEATURE_DATASET_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Write&lt;space&gt;Feature&lt;space&gt;Dataset,WRITE%Warn&lt;space&gt;and&lt;space&gt;Ignore&lt;space&gt;Feature&lt;space&gt;Dataset,IGNORE%Error&lt;space&gt;and&lt;space&gt;End&lt;space&gt;Translation,END&quot;,GEODATABASE_FILE&lt;space&gt;Feature&lt;space&gt;Dataset&lt;space&gt;Handling:,GEODATABASE_FILE_BEGIN_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;Before&lt;space&gt;Write,GEODATABASE_FILE_XY_SCALE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ANNOTATION_UNITS" PARM_VALUE="unknown_units"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_BEGIN_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COMPRESS_AT_END" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE_TYPE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DEFAULT_Z_VALUE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DUMP_FAILED_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_FAST_DELETES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ENABLE_LOAD_ONLY_MODE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_END_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FEATURE_DATASET_HANDLING" PARM_VALUE="WRITE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FFS_DUMP_FILE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODB_SHARED_WRT_ADV_PARM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GRID_1" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_MEASURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_HAS_Z_VALUES" PARM_VALUE="auto_detect"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_FAILED_FEATURE_ENTRY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IMPORT_XML_TEMPLATE_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_MAX_NUMBER_FAILED_FEATURES" PARM_VALUE="-1"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_OVERWRITE_GEODB" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_PRESERVE_GLOBALID" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_REQUESTED_GEODATABASE_VERSION" PARM_VALUE="CURRENT"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_GEOM" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLIFY_NETWORK_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_INTERVAL" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSACTION_TYPE" PARM_VALUE="TRANSACTIONS"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_VALIDATE_FEATURES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_XY_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_X_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Y_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_ORIGIN" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_Z_SCALE" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="point:Coerced,ftp_feature_type_name,point,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_point,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,text_string&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;igds_level_name&lt;comma&gt;char&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;,ftp_format_attributes,fme_feature_type&lt;comma&gt;fme_text_string&lt;comma&gt;fme_geometry,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;GEODB_ORIGIN_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_ADVANCED_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_XY_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_Z_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_GROUP&lt;comma&gt;&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;comma&gt;GEODB_XORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_YORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_XYSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;comma&gt;GEODB_ZORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_ZSCALE&lt;comma&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_ORIGIN&lt;comma&gt;&lt;comma&gt;GEODB_MEASURES_SCALE&lt;comma&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.&lt;space&gt;%-#&lt;openbracket&gt;&lt;closebracket&gt;&lt;quote&gt;&lt;openparen&gt;&lt;closeparen&gt;!?*&lt;apos&gt;&lt;amp&gt;+&lt;backslash&gt;&lt;solidus&gt;&lt;opencurly&gt;&lt;closecurly&gt;|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;char&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;blob&lt;comma&gt;fme_binarybuffer&lt;comma&gt;blob&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;blob&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;globalid&lt;comma&gt;fme_buffer&lt;comma&gt;guid&lt;comma&gt;fme_buffer&lt;comma&gt;date&lt;comma&gt;fme_datetime&lt;comma&gt;timestamp_offset&lt;comma&gt;fme_datetime&lt;comma&gt;date_only&lt;comma&gt;fme_date&lt;comma&gt;time_only&lt;comma&gt;fme_time&lt;comma&gt;integer&lt;comma&gt;fme_int32&lt;comma&gt;integer&lt;comma&gt;fme_uint16&lt;comma&gt;smallint&lt;comma&gt;fme_int16&lt;comma&gt;smallint&lt;comma&gt;fme_int8&lt;comma&gt;smallint&lt;comma&gt;fme_uint8&lt;comma&gt;bigint&lt;comma&gt;fme_int64&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;char&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;boolean&lt;comma&gt;fme_boolean&lt;comma&gt;&lt;quote&gt;double&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;subtype&lt;openparen&gt;stringset&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;subtype_codes&lt;openparen&gt;stringmap&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;range_domain&lt;openparen&gt;range_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;coded_domain&lt;openparen&gt;coded_domain&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,&lt;backslash&gt;&lt;backslash&gt;&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|&lt;openbracket&gt;%#&lt;space&gt;&lt;apos&gt;&lt;amp&gt;+-&lt;closebracket&gt;.^~&lt;dollar&gt;&lt;comma&gt;&lt;closeparen&gt;&lt;openparen&gt;,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point&lt;comma&gt;fme_point&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_line&lt;comma&gt;geodb_multipoint&lt;comma&gt;fme_point&lt;comma&gt;geodb_table&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_table&lt;comma&gt;fme_collection&lt;comma&gt;geodb_arc&lt;comma&gt;fme_arc&lt;comma&gt;geodb_ellipse&lt;comma&gt;fme_ellipse&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;geodb_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_pro_annotation&lt;comma&gt;fme_text&lt;comma&gt;geodb_dimension&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_junction&lt;comma&gt;fme_point&lt;comma&gt;geodb_simple_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_complex_edge&lt;comma&gt;fme_line&lt;comma&gt;geodb_attributed_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_relationship&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_undefined&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_metadata&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;geodb_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_raster&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;geodb_table&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_group&lt;space&gt;fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_common_group&lt;space&gt;fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group&lt;space&gt;General&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_feature_operation&lt;space&gt;Insert&lt;comma&gt;INSERT&lt;comma&gt;fme_update_geometry&lt;comma&gt;fme_selection_group&lt;comma&gt;mie_pack%Update&lt;comma&gt;UPDATE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert&lt;comma&gt;UPSERT&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Delete&lt;comma&gt;DELETE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;fme_update_geometry&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;comma&gt;fme_spatial_group&lt;comma&gt;fme_advanced_group&lt;comma&gt;oracle_sequenced_cols%&lt;lt&gt;at&lt;gt&gt;Value&lt;lt&gt;openparen&lt;gt&gt;fme_db_operation&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;MULTIPLE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;space&gt;Feature&lt;space&gt;Operation&lt;quote&gt;&lt;comma&gt;INSERT&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_table_handling&lt;space&gt;Use&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;USE_EXISTING&lt;comma&gt;fme_table_creation_group%Create&lt;lt&gt;space&lt;gt&gt;If&lt;lt&gt;space&lt;gt&gt;Needed&lt;comma&gt;CREATE_IF_MISSING%Drop&lt;lt&gt;space&lt;gt&gt;and&lt;lt&gt;space&lt;gt&gt;Create&lt;comma&gt;DROP_CREATE%Truncate&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;TRUNCATE_EXISTING&lt;comma&gt;fme_table_creation_group&lt;space&gt;Table&lt;space&gt;Handling&lt;quote&gt;&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;fme_update_geometry&lt;space&gt;Yes&lt;comma&gt;YES%No&lt;comma&gt;NO&lt;space&gt;Update&lt;space&gt;Spatial&lt;space&gt;Column&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;YES&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_selection_group&lt;space&gt;fme_selection_method&lt;space&gt;Row&lt;space&gt;Selection&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;RADIOPARAMETERGROUP&lt;space&gt;fme_selection_method&lt;space&gt;fme_match_columns&lt;comma&gt;MATCH_COLUMNS%fme_where_builder_clause&lt;comma&gt;BUILDER&lt;space&gt;Row&lt;space&gt;Selection&lt;space&gt;Method&lt;quote&gt;&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;ATTRLIST_COMMAS&lt;space&gt;fme_match_columns&lt;space&gt;Match&lt;space&gt;Columns&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;TEXT_EDIT_SQL_CFG_OR_ATTR&lt;space&gt;fme_where_builder_clause&lt;space&gt;MODE&lt;comma&gt;WHERE&lt;space&gt;WHERE&lt;space&gt;Clause&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_table_creation_group&lt;space&gt;FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP&lt;space&gt;Table&lt;space&gt;Creation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ORIGIN_GROUP&lt;space&gt;GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP&lt;space&gt;Origin&lt;space&gt;and&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ANNO_GROUP&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_ADVANCED_GROUP&lt;space&gt;GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;%GEODB_AVG_NUM_POINTS&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_XY_GROUP&lt;space&gt;GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_Z_GROUP&lt;space&gt;GEODB_ZORIGIN%GEODB_ZSCALE&lt;space&gt;Z&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;GEODB_MEASURES_GROUP&lt;space&gt;GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_NAME&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_OBJECT_ID_ALIAS&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_NAME&lt;space&gt;Shape&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_SHAPE_ALIAS&lt;space&gt;Shape&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;GEODB_CONFIG_KEYWORD&lt;space&gt;Configuration&lt;space&gt;Keyword&lt;quote&gt;&lt;comma&gt;DEFAULTS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;space&gt;Grid&lt;space&gt;1&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;GEODB_AVG_NUM_POINTS&lt;space&gt;Average&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Points&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XORIGIN&lt;space&gt;X&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_YORIGIN&lt;space&gt;Y&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_XYSCALE&lt;space&gt;X&lt;solidus&gt;Y&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_Z_VALUES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Z&lt;space&gt;Values&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZORIGIN&lt;space&gt;Z&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ZSCALE&lt;space&gt;Z&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;GEODB_HAS_MEASURES&lt;space&gt;yes%no&lt;space&gt;Contains&lt;space&gt;Measures&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_ORIGIN&lt;space&gt;Measure&lt;space&gt;Origin&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_MEASURES_SCALE&lt;space&gt;Measure&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;GEODB_ANNO_REFERENCE_SCALE&lt;space&gt;Annotation&lt;space&gt;Reference&lt;space&gt;Scale&lt;quote&gt;&lt;comma&gt;,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;geodb_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;GEODB_UPDATE_KEY_COLUMNS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_DROP_TABLE&lt;comma&gt;NO&lt;comma&gt;GEODB_TRUNCATE_TABLE&lt;comma&gt;NO&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_selection_method&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_update_geometry&lt;comma&gt;YES&lt;comma&gt;GEODB_OBJECT_ID_NAME&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_OBJECT_ID_ALIAS&lt;comma&gt;OBJECTID&lt;comma&gt;GEODB_SHAPE_NAME&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_SHAPE_ALIAS&lt;comma&gt;SHAPE&lt;comma&gt;GEODB_CONFIG_KEYWORD&lt;comma&gt;DEFAULTS&lt;comma&gt;GEODB_FEATURE_DATASET&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_GRID&lt;opencurly&gt;1&lt;closecurly&gt;&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_AVG_NUM_POINTS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_Z_VALUES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_HAS_MEASURES&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;GEODB_ANNO_REFERENCE_SCALE&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;_GEODBOverwriteGEODB&lt;space&gt;_GEODBOutTransactionType&lt;space&gt;_GEODBOutSimplifyGeometry&lt;space&gt;_GEODBOutXOrigin&lt;space&gt;_GEODBOutYOrigin&lt;space&gt;_GEODBOutScale&lt;space&gt;_GEODBOutZOrigin&lt;space&gt;_GEODBOutZScale&lt;space&gt;_GEODBOutGrid1&lt;space&gt;_TRANSLATE_SPATIAL_DATA_ONLY&lt;space&gt;_GEODBInResolveDomains&lt;space&gt;_GEODBInResolveSubtypeNames&lt;space&gt;_GEODBInIgnoreNetworkInfo&lt;space&gt;_GEODBInIgnoreRelationshipInfo&lt;space&gt;_GEODBInSplitComplexEdges&lt;space&gt;_GEODBInWhereClause&lt;space&gt;NULL_IN_SPLIT_COMPLEX_ANNOS&lt;space&gt;NULL_IN_CACHE_MULTIPATCH_TEXTURES&lt;space&gt;NULL_IN_SEARCH_METHOD&lt;space&gt;NULL_IN_SEARCH_ORDER&lt;space&gt;NULL_IN_SEARCH_FEATURE&lt;space&gt;NULL_IN_CHECK_SIMPLE_GEOM&lt;space&gt;NULL_IN_MERGE_FEAT_LINKED_ANNOS&lt;space&gt;NULL_IN_READ_THREE_POINT_ARCS&lt;space&gt;NULL_IN_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;NULL_IN_SIMPLE_DONUT_GEOMETRY&lt;space&gt;_GEODB_IN_ALIAS_MODE&lt;space&gt;GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION&lt;space&gt;GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE&lt;space&gt;GEODATABASE_FILE_OUT_WRITER_MODE&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION&lt;space&gt;GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL&lt;space&gt;GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY&lt;space&gt;GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_FFS_DUMP_FILE&lt;space&gt;GEODATABASE_FILE_OUT_ANNOTATION_UNITS&lt;space&gt;GEODATABASE_FILE_OUT_HAS_MEASURES&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_ORIGIN&lt;space&gt;GEODATABASE_FILE_OUT_MEASURES_SCALE&lt;space&gt;GEODATABASE_FILE_OUT_COMPRESS_AT_END&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES&lt;space&gt;GEODATABASE_FILE_OUT_PRESERVE_GLOBALID&lt;space&gt;GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE&lt;space&gt;GEODATABASE_FILE_OUT_VALIDATE_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES&lt;space&gt;GEODATABASE_FILE_OUT_BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;GEODATABASE_FILE_OUT_END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;PARAMS_TO_NOT_PROPAGATE_ON_INSPECT&lt;comma&gt;&lt;quote&gt;BEGIN_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;space&gt;END_SQL&lt;opencurly&gt;0&lt;closecurly&gt;&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;ATTRIBUTE_READING_HISTORIC&lt;comma&gt;ALL&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;&lt;quote&gt;Feature&lt;space&gt;Class&lt;space&gt;or&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;FeatureClass1&lt;comma&gt;SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME&lt;comma&gt;NO&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;File&lt;space&gt;Geodatabase&lt;quote&gt;&lt;comma&gt;SQL_EXECUTE_DIRECTIVES&lt;comma&gt;INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="27"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="2018.7701877018762 -193.87608892812614"
#!   BOUNDING_RECT="2018.7701877018762 -193.87608892812614 430 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="igds_level_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; text_string SET_TO &lt;at&gt;Value&lt;openparen&gt;fme_text_string&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="18"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="17"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="10"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="9"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="19"
#!   SOURCE_PORT_DESC="fo 0 COERCED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="23"
#!   SOURCE_NODE="19"
#!   TARGET_NODE="14"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 Coerced"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="22"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="29"
#!   SOURCE_NODE="27"
#!   TARGET_NODE="24"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Coerced"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="8"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="28"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="27"
#!   SOURCE_PORT_DESC="fo 1 Point"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="27"
#!   SOURCE_PORT_DESC="fo 2 Text"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="16"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="19"
#!   SOURCE_PORT_DESC="fo 3 Curve"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="13"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 4 Area"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="26"
#!   SOURCE_NODE="17"
#!   TARGET_NODE="20"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO PARAMETER_2 C:\Users\<USER>\Desktop\新建文件夹 (liujun)\统计

DEFAULT_MACRO PARAMETER C:\Users\<USER>\Desktop\新建文件夹 (1111111)

# ============================================================================
INCLUDE [ if {{$(PARAMETER_2$encode)} == {}} { puts_real {Parameter 'PARAMETER_2' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)none2none.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME none2none
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 18"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_2_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode),FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "PATH" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT PATH FEATURE_TYPE FeatureReader_2_PATH
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE FeatureReader_2_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester_2)" = dgn ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_DIRECTIVES APPLY_WORLD_FILE,YES,APPLY_WORLD_FILE_TO_ATTRS,YES,ASSUME_MATCHING_UNITS,NO,CURVE_VERTICES,,ELEVATION_SHIFT_FACTOR,,ENCODING,gbk,EXPAND_CELLS,yes,EXPAND_UNNAMED_CELLS,no,EXPLODE_DIMENSION_ELEM,YES,EXPOSE_ATTRS_GROUP,,IGDS_EXPOSE_FORMAT_ATTRS,,IGDS_FRAMME,NO,IGDS_MSLINKS,NO,IGDS_RDR_ADV_PARM_GROUP,,METAFILE,design,NETWORK_AUTHENTICATION,,OVERRIDE_GLOBAL_ORIGIN,no,PRESERVE_CELL_INSERTS,no,PRESERVE_CURVES,NO,READ_SELECTED_MODELS,NO,READ_TAGS,NO,READ_XATTRS_AND_ITEM_SETS,NO,READ_XREF_FILES,NO,READ_XREFS_AS_ELEMENTS,YES,READER_META_ATTRIBUTES,fme_dataset,REMOVE_DUPLICATES,NO,SPLIT_COMPLEX_CHAINS,NO,SPLIT_MULTITEXT,YES,SUBS_PER_MASTER,,TAGS_AS_TEXT,NO,UNITS,IGDS_MASTER_UNITS,UOR_SCALE,,UORS_PER_SUB,,USE_LEVEL_NAMES,NO,USE_RICH_ARC_GEOM,YES,USE_SEARCH_ENVELOPE,NO,XREF_OPTIONS,
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Tester_2_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { IGDS  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,design }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_<OTHER>           }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer }    INPUT  FEATURE_TYPE FeatureReader_<OTHER>    OUTPUT { FEATURE_TYPE AttributeExposer_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE AttributeExposer_OUTPUT    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_text_string<closeparen>,Tester)" != Pattern<space>Control<space>Element ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} GeometryFilterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { GeometryFilter }    INPUT  FEATURE_TYPE Tester_PASSED    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    FILTER_TYPES { Null,Point,Text,Area,Surface,Solid,Raster,PointCloud,VoxelGrid,Curve }    INSTANCES { INSTANTIATE }    BREAK_AGG { Yes }    OUTPUT { <UNFILTERED> FEATURE_TYPE GeometryFilter_<UNFILTERED>  }    OUTPUT { Null FEATURE_TYPE GeometryFilter_Null  }    OUTPUT { Point FEATURE_TYPE GeometryFilter_Point  }    OUTPUT { Text FEATURE_TYPE GeometryFilter_Text  }    OUTPUT { Area FEATURE_TYPE GeometryFilter_Area  }    OUTPUT { Surface FEATURE_TYPE GeometryFilter_Surface  }    OUTPUT { Solid FEATURE_TYPE GeometryFilter_Solid  }    OUTPUT { Raster FEATURE_TYPE GeometryFilter_Raster  }    OUTPUT { PointCloud FEATURE_TYPE GeometryFilter_PointCloud  }    OUTPUT { VoxelGrid FEATURE_TYPE GeometryFilter_VoxelGrid  }    OUTPUT { Curve FEATURE_TYPE GeometryFilter_Curve  }
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter Null Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_Null
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter Surface Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_Surface
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter Solid Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_Solid
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter Raster Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_Raster
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter PointCloud Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_PointCloud
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter VoxelGrid Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_VoxelGrid
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter <UNFILTERED> Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_<UNFILTERED>
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE GeometryFilter_Point    INPUT  FEATURE_TYPE GeometryFilter_Text    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "text_string" "SET_TO" "<at>Value<openparen>fme_text_string<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode)<backslash>point.gdb,FeatureWriter_2)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter_2 }    WRITER_TYPE { GEODATABASE_FILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter_2)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_GEODB<comma>NO<comma>DATASET_TEMPLATE<comma><comma>IMPORT_XML_TEMPLATE_GROUP<comma>NO<comma>TEMPLATEFILE<comma><lt>Unused<gt><comma>IMPORT_KIND<comma><lt>Unused<gt><comma>TRANSACTION_TYPE<comma>TRANSACTIONS<comma>FEATURE_DATASET_HANDLING<comma>WRITE<comma>SIMPLIFY_GEOM<comma>No<comma>HAS_Z_VALUES<comma>auto_detect<comma>X_ORIGIN<comma>0<comma>Y_ORIGIN<comma>0<comma>XY_SCALE<comma>0<comma>Z_ORIGIN<comma>0<comma>Z_SCALE<comma>0<comma>GRID_1<comma>0<comma>GEODB_SHARED_WRT_ADV_PARM_GROUP<comma><comma>REQUESTED_GEODATABASE_VERSION<comma>CURRENT<comma>DEFAULT_Z_VALUE<comma>0<comma>TRANSACTION<comma>0<comma>TRANSACTION_INTERVAL<comma>1000<comma>IGNORE_FAILED_FEATURE_ENTRY<comma>no<comma>MAX_NUMBER_FAILED_FEATURES<comma>-1<comma>DUMP_FAILED_FEATURES<comma>no<comma>FFS_DUMP_FILE<comma><comma>ANNOTATION_UNITS<comma>unknown_units<comma>HAS_MEASURES<comma>no<comma>COMPRESS_AT_END<comma>no<comma>ENABLE_FAST_DELETES<comma>yes<comma>PRESERVE_GLOBALID<comma>no<comma>ENABLE_LOAD_ONLY_MODE<comma>no<comma>VALIDATE_FEATURES<comma>no<comma>SIMPLIFY_NETWORK_FEATURES<comma>no<comma>BEGIN_SQL<opencurly>0<closecurly><comma><comma>END_SQL<opencurly>0<closecurly><comma><comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE_TYPE,METAFILE,GEODATABASE_FILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.<space>%-#<openbracket><closebracket><quote><openparen><closeparen>!?*<apos><amp>+<backslash><solidus><opencurly><closecurly>|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>char<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>char<openparen>2048<closeparen><comma>fme_buffer<comma>char<openparen>2048<closeparen><comma>fme_xml<comma>char<openparen>2048<closeparen><comma>fme_json<comma>blob<comma>fme_binarybuffer<comma>blob<comma>fme_varbinary<openparen>width<closeparen><comma>blob<comma>fme_binary<openparen>width<closeparen><comma>globalid<comma>fme_buffer<comma>guid<comma>fme_buffer<comma>date<comma>fme_datetime<comma>timestamp_offset<comma>fme_datetime<comma>date_only<comma>fme_date<comma>time_only<comma>fme_time<comma>integer<comma>fme_int32<comma>integer<comma>fme_uint16<comma>smallint<comma>fme_int16<comma>smallint<comma>fme_int8<comma>smallint<comma>fme_uint8<comma>bigint<comma>fme_int64<comma>float<comma>fme_real32<comma>double<comma>fme_real64<comma>double<comma>fme_uint32<comma>char<openparen>20<closeparen><comma>fme_uint64<comma>boolean<comma>fme_boolean<comma><quote>double<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma>subtype<openparen>stringset<closeparen><comma>fme_char<openparen>width<closeparen><comma>subtype_codes<openparen>stringmap<closeparen><comma>fme_char<openparen>width<closeparen><comma>range_domain<openparen>range_domain<closeparen><comma>fme_char<openparen>width<closeparen><comma>coded_domain<openparen>coded_domain<closeparen><comma>fme_char<openparen>width<closeparen>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,<backslash><backslash><quote>:?*<lt><gt>|<openbracket>%#<space><apos><amp>+-<closebracket>.^~<dollar><comma><closeparen><openparen>,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point<comma>fme_point<comma>geodb_polygon<comma>fme_polygon<comma>geodb_polygon<comma>fme_rounded_rectangle<comma>geodb_polyline<comma>fme_line<comma>geodb_multipoint<comma>fme_point<comma>geodb_table<comma>fme_no_geom<comma>geodb_table<comma>fme_collection<comma>geodb_arc<comma>fme_arc<comma>geodb_ellipse<comma>fme_ellipse<comma>geodb_polygon<comma>fme_rectangle<comma>geodb_annotation<comma>fme_text<comma>geodb_pro_annotation<comma>fme_text<comma>geodb_dimension<comma>fme_point<comma>geodb_simple_junction<comma>fme_point<comma>geodb_simple_edge<comma>fme_line<comma>geodb_complex_edge<comma>fme_line<comma>geodb_attributed_relationship<comma>fme_no_geom<comma>geodb_relationship<comma>fme_no_geom<comma>geodb_undefined<comma>fme_no_geom<comma>geodb_metadata<comma>fme_polygon<comma>geodb_multipatch<comma>fme_surface<comma>geodb_multipatch<comma>fme_solid<comma>geodb_polygon<comma>fme_raster<comma>geodb_polygon<comma>fme_point_cloud<comma>geodb_polygon<comma>fme_voxel_grid<comma>geodb_table<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>fme_configuration_group<space>fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group<space>Table<quote><comma><comma><quote>GUI<space>NAMEDGROUP<space>fme_configuration_common_group<space>fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group<space>General<quote><comma><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_feature_operation<space>Insert<comma>INSERT<comma>fme_update_geometry<comma>fme_selection_group<comma>mie_pack%Update<comma>UPDATE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert<comma>UPSERT<comma>fme_where_builder_clause<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Delete<comma>DELETE<comma>++fme_table_handling+USE_EXISTING<comma>fme_update_geometry<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<comma>fme_spatial_group<comma>fme_advanced_group<comma>oracle_sequenced_cols%<lt>at<gt>Value<lt>openparen<gt>fme_db_operation<lt>closeparen<gt><comma>MULTIPLE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<space>Feature<space>Operation<quote><comma>INSERT<comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_table_handling<space>Use<lt>space<gt>Existing<comma>USE_EXISTING<comma>fme_table_creation_group%Create<lt>space<gt>If<lt>space<gt>Needed<comma>CREATE_IF_MISSING%Drop<lt>space<gt>and<lt>space<gt>Create<comma>DROP_CREATE%Truncate<lt>space<gt>Existing<comma>TRUNCATE_EXISTING<comma>fme_table_creation_group<space>Table<space>Handling<quote><comma>CREATE_IF_MISSING<comma><quote>GUI<space>WHOLE_LINE<space>LOOKUP_CHOICE<space>fme_update_geometry<space>Yes<comma>YES%No<comma>NO<space>Update<space>Spatial<space>Column<openparen>s<closeparen><quote><comma>YES<comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_selection_group<space>fme_selection_method<space>Row<space>Selection<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>RADIOPARAMETERGROUP<space>fme_selection_method<space>fme_match_columns<comma>MATCH_COLUMNS%fme_where_builder_clause<comma>BUILDER<space>Row<space>Selection<space>Method<quote><comma>MATCH_COLUMNS<comma><quote>GUI<space>WHOLE_LINE<space>ATTRLIST_COMMAS<space>fme_match_columns<space>Match<space>Columns<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>TEXT_EDIT_SQL_CFG_OR_ATTR<space>fme_where_builder_clause<space>MODE<comma>WHERE<space>WHERE<space>Clause<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_table_creation_group<space>FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP<space>Table<space>Creation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ORIGIN_GROUP<space>GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP<space>Origin<space>and<space>Scale<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ANNO_GROUP<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ADVANCED_GROUP<space>GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID<opencurly>1<closecurly>%GEODB_AVG_NUM_POINTS<space>Advanced<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_XY_GROUP<space>GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE<space>X<solidus>Y<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_Z_GROUP<space>GEODB_ZORIGIN%GEODB_ZSCALE<space>Z<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_MEASURES_GROUP<space>GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE<space>Measures<quote><comma><comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_NAME<space>Object<space>ID<space>Field<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_ALIAS<space>Object<space>ID<space>Alias<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_NAME<space>Shape<space>Field<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_ALIAS<space>Shape<space>Alias<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_CONFIG_KEYWORD<space>Configuration<space>Keyword<quote><comma>DEFAULTS<comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_GRID<opencurly>1<closecurly><space>Grid<space>1<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>GEODB_AVG_NUM_POINTS<space>Average<space>Number<space>of<space>Points<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XORIGIN<space>X<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_YORIGIN<space>Y<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XYSCALE<space>X<solidus>Y<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_Z_VALUES<space>yes%no<space>Contains<space>Z<space>Values<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZORIGIN<space>Z<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZSCALE<space>Z<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_MEASURES<space>yes%no<space>Contains<space>Measures<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_ORIGIN<space>Measure<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_SCALE<space>Measure<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<space>Reference<space>Scale<quote><comma>,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>geodb_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>GEODB_UPDATE_KEY_COLUMNS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_DROP_TABLE<comma>NO<comma>GEODB_TRUNCATE_TABLE<comma>NO<comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_selection_method<comma>MATCH_COLUMNS<comma>fme_match_columns<comma><quote><quote><quote><quote><quote><quote><comma>fme_where_builder_clause<comma><quote><quote><quote><quote><quote><quote><comma>fme_update_geometry<comma>YES<comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_FEATURE_DATASET<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_GRID<opencurly>1<closecurly><comma><quote><quote><quote><quote><quote><quote><comma>GEODB_AVG_NUM_POINTS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_Z_VALUES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_MEASURES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_ANNO_REFERENCE_SCALE<comma><quote><quote><quote><quote><quote><quote>,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS<comma><quote>_GEODBOverwriteGEODB<space>_GEODBOutTransactionType<space>_GEODBOutSimplifyGeometry<space>_GEODBOutXOrigin<space>_GEODBOutYOrigin<space>_GEODBOutScale<space>_GEODBOutZOrigin<space>_GEODBOutZScale<space>_GEODBOutGrid1<space>_TRANSLATE_SPATIAL_DATA_ONLY<space>_GEODBInResolveDomains<space>_GEODBInResolveSubtypeNames<space>_GEODBInIgnoreNetworkInfo<space>_GEODBInIgnoreRelationshipInfo<space>_GEODBInSplitComplexEdges<space>_GEODBInWhereClause<space>NULL_IN_SPLIT_COMPLEX_ANNOS<space>NULL_IN_CACHE_MULTIPATCH_TEXTURES<space>NULL_IN_SEARCH_METHOD<space>NULL_IN_SEARCH_ORDER<space>NULL_IN_SEARCH_FEATURE<space>NULL_IN_CHECK_SIMPLE_GEOM<space>NULL_IN_MERGE_FEAT_LINKED_ANNOS<space>NULL_IN_READ_THREE_POINT_ARCS<space>NULL_IN_BEGIN_SQL<opencurly>0<closecurly><space>NULL_IN_END_SQL<opencurly>0<closecurly><space>NULL_IN_SIMPLE_DONUT_GEOMETRY<space>_GEODB_IN_ALIAS_MODE<space>GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION<space>GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE<space>GEODATABASE_FILE_OUT_WRITER_MODE<space>GEODATABASE_FILE_OUT_TRANSACTION<space>GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL<space>GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY<space>GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_FFS_DUMP_FILE<space>GEODATABASE_FILE_OUT_ANNOTATION_UNITS<space>GEODATABASE_FILE_OUT_HAS_MEASURES<space>GEODATABASE_FILE_OUT_MEASURES_ORIGIN<space>GEODATABASE_FILE_OUT_MEASURES_SCALE<space>GEODATABASE_FILE_OUT_COMPRESS_AT_END<space>GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES<space>GEODATABASE_FILE_OUT_PRESERVE_GLOBALID<space>GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE<space>GEODATABASE_FILE_OUT_VALIDATE_FEATURES<space>GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES<space>GEODATABASE_FILE_OUT_BEGIN_SQL<opencurly>0<closecurly><space>GEODATABASE_FILE_OUT_END_SQL<opencurly>0<closecurly><quote><comma>PARAMS_TO_NOT_PROPAGATE_ON_INSPECT<comma><quote>BEGIN_SQL<opencurly>0<closecurly><space>END_SQL<opencurly>0<closecurly><quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>ATTRIBUTE_READING_HISTORIC<comma>ALL<comma>FEATURE_TYPE_NAME<comma><quote>Feature<space>Class<space>or<space>Table<quote><comma>FEATURE_TYPE_DEFAULT_NAME<comma>FeatureClass1<comma>SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME<comma>NO<comma>READER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>SQL_EXECUTE_DIRECTIVES<comma>INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "point:Coerced,ftp_feature_type_name,point,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_point,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,text_string<comma>char<lt>openparen<gt>200<lt>closeparen<gt><comma>igds_level_name<comma>char<lt>openparen<gt>200<lt>closeparen<gt>,ftp_user_attribute_values,<comma>,ftp_format_attributes,fme_feature_type<comma>fme_text_string<comma>fme_geometry,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>GEODB_ORIGIN_GROUP<comma><comma>GEODB_ANNO_GROUP<comma><comma>GEODB_ADVANCED_GROUP<comma><comma>GEODB_XY_GROUP<comma><comma>GEODB_Z_GROUP<comma><comma>GEODB_MEASURES_GROUP<comma><comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_GRID<opencurly>1<closecurly><comma><comma>GEODB_AVG_NUM_POINTS<comma><comma>GEODB_XORIGIN<comma><comma>GEODB_YORIGIN<comma><comma>GEODB_XYSCALE<comma><comma>GEODB_HAS_Z_VALUES<comma><comma>GEODB_ZORIGIN<comma><comma>GEODB_ZSCALE<comma><comma>GEODB_HAS_MEASURES<comma><comma>GEODB_MEASURES_ORIGIN<comma><comma>GEODB_MEASURES_SCALE<comma><comma>GEODB_ANNO_REFERENCE_SCALE<comma>" }    WRITER_PARAMS { "ANNOTATION_UNITS,unknown_units,BEGIN_SQL{0},,COMPRESS_AT_END,no,COORDINATE_SYSTEM_GRANULARITY,FEATURE_TYPE,DEFAULT_Z_VALUE,0,DESTINATION_DATASETTYPE_VALIDATION,Yes,DUMP_FAILED_FEATURES,no,ENABLE_FAST_DELETES,yes,ENABLE_LOAD_ONLY_MODE,no,END_SQL{0},,FEATURE_DATASET_HANDLING,WRITE,FFS_DUMP_FILE,,GEODB_SHARED_WRT_ADV_PARM_GROUP,,GRID_1,0,HAS_MEASURES,no,HAS_Z_VALUES,auto_detect,IGNORE_FAILED_FEATURE_ENTRY,no,IMPORT_XML_TEMPLATE_GROUP,NO,MAX_NUMBER_FAILED_FEATURES,-1,OVERWRITE_GEODB,NO,PRESERVE_GLOBALID,no,REQUESTED_GEODATABASE_VERSION,CURRENT,SIMPLIFY_GEOM,No,SIMPLIFY_NETWORK_FEATURES,no,TRANSACTION,0,TRANSACTION_INTERVAL,1000,TRANSACTION_TYPE,TRANSACTIONS,VALIDATE_FEATURES,no,X_ORIGIN,0,XY_SCALE,0,Y_ORIGIN,0,Z_ORIGIN,0,Z_SCALE,0" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Coerced FEATURE_TYPE AttributeCreator_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,point,FeatureWriter_2))
# -------------------------------------------------------------------------
FACTORY_DEF {*} GQueryFactory    FACTORY_NAME { GeometryCoercer }    INPUT  FEATURE_TYPE GeometryFilter_Area    GQUERY { "for<space><dollar>geom<space>in<space><solidus><solidus>geometry<lf>where<space><dollar>geom<solidus>count<openparen>parent::geometry<closeparen><space>=<space>0<lf>return<space>number<openparen><dollar>geom<solidus><at>fme_id<closeparen>" }    ACTION COERCE_GEOMETRY    PARAMETER { fme_line BASENAME GeometryCoercer }    OUTPUT { RESULT FEATURE_TYPE GeometryCoercer_COERCED  }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_3 }    INPUT  FEATURE_TYPE GeometryCoercer_COERCED    INPUT  FEATURE_TYPE GeometryFilter_Curve    TEST { 1 = 1 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_3_PASSED         }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode)<backslash>line.gdb,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { GEODATABASE_FILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_GEODB<comma>NO<comma>DATASET_TEMPLATE<comma><comma>IMPORT_XML_TEMPLATE_GROUP<comma>NO<comma>TEMPLATEFILE<comma><lt>Unused<gt><comma>IMPORT_KIND<comma><lt>Unused<gt><comma>TRANSACTION_TYPE<comma>TRANSACTIONS<comma>FEATURE_DATASET_HANDLING<comma>WRITE<comma>SIMPLIFY_GEOM<comma>No<comma>HAS_Z_VALUES<comma>auto_detect<comma>X_ORIGIN<comma>0<comma>Y_ORIGIN<comma>0<comma>XY_SCALE<comma>0<comma>Z_ORIGIN<comma>0<comma>Z_SCALE<comma>0<comma>GRID_1<comma>0<comma>GEODB_SHARED_WRT_ADV_PARM_GROUP<comma><comma>REQUESTED_GEODATABASE_VERSION<comma>CURRENT<comma>DEFAULT_Z_VALUE<comma>0<comma>TRANSACTION<comma>0<comma>TRANSACTION_INTERVAL<comma>1000<comma>IGNORE_FAILED_FEATURE_ENTRY<comma>no<comma>MAX_NUMBER_FAILED_FEATURES<comma>-1<comma>DUMP_FAILED_FEATURES<comma>no<comma>FFS_DUMP_FILE<comma><comma>ANNOTATION_UNITS<comma>unknown_units<comma>HAS_MEASURES<comma>no<comma>COMPRESS_AT_END<comma>no<comma>ENABLE_FAST_DELETES<comma>yes<comma>PRESERVE_GLOBALID<comma>no<comma>ENABLE_LOAD_ONLY_MODE<comma>no<comma>VALIDATE_FEATURES<comma>no<comma>SIMPLIFY_NETWORK_FEATURES<comma>no<comma>BEGIN_SQL<opencurly>0<closecurly><comma><comma>END_SQL<opencurly>0<closecurly><comma><comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE_TYPE,METAFILE,GEODATABASE_FILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,:.<space>%-#<openbracket><closebracket><quote><openparen><closeparen>!?*<apos><amp>+<backslash><solidus><opencurly><closecurly>|=,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,char<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>char<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>char<openparen>2048<closeparen><comma>fme_buffer<comma>char<openparen>2048<closeparen><comma>fme_xml<comma>char<openparen>2048<closeparen><comma>fme_json<comma>blob<comma>fme_binarybuffer<comma>blob<comma>fme_varbinary<openparen>width<closeparen><comma>blob<comma>fme_binary<openparen>width<closeparen><comma>globalid<comma>fme_buffer<comma>guid<comma>fme_buffer<comma>date<comma>fme_datetime<comma>timestamp_offset<comma>fme_datetime<comma>date_only<comma>fme_date<comma>time_only<comma>fme_time<comma>integer<comma>fme_int32<comma>integer<comma>fme_uint16<comma>smallint<comma>fme_int16<comma>smallint<comma>fme_int8<comma>smallint<comma>fme_uint8<comma>bigint<comma>fme_int64<comma>float<comma>fme_real32<comma>double<comma>fme_real64<comma>double<comma>fme_uint32<comma>char<openparen>20<closeparen><comma>fme_uint64<comma>boolean<comma>fme_boolean<comma><quote>double<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma>subtype<openparen>stringset<closeparen><comma>fme_char<openparen>width<closeparen><comma>subtype_codes<openparen>stringmap<closeparen><comma>fme_char<openparen>width<closeparen><comma>range_domain<openparen>range_domain<closeparen><comma>fme_char<openparen>width<closeparen><comma>coded_domain<openparen>coded_domain<closeparen><comma>fme_char<openparen>width<closeparen>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,<backslash><backslash><quote>:?*<lt><gt>|<openbracket>%#<space><apos><amp>+-<closebracket>.^~<dollar><comma><closeparen><openparen>,FEATURE_TYPE_LENGTH,160,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>GEODATABASE_FILE.fmf,FORMAT_NAME,GEODATABASE_FILE,GEOM_MAP,geodb_point<comma>fme_point<comma>geodb_polygon<comma>fme_polygon<comma>geodb_polygon<comma>fme_rounded_rectangle<comma>geodb_polyline<comma>fme_line<comma>geodb_multipoint<comma>fme_point<comma>geodb_table<comma>fme_no_geom<comma>geodb_table<comma>fme_collection<comma>geodb_arc<comma>fme_arc<comma>geodb_ellipse<comma>fme_ellipse<comma>geodb_polygon<comma>fme_rectangle<comma>geodb_annotation<comma>fme_text<comma>geodb_pro_annotation<comma>fme_text<comma>geodb_dimension<comma>fme_point<comma>geodb_simple_junction<comma>fme_point<comma>geodb_simple_edge<comma>fme_line<comma>geodb_complex_edge<comma>fme_line<comma>geodb_attributed_relationship<comma>fme_no_geom<comma>geodb_relationship<comma>fme_no_geom<comma>geodb_undefined<comma>fme_no_geom<comma>geodb_metadata<comma>fme_polygon<comma>geodb_multipatch<comma>fme_surface<comma>geodb_multipatch<comma>fme_solid<comma>geodb_polygon<comma>fme_raster<comma>geodb_polygon<comma>fme_point_cloud<comma>geodb_polygon<comma>fme_voxel_grid<comma>geodb_table<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,Ascending,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,GEODATABASE_FILE,WRITER_ATTR_INDEX_TYPES,Ascending,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>fme_configuration_group<space>fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group<space>Table<quote><comma><comma><quote>GUI<space>NAMEDGROUP<space>fme_configuration_common_group<space>fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group<space>General<quote><comma><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_feature_operation<space>Insert<comma>INSERT<comma>fme_update_geometry<comma>fme_selection_group<comma>mie_pack%Update<comma>UPDATE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert<comma>UPSERT<comma>fme_where_builder_clause<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Delete<comma>DELETE<comma>++fme_table_handling+USE_EXISTING<comma>fme_update_geometry<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<comma>fme_spatial_group<comma>fme_advanced_group<comma>oracle_sequenced_cols%<lt>at<gt>Value<lt>openparen<gt>fme_db_operation<lt>closeparen<gt><comma>MULTIPLE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<space>Feature<space>Operation<quote><comma>INSERT<comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_table_handling<space>Use<lt>space<gt>Existing<comma>USE_EXISTING<comma>fme_table_creation_group%Create<lt>space<gt>If<lt>space<gt>Needed<comma>CREATE_IF_MISSING%Drop<lt>space<gt>and<lt>space<gt>Create<comma>DROP_CREATE%Truncate<lt>space<gt>Existing<comma>TRUNCATE_EXISTING<comma>fme_table_creation_group<space>Table<space>Handling<quote><comma>CREATE_IF_MISSING<comma><quote>GUI<space>WHOLE_LINE<space>LOOKUP_CHOICE<space>fme_update_geometry<space>Yes<comma>YES%No<comma>NO<space>Update<space>Spatial<space>Column<openparen>s<closeparen><quote><comma>YES<comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_selection_group<space>fme_selection_method<space>Row<space>Selection<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>RADIOPARAMETERGROUP<space>fme_selection_method<space>fme_match_columns<comma>MATCH_COLUMNS%fme_where_builder_clause<comma>BUILDER<space>Row<space>Selection<space>Method<quote><comma>MATCH_COLUMNS<comma><quote>GUI<space>WHOLE_LINE<space>ATTRLIST_COMMAS<space>fme_match_columns<space>Match<space>Columns<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>TEXT_EDIT_SQL_CFG_OR_ATTR<space>fme_where_builder_clause<space>MODE<comma>WHERE<space>WHERE<space>Clause<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_table_creation_group<space>FME_DISCLOSURE_OPEN%GEODB_FEATURE_DATASET%GEODB_HAS_Z_VALUES%GEODB_HAS_MEASURES%GEODB_ORIGIN_GROUP%GEODB_ANNO_GROUP%GEODB_ADVANCED_GROUP<space>Table<space>Creation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ORIGIN_GROUP<space>GEODB_XY_GROUP%GEODB_Z_GROUP%GEODB_MEASURES_GROUP<space>Origin<space>and<space>Scale<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ANNO_GROUP<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_ADVANCED_GROUP<space>GEODB_OBJECT_ID_NAME%GEODB_OBJECT_ID_ALIAS%GEODB_SHAPE_NAME%GEODB_SHAPE_ALIAS%GEODB_CONFIG_KEYWORD%GEODB_GRID<opencurly>1<closecurly>%GEODB_AVG_NUM_POINTS<space>Advanced<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_XY_GROUP<space>GEODB_XORIGIN%GEODB_YORIGIN%GEODB_XYSCALE<space>X<solidus>Y<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_Z_GROUP<space>GEODB_ZORIGIN%GEODB_ZSCALE<space>Z<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>GEODB_MEASURES_GROUP<space>GEODB_MEASURES_ORIGIN%GEODB_MEASURES_SCALE<space>Measures<quote><comma><comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_NAME<space>Object<space>ID<space>Field<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_OBJECT_ID_ALIAS<space>Object<space>ID<space>Alias<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_NAME<space>Shape<space>Field<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_SHAPE_ALIAS<space>Shape<space>Alias<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>GEODB_CONFIG_KEYWORD<space>Configuration<space>Keyword<quote><comma>DEFAULTS<comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_GRID<opencurly>1<closecurly><space>Grid<space>1<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>GEODB_AVG_NUM_POINTS<space>Average<space>Number<space>of<space>Points<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XORIGIN<space>X<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_YORIGIN<space>Y<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_XYSCALE<space>X<solidus>Y<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_Z_VALUES<space>yes%no<space>Contains<space>Z<space>Values<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZORIGIN<space>Z<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ZSCALE<space>Z<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>GEODB_HAS_MEASURES<space>yes%no<space>Contains<space>Measures<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_ORIGIN<space>Measure<space>Origin<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_MEASURES_SCALE<space>Measure<space>Scale<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>GEODB_ANNO_REFERENCE_SCALE<space>Annotation<space>Reference<space>Scale<quote><comma>,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>geodb_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>GEODB_UPDATE_KEY_COLUMNS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_DROP_TABLE<comma>NO<comma>GEODB_TRUNCATE_TABLE<comma>NO<comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_selection_method<comma>MATCH_COLUMNS<comma>fme_match_columns<comma><quote><quote><quote><quote><quote><quote><comma>fme_where_builder_clause<comma><quote><quote><quote><quote><quote><quote><comma>fme_update_geometry<comma>YES<comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_FEATURE_DATASET<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_GRID<opencurly>1<closecurly><comma><quote><quote><quote><quote><quote><quote><comma>GEODB_AVG_NUM_POINTS<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_Z_VALUES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_HAS_MEASURES<comma><quote><quote><quote><quote><quote><quote><comma>GEODB_ANNO_REFERENCE_SCALE<comma><quote><quote><quote><quote><quote><quote>,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS<comma><quote>_GEODBOverwriteGEODB<space>_GEODBOutTransactionType<space>_GEODBOutSimplifyGeometry<space>_GEODBOutXOrigin<space>_GEODBOutYOrigin<space>_GEODBOutScale<space>_GEODBOutZOrigin<space>_GEODBOutZScale<space>_GEODBOutGrid1<space>_TRANSLATE_SPATIAL_DATA_ONLY<space>_GEODBInResolveDomains<space>_GEODBInResolveSubtypeNames<space>_GEODBInIgnoreNetworkInfo<space>_GEODBInIgnoreRelationshipInfo<space>_GEODBInSplitComplexEdges<space>_GEODBInWhereClause<space>NULL_IN_SPLIT_COMPLEX_ANNOS<space>NULL_IN_CACHE_MULTIPATCH_TEXTURES<space>NULL_IN_SEARCH_METHOD<space>NULL_IN_SEARCH_ORDER<space>NULL_IN_SEARCH_FEATURE<space>NULL_IN_CHECK_SIMPLE_GEOM<space>NULL_IN_MERGE_FEAT_LINKED_ANNOS<space>NULL_IN_READ_THREE_POINT_ARCS<space>NULL_IN_BEGIN_SQL<opencurly>0<closecurly><space>NULL_IN_END_SQL<opencurly>0<closecurly><space>NULL_IN_SIMPLE_DONUT_GEOMETRY<space>_GEODB_IN_ALIAS_MODE<space>GEODATABASE_FILE_OUT_REQUESTED_GEODATABASE_VERSION<space>GEODATABASE_FILE_OUT_DEFAULT_Z_VALUE<space>GEODATABASE_FILE_OUT_WRITER_MODE<space>GEODATABASE_FILE_OUT_TRANSACTION<space>GEODATABASE_FILE_OUT_TRANSACTION_INTERVAL<space>GEODATABASE_FILE_OUT_IGNORE_FAILED_FEATURE_ENTRY<space>GEODATABASE_FILE_OUT_MAX_NUMBER_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_DUMP_FAILED_FEATURES<space>GEODATABASE_FILE_OUT_FFS_DUMP_FILE<space>GEODATABASE_FILE_OUT_ANNOTATION_UNITS<space>GEODATABASE_FILE_OUT_HAS_MEASURES<space>GEODATABASE_FILE_OUT_MEASURES_ORIGIN<space>GEODATABASE_FILE_OUT_MEASURES_SCALE<space>GEODATABASE_FILE_OUT_COMPRESS_AT_END<space>GEODATABASE_FILE_OUT_ENABLE_FAST_DELETES<space>GEODATABASE_FILE_OUT_PRESERVE_GLOBALID<space>GEODATABASE_FILE_OUT_ENABLE_LOAD_ONLY_MODE<space>GEODATABASE_FILE_OUT_VALIDATE_FEATURES<space>GEODATABASE_FILE_OUT_SIMPLIFY_NETWORK_FEATURES<space>GEODATABASE_FILE_OUT_BEGIN_SQL<opencurly>0<closecurly><space>GEODATABASE_FILE_OUT_END_SQL<opencurly>0<closecurly><quote><comma>PARAMS_TO_NOT_PROPAGATE_ON_INSPECT<comma><quote>BEGIN_SQL<opencurly>0<closecurly><space>END_SQL<opencurly>0<closecurly><quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>ATTRIBUTE_READING_HISTORIC<comma>ALL<comma>FEATURE_TYPE_NAME<comma><quote>Feature<space>Class<space>or<space>Table<quote><comma>FEATURE_TYPE_DEFAULT_NAME<comma>FeatureClass1<comma>SUPPORTS_SCHEMA_IN_FEATURE_TYPE_NAME<comma>NO<comma>READER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>the<space>Esri<space>File<space>Geodatabase<quote><comma>SQL_EXECUTE_DIRECTIVES<comma>INCLUDE:NAMED_CONNECTION%DATASET%CREATE_FEATURE_TABLES_FROM_DATA,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "line:Coerced,ftp_feature_type_name,line,ftp_writer,GEODATABASE_FILE,ftp_geometry,geodb_polyline,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,igds_level_name<comma>char<lt>openparen<gt>200<lt>closeparen<gt>,ftp_format_attributes,fme_feature_type<comma>fme_text_string<comma>fme_geometry,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>GEODB_ORIGIN_GROUP<comma><comma>GEODB_ANNO_GROUP<comma><comma>GEODB_ADVANCED_GROUP<comma><comma>GEODB_XY_GROUP<comma><comma>GEODB_Z_GROUP<comma><comma>GEODB_MEASURES_GROUP<comma><comma>GEODB_OBJECT_ID_NAME<comma>OBJECTID<comma>GEODB_OBJECT_ID_ALIAS<comma>OBJECTID<comma>GEODB_SHAPE_NAME<comma>SHAPE<comma>GEODB_SHAPE_ALIAS<comma>SHAPE<comma>GEODB_CONFIG_KEYWORD<comma>DEFAULTS<comma>GEODB_GRID<opencurly>1<closecurly><comma><comma>GEODB_AVG_NUM_POINTS<comma><comma>GEODB_XORIGIN<comma><comma>GEODB_YORIGIN<comma><comma>GEODB_XYSCALE<comma><comma>GEODB_HAS_Z_VALUES<comma><comma>GEODB_ZORIGIN<comma><comma>GEODB_ZSCALE<comma><comma>GEODB_HAS_MEASURES<comma><comma>GEODB_MEASURES_ORIGIN<comma><comma>GEODB_MEASURES_SCALE<comma><comma>GEODB_ANNO_REFERENCE_SCALE<comma>" }    WRITER_PARAMS { "ANNOTATION_UNITS,unknown_units,BEGIN_SQL{0},,COMPRESS_AT_END,no,COORDINATE_SYSTEM_GRANULARITY,FEATURE_TYPE,DEFAULT_Z_VALUE,0,DESTINATION_DATASETTYPE_VALIDATION,Yes,DUMP_FAILED_FEATURES,no,ENABLE_FAST_DELETES,yes,ENABLE_LOAD_ONLY_MODE,no,END_SQL{0},,FEATURE_DATASET_HANDLING,WRITE,FFS_DUMP_FILE,,GEODB_SHARED_WRT_ADV_PARM_GROUP,,GRID_1,0,HAS_MEASURES,no,HAS_Z_VALUES,auto_detect,IGNORE_FAILED_FEATURE_ENTRY,no,IMPORT_XML_TEMPLATE_GROUP,NO,MAX_NUMBER_FAILED_FEATURES,-1,OVERWRITE_GEODB,NO,PRESERVE_GLOBALID,no,REQUESTED_GEODATABASE_VERSION,CURRENT,SIMPLIFY_GEOM,No,SIMPLIFY_NETWORK_FEATURES,no,TRANSACTION,0,TRANSACTION_INTERVAL,1000,TRANSACTION_TYPE,TRANSACTIONS,VALIDATE_FEATURES,no,X_ORIGIN,0,XY_SCALE,0,Y_ORIGIN,0,Z_ORIGIN,0,Z_SCALE,0" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Coerced FEATURE_TYPE Tester_3_PASSED  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,line,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

