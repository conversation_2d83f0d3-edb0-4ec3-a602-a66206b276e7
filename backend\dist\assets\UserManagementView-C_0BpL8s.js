import{d as ye,r as n,M as J,L as P,o as Ue,c as x,e as t,w as l,f as o,i as v,E as r,Z as W,p as X,b as f,y as d,D as Y,I as h,J as ee,A as ae,l as ke,s as xe,T as O,k as b,_ as Ce}from"./index-LgsLWi3x.js";const ze={class:"user-mgmt-tabs-container"},$e={key:0,class:"tab-content"},De={class:"toolbar"},Te={class:"action-btn-group"},Be={class:"table-pagination"},Re={key:0,class:"tab-content"},Ae={class:"toolbar"},Fe={class:"table-pagination"},Se={style:{"text-align":"center"}},qe={style:{margin:"16px 0"}},Pe={style:{"font-weight":"bold"}},Le=ye({__name:"UserManagementView",setup(Ee){const C=n("user"),te=()=>{},L=n([]),z=n([]),$=n([]),w=n(!1),E=n(),m=J({username:"",password:"",real_name:"",role:"",department:""}),le={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{pattern:/^[a-zA-Z0-9]+$/,message:"仅限字母和数字",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:8,message:"至少8位",trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"需含大小写字母和数字",trigger:"blur"}],real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5]+$/,message:"必须是中文",trigger:"blur"}],role:[{required:!0,message:"请选择类型",trigger:"change"}],department:[{required:!0,message:"请选择部门",trigger:"change"}]},V=n(!1),M=n(),u=J({username:"",real_name:"",role:"",department:""}),se={real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5]+$/,message:"必须是中文",trigger:"blur"}],role:[{required:!0,message:"请选择类型",trigger:"change"}],department:[{required:!0,message:"请选择部门",trigger:"change"}]},D=n(!1),G=n(""),y=n(!1),N=n(),T=J({department:""}),ne={department:[{required:!0,message:"请输入部门名称",trigger:"blur"}]},U=n(""),B=n(1),R=n(10),re=P(()=>L.value.length),oe=P(()=>{const s=(B.value-1)*R.value;return L.value.slice(s,s+R.value)}),A=n(1),F=n(10),de=P(()=>$.value.length),ue=P(()=>{const s=(A.value-1)*F.value;return $.value.slice(s,s+F.value)}),k=async()=>{const s=await v.post("/api/user/list");s.data.success?L.value=s.data.users:r.error(s.data.message||"获取用户失败")},Z=async()=>{const s=await v.post("/api/get_departments");s.data&&Array.isArray(s.data.departments)?(z.value=s.data.departments,$.value=s.data.departments.map(e=>({department:e}))):(z.value=[],$.value=[])},ie=()=>{E.value&&E.value.validate(async s=>{if(s){const e=await v.post("/api/user/add",m);e.data.success?(r.success("新增用户成功"),w.value=!1,k()):r.error(e.data.message||"新增用户失败")}})},me=s=>{u.username=s.username,u.real_name=s.real_name,u.role=s.role,u.department=s.department,V.value=!0},pe=()=>{M.value&&M.value.validate(async s=>{if(s){const e=await v.post("/api/user/update",u);e.data.success?(r.success("修改成功"),V.value=!1,k()):r.error(e.data.message||"修改失败")}})},fe=s=>{if(s.username==="admin"){r.warning("admin账号禁止删除");return}if(s.role==="admin"&&s.username!=="admin"){r.warning("禁止删除其他管理员账号");return}O.confirm(`确定要删除用户【${s.username}】吗？`,"二次确认",{type:"warning",confirmButtonText:"删除",cancelButtonText:"取消"}).then(async()=>{const e=await v.post("/api/user/delete",{username:s.username});e.data.success?(r.success("删除成功"),k()):r.error(e.data.message||"删除失败")}).catch(()=>{})},ge=async s=>{if(s.username===U.value){r.warning("不能重置自己密码");return}if(s.role==="admin"&&s.username!=="admin"){r.warning("禁止重置其他管理员密码");return}O.confirm(`确定要重置用户【${s.username}】的密码吗？`,"二次确认",{type:"warning",confirmButtonText:"重置",cancelButtonText:"取消"}).then(async()=>{const e=await v.post("/api/user/reset_pwd",{username:s.username});e.data.success?(G.value=e.data.new_password,D.value=!0):r.error(e.data.message||"重置失败")}).catch(()=>{})},ve=()=>{N.value&&N.value.validate(async s=>{if(s){const e=await v.post("/api/department/add",T);e.data.success?(r.success("新增部门成功"),y.value=!1,Z()):r.error(e.data.message||"新增部门失败")}})},be=s=>{O.confirm(`确定要删除部门【${s.department}】吗？`,"二次确认",{type:"warning",confirmButtonText:"删除",cancelButtonText:"取消"}).then(async()=>{const e=await v.post("/api/department/delete",{department:s.department});e.data.success?(r.success("删除成功"),Z(),k()):r.error(e.data.message||"删除失败")}).catch(()=>{})};return Ue(()=>{try{const s=sessionStorage.getItem("user");if(s){const e=JSON.parse(s);U.value=e.username}}catch{U.value=""}k(),Z()}),(s,e)=>{const i=o("el-button"),g=o("el-table-column"),_e=o("el-tag"),H=o("el-table"),K=o("el-pagination"),Q=o("el-tab-pane"),ce=o("el-tabs"),we=o("el-card"),_=o("el-input"),p=o("el-form-item"),c=o("el-option"),S=o("el-select"),I=o("el-form"),q=o("el-dialog"),Ve=o("el-icon");return b(),x("div",ze,[t(we,{shadow:"hover",class:"main-card"},{default:l(()=>[t(ce,{modelValue:C.value,"onUpdate:modelValue":e[8]||(e[8]=a=>C.value=a),onTabClick:te,class:"mgmt-tabs"},{default:l(()=>[t(Q,{label:"用户管理",name:"user"},{default:l(()=>[t(W,{name:"slide-fade",mode:"out-in"},{default:l(()=>[C.value==="user"?(b(),x("div",$e,[f("div",De,[t(i,{type:"primary",onClick:e[0]||(e[0]=a=>w.value=!0)},{default:l(()=>e[27]||(e[27]=[d("新增用户")])),_:1})]),t(H,{data:oe.value,style:{width:"100%"},border:""},{default:l(()=>[t(g,{prop:"username",label:"用户名"}),t(g,{prop:"real_name",label:"真实姓名",width:"90"}),t(g,{prop:"role",label:"类型",width:"100"},{default:l(a=>[t(_e,{type:a.row.role==="admin"?"danger":"info"},{default:l(()=>[d(Y(a.row.role==="admin"?"管理员":"普通用户"),1)]),_:2},1032,["type"])]),_:1}),t(g,{prop:"department",label:"部门"}),t(g,{prop:"created_at",label:"注册时间"}),t(g,{label:"操作",width:"200"},{default:l(a=>[f("div",Te,[t(i,{size:"small",onClick:j=>me(a.row)},{default:l(()=>e[28]||(e[28]=[d("编辑")])),_:2},1032,["onClick"]),t(i,{size:"small",type:"warning",onClick:j=>ge(a.row),disabled:a.row.username==="admin"||a.row.username===U.value||a.row.role==="admin"&&a.row.username!=="admin"},{default:l(()=>e[29]||(e[29]=[d("重置密码")])),_:2},1032,["onClick","disabled"]),t(i,{size:"small",type:"danger",onClick:j=>fe(a.row),disabled:a.row.username==="admin"||a.row.role==="admin"&&a.row.username!=="admin"},{default:l(()=>e[30]||(e[30]=[d("删除")])),_:2},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["data"]),f("div",Be,[t(K,{"current-page":B.value,"onUpdate:currentPage":e[1]||(e[1]=a=>B.value=a),"page-size":R.value,"onUpdate:pageSize":e[2]||(e[2]=a=>R.value=a),"page-sizes":[5,10,20,50],total:re.value,layout:"total, sizes, prev, pager, next, jumper",background:"",onSizeChange:e[3]||(e[3]=a=>B.value=1)},null,8,["current-page","page-size","total"])])])):X("",!0)]),_:1})]),_:1}),t(Q,{label:"部门管理",name:"dept"},{default:l(()=>[t(W,{name:"slide-fade",mode:"out-in"},{default:l(()=>[C.value==="dept"?(b(),x("div",Re,[f("div",Ae,[t(i,{type:"success",onClick:e[4]||(e[4]=a=>y.value=!0)},{default:l(()=>e[31]||(e[31]=[d("新增部门")])),_:1})]),t(H,{data:ue.value,style:{width:"400px","margin-top":"8px","font-size":"16px"},border:"",size:"small",class:"dept-table"},{default:l(()=>[t(g,{prop:"department",label:"部门名称"}),t(g,{label:"操作",width:"100"},{default:l(a=>[t(i,{size:"small",type:"danger",onClick:j=>be(a.row)},{default:l(()=>e[32]||(e[32]=[d("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),f("div",Fe,[t(K,{"current-page":A.value,"onUpdate:currentPage":e[5]||(e[5]=a=>A.value=a),"page-size":F.value,"onUpdate:pageSize":e[6]||(e[6]=a=>F.value=a),"page-sizes":[5,10,20,50],total:de.value,layout:"total, sizes, prev, pager, next, jumper",background:"",onSizeChange:e[7]||(e[7]=a=>A.value=1)},null,8,["current-page","page-size","total"])])])):X("",!0)]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(q,{modelValue:w.value,"onUpdate:modelValue":e[15]||(e[15]=a=>w.value=a),title:"新增用户",width:"400px"},{footer:l(()=>[t(i,{onClick:e[14]||(e[14]=a=>w.value=!1)},{default:l(()=>e[33]||(e[33]=[d("取消")])),_:1}),t(i,{type:"primary",onClick:ie},{default:l(()=>e[34]||(e[34]=[d("确定")])),_:1})]),default:l(()=>[t(I,{model:m,rules:le,ref_key:"addUserFormRef",ref:E,"label-width":"80px"},{default:l(()=>[t(p,{label:"用户名",prop:"username"},{default:l(()=>[t(_,{modelValue:m.username,"onUpdate:modelValue":e[9]||(e[9]=a=>m.username=a),placeholder:"仅限字母和数字"},null,8,["modelValue"])]),_:1}),t(p,{label:"密码",prop:"password"},{default:l(()=>[t(_,{modelValue:m.password,"onUpdate:modelValue":e[10]||(e[10]=a=>m.password=a),type:"password","show-password":"",placeholder:"至少8位，含大小写字母和数字"},null,8,["modelValue"])]),_:1}),t(p,{label:"真实姓名",prop:"real_name"},{default:l(()=>[t(_,{modelValue:m.real_name,"onUpdate:modelValue":e[11]||(e[11]=a=>m.real_name=a),placeholder:"中文姓名"},null,8,["modelValue"])]),_:1}),t(p,{label:"类型",prop:"role"},{default:l(()=>[t(S,{modelValue:m.role,"onUpdate:modelValue":e[12]||(e[12]=a=>m.role=a),placeholder:"请选择类型"},{default:l(()=>[t(c,{label:"管理员",value:"admin"}),t(c,{label:"普通用户",value:"user"})]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"部门",prop:"department"},{default:l(()=>[t(S,{modelValue:m.department,"onUpdate:modelValue":e[13]||(e[13]=a=>m.department=a),placeholder:"请选择部门"},{default:l(()=>[(b(!0),x(h,null,ee(z.value,a=>(b(),ae(c,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(q,{modelValue:V.value,"onUpdate:modelValue":e[21]||(e[21]=a=>V.value=a),title:"编辑用户",width:"400px"},{footer:l(()=>[t(i,{onClick:e[20]||(e[20]=a=>V.value=!1)},{default:l(()=>e[35]||(e[35]=[d("取消")])),_:1}),t(i,{type:"primary",onClick:pe},{default:l(()=>e[36]||(e[36]=[d("保存")])),_:1})]),default:l(()=>[t(I,{model:u,rules:se,ref_key:"editUserFormRef",ref:M,"label-width":"80px"},{default:l(()=>[t(p,{label:"用户名"},{default:l(()=>[t(_,{modelValue:u.username,"onUpdate:modelValue":e[16]||(e[16]=a=>u.username=a),disabled:""},null,8,["modelValue"])]),_:1}),t(p,{label:"真实姓名",prop:"real_name"},{default:l(()=>[t(_,{modelValue:u.real_name,"onUpdate:modelValue":e[17]||(e[17]=a=>u.real_name=a)},null,8,["modelValue"])]),_:1}),t(p,{label:"类型",prop:"role"},{default:l(()=>[t(S,{modelValue:u.role,"onUpdate:modelValue":e[18]||(e[18]=a=>u.role=a),disabled:U.value!=="admin"},{default:l(()=>[t(c,{label:"管理员",value:"admin"}),t(c,{label:"普通用户",value:"user"})]),_:1},8,["modelValue","disabled"])]),_:1}),t(p,{label:"部门",prop:"department"},{default:l(()=>[t(S,{modelValue:u.department,"onUpdate:modelValue":e[19]||(e[19]=a=>u.department=a)},{default:l(()=>[(b(!0),x(h,null,ee(z.value,a=>(b(),ae(c,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(q,{modelValue:D.value,"onUpdate:modelValue":e[23]||(e[23]=a=>D.value=a),title:"重置密码",width:"350px"},{default:l(()=>[f("div",Se,[t(Ve,{style:{"font-size":"32px",color:"#67c23a"}},{default:l(()=>[t(ke(xe))]),_:1}),f("div",qe,[e[37]||(e[37]=d("新密码：")),f("span",Pe,Y(G.value),1)]),e[39]||(e[39]=f("div",{style:{color:"#f56c6c","margin-bottom":"12px"}},"请妥善保存新密码，页面关闭后将无法再次查看！",-1)),t(i,{type:"primary",onClick:e[22]||(e[22]=a=>D.value=!1)},{default:l(()=>e[38]||(e[38]=[d("知道了")])),_:1})])]),_:1},8,["modelValue"]),t(q,{modelValue:y.value,"onUpdate:modelValue":e[26]||(e[26]=a=>y.value=a),title:"新增部门",width:"350px"},{footer:l(()=>[t(i,{onClick:e[25]||(e[25]=a=>y.value=!1)},{default:l(()=>e[40]||(e[40]=[d("取消")])),_:1}),t(i,{type:"primary",onClick:ve},{default:l(()=>e[41]||(e[41]=[d("确定")])),_:1})]),default:l(()=>[t(I,{model:T,rules:ne,ref_key:"addDeptFormRef",ref:N,"label-width":"80px"},{default:l(()=>[t(p,{label:"部门名称",prop:"department"},{default:l(()=>[t(_,{modelValue:T.department,"onUpdate:modelValue":e[24]||(e[24]=a=>T.department=a),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Ne=Ce(Le,[["__scopeId","data-v-68c4faeb"]]);export{Ne as default};
