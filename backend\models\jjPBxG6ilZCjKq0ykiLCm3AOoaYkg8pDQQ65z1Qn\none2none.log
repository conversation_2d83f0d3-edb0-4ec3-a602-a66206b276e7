2025-05-15 20:03:58|   0.0|  0.0|INFORM|Current FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|System Time: 20250515120358 UTC
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Workspace was last saved in FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME_HOME is 'C:\Program Files\FME\'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME ESRI ArcGIS Server Edition (floating)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Permanent License.
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Machine host name is: DESKTOP-UJA03KF
2025-05-15 20:03:58|   0.0|  0.0|INFORM|OS Locale Name     : zh_CN
2025-05-15 20:03:58|   0.0|  0.0|INFORM|OS Locale Encoding : GBK
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Process Encoding   : UTF-8
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME API version: '4.0 20230426'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: FME_BASE is 'no'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: FME_MF_DIR is 'D:\GeoStream_Integration\fronted\backend\models\jjPBxG6ilZCjKq0ykiLCm3AOoaYkg8pDQQ65z1Qn/'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: FME_MF_NAME is 'dgn转GIS.fmw'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: FME_PRODUCT_NAME is 'FME(R) 2023.1.0.0'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Operating System: Microsoft Windows 10 64-bit  (Build 19045)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Platform: WIN64
2025-05-15 20:03:58|   0.0|  0.0|INFORM|System Status: 16.08 GB of disk space available in the FME temporary folder (C:\Users\<USER>\AppData\Local\Temp)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|System Status: 63.79 GB of physical memory available
2025-05-15 20:03:58|   0.0|  0.0|INFORM|System Status: 255.18 GB of virtual memory available
2025-05-15 20:03:58|   0.0|  0.0|INFORM|START - ProcessID: 9920, peak process memory usage: 43424 kB, current process memory usage: 43424 kB
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Command line arguments are `C:\Program Files\FME\fme.exe' `D:\GeoStream_Integration\fronted\backend\models\jjPBxG6ilZCjKq0ykiLCm3AOoaYkg8pDQQ65z1Qn\dgn转GIS.fmw' `--PARAMETER_2' `D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000' `--PARAMETER' `D:\GeoStream_Integration\fronted\backend\models\jjPBxG6ilZCjKq0ykiLCm3AOoaYkg8pDQQ65z1Qn\output\task_1747310634257_uy9jq0kem'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Connection Storage: 'C:\Users\<USER>\AppData\Roaming\Safe Software\FME\'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Shared folders for formats are : C:\Program Files\FME\datasources;C:\Users\<USER>\Documents\FME\Formats
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Shared folders for transformers are : C:\Users\<USER>\AppData\Roaming\Safe Software\FME\Packages\23619-win64\transformers;C:\Program Files\FME\transformers
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Shared folders for coordinate systems are : C:\Users\<USER>\Documents\FME\CoordinateSystems
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Shared folders for coordinate system exceptions are : C:\Users\<USER>\Documents\FME\CoordinateSystemExceptions
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Shared folders for coordinate system grid overrides are : C:\Users\<USER>\Documents\FME\CoordinateSystemGridOverrides
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Shared folders for CS-MAP transformation exceptions are : C:\Users\<USER>\Documents\FME\CsmapTransformationExceptions
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Shared folders for transformer categories are : C:\Users\<USER>\Documents\FME\TransformerCategories
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Reader Keyword is `MULTI_READER'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Writer Keyword is `MULTI_DEST'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Writer Group Definition Keyword is `MULTI_DEST_DEF'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Reader type is `MULTI_READER'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Writer type is `MULTI_WRITER'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Current working folder is `D:\GeoStream_Integration\fronted\backend'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Temporary folder is `C:\Users\<USER>\AppData\Local\Temp', set from environment variable `TEMP'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Cache folder is 'C:\Users\<USER>\AppData\Local\Temp'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: FME_HOME is `C:\Program Files\FME\'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Start freeing memory when the process exceeds 191.38 GB
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME Configuration: Stop freeing memory when the process is below 143.54 GB
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Creating writer for format: 
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Creating reader for format: 
2025-05-15 20:03:58|   0.0|  0.0|INFORM|MULTI_READER(MULTI_READER): Will fail with first member reader failure
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Using Multi Reader with keyword `MULTI_READER' to read multiple datasets
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Using MultiWriter with keyword `MULTI_DEST' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Loaded module 'Geometry_func' from file 'C:\Program Files\FME\plugins/Geometry_func.dll'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME API version of module 'Geometry_func' matches current internal version (4.0 20230426)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Loaded module 'QueryFactory' from file 'C:\Program Files\FME\plugins/QueryFactory.dll'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME API version of module 'QueryFactory' matches current internal version (4.0 20230426)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Loaded module 'GeometryFilterFactory' from file 'C:\Program Files\FME\plugins/GeometryFilterFactory.dll'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME API version of module 'GeometryFilterFactory' matches current internal version (4.0 20230426)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Loaded module 'GQueryFactory' from file 'C:\Program Files\FME\plugins/GQueryFactory.dll'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME API version of module 'GQueryFactory' matches current internal version (4.0 20230426)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Emptying factory pipeline
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Loaded module 'PATH' from file 'C:\Program Files\FME\plugins/PATH.dll'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|FME API version of module 'PATH' matches current internal version (4.0 20230426)
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Performing query against PATH dataset `D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Path Reader: Opening the PATH Reader on folder 'D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Path Reader: Using Glob Pattern '*'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Path Reader: Allowed Path Type set to 'ANY'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Path Reader: Recurse into subdirectories 'true'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Path Reader: Hidden Files and Folders set to 'INCLUDE'
2025-05-15 20:03:58|   0.0|  0.0|INFORM|Path Reader: Retrieve file properties 'false'
2025-05-15 20:03:58|   0.4|  0.4|INFORM|Creating reader for format: Bentley MicroStation Design (V7)
2025-05-15 20:03:58|   0.4|  0.0|INFORM|Performing query against IGDS dataset `D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000\种子文件-2000.dgn'
2025-05-15 20:03:58|   0.4|  0.0|INFORM|Creating reader for format: Bentley MicroStation Design (V7)
2025-05-15 20:03:58|   0.4|  0.0|INFORM|IGDS READER: Using Enhanced geometry.
2025-05-15 20:03:58|   0.4|  0.0|INFORM|Using IGDS Reader to read dataset `D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000\种子文件-2000.dgn'
2025-05-15 20:03:59|   0.5|  0.1|INFORM|Creating reader for format: Bentley MicroStation Design (V8)
2025-05-15 20:03:59|   0.6|  0.1|INFORM|Trying to find a DYNAMIC plugin for reader named `DGNV8'
2025-05-15 20:03:59|   0.9|  0.3|INFORM|Loaded module 'DGNV8' from file 'C:\Program Files\FME\plugins/DGNV8.dll'
2025-05-15 20:03:59|   0.9|  0.0|INFORM|FME API version of module 'DGNV8' matches current internal version (4.0 20230426)
2025-05-15 20:03:59|   0.9|  0.0|INFORM|DESIGN READER: Opening DGN V8 file 'D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000\种子文件-2000.dgn'
2025-05-15 20:03:59|   1.0|  0.1|INFORM|DESIGN READER: Using Enhanced geometry
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Exploding cells into their component pieces 
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Unnamed_cells are not being expanded -- Donuts may be formed if multiple intersecting polygons existed which may not retain their original color
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Only centerline of multiline will be imported
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Exploding dimension element into its pieces
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: While processing XREF files, their parent's model will not be used
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Discarding cell insert points
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Discarding unnamed cell (groups) insert points
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Vectorizing curve elements into line elements (5 points per segment)
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER: Model 'Default' Coordinate Information:
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER:           1 UORs per subunit and 1000 subunit per master unit
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER:           Global origin X: 2147483648 Y: 2147483648 Z: 2147483648
2025-05-15 20:03:59|   1.0|  0.0|INFORM|DESIGN READER:           One FME Feature coordinate unit is equal to 1000 UORs (Master Units)
2025-05-15 20:03:59|   1.2|  0.2|INFORM|Creating reader for format: Autodesk AutoCAD DWG/DXF
2025-05-15 20:03:59|   1.2|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `ACAD'
2025-05-15 20:03:59|   1.2|  0.0|INFORM|Loaded module 'ACAD' from file 'C:\Program Files\FME\plugins/acad/ACAD.dll'
2025-05-15 20:03:59|   1.2|  0.0|INFORM|FME API version of module 'acad/ACAD' matches current internal version (4.0 20230426)
2025-05-15 20:04:00|   1.6|  0.4|INFORM|AutoCAD Reader: Successfully opened the source dataset 'Design' file 'D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000\种子文件-2000.dgn'
2025-05-15 20:04:00|   2.0|  0.4|INFORM|Creating reader for format: Autodesk AutoCAD DWG/DXF
2025-05-15 20:04:00|   2.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `ACAD'
2025-05-15 20:04:01|   2.5|  0.5|INFORM|AutoCAD Reader: Successfully opened the source dataset 'Design' file 'D:\GeoStream_Integration\fronted\backend\temp\ZSRWlfyyTy0MKno0gEVhRYvn0vdkhyV408usca5K\种子文件-2000\种子文件-2000.dgn'
2025-05-15 20:04:01|   2.5|  0.0|INFORM|Creating writer for format: 
2025-05-15 20:04:01|   2.5|  0.0|INFORM|Using MultiWriter with keyword `FeatureWriter_2' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-15 20:04:01|   2.6|  0.1|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_color.blue' to 'igds_color_blue' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.6|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_color.green' to 'igds_color_green' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.6|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_color.red' to 'igds_color_red' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.6|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.flags' to 'igds_linkage_0__flags' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.6|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.string' to 'igds_linkage_0__string' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.6|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.string_id' to 'igds_linkage_0__string_id' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.6|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.type' to 'igds_linkage_0__type' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.6|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_text_string{0}' to 'igds_text_string_0_' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:01|   2.7|  0.1|INFORM|Creating writer for format: Esri Geodatabase (File Geodb)
2025-05-15 20:04:01|   2.7|  0.0|INFORM|Trying to find a DYNAMIC plugin for writer named `GEODATABASE_FILE'
2025-05-15 20:04:01|   2.7|  0.0|INFORM|Loaded module 'GEODATABASE_FILE' from file 'C:\Program Files\FME\plugins/..\geodatabase9.dll'
2025-05-15 20:04:01|   2.7|  0.0|INFORM|FME API version of module '..\geodatabase9' matches current internal version (4.0 20230426)
2025-05-15 20:04:01|   2.7|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-15 20:04:01|   2.7|  0.0|INFORM|Writer `FeatureWriter_2_0' of type `GEODATABASE_FILE' using group definition keyword `FeatureWriter_2_0_DEF'
2025-05-15 20:04:03|   4.8|  2.1|INFORM|The ArcObjects license 'Advanced' is being selected from the ESRILicenseInfo ArcObjects call
2025-05-15 20:04:03|   5.1|  0.3|INFORM|FME has checked out an Esri license. The product checked out is 'Advanced'
2025-05-15 20:04:03|   5.1|  0.0|INFORM|Installed ArcGIS version is '3.1.6'
2025-05-15 20:04:03|   5.2|  0.1|INFORM|Created and connected to the ArcGIS 10.0 File Geodatabase at 'D:\GeoStream_Integration\fronted\backend\models\jjPBxG6ilZCjKq0ykiLCm3AOoaYkg8pDQQ65z1Qn\output\task_1747310634257_uy9jq0kem\point.gdb'
2025-05-15 20:04:03|   5.2|  0.0|INFORM|Fast deletes enabled
2025-05-15 20:04:03|   5.2|  0.0|INFORM|The 'HAS_Z_VALUES' keyword is set to 'AUTO_DETECT'. With the exception of MultiPatch feature classes, the dimensionality of new feature classes will be based on the first feature written to the feature class. (Features with no geometry are considered 2D)
2025-05-15 20:04:03|   5.2|  0.0|INFORM|A default z-value of '0' will be used for all 3D features where z-values are not provided
2025-05-15 20:04:03|   5.2|  0.0|INFORM|Esri Geodatabase Writer: Not simplifying geometries being written
2025-05-15 20:04:03|   5.2|  0.0|INFORM|Transactions are being used by the Esri Geodatabase Writer
2025-05-15 20:04:03|   5.2|  0.0|INFORM|Esri Geodatabase Writer: Creating feature type `point'
2025-05-15 20:04:03|   5.4|  0.2|INFORM|Esri Geodatabase Writer: The field 'OBJECTID' in feature type 'point' will not be updated since it is not editable
2025-05-15 20:04:03|   5.5|  0.1|INFORM|Creating writer for format: 
2025-05-15 20:04:03|   5.5|  0.0|INFORM|Using MultiWriter with keyword `FeatureWriter' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_color.blue' to 'igds_color_blue' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_color.green' to 'igds_color_green' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_color.red' to 'igds_color_red' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_fill_color.blue' to 'igds_fill_color_blue' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_fill_color.green' to 'igds_fill_color_green' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_fill_color.red' to 'igds_fill_color_red' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.blob' to 'igds_linkage_0__blob' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.blobsize' to 'igds_linkage_0__blobsize' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.flags' to 'igds_linkage_0__flags' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.modified' to 'igds_linkage_0__modified' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.readonly' to 'igds_linkage_0__readonly' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.type' to 'igds_linkage_0__type' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{0}.userId' to 'igds_linkage_0__userId' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{1}.flags' to 'igds_linkage_1__flags' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{1}.string' to 'igds_linkage_1__string' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{1}.string_id' to 'igds_linkage_1__string_id' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{1}.type' to 'igds_linkage_1__type' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{2}.blob' to 'igds_linkage_2__blob' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{2}.blobsize' to 'igds_linkage_2__blobsize' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{2}.flags' to 'igds_linkage_2__flags' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{2}.modified' to 'igds_linkage_2__modified' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{2}.readonly' to 'igds_linkage_2__readonly' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{2}.type' to 'igds_linkage_2__type' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{2}.userId' to 'igds_linkage_2__userId' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{3}.flags' to 'igds_linkage_3__flags' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{3}.string' to 'igds_linkage_3__string' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{3}.string_id' to 'igds_linkage_3__string_id' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{3}.type' to 'igds_linkage_3__type' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{4}.blob' to 'igds_linkage_4__blob' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{4}.blobsize' to 'igds_linkage_4__blobsize' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{4}.flags' to 'igds_linkage_4__flags' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{4}.modified' to 'igds_linkage_4__modified' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{4}.readonly' to 'igds_linkage_4__readonly' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{4}.type' to 'igds_linkage_4__type' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|WARN  |GEODATABASE_FILE Writer: Renamed user attribute 'igds_linkage{4}.userId' to 'igds_linkage_4__userId' based on format constraints for invalid characters, length, and case
2025-05-15 20:04:03|   5.5|  0.0|INFORM|Creating writer for format: Esri Geodatabase (File Geodb)
2025-05-15 20:04:03|   5.5|  0.0|INFORM|Trying to find a DYNAMIC plugin for writer named `GEODATABASE_FILE'
2025-05-15 20:04:03|   5.5|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-05-15 20:04:03|   5.5|  0.0|INFORM|Writer `FeatureWriter_0' of type `GEODATABASE_FILE' using group definition keyword `FeatureWriter_0_DEF'
2025-05-15 20:04:03|   5.5|  0.0|INFORM|An ArcGIS license is already checked out. The product checked out is 'Advanced'
2025-05-15 20:04:03|   5.5|  0.0|INFORM|Installed ArcGIS version is '3.1.6'
2025-05-15 20:04:03|   5.6|  0.1|INFORM|Created and connected to the ArcGIS 10.0 File Geodatabase at 'D:\GeoStream_Integration\fronted\backend\models\jjPBxG6ilZCjKq0ykiLCm3AOoaYkg8pDQQ65z1Qn\output\task_1747310634257_uy9jq0kem\line.gdb'
2025-05-15 20:04:03|   5.6|  0.0|INFORM|Fast deletes enabled
2025-05-15 20:04:03|   5.6|  0.0|INFORM|The 'HAS_Z_VALUES' keyword is set to 'AUTO_DETECT'. With the exception of MultiPatch feature classes, the dimensionality of new feature classes will be based on the first feature written to the feature class. (Features with no geometry are considered 2D)
2025-05-15 20:04:03|   5.6|  0.0|INFORM|A default z-value of '0' will be used for all 3D features where z-values are not provided
2025-05-15 20:04:03|   5.6|  0.0|INFORM|Esri Geodatabase Writer: Not simplifying geometries being written
2025-05-15 20:04:03|   5.6|  0.0|INFORM|Transactions are being used by the Esri Geodatabase Writer
2025-05-15 20:04:04|   5.6|  0.0|INFORM|Esri Geodatabase Writer: Creating feature type `line'
2025-05-15 20:04:04|   5.6|  0.0|INFORM|Esri Geodatabase Writer: The field 'OBJECTID' in feature type 'line' will not be updated since it is not editable
2025-05-15 20:04:04|   5.6|  0.0|INFORM|Esri Geodatabase Writer: The field 'SHAPE_Length' in feature type 'line' will not be updated since it is not editable
2025-05-15 20:04:05|   7.1|  1.5|INFORM|Transaction #1 was successfully committed
2025-05-15 20:04:07|   8.8|  1.7|INFORM|Transaction #1 was successfully committed
2025-05-15 20:04:07|   8.8|  0.0|INFORM|Transaction #2 was successfully committed
2025-05-15 20:04:07|   9.2|  0.4|INFORM|Transaction #3 was successfully committed
2025-05-15 20:04:08|   9.9|  0.7|INFORM|Transaction #4 was successfully committed
2025-05-15 20:04:09|  10.9|  1.0|INFORM|Transaction #5 was successfully committed
2025-05-15 20:04:13|  11.9|  1.0|INFORM|Transaction #6 was successfully committed
2025-05-15 20:04:13|  12.1|  0.2|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:13|  12.1|  0.0|WARN  |... Last line repeated 7 times ...
2025-05-15 20:04:13|  12.1|  0.0|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:13|  12.1|  0.0|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:13|  12.1|  0.0|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:14|  13.1|  1.0|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:14|  13.2|  0.1|WARN  |... Last line repeated 3 times ...
2025-05-15 20:04:14|  13.2|  0.0|INFORM|Transaction #2 was successfully committed
2025-05-15 20:04:14|  13.2|  0.0|INFORM|Transaction #7 was successfully committed
2025-05-15 20:04:15|  14.1|  0.9|INFORM|Transaction #8 was successfully committed
2025-05-15 20:04:16|  15.1|  1.0|INFORM|Transaction #9 was successfully committed
2025-05-15 20:04:17|  15.3|  0.2|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:17|  16.2|  0.9|INFORM|Transaction #10 was successfully committed
2025-05-15 20:04:17|  16.2|  0.0|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:18|  16.4|  0.2|WARN  |... Last line repeated 7 times ...
2025-05-15 20:04:18|  16.4|  0.0|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:18|  16.4|  0.0|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:18|  16.4|  0.0|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:18|  16.4|  0.0|WARN  |... Last line repeated 3 times ...
2025-05-15 20:04:18|  16.4|  0.0|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:18|  16.4|  0.0|WARN  |... Last line repeated 5 times ...
2025-05-15 20:04:18|  16.4|  0.0|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:18|  16.7|  0.3|INFORM|Transaction #3 was successfully committed
2025-05-15 20:04:19|  17.5|  0.8|INFORM|Transaction #11 was successfully committed
2025-05-15 20:04:20|  18.7|  1.2|INFORM|Transaction #12 was successfully committed
2025-05-15 20:04:21|  19.5|  0.8|INFORM|Transaction #4 was successfully committed
2025-05-15 20:04:21|  19.8|  0.3|INFORM|Transaction #13 was successfully committed
2025-05-15 20:04:22|  20.9|  1.1|INFORM|Transaction #14 was successfully committed
2025-05-15 20:04:23|  21.3|  0.4|INFORM|Transaction #15 was successfully committed
2025-05-15 20:04:23|  21.7|  0.4|INFORM|Transaction #16 was successfully committed
2025-05-15 20:04:24|  22.4|  0.7|INFORM|Transaction #17 was successfully committed
2025-05-15 20:04:25|  23.3|  0.9|INFORM|Transaction #18 was successfully committed
2025-05-15 20:04:28|  24.3|  1.0|INFORM|Transaction #19 was successfully committed
2025-05-15 20:04:29|  24.9|  0.6|INFORM|Transaction #20 was successfully committed
2025-05-15 20:04:30|  25.7|  0.8|INFORM|Transaction #21 was successfully committed
2025-05-15 20:04:31|  26.5|  0.8|INFORM|Transaction #22 was successfully committed
2025-05-15 20:04:31|  26.6|  0.1|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:31|  27.0|  0.4|INFORM|Transaction #5 was successfully committed
2025-05-15 20:04:33|  28.4|  1.4|INFORM|Transaction #6 was successfully committed
2025-05-15 20:04:33|  29.0|  0.6|INFORM|Transaction #23 was successfully committed
2025-05-15 20:04:34|  30.1|  1.1|INFORM|Transaction #24 was successfully committed
2025-05-15 20:04:35|  30.9|  0.8|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:35|  31.0|  0.1|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:35|  31.0|  0.0|INFORM|Transaction #25 was successfully committed
2025-05-15 20:04:36|  31.4|  0.4|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:36|  31.5|  0.1|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:36|  31.5|  0.0|INFORM|Transaction #7 was successfully committed
2025-05-15 20:04:36|  32.0|  0.5|INFORM|Transaction #26 was successfully committed
2025-05-15 20:04:37|  32.5|  0.5|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:37|  32.9|  0.4|INFORM|Transaction #27 was successfully committed
2025-05-15 20:04:38|  33.8|  0.9|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:38|  34.1|  0.3|WARN  |... Last line repeated 17 times ...
2025-05-15 20:04:38|  34.1|  0.0|INFORM|Transaction #28 was successfully committed
2025-05-15 20:04:39|  34.6|  0.5|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:39|  34.9|  0.3|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:39|  34.9|  0.0|INFORM|Transaction #8 was successfully committed
2025-05-15 20:04:39|  35.2|  0.3|INFORM|Transaction #29 was successfully committed
2025-05-15 20:04:43|  36.1|  0.9|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:43|  36.5|  0.4|INFORM|Transaction #30 was successfully committed
2025-05-15 20:04:44|  37.5|  1.0|INFORM|Transaction #31 was successfully committed
2025-05-15 20:04:45|  38.2|  0.7|INFORM|Transaction #9 was successfully committed
2025-05-15 20:04:45|  38.4|  0.2|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:45|  38.5|  0.1|INFORM|Transaction #32 was successfully committed
2025-05-15 20:04:46|  39.2|  0.7|INFORM|Transaction #33 was successfully committed
2025-05-15 20:04:47|  40.0|  0.8|WARN  |DESIGN READER: Found element with spline geometry which has invalid knots. Knots regenerated for spline interpolation
2025-05-15 20:04:47|  40.1|  0.1|WARN  |... Last line repeated 2 times ...
2025-05-15 20:04:47|  40.1|  0.0|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:47|  40.2|  0.1|INFORM|Transaction #34 was successfully committed
2025-05-15 20:04:48|  41.0|  0.8|INFORM|Transaction #35 was successfully committed
2025-05-15 20:04:49|  41.8|  0.8|INFORM|Transaction #10 was successfully committed
2025-05-15 20:04:49|  42.0|  0.2|INFORM|Transaction #36 was successfully committed
2025-05-15 20:04:49|  42.6|  0.6|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:50|  42.9|  0.3|INFORM|Transaction #37 was successfully committed
2025-05-15 20:04:51|  43.8|  0.9|INFORM|Transaction #38 was successfully committed
2025-05-15 20:04:52|  44.8|  1.0|INFORM|Transaction #11 was successfully committed
2025-05-15 20:04:52|  44.8|  0.0|INFORM|Transaction #39 was successfully committed
2025-05-15 20:04:53|  45.8|  1.0|INFORM|Transaction #40 was successfully committed
2025-05-15 20:04:54|  46.9|  1.1|INFORM|Transaction #41 was successfully committed
2025-05-15 20:04:55|  47.8|  0.9|INFORM|Transaction #12 was successfully committed
2025-05-15 20:04:55|  47.8|  0.0|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:55|  47.9|  0.1|WARN  |DESIGN READER: Failed to read element with spline geometry. Skipping element
2025-05-15 20:04:55|  47.9|  0.0|INFORM|Transaction #42 was successfully committed
2025-05-15 20:04:59|  49.2|  1.3|INFORM|Transaction #43 was successfully committed
2025-05-15 20:05:00|  50.4|  1.2|INFORM|Transaction #44 was successfully committed
2025-05-15 20:05:00|  50.7|  0.3|INFORM|Transaction #13 was successfully committed
2025-05-15 20:05:01|  51.1|  0.4|INFORM|Transaction #45 was successfully committed
2025-05-15 20:05:01|  51.6|  0.5|INFORM|Transaction #46 was successfully committed
2025-05-15 20:05:01|  52.0|  0.4|INFORM|Transaction #47 was successfully committed
2025-05-15 20:05:02|  52.2|  0.2|INFORM|Transaction #48 was successfully committed
2025-05-15 20:05:03|  52.7|  0.5|INFORM|Transaction #49 was successfully committed
2025-05-15 20:05:03|  53.1|  0.4|INFORM|Transaction #50 was successfully committed
2025-05-15 20:05:04|  53.5|  0.4|INFORM|Transaction #51 was successfully committed
2025-05-15 20:05:04|  53.7|  0.2|INFORM|Transaction #52 was successfully committed
2025-05-15 20:05:04|  54.1|  0.4|INFORM|Transaction #53 was successfully committed
2025-05-15 20:05:05|  54.5|  0.4|INFORM|Transaction #54 was successfully committed
2025-05-15 20:05:05|  54.8|  0.3|INFORM|Transaction #55 was successfully committed
2025-05-15 20:05:05|  55.1|  0.3|INFORM|Transaction #56 was successfully committed
2025-05-15 20:05:06|  55.3|  0.2|STATS |Creator_XML_Creator (CreationFactory): Created 1 features
2025-05-15 20:05:06|  55.3|  0.0|STATS |Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |Creator_CREATED Brancher -1 18 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-05-15 20:05:06|  55.3|  0.0|STATS |_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|INFORM|Path Reader: Closing the PATH Reader
2025-05-15 20:05:06|  55.3|  0.0|STATS |Tester_2 (TestFactory): Tested 1 input feature(s) -- 1 feature(s) passed and 0 feature(s) failed
2025-05-15 20:05:06|  55.3|  0.0|INFORM|DESIGN READER: Closing DGN V8 file
2025-05-15 20:05:06|  55.3|  0.0|STATS |AttributeExposer (TeeFactory): Cloned 69749 input feature(s) into 69749 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |Tester (TestFactory): Tested 69749 input feature(s) -- 69749 feature(s) passed and 0 feature(s) failed
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeometryFilter Null Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeometryFilter Surface Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeometryFilter Solid Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeometryFilter Raster Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeometryFilter PointCloud Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeometryFilter VoxelGrid Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeodatabaseRelationshipFeaturesPipeline::GeodatabaseRelationshipFeatures (SortFactory): Finished sorting a total of 0 features.
2025-05-15 20:05:06|  55.3|  0.0|INFORM|Transaction #14 (final transaction) was successfully committed
2025-05-15 20:05:06|  55.3|  0.0|INFORM|Closing the Esri Geodatabase writer
2025-05-15 20:05:06|  55.3|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.3|  0.0|INFORM|Feature output statistics for `GEODATABASE_FILE' writer using keyword `FeatureWriter_2_0':
2025-05-15 20:05:06|  55.3|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.3|  0.0|STATS |                               Features Written
2025-05-15 20:05:06|  55.3|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.3|  0.0|STATS |point                                                                    13081
2025-05-15 20:05:06|  55.3|  0.0|STATS |==============================================================================
2025-05-15 20:05:06|  55.3|  0.0|STATS |Total Features Written                                                   13081
2025-05-15 20:05:06|  55.3|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.3|  0.0|STATS |Tester_3 (TestFactory): Tested 56668 input feature(s) -- 56668 feature(s) passed and 0 feature(s) failed
2025-05-15 20:05:06|  55.3|  0.0|STATS |GeodatabaseRelationshipFeaturesPipeline::GeodatabaseRelationshipFeatures (SortFactory): Finished sorting a total of 0 features.
2025-05-15 20:05:06|  55.3|  0.0|INFORM|Transaction #57 (final transaction) was successfully committed
2025-05-15 20:05:06|  55.4|  0.1|INFORM|Closing the Esri Geodatabase writer
2025-05-15 20:05:06|  55.4|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|INFORM|Feature output statistics for `GEODATABASE_FILE' writer using keyword `FeatureWriter_0':
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |                               Features Written
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |line                                                                     56668
2025-05-15 20:05:06|  55.4|  0.0|STATS |==============================================================================
2025-05-15 20:05:06|  55.4|  0.0|STATS |Total Features Written                                                   56668
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |Destination Feature Type Routing Correlator (RoutingFactory): Tested 0 input feature(s), wrote 0 output feature(s): 0 matched merge filters, 0 were routed to output, 0 could not be routed.
2025-05-15 20:05:06|  55.4|  0.0|STATS |Final Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |                            Features Read Summary
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |==============================================================================
2025-05-15 20:05:06|  55.4|  0.0|STATS |Total Features Read                                                          0
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |                           Features Written Summary
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|STATS |==============================================================================
2025-05-15 20:05:06|  55.4|  0.0|STATS |Total Features Written                                                       0
2025-05-15 20:05:06|  55.4|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-05-15 20:05:06|  55.4|  0.0|INFORM|Translation was SUCCESSFUL with 115 warning(s) (0 feature(s) output)
2025-05-15 20:05:06|  55.4|  0.0|INFORM|FME Session Duration: 1 minute 7.9 seconds. (CPU: 51.3s user, 4.1s system)
2025-05-15 20:05:06|  55.4|  0.0|INFORM|END - ProcessID: 9920, peak process memory usage: 328084 kB, current process memory usage: 183720 kB
