{"version": 3, "file": "hy-am.min.mjs", "sources": ["../../../../packages/locale/lang/hy-am.ts"], "sourcesContent": ["export default {\n  name: 'hy-am',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Լաւ',\n      clear: 'Մաքրել',\n    },\n    datepicker: {\n      now: 'Հիմա',\n      today: 'Այսօր',\n      cancel: 'Չեղարկել',\n      clear: 'Մաքրել',\n      confirm: 'Լաւ',\n      selectDate: 'Ընտրեք ամսաթիւը',\n      selectTime: 'Ընտրեք ժամանակը',\n      startDate: 'Սկզբ. ամսաթիւը',\n      startTime: 'Սկզբ. ժամանակը',\n      endDate: 'Վերջ. ամսաթիվը',\n      endTime: 'Վերջ. ժամանակը',\n      prevYear: 'Նախորդ տարի',\n      nextYear: 'Յաջորդ տարի',\n      prevMonth: 'Նախորդ ամիս',\n      nextMonth: 'Յաջորդ ամիս',\n      year: 'Տարի',\n      month1: 'Յունուար',\n      month2: 'Փետրուար',\n      month3: 'Մարտ',\n      month4: 'Ապրիլ',\n      month5: 'Մայիս',\n      month6: 'Յունիս',\n      month7: 'Յուլիս',\n      month8: 'Օգոստոս',\n      month9: 'Սեպտեմբեր',\n      month10: 'Յոկտեմբեր',\n      month11: 'Նոյեմբեր',\n      month12: 'Դեկտեմբեր',\n      week: 'Շաբաթ',\n      weeks: {\n        sun: 'Կիր',\n        mon: 'Երկ',\n        tue: 'Եր',\n        wed: 'Չոր',\n        thu: 'Հինգ',\n        fri: 'Ուրբ',\n        sat: 'Շաբ',\n      },\n      months: {\n        jan: 'Յունվ',\n        feb: 'Փետ',\n        mar: 'Մար',\n        apr: 'Ապր',\n        may: 'Մայ',\n        jun: 'Յուն',\n        jul: 'Յուլ',\n        aug: 'Օգ',\n        sep: 'Սեպտ',\n        oct: 'Յոկ',\n        nov: 'Նոյ',\n        dec: 'Դեկ',\n      },\n    },\n    select: {\n      loading: 'Բեռնում',\n      noMatch: 'Համապատասխան տուեալներ չկան',\n      noData: 'Տվյալներ չկան',\n      placeholder: 'Ընտրել',\n    },\n    mention: {\n      loading: 'Բեռնում',\n    },\n    cascader: {\n      noMatch: 'Համապատասխան տուեալներ չկան',\n      loading: 'Բեռնում',\n      placeholder: 'Ընտրել',\n      noData: 'Տվյալներ չկան',\n    },\n    pagination: {\n      goto: 'Անցնել',\n      pagesize: ' էջում',\n      total: 'Ընդամենը {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Հաղորդագրութիւն',\n      confirm: 'Լաւ',\n      cancel: 'Չեղարկել',\n      error: 'Անվաւեր տուեալների մուտք',\n    },\n    upload: {\n      deleteTip: 'Սեղմեք [Ջնջել] ջնջելու համար',\n      delete: 'Ջնջել',\n      preview: 'Նախադիտում',\n      continue: 'Շարունակել',\n    },\n    table: {\n      emptyText: 'Տուեալներ չկան',\n      confirmFilter: 'Յաստատել',\n      resetFilter: 'Վերագործարկել',\n      clearFilter: 'Բոլորը',\n      sumText: 'Գումարը',\n    },\n    tree: {\n      emptyText: 'Տուեալներ չկան',\n    },\n    transfer: {\n      noMatch: 'Համապատասխան տուեալներ չկան',\n      noData: 'Տուեալներ չկան',\n      titles: ['Ցուցակ 1', 'Ցուցակ 2'],\n      filterPlaceholder: 'Մուտքագրեք բանալի բառ',\n      noCheckedFormat: '{total} միաւոր',\n      hasCheckedFormat: '{checked}/{total} ընտրուած է',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,WAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,kDAAkD,CAAC,KAAK,CAAC,sCAAsC,CAAC,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC,uFAAuF,CAAC,UAAU,CAAC,uFAAuF,CAAC,SAAS,CAAC,4EAA4E,CAAC,SAAS,CAAC,4EAA4E,CAAC,OAAO,CAAC,4EAA4E,CAAC,OAAO,CAAC,4EAA4E,CAAC,QAAQ,CAAC,+DAA+D,CAAC,QAAQ,CAAC,+DAA+D,CAAC,SAAS,CAAC,+DAA+D,CAAC,SAAS,CAAC,+DAA+D,CAAC,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,wDAAwD,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,wDAAwD,CAAC,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,0JAA0J,CAAC,MAAM,CAAC,2EAA2E,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0JAA0J,CAAC,OAAO,CAAC,4CAA4C,CAAC,WAAW,CAAC,sCAAsC,CAAC,MAAM,CAAC,2EAA2E,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,QAAQ,CAAC,iCAAiC,CAAC,KAAK,CAAC,0DAA0D,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,4FAA4F,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,kDAAkD,CAAC,KAAK,CAAC,wIAAwI,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iJAAiJ,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,8DAA8D,CAAC,QAAQ,CAAC,8DAA8D,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iFAAiF,CAAC,aAAa,CAAC,kDAAkD,CAAC,WAAW,CAAC,gFAAgF,CAAC,WAAW,CAAC,sCAAsC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iFAAiF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0JAA0J,CAAC,MAAM,CAAC,iFAAiF,CAAC,MAAM,CAAC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,CAAC,iBAAiB,CAAC,sHAAsH,CAAC,eAAe,CAAC,8CAA8C,CAAC,gBAAgB,CAAC,2EAA2E,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}