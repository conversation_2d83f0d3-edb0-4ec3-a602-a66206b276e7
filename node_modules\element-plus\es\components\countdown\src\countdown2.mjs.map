{"version": 3, "file": "countdown2.mjs", "sources": ["../../../../../../packages/components/countdown/src/countdown.vue"], "sourcesContent": ["<template>\n  <el-statistic\n    :value=\"rawValue\"\n    :title=\"title\"\n    :prefix=\"prefix\"\n    :suffix=\"suffix\"\n    :value-style=\"valueStyle\"\n    :formatter=\"formatter\"\n  >\n    <template v-for=\"(_, name) in $slots\" #[name]>\n      <slot :name=\"name\" />\n    </template>\n  </el-statistic>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'\nimport { ElStatistic } from '@element-plus/components/statistic'\nimport { cAF, rAF } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { countdownEmits, countdownProps } from './countdown'\nimport { formatTime, getTime } from './utils'\n\ndefineOptions({\n  name: 'ElCountdown',\n})\n\nconst props = defineProps(countdownProps)\nconst emit = defineEmits(countdownEmits)\n\nlet timer: ReturnType<typeof rAF> | undefined\nconst rawValue = ref<number>(0)\nconst displayValue = computed(() => formatTime(rawValue.value, props.format))\n\nconst formatter = (val: number) => formatTime(val, props.format)\n\nconst stopTimer = () => {\n  if (timer) {\n    cAF(timer)\n    timer = undefined\n  }\n}\n\nconst startTimer = () => {\n  const timestamp = getTime(props.value)\n  const frameFunc = () => {\n    let diff = timestamp - Date.now()\n    emit(CHANGE_EVENT, diff)\n    if (diff <= 0) {\n      diff = 0\n      stopTimer()\n      emit('finish')\n    } else {\n      timer = rAF(frameFunc)\n    }\n    rawValue.value = diff\n  }\n  timer = rAF(frameFunc)\n}\n\nonMounted(() => {\n  rawValue.value = getTime(props.value) - Date.now()\n\n  watch(\n    () => [props.value, props.format],\n    () => {\n      stopTimer()\n      startTimer()\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nonBeforeUnmount(() => {\n  stopTimer()\n})\n\ndefineExpose({\n  /**\n   * @description current display value\n   */\n  displayValue,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref"], "mappings": ";;;;;;;;mCAuBc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAI,IAAA,KAAA,CAAA;AACJ,IAAM,MAAA,QAAA,GAAW,IAAY,CAAC,CAAA,CAAA;AAC9B,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM,UAAA,CAAW,SAAS,KAAO,EAAA,KAAA,CAAM,MAAM,CAAC,CAAA,CAAA;AAE5E,IAAA,MAAM,YAAY,CAAC,GAAA,KAAgB,UAAW,CAAA,GAAA,EAAK,MAAM,MAAM,CAAA,CAAA;AAE/D,IAAA,MAAM,YAAY,MAAM;AACtB,MAAA,IAAI,KAAO,EAAA;AACT,QAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AACT,QAAQ,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,OACV;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,aAAa,MAAM;AACvB,MAAM,MAAA,SAAA,GAAY,OAAQ,CAAA,KAAA,CAAM,KAAK,CAAA,CAAA;AACrC,MAAA,MAAM,YAAY,MAAM;AACtB,QAAI,IAAA,IAAA,GAAO,SAAY,GAAA,IAAA,CAAK,GAAI,EAAA,CAAA;AAChC,QAAA,IAAA,CAAK,cAAc,IAAI,CAAA,CAAA;AACvB,QAAA,IAAI,QAAQ,CAAG,EAAA;AACb,UAAO,IAAA,GAAA,CAAA,CAAA;AACP,UAAU,SAAA,EAAA,CAAA;AACV,UAAA,IAAA,CAAK,QAAQ,CAAA,CAAA;AAAA,SACR,MAAA;AACL,UAAA,KAAA,GAAQ,IAAI,SAAS,CAAA,CAAA;AAAA,SACvB;AACA,QAAA,QAAA,CAAS,KAAQ,GAAA,IAAA,CAAA;AAAA,OACnB,CAAA;AACA,MAAA,KAAA,GAAQ,IAAI,SAAS,CAAA,CAAA;AAAA,KACvB,CAAA;AAEA,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,QAAA,CAAS,QAAQ,OAAQ,CAAA,KAAA,CAAM,KAAK,CAAA,GAAI,KAAK,GAAI,EAAA,CAAA;AAEjD,MAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,KAAA,EAAA,KAAA,CAAA,MAAA,CAAA,EAAA,MAAA;AAAA,QACE,SAAa,EAAA,CAAA;AAAmB,QAChC,UAAM,EAAA,CAAA;AACJ,OAAU,EAAA;AACV,QAAW,SAAA,EAAA,IAAA;AAAA,OACb,CAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IAAA,eACa,CAAA,MAAA;AAAA,MACb,SAAA,EAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,IACF,MAAC,CAAA;AAED,MAAA,YAAA;AACE,KAAU,CAAA,CAAA;AAAA,IACZ,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;AAED,MAAa,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,WAAA,CAAA,EAAA;AAAA,QAAA,KAAA,EAAA,QAAA,CAAA,KAAA;AAAA,QAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,QAAA,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,QAIX,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,QACD,aAAA,EAAA,IAAA,CAAA,UAAA;;;;;;;;;;;;;;;;;;;;;"}