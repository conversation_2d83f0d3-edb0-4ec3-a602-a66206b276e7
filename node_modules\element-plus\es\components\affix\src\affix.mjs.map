{"version": 3, "file": "affix.mjs", "sources": ["../../../../../../packages/components/affix/src/affix.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isBoolean,\n  isNumber,\n} from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type { ZIndexProperty } from 'csstype'\nimport type Affix from './affix.vue'\n\nexport const affixProps = buildProps({\n  /**\n   * @description affix element zIndex value\n   * */\n  zIndex: {\n    type: definePropType<ZIndexProperty>([Number, String]),\n    default: 100,\n  },\n  /**\n   * @description target container. (CSS selector)\n   */\n  target: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description offset distance\n   * */\n  offset: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description position of affix\n   * */\n  position: {\n    type: String,\n    values: ['top', 'bottom'],\n    default: 'top',\n  },\n} as const)\nexport type AffixProps = ExtractPropTypes<typeof affixProps>\n\nexport const affixEmits = {\n  scroll: ({ scrollTop, fixed }: { scrollTop: number; fixed: boolean }) =>\n    isNumber(scrollTop) && isBoolean(fixed),\n  [CHANGE_EVENT]: (fixed: boolean) => isBoolean(fixed),\n}\nexport type AffixEmits = typeof affixEmits\n\nexport type AffixInstance = InstanceType<typeof Affix> & unknown\n"], "names": [], "mappings": ";;;;AAOY,MAAC,UAAU,GAAG,UAAU,CAAC;AACrC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC7B,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,UAAU,GAAG;AAC1B,EAAE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC;AAC3E,EAAE,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;AAC7C;;;;"}