#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2024\9\0927\to_ytt\提取、修正中心线工具.fmw
#          --file_2 "$(FME_MF_DIR)测试\梁溪河测线点.shp"
#          --file "$(FME_MF_DIR)测试\梁溪河边缘测深线.shp"
#          --MarkerInterval "10"
#          --TOLERANCE "5"
#          --dir "$(FME_MF_DIR)成果"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="26083 6029.3"
#!   DOC_TOP_LEFT="-1057.58 -6133.05"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="cf7593ff-f86a-4f75-b2df-1452978c785d"
#!   FME_DOCUMENT_PRIORGUID="c3a89f51-a539-4900-b969-3160ae576154,279756ce-6699-4a07-80e0-d81e94efda16,********-0b68-49e3-a78a-eae9eba3c055,356b4e52-9fc3-4cfd-b1fe-4301664d8ae5,924251a9-5218-49bf-9ae7-95c083f37bac,*************-40cb-bae6-fe01c42c2128"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2024-09-30T16:35:46"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="NO"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="20326 -4660.95"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="94"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader"
#!   DATASET="FeatureReader/梁溪河边缘测深线.shp"
#! />
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="FeatureReader_2/梁溪河测线点.shp"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/成果"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file_2 INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 点"
#!   DEFAULT_VALUE="$(FME_MF_DIR)测试\梁溪河测线点.shp"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 面"
#!   DEFAULT_VALUE="$(FME_MF_DIR)测试\梁溪河边缘测深线.shp"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL STRING_OR_ATTR MarkerInterval 中线折点间隔（米）"
#!   DEFAULT_VALUE="10"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI FLOAT TOLERANCE 匹配高程点范围阈值（米）"
#!   DEFAULT_VALUE="5"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR dir 导出成果目录"
#!   DEFAULT_VALUE="$(FME_MF_DIR)成果"
#!   IS_STAND_ALONE="false"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="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"
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="file_2" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)测试\梁溪河测线点.shp"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file_2 INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 点"
#! />
#!     <INFO NAME="file" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)测试\梁溪河边缘测深线.shp"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 面"
#! />
#!     <INFO NAME="MarkerInterval" 
#!   DEFAULT_VALUE="10"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL STRING_OR_ATTR MarkerInterval 中线折点间隔（米）"
#! />
#!     <INFO NAME="TOLERANCE" 
#!   DEFAULT_VALUE="5"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI FLOAT TOLERANCE 匹配高程点范围阈值（米）"
#! />
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)成果"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR dir 导出成果目录"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! <BOOKMARK
#!   IDENTIFIER="74"
#!   NAME="平滑中线"
#!   DESCRIPTION=""
#!   TOP_LEFT="2675.0272502725024 -852.50962509625037"
#!   ORDER="500000000000037"
#!   PALETTE_COLOR="Color3"
#!   BOTTOM_RIGHT="3205.0272502725024 -1248.5096250962504"
#!   BOUNDING_RECT="2675.0272502725024 -852.50962509625037 530 396"
#!   STICKY="true"
#!   COLOUR="1,0.85490196078431369,0.59999999999999998,1"
#!   CONTENTS="72 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="75"
#!   NAME="提取中线"
#!   DESCRIPTION=""
#!   TOP_LEFT="1859.2779434754052 -583.13253132531327"
#!   ORDER="500000000000038"
#!   PALETTE_COLOR="Color4"
#!   BOTTOM_RIGHT="2451.2779434754052 -919.13253132531327"
#!   BOUNDING_RECT="1859.2779434754052 -583.13253132531327 592 336"
#!   STICKY="true"
#!   COLOUR="0.85098039215686272,0.92941176470588238,0.60392156862745094,1"
#!   CONTENTS="11 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="77"
#!   NAME="投影坐标系"
#!   DESCRIPTION=""
#!   TOP_LEFT="1171.5077873855664 -583.13253132531327"
#!   ORDER="500000000000039"
#!   PALETTE_COLOR="Color5"
#!   BOTTOM_RIGHT="1763.5077873855664 -859.13253132531327"
#!   BOUNDING_RECT="1171.5077873855664 -583.13253132531327 592 276"
#!   STICKY="true"
#!   COLOUR="0.59607843137254901,0.91764705882352937,0.72549019607843135,1"
#!   CONTENTS="7 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="135"
#!   NAME="中心线折点"
#!   DESCRIPTION=""
#!   TOP_LEFT="6805.0977771316166 -613.13253132531315"
#!   ORDER="500000000000094"
#!   PALETTE_COLOR="Color3"
#!   BOTTOM_RIGHT="7397.0977771316166 -889.13253132531315"
#!   BOUNDING_RECT="6805.0977771316166 -613.13253132531315 592 276"
#!   STICKY="true"
#!   COLOUR="1,0.85490196078431369,0.59999999999999998,1"
#!   CONTENTS="42 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="136"
#!   NAME="阈值内点"
#!   DESCRIPTION=""
#!   TOP_LEFT="7527.2488382383171 -958.14827305782251"
#!   ORDER="500000000000095"
#!   PALETTE_COLOR="Color4"
#!   BOTTOM_RIGHT="8149.2499064960466 -1354.1482730578225"
#!   BOUNDING_RECT="7527.2488382383171 -958.14827305782251 622.00106825772946 396"
#!   STICKY="true"
#!   COLOUR="0.85098039215686272,0.92941176470588238,0.60392156862745094,1"
#!   CONTENTS="25 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="154"
#!   NAME="提取交叉点，计算交叉点高程值"
#!   DESCRIPTION=""
#!   TOP_LEFT="16191.874375265485 -1989.4775165142951"
#!   ORDER="500000000000107"
#!   PALETTE_COLOR="Color5"
#!   BOTTOM_RIGHT="20572.080720807207 -2505.4775165142951"
#!   BOUNDING_RECT="16191.874375265485 -1989.4775165142951 4380.2063455417228 516"
#!   STICKY="true"
#!   COLOUR="0.59607843137254901,0.91764705882352937,0.72549019607843135,1"
#!   CONTENTS="193 153 228 230 182 138 192 229 181 180 191 187 125 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="155"
#!   NAME="去除头尾段"
#!   DESCRIPTION=""
#!   TOP_LEFT="18546.721200405773 -2834.0247054644456"
#!   ORDER="500000000000114"
#!   PALETTE_COLOR="Color6"
#!   BOTTOM_RIGHT="19138.721200405773 -3170.0247054644456"
#!   BOUNDING_RECT="18546.721200405773 -2834.0247054644456 592 336"
#!   STICKY="true"
#!   COLOUR="0.59215686274509804,0.8901960784313725,0.90980392156862744,1"
#!   CONTENTS="158 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="225"
#!   NAME="去除头尾段"
#!   DESCRIPTION=""
#!   TOP_LEFT="18586.278943040659 -4503.0827797679049"
#!   ORDER="500000000000114"
#!   PALETTE_COLOR="Color6"
#!   BOTTOM_RIGHT="19178.278943040659 -4839.0827797679049"
#!   BOUNDING_RECT="18586.278943040659 -4503.0827797679049 592 336"
#!   STICKY="true"
#!   COLOUR="0.59215686274509804,0.8901960784313725,0.90980392156862744,1"
#!   CONTENTS="206 "
#! >
#! </BOOKMARK>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-1057.5826912115272 -672.00191232681539"
#!   BOUNDING_RECT="-1057.5826912115272 -672.00191232681539 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-166.79997386054433 -582.62032894138463"
#!   BOUNDING_RECT="-166.79997386054433 -582.62032894138463 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(file)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u628a&gt;&lt;u6d4b&gt;&lt;u91cf&gt;&lt;u4f5c&gt;&lt;u4e3a&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;&lt;u7a7a&gt;&lt;u767d&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u8bfb&gt;&lt;u4f5c&gt;&lt;uff1a&gt;,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u4fee&gt;&lt;u526a&gt;&lt;u524d&gt;&lt;u90e8&gt;&lt;u7684&gt;&lt;u7a7a&gt;&lt;u683c&gt;,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u641c&gt;&lt;u7d22&gt;&lt;u8303&gt;&lt;u56f4&gt;,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;&lt;u6570&gt;&lt;u503c&gt;&lt;u578b&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;u5904&gt;&lt;u7406&gt;,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;&lt;u5b57&gt;&lt;u7b26&gt;&lt;u7f16&gt;&lt;u7801&gt;,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u62a5&gt;&lt;u544a&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u5f02&gt;&lt;u5e38&gt;,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;&lt;u73af&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u53d1&gt;&lt;u73b0&gt;,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-166.79997386054433 -1199.9843379386175"
#!   BOUNDING_RECT="-166.79997386054433 -1199.9843379386175 457.00106825772946 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(file_2)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u628a&gt;&lt;u6d4b&gt;&lt;u91cf&gt;&lt;u4f5c&gt;&lt;u4e3a&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;&lt;u7a7a&gt;&lt;u767d&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u8bfb&gt;&lt;u4f5c&gt;&lt;uff1a&gt;,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u4fee&gt;&lt;u526a&gt;&lt;u524d&gt;&lt;u90e8&gt;&lt;u7684&gt;&lt;u7a7a&gt;&lt;u683c&gt;,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u641c&gt;&lt;u7d22&gt;&lt;u8303&gt;&lt;u56f4&gt;,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;&lt;u6570&gt;&lt;u503c&gt;&lt;u578b&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;u5904&gt;&lt;u7406&gt;,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;&lt;u5b57&gt;&lt;u7b26&gt;&lt;u7f16&gt;&lt;u7801&gt;,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u62a5&gt;&lt;u544a&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u5f02&gt;&lt;u5e38&gt;,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;&lt;u73af&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u53d1&gt;&lt;u73b0&gt;,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="1259.5077873855664 -693.13253132531327"
#!   BOUNDING_RECT="1259.5077873855664 -693.13253132531327 454 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="1259.5077873855664 -1379.9843379386175"
#!   BOUNDING_RECT="1259.5077873855664 -1379.9843379386175 454 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="11"
#!   TYPE="CenterlineReplacer"
#!   VERSION="6"
#!   POSITION="1947.2779434754052 -693.13253132531327"
#!   BOUNDING_RECT="1947.2779434754052 -693.13253132531327 454 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CENTERLINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="APPROX_CENTER_LINE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="CenterlineReplacer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="16"
#!   TYPE="Chopper"
#!   VERSION="8"
#!   POSITION="2725.0272502725024 -103.75193751937513"
#!   BOUNDING_RECT="2725.0272502725024 -103.75193751937513 454 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CHOPPED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNTOUCHED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="APPROX_LENGTH" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Deaggregate"/>
#!     <XFORM_PARM PARM_NAME="GRID_AREAS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_VERTICES" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="By Vertex"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="REMNANT_ATTR" PARM_VALUE="_remnant"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Chopper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="21"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="2593.43825123233 -1409.9843379386175"
#!   BOUNDING_RECT="2593.43825123233 -1409.9843379386175 454 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; point_&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt; SET_TO &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt; buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="23"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="3477.7248948701731 -898.13428134281332"
#!   BOUNDING_RECT="3477.7248948701731 -898.13428134281332 454 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; point_&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt; SET_TO &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt; buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="25"
#!   TYPE="PointOnPointOverlayer"
#!   VERSION="5"
#!   POSITION="7577.2488382383171 -1068.1482730578225"
#!   BOUNDING_RECT="7577.2488382383171 -1068.1482730578225 522.00106825772946 71"
#!   ORDER="500000000000012"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP1" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_SUPER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt; &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y point_&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt; point_&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt; &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;z &lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="Selected Attributes"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="点"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OV_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOLERANCE" PARM_VALUE="$(TOLERANCE)"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="PointOnPointOverlayer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="28"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="1947.2779434754052 -1379.9843379386175"
#!   BOUNDING_RECT="1947.2779434754052 -1379.9843379386175 454 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="31"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="4157.0557116316158 -723.13253132531315"
#!   BOUNDING_RECT="4157.0557116316158 -723.13253132531315 454 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="35"
#!   TYPE="CoordinateExtractor"
#!   VERSION="4"
#!   POSITION="3215.3194700445183 -1349.9843379386175"
#!   BOUNDING_RECT="3215.3194700445183 -1349.9843379386175 469.00106825772946 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内xy{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内xy{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内xy{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ALL_ATTRIBUTES_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="IND" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTRIBUTES_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="阈值内xy"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="All Coordinates"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="CoordinateExtractor"/>
#!     <XFORM_PARM PARM_NAME="X_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Y_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_DEFAULT" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="38"
#!   TYPE="CoordinateExtractor"
#!   VERSION="4"
#!   POSITION="4792.6085795783674 -693.13253132531315"
#!   BOUNDING_RECT="4792.6085795783674 -693.13253132531315 469.00106825772946 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点xy{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点xy{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点xy{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ALL_ATTRIBUTES_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="IND" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTRIBUTES_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="中心线折点xy"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="All Coordinates"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="CoordinateExtractor_2"/>
#!     <XFORM_PARM PARM_NAME="X_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Y_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_DEFAULT" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="33"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="5546.9304693046934 -693.13253132531315"
#!   BOUNDING_RECT="5546.9304693046934 -693.13253132531315 454 71"
#!   ORDER="500000000000016"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="中心线折点xy{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="40"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="6229.02863644021 -723.13253132531315"
#!   BOUNDING_RECT="6229.02863644021 -723.13253132531315 454 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x SET_TO &lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt; real64  &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y SET_TO &lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt; real64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="42"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="6893.0977771316166 -723.13253132531315"
#!   BOUNDING_RECT="6893.0977771316166 -723.13253132531315 454 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y,point_&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;,&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="44"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="4041.5191114728605 -1349.9843379386175"
#!   BOUNDING_RECT="4041.5191114728605 -1349.9843379386175 454 71"
#!   ORDER="500000000000016"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="阈值内xy{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="47"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="4891.8881920867443 -1379.9843379386175"
#!   BOUNDING_RECT="4891.8881920867443 -1379.9843379386175 454 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x SET_TO &lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt; real64  &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y SET_TO &lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt; real64  &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;z SET_TO &lt;at&gt;Value&lt;openparen&gt;z&lt;closeparen&gt; real64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="48"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="5661.1266537021265 -1379.9843379386175"
#!   BOUNDING_RECT="5661.1266537021265 -1379.9843379386175 454 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;&lt;u5e8f&gt;&lt;u53f7&gt;,&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x,&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y,point_&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;,&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;z"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="52"
#!   TYPE="GeometryFilter"
#!   VERSION="8"
#!   POSITION="540.8707933233178 -663.13253132531315"
#!   BOUNDING_RECT="540.8707933233178 -663.13253132531315 454 71"
#!   ORDER="500000000000022"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Point"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <OUTPUT_FEAT NAME="Area"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;UNFILTERED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILTER_MODE" PARM_VALUE="SIMPLE"/>
#!     <XFORM_PARM PARM_NAME="FILTER_MULTI" PARM_VALUE="FILTER_TYPES]&quot;Point Area&quot;]FME_CONTROLLER_QUERY_FILE]]FME_CONTROLLER_CHOICE]Simple"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryFilter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="55"
#!   TYPE="GeometryFilter"
#!   VERSION="8"
#!   POSITION="626.51682750965915 -1319.9843379386175"
#!   BOUNDING_RECT="626.51682750965915 -1319.9843379386175 454 71"
#!   ORDER="500000000000022"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Point"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <OUTPUT_FEAT NAME="Area"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;UNFILTERED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILTER_MODE" PARM_VALUE="SIMPLE"/>
#!     <XFORM_PARM PARM_NAME="FILTER_MULTI" PARM_VALUE="FILTER_TYPES]&quot;Point Area&quot;]FME_CONTROLLER_QUERY_FILE]]FME_CONTROLLER_CHOICE]Simple"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryFilter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="72"
#!   TYPE="DistanceMarker"
#!   VERSION="1"
#!   POSITION="2725.0272502725024 -962.50962509625037"
#!   BOUNDING_RECT="2725.0272502725024 -962.50962509625037 430 71"
#!   ORDER="500000000000036"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;Rejected&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="BegorEndpoints" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="DEAG_YES_NO" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="MarkerInterval" PARM_VALUE="$(MarkerInterval)"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DistanceMarker"/>
#!     <XFORM_PARM PARM_NAME="__COMPOUND_PARAMETERS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="79"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="7913.4326987952918 -403.36347066950668"
#!   BOUNDING_RECT="7913.4326987952918 -403.36347066950668 454 71"
#!   ORDER="500000000000023"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="点{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="81"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="9104.3029995517409 -525.41286282428041"
#!   BOUNDING_RECT="9104.3029995517409 -525.41286282428041 454 71"
#!   ORDER="500000000000040"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;point_&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;closeparen&gt; != &quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="58"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="9799.146904512525 -585.41286282428041"
#!   BOUNDING_RECT="9799.146904512525 -585.41286282428041 454 71"
#!   ORDER="500000000000042"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="距离" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u8ddd&gt;&lt;u79bb&gt; SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;sqrt&lt;openparen&gt;&lt;at&gt;abs&lt;openparen&gt;&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x&lt;closeparen&gt;-&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x&lt;closeparen&gt;&lt;closeparen&gt;*&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x&lt;closeparen&gt;-&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x&lt;closeparen&gt;&lt;closeparen&gt;-&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y&lt;closeparen&gt;-&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y&lt;closeparen&gt;&lt;closeparen&gt;*&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y&lt;closeparen&gt;-&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="60"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="10531.762926324911 -621.38135519617776"
#!   BOUNDING_RECT="10531.762926324911 -621.38135519617776 454 71"
#!   ORDER="500000000000043"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="距离" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt; NATURAL ASCENDING &lt;u8ddd&gt;&lt;u79bb&gt; NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="62"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="11492.991482709856 -585.41286282428041"
#!   BOUNDING_RECT="11492.991482709856 -585.41286282428041 454 71"
#!   ORDER="500000000000044"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="距离" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="距离" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="64"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="12213.165609916969 -655.57720794599209"
#!   BOUNDING_RECT="12213.165609916969 -655.57720794599209 454 71"
#!   ORDER="500000000000045"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="距离" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&lt;u8ddd&gt;&lt;u79bb&gt; &lt;u8ddd&gt;&lt;u79bb&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x &lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x&lt;closeparen&gt; real64 SET_TO &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y &lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y&lt;closeparen&gt; real64 SET_TO  &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z &lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;z&lt;closeparen&gt; real64 SET_TO point_&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt; point_&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;  buffer NO_OP &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt; &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;  int64 NO_OP &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;x  real64 NO_OP &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;y  real64 NO_OP &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;z &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;z  real64 NO_OP point_&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt; point_&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;  buffer NO_OP &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;&lt;u5e8f&gt;&lt;u53f7&gt; &lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;&lt;u5e8f&gt;&lt;u53f7&gt;  int64 NO_OP _overlaps _overlaps  uint32 NO_OP _element_index _element_index  uint32 NO_OP"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="66"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="12936.950021674129 -729.4909905620799"
#!   BOUNDING_RECT="12936.950021674129 -729.4909905620799 454 71"
#!   ORDER="500000000000046"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z,&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="68"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="9828.6697152685847 -1088.403741180269"
#!   BOUNDING_RECT="9828.6697152685847 -1088.403741180269 454 71"
#!   ORDER="500000000000047"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST 1 = 1"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="71"
#!   TYPE="FeatureMerger"
#!   VERSION="20"
#!   POSITION="10155.458697444117 -1607.605361767904"
#!   BOUNDING_RECT="10155.458697444117 -1607.605361767904 511 71"
#!   ORDER="500000000000048"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="MERGED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNMERGED_REQUESTOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="USED_SUPPLIER"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="numReferences" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="UNUSED_SUPPLIER"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Supplier"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Requestor"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEOM_TYPE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="JOIN_ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="JOIN_KEYS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt; &lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt; AUTO"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MERGE_COUNT_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MERGE_TYPE" PARM_VALUE="Attributes Only"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="PROCESS_DUPS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REJECT_NULL_MISSING_KEYS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SUPPLIERS_FIRST" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SUPPLIER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureMerger"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="70"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="12075.344636303515 -1340.405945454022"
#!   BOUNDING_RECT="12075.344636303515 -1340.405945454022 454 71"
#!   ORDER="500000000000047"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST 1 = 1"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="87"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="24387.163600842981 -6062.0543422087831"
#!   BOUNDING_RECT="24387.163600842981 -6062.0543422087831 430 71"
#!   ORDER="500000000000050"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;utf-8&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;u7c7b&gt;&lt;u578b&gt;&lt;u5b58&gt;&lt;u50a8&gt;,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;&lt;u8f93&gt;&lt;u51fa&gt;&lt;u5c3a&gt;&lt;u5bf8&gt;&lt;u6807&gt;&lt;u6ce8&gt;,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u89c6&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;&lt;u4e3a&gt;&lt;u5ea6&gt;&lt;u91cf&gt;,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;&lt;u8868&gt;&lt;u9762&gt;&lt;u548c&gt;&lt;u4e09&gt;&lt;u7ef4&gt;&lt;u4f53&gt;&lt;u5b58&gt;&lt;u50a8&gt;,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u5c06&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u96c6&gt;&lt;u62c6&gt;&lt;u5206&gt;&lt;u4e3a&gt;2GB&lt;u6587&gt;&lt;u4ef6&gt;,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;&lt;u5b57&gt;&lt;u7b26&gt;&lt;u7f16&gt;&lt;u7801&gt;,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;&lt;u521b&gt;&lt;u5efa&gt;&lt;u63a7&gt;&lt;u4ef6&gt;&lt;u7d22&gt;&lt;u5f15&gt;:,SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u521b&gt;&lt;u5efa&gt;&lt;u538b&gt;&lt;u7f29&gt;&lt;u7684&gt;shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="utf-8"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u5e73&gt;&lt;u6ed1&gt;&lt;u70b9&gt;_point:Passed,ftp_feature_type_name,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u5e73&gt;&lt;u6ed1&gt;&lt;u70b9&gt;_point,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,&lt;lt&gt;u9ad8&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;_line:Coerced,ftp_feature_type_name,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;_line,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO;&lt;u4fee&gt;&lt;u6b63&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;&lt;u70b9&gt;&lt;u540e&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;_line:Aggregate,ftp_feature_type_name,&lt;u4fee&gt;&lt;u6b63&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;&lt;u70b9&gt;&lt;u540e&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;_line,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,Q__network&lt;comma&gt;double,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="89"
#!   TYPE="VertexCreator"
#!   VERSION="5"
#!   POSITION="14474.601267751807 -868.94950688637323"
#!   BOUNDING_RECT="14474.601267751807 -868.94950688637323 454 71"
#!   ORDER="500000000000052"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_PARAMETERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLOSE_LINES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_DUPLICATES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MISSING_VAL_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MODE_NAME" PARM_VALUE="Replace with Point"/>
#!     <XFORM_PARM PARM_NAME="PARAMETER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="VertexCreator"/>
#!     <XFORM_PARM PARM_NAME="XVAL" PARM_VALUE="@Value(中心线折点x)"/>
#!     <XFORM_PARM PARM_NAME="YVAL" PARM_VALUE="@Value(中心线折点y)"/>
#!     <XFORM_PARM PARM_NAME="ZVAL" PARM_VALUE="@Value(中心线折点z)"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="93"
#!   TYPE="Snipper"
#!   VERSION="2"
#!   POSITION="2701.0272502725024 -495.00675006750066"
#!   BOUNDING_RECT="2701.0272502725024 -495.00675006750066 454 71"
#!   ORDER="500000000000058"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ENDLOCATIONATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ENDVERTEXATTR" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="INDEX_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="MEASUREMENTTYPE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASUREMENT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MEASURE_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SNIPMODE" PARM_VALUE="Vertex"/>
#!     <XFORM_PARM PARM_NAME="STARTLOCATIONATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="STARTVERTEXATTR" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Values_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Snipper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="95"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="3354.6531117485083 -663.00345525194393"
#!   BOUNDING_RECT="3354.6531117485083 -663.00345525194393 454 71"
#!   ORDER="500000000000059"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="counter"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Global"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="98"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="12179.139648539342 -1890.1974734033051"
#!   BOUNDING_RECT="12179.139648539342 -1890.1974734033051 454 71"
#!   ORDER="500000000000061"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt; NATURAL ASCENDING &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt; NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="100"
#!   TYPE="LineBuilder"
#!   VERSION="7"
#!   POSITION="12280.47994765662 -2367.9683082545116"
#!   BOUNDING_RECT="12280.47994765662 -2367.9683082545116 454 71"
#!   ORDER="500000000000062"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="LINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="POLYGON"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ACCUM_INPUT_ATTRS" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="大线段范围序号"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_DUPLICATES" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="LineBuilder"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="102"
#!   TYPE="LineOnLineOverlayer"
#!   VERSION="9"
#!   POSITION="12965.003901791968 -2307.9683082545116"
#!   BOUNDING_RECT="12965.003901791968 -2307.9683082545116 486.00106825772946 71"
#!   ORDER="500000000000071"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="LINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="COLLAPSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRS_ACCUM_MODE" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_DROP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OV_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SEPARATE_COLLINEAR_SEGMENTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="LineOnLineOverlayer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="107"
#!   TYPE="NetworkTopologyCalculator"
#!   VERSION="2"
#!   POSITION="13705.940630834881 -2427.9683082545116"
#!   BOUNDING_RECT="13705.940630834881 -2427.9683082545116 620.00106825772946 71"
#!   ORDER="500000000000075"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="NETWORK"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_network_id" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NETWORK_ID_ATTR" PARM_VALUE="_network_id"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="NetworkTopologyCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="109"
#!   TYPE="Aggregator"
#!   VERSION="17"
#!   POSITION="14546.127604133184 -2401.8097323830384"
#!   BOUNDING_RECT="14546.127604133184 -2401.8097323830384 454 71"
#!   ORDER="500000000000076"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AGGREGATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_network_id" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ACCUM_ATTRS_NAME" PARM_VALUE="Drop Incoming Attributes"/>
#!     <XFORM_PARM PARM_NAME="AGGREGATE_TYPE" PARM_VALUE="Homogeneous Collection (If Possible)"/>
#!     <XFORM_PARM PARM_NAME="ASSEMBLE_HIERARCHY_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="ASSEMBLE_ONE_LEVEL_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_TO_TRAIT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AV" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CHILD_ID_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONCAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="COUNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CYCLE_ID_ERROR_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEOM_NAME_FIELD" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="_network_id"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ID_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ID_SELECTION" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="Geometry - Assemble One Level"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARENT_ID_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_ID" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SEP" PARM_VALUE="&lt;comma&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUM" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WEIGHT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Aggregator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="104"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="11183.14754576117 -2547.9683082545116"
#!   BOUNDING_RECT="11183.14754576117 -2547.9683082545116 454 71"
#!   ORDER="500000000000077"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt; NATURAL ASCENDING &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt; NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="116"
#!   TYPE="PointOnLineOverlayer"
#!   VERSION="9"
#!   POSITION="15011.01967541414 -2951.8159513318151"
#!   BOUNDING_RECT="15011.01967541414 -2951.8159513318151 504.00106825772946 71"
#!   ORDER="500000000000083"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="LINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程点{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP1" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP_CANDIDATE" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_SUPER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="Selected Attributes"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_NAME" PARM_VALUE="高程点"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MERGE_MEASURES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OV_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOLERANCE" PARM_VALUE="0.000001"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="PointOnLineOverlayer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="118"
#!   TYPE="Junction"
#!   VERSION="0"
#!   POSITION="12792.087168200873 -3026.2732176436084"
#!   BOUNDING_RECT="12792.087168200873 -3026.2732176436084 61 61"
#!   ORDER="500000000000084"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Junction"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="117"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="14052.993790807466 -3322.4517027778979"
#!   BOUNDING_RECT="14052.993790807466 -3322.4517027778979 454 71"
#!   ORDER="500000000000087"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="120"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="14474.601267751807 -2645.7305790449204"
#!   BOUNDING_RECT="14474.601267751807 -2645.7305790449204 454 71"
#!   ORDER="500000000000088"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="127"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="15762.25001380448 -2879.5124907770823"
#!   BOUNDING_RECT="15762.25001380448 -2879.5124907770823 454 71"
#!   ORDER="500000000000091"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="139"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="4568.6598170329517 -1022.5096250962504"
#!   BOUNDING_RECT="4568.6598170329517 -1022.5096250962504 454 71"
#!   ORDER="500000000000097"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST 1 = 1"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="134"
#!   TYPE="Inspector"
#!   VERSION="5"
#!   POSITION="6958.5979469159229 -403.36347066950668"
#!   BOUNDING_RECT="6958.5979469159229 -403.36347066950668 609.00106825772946 71"
#!   ORDER="500000000000101"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <XFORM_PARM PARM_NAME="AREA_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PEN_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="POINTCLOUD_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_NUM_COLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="RASTER_NUM_ROWS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="RASTER_START_COL" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="RASTER_START_ROW" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="RASTER_VISUALIZATION_OPTION" PARM_VALUE="No Reduction"/>
#!     <XFORM_PARM PARM_NAME="TACKATTRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="THINNER_INTERVAL" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="THINNER_MAX_NUM_POINTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="THINNER_TYPE_DISPLAY" PARM_VALUE="No Thinning"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_Output"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="142"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="8533.2818217728691 -501.18998743212728"
#!   BOUNDING_RECT="8533.2818217728691 -501.18998743212728 454 71"
#!   ORDER="500000000000040"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;z&lt;closeparen&gt; != &quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="145"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="8420.600510352926 -1019.0319294497291"
#!   BOUNDING_RECT="8420.600510352926 -1019.0319294497291 454 71"
#!   ORDER="500000000000102"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="点{}.point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="点{}.point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;_overlaps&lt;closeparen&gt; != 0"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="148"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="9643.4388256925995 -193.6160665954485"
#!   BOUNDING_RECT="9643.4388256925995 -193.6160665954485 454 71"
#!   ORDER="500000000000103"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_中心线折点" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="point_阈值内" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="阈值内序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u9608&gt;&lt;u503c&gt;&lt;u5185&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt; = &quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_8"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="133"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="13705.940630834881 -798.24167806895423"
#!   BOUNDING_RECT="13705.940630834881 -798.24167806895423 454 71"
#!   ORDER="500000000000104"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;x &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;y"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="125"
#!   TYPE="LineOnLineOverlayer"
#!   VERSION="9"
#!   POSITION="16241.874375265485 -2099.4775165142951"
#!   BOUNDING_RECT="16241.874375265485 -2099.4775165142951 525.00106825772946 71"
#!   ORDER="500000000000105"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="LINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="COLLAPSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRS_ACCUM_MODE" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_DROP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OV_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SEPARATE_COLLINEAR_SEGMENTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="LineOnLineOverlayer_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="138"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="16917.832221800465 -2211.571028753764"
#!   BOUNDING_RECT="16917.832221800465 -2211.571028753764 454 71"
#!   ORDER="500000000000106"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;_overlaps&lt;closeparen&gt; &gt;= 3"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="129"
#!   TYPE="PointOnLineOverlayer"
#!   VERSION="9"
#!   POSITION="18001.898940881594 -3041.9039242566319"
#!   BOUNDING_RECT="18001.898940881594 -3041.9039242566319 543.00106825772946 71"
#!   ORDER="500000000000092"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="LINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP1" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP_CANDIDATE" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_SUPER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="Selected Attributes"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_NAME" PARM_VALUE="Z"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MERGE_MEASURES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OV_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOLERANCE" PARM_VALUE="0.0001"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="PointOnLineOverlayer_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="158"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="18634.721200405773 -2944.0247054644456"
#!   BOUNDING_RECT="18634.721200405773 -2944.0247054644456 454 71"
#!   ORDER="500000000000109"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;_overlaps&lt;closeparen&gt; = 1"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_9"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="156"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="17474.480300358562 -2671.5230804481948"
#!   BOUNDING_RECT="17474.480300358562 -2671.5230804481948 454 71"
#!   ORDER="500000000000113"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z SET_TO 0 uint64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="160"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="20699.441569633542 -3559.2873716595745"
#!   BOUNDING_RECT="20699.441569633542 -3559.2873716595745 454 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="大线段范围序号"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="161"
#!   TYPE="DistanceMarker"
#!   VERSION="1"
#!   POSITION="20114.296577109119 -3511.6584205874551"
#!   BOUNDING_RECT="20114.296577109119 -3511.6584205874551 430 71"
#!   ORDER="500000000000036"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;Rejected&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="BegorEndpoints" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="DEAG_YES_NO" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="MarkerInterval" PARM_VALUE="$(MarkerInterval)"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DistanceMarker_2"/>
#!     <XFORM_PARM PARM_NAME="__COMPOUND_PARAMETERS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="163"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="19281.40781343888 -3230.9036742541325"
#!   BOUNDING_RECT="19281.40781343888 -3230.9036742541325 454 71"
#!   ORDER="500000000000059"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="counter"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Global"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="167"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="21322.942027058489 -3559.2873716595745"
#!   BOUNDING_RECT="21322.942027058489 -3559.2873716595745 454 71"
#!   ORDER="500000000000115"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u9ad8&gt;&lt;u7a0b&gt;min SET_TO &lt;at&gt;Value&lt;openparen&gt;Z&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u9ad8&gt;&lt;u7a0b&gt;max SET_TO &lt;at&gt;Value&lt;openparen&gt;Z&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="169"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="21472.089720897209 -4300.0430004300033"
#!   BOUNDING_RECT="21472.089720897209 -4300.0430004300033 454 71"
#!   ORDER="500000000000116"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST 1 = 1"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_10"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="172"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="21775.217752177523 -3768.7876878768784"
#!   BOUNDING_RECT="21775.217752177523 -3768.7876878768784 454 71"
#!   ORDER="500000000000117"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;,NUMERIC_MODE,,MAX,,,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="大线段范围序号"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="171"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="22509.600096000962 -3906.9134691346903"
#!   BOUNDING_RECT="22509.600096000962 -3906.9134691346903 454 71"
#!   ORDER="500000000000118"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u9ad8&gt;&lt;u7a0b&gt; SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;&lt;at&gt;Evaluate&lt;openparen&gt;&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;max&lt;closeparen&gt;-&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt;&lt;closeparen&gt;&lt;solidus&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;.max&lt;closeparen&gt;*&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt;&lt;closeparen&gt;&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt; = 0&apos;&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS2___&quot; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_8"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="176"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="22037.720377203768 -4360.0430004300033"
#!   BOUNDING_RECT="22037.720377203768 -4360.0430004300033 454 71"
#!   ORDER="500000000000119"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u9ad8&gt;&lt;u7a0b&gt;,&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;,&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;,&lt;u9ad8&gt;&lt;u7a0b&gt;max,&lt;u9ad8&gt;&lt;u7a0b&gt;min"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="178"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="22762.727627276272 -4360.0430004300033"
#!   BOUNDING_RECT="22762.727627276272 -4360.0430004300033 454 71"
#!   ORDER="500000000000120"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt; = 2"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_11"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="180"
#!   TYPE="PointOnLineOverlayer"
#!   VERSION="9"
#!   POSITION="17684.551845518446 -2071.8957189571897"
#!   BOUNDING_RECT="17684.551845518446 -2071.8957189571897 542.00106825772946 71"
#!   ORDER="500000000000122"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="折点高程{}.高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="折点高程{}.高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="LINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP1" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP_CANDIDATE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_SUPER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;u9ad8&gt;&lt;u7a0b&gt;max &lt;u9ad8&gt;&lt;u7a0b&gt;min"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="Selected Attributes"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="折点高程"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MERGE_MEASURES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OV_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOLERANCE" PARM_VALUE="0.00001"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="PointOnLineOverlayer_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="183"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="19310.737013387265 -2505.147739101139"
#!   BOUNDING_RECT="19310.737013387265 -2505.147739101139 454 71"
#!   ORDER="500000000000115"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u9ad8&gt;&lt;u7a0b&gt;min SET_TO &lt;at&gt;Value&lt;openparen&gt;Z&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u9ad8&gt;&lt;u7a0b&gt;max SET_TO &lt;at&gt;Value&lt;openparen&gt;Z&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_9"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="182"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="18869.098044174221 -2211.571028753764"
#!   BOUNDING_RECT="18869.098044174221 -2211.571028753764 454 71"
#!   ORDER="500000000000124"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="折点高程{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="187"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="20059.295508851388 -2123.0522087399081"
#!   BOUNDING_RECT="20059.295508851388 -2123.0522087399081 485.00106825772946 71"
#!   ORDER="500000000000126"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="周围高程.mean" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="周围高程" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="周围高程.mean" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="&lt;u5468&gt;&lt;u56f4&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;,NUMERIC_MODE,,,,,MEAN,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="189"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="21108.355233238264 -2183.0522087399081"
#!   BOUNDING_RECT="21108.355233238264 -2183.0522087399081 466.00106825772946 71"
#!   ORDER="500000000000127"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="周围高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="周围高程.mean" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u5468&gt;&lt;u56f4&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;.mean&lt;closeparen&gt; real64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_10"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="191"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="18328.308283082835 -2271.8977189771899"
#!   BOUNDING_RECT="18328.308283082835 -2271.8977189771899 454 71"
#!   ORDER="500000000000128"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="折点高程{}.高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="折点高程{}.高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="186"
#!   TYPE="Junction"
#!   VERSION="0"
#!   POSITION="17731.734862904181 -3119.516421767747"
#!   BOUNDING_RECT="17731.734862904181 -3119.516421767747 61 61"
#!   ORDER="500000000000129"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Junction_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="197"
#!   TYPE="Junction"
#!   VERSION="0"
#!   POSITION="17731.734862904181 -3351.9039242566319"
#!   BOUNDING_RECT="17731.734862904181 -3351.9039242566319 61 61"
#!   ORDER="500000000000130"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Junction_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="200"
#!   TYPE="PointOnLineOverlayer"
#!   VERSION="9"
#!   POSITION="17803.715907680449 -4011.4192410650744"
#!   BOUNDING_RECT="17803.715907680449 -4011.4192410650744 543.00106825772946 71"
#!   ORDER="500000000000092"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="POINT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="周围高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="周围高程.mean" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="LINE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="周围高程" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="周围高程.mean" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP1" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP_CANDIDATE" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_SUPER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="Selected Attributes"/>
#!     <XFORM_PARM PARM_NAME="CANDIDATE_LIST_NAME" PARM_VALUE="Z"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MERGE_MEASURES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OV_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOLERANCE" PARM_VALUE="0.0001"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="PointOnLineOverlayer_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="202"
#!   TYPE="Junction"
#!   VERSION="0"
#!   POSITION="17343.373506452568 -3166.8474158697672"
#!   BOUNDING_RECT="17343.373506452568 -3166.8474158697672 61 61"
#!   ORDER="500000000000131"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Junction_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="206"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="18674.278943040659 -4613.0827797679058"
#!   BOUNDING_RECT="18674.278943040659 -4613.0827797679058 454 71"
#!   ORDER="500000000000109"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;_overlaps&lt;closeparen&gt; = 1"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_12"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="207"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="20481.321296891783 -5184.4853582393507"
#!   BOUNDING_RECT="20481.321296891783 -5184.4853582393507 454 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="大线段范围序号"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="208"
#!   TYPE="DistanceMarker"
#!   VERSION="1"
#!   POSITION="19896.17630436736 -5136.8564071672308"
#!   BOUNDING_RECT="19896.17630436736 -5136.8564071672308 430 71"
#!   ORDER="500000000000036"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;Rejected&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="BegorEndpoints" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="DEAG_YES_NO" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="MarkerInterval" PARM_VALUE="$(MarkerInterval)"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DistanceMarker_3"/>
#!     <XFORM_PARM PARM_NAME="__COMPOUND_PARAMETERS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="209"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="19178.420270971797 -4949.3043472467361"
#!   BOUNDING_RECT="19178.420270971797 -4949.3043472467361 454 71"
#!   ORDER="500000000000059"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u5927&gt;&lt;u7ebf&gt;&lt;u6bb5&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;u5e8f&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="counter"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Global"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_8"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="210"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="21104.82175431673 -5184.4853582393507"
#!   BOUNDING_RECT="21104.82175431673 -5184.4853582393507 454 71"
#!   ORDER="500000000000115"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u9ad8&gt;&lt;u7a0b&gt;min SET_TO &lt;at&gt;Value&lt;openparen&gt;Z&lt;opencurly&gt;0&lt;closecurly&gt;.&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u9ad8&gt;&lt;u7a0b&gt;max SET_TO &lt;at&gt;Value&lt;openparen&gt;Z&lt;opencurly&gt;1&lt;closecurly&gt;.&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;z&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_11"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="212"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="21557.097479435764 -5393.9856744566541"
#!   BOUNDING_RECT="21557.097479435764 -5393.9856744566541 454 71"
#!   ORDER="500000000000117"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;,NUMERIC_MODE,,MAX,,,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="大线段范围序号"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="213"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="22291.479823259204 -5532.1114557144656"
#!   BOUNDING_RECT="22291.479823259204 -5532.1114557144656 454 71"
#!   ORDER="500000000000118"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u9ad8&gt;&lt;u7a0b&gt; SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;&lt;at&gt;Evaluate&lt;openparen&gt;&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;max&lt;closeparen&gt;-&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt;&lt;closeparen&gt;&lt;solidus&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;.max&lt;closeparen&gt;*&lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt;&lt;closeparen&gt;&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u4e2d&gt;&lt;u5fc3&gt;&lt;u7ebf&gt;&lt;u6298&gt;&lt;u70b9&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;closeparen&gt; = 0&apos;&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS2___&quot; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_12"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="228"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="19505.145546504966 -2183.0522087399081"
#!   BOUNDING_RECT="19505.145546504966 -2183.0522087399081 464.00106825772946 71"
#!   ORDER="500000000000132"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="周围高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u5468&gt;&lt;u56f4&gt;&lt;u9ad8&gt;&lt;u7a0b&gt; SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;0&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;max&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;max&lt;closeparen&gt;&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;min&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS3___&quot; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_13"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="214"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="20699.441569633542 -2300.5549204428216"
#!   BOUNDING_RECT="20699.441569633542 -2300.5549204428216 454 71"
#!   ORDER="500000000000133"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="周围高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="周围高程.mean" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="周围高程" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="周围高程.mean" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="211"
#!   TYPE="CoordinateExtractor"
#!   VERSION="4"
#!   POSITION="23125.231252312529 -5368.9106720854452"
#!   BOUNDING_RECT="23125.231252312529 -5368.9106720854452 507.00106825772946 71"
#!   ORDER="500000000000135"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ALL_ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IND" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTRIBUTES_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_indices"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="All Coordinates"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="CoordinateExtractor_3"/>
#!     <XFORM_PARM PARM_NAME="X_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Y_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_DEFAULT" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="227"
#!   TYPE="VertexCreator"
#!   VERSION="5"
#!   POSITION="24571.389330914582 -5442.049701348079"
#!   BOUNDING_RECT="24571.389330914582 -5442.049701348079 454 71"
#!   ORDER="500000000000136"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_PARAMETERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLOSE_LINES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_DUPLICATES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURE_TYPE" PARM_VALUE="Continuous"/>
#!     <XFORM_PARM PARM_NAME="MISSING_VAL_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MODE_NAME" PARM_VALUE="Replace with Point"/>
#!     <XFORM_PARM PARM_NAME="PARAMETER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="VertexCreator_2"/>
#!     <XFORM_PARM PARM_NAME="XVAL" PARM_VALUE="@Value(x)"/>
#!     <XFORM_PARM PARM_NAME="YVAL" PARM_VALUE="@Value(y)"/>
#!     <XFORM_PARM PARM_NAME="ZVAL" PARM_VALUE="@Value(高程)"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="232"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="23966.330088832801 -5412.2881654348448"
#!   BOUNDING_RECT="23966.330088832801 -5412.2881654348448 454 71"
#!   ORDER="500000000000138"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="高程max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Z{}.中心线折点z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="大线段范围序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="中心线折点序号.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="_indices{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="221"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="24214.739964846463 -5765.7547904554631"
#!   BOUNDING_RECT="24214.739964846463 -5765.7547904554631 454 71"
#!   ORDER="500000000000134"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="高程" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u9ad8&gt;&lt;u7a0b&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_8"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="6"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="12"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="11"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="29"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="28"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="19"
#!   SOURCE_NODE="11"
#!   TARGET_NODE="16"
#!   SOURCE_PORT_DESC="fo 0 CENTERLINE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="88"
#!   SOURCE_NODE="11"
#!   TARGET_NODE="87"
#!   SOURCE_PORT_DESC="fo 0 CENTERLINE"
#!   TARGET_PORT_DESC="fi 1 Coerced"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="true"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="94"
#!   SOURCE_NODE="11"
#!   TARGET_NODE="93"
#!   SOURCE_PORT_DESC="fo 0 CENTERLINE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="2"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="36"
#!   SOURCE_NODE="21"
#!   TARGET_NODE="35"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="32"
#!   SOURCE_NODE="23"
#!   TARGET_NODE="31"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="146"
#!   SOURCE_NODE="25"
#!   TARGET_NODE="145"
#!   SOURCE_PORT_DESC="fo 0 POINT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="30"
#!   SOURCE_NODE="28"
#!   TARGET_NODE="21"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="39"
#!   SOURCE_NODE="31"
#!   TARGET_NODE="38"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="140"
#!   SOURCE_NODE="31"
#!   TARGET_NODE="139"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="43"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="40"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="37"
#!   SOURCE_NODE="35"
#!   TARGET_NODE="44"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="41"
#!   SOURCE_NODE="38"
#!   TARGET_NODE="33"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="45"
#!   SOURCE_NODE="40"
#!   TARGET_NODE="42"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="46"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="25"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 POINT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="84"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="71"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 SUPPLIER"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="137"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="134"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="2"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="49"
#!   SOURCE_NODE="44"
#!   TARGET_NODE="47"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="50"
#!   SOURCE_NODE="47"
#!   TARGET_NODE="48"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="51"
#!   SOURCE_NODE="48"
#!   TARGET_NODE="25"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 POINT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="141"
#!   SOURCE_NODE="48"
#!   TARGET_NODE="139"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="57"
#!   SOURCE_NODE="55"
#!   TARGET_NODE="9"
#!   SOURCE_PORT_DESC="fo 0 Point"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="61"
#!   SOURCE_NODE="58"
#!   TARGET_NODE="60"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="132"
#!   SOURCE_NODE="60"
#!   TARGET_NODE="62"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="65"
#!   SOURCE_NODE="62"
#!   TARGET_NODE="64"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="67"
#!   SOURCE_NODE="64"
#!   TARGET_NODE="66"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="151"
#!   SOURCE_NODE="66"
#!   TARGET_NODE="133"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="83"
#!   SOURCE_NODE="68"
#!   TARGET_NODE="71"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 REQUESTOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="85"
#!   SOURCE_NODE="68"
#!   TARGET_NODE="70"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="105"
#!   SOURCE_NODE="68"
#!   TARGET_NODE="104"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="2"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="119"
#!   SOURCE_NODE="68"
#!   TARGET_NODE="118"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="3"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="99"
#!   SOURCE_NODE="70"
#!   TARGET_NODE="98"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="78"
#!   SOURCE_NODE="72"
#!   TARGET_NODE="23"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="143"
#!   SOURCE_NODE="79"
#!   TARGET_NODE="142"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="149"
#!   SOURCE_NODE="81"
#!   TARGET_NODE="148"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="92"
#!   SOURCE_NODE="89"
#!   TARGET_NODE="68"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="true"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="97"
#!   SOURCE_NODE="95"
#!   TARGET_NODE="72"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 INPUT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="101"
#!   SOURCE_NODE="98"
#!   TARGET_NODE="100"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="110"
#!   SOURCE_NODE="107"
#!   TARGET_NODE="109"
#!   SOURCE_PORT_DESC="fo 0 NETWORK"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="123"
#!   SOURCE_NODE="107"
#!   TARGET_NODE="120"
#!   SOURCE_PORT_DESC="fo 0 NETWORK"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="111"
#!   SOURCE_NODE="109"
#!   TARGET_NODE="87"
#!   SOURCE_PORT_DESC="fo 0 AGGREGATE"
#!   TARGET_PORT_DESC="fi 2 Aggregate"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="122"
#!   SOURCE_NODE="117"
#!   TARGET_NODE="116"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 POINT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="203"
#!   SOURCE_NODE="117"
#!   TARGET_NODE="202"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="121"
#!   SOURCE_NODE="118"
#!   TARGET_NODE="117"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="124"
#!   SOURCE_NODE="120"
#!   TARGET_NODE="116"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 LINE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="153"
#!   SOURCE_NODE="125"
#!   TARGET_NODE="138"
#!   SOURCE_PORT_DESC="fo 0 POINT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="198"
#!   SOURCE_NODE="127"
#!   TARGET_NODE="197"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="126"
#!   SOURCE_NODE="127"
#!   TARGET_NODE="125"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 LINE"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="152"
#!   SOURCE_NODE="133"
#!   TARGET_NODE="89"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="157"
#!   SOURCE_NODE="138"
#!   TARGET_NODE="156"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="181"
#!   SOURCE_NODE="138"
#!   TARGET_NODE="180"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 POINT"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="144"
#!   SOURCE_NODE="142"
#!   TARGET_NODE="81"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="147"
#!   SOURCE_NODE="145"
#!   TARGET_NODE="79"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="150"
#!   SOURCE_NODE="148"
#!   TARGET_NODE="58"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="195"
#!   SOURCE_NODE="156"
#!   TARGET_NODE="186"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="168"
#!   SOURCE_NODE="160"
#!   TARGET_NODE="167"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="166"
#!   SOURCE_NODE="161"
#!   TARGET_NODE="160"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="164"
#!   SOURCE_NODE="163"
#!   TARGET_NODE="161"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 INPUT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="184"
#!   SOURCE_NODE="163"
#!   TARGET_NODE="183"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="173"
#!   SOURCE_NODE="167"
#!   TARGET_NODE="172"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="177"
#!   SOURCE_NODE="169"
#!   TARGET_NODE="176"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="174"
#!   SOURCE_NODE="171"
#!   TARGET_NODE="169"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="179"
#!   SOURCE_NODE="176"
#!   TARGET_NODE="178"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="192"
#!   SOURCE_NODE="180"
#!   TARGET_NODE="191"
#!   SOURCE_PORT_DESC="fo 0 POINT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="229"
#!   SOURCE_NODE="182"
#!   TARGET_NODE="228"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="185"
#!   SOURCE_NODE="183"
#!   TARGET_NODE="180"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 LINE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="196"
#!   SOURCE_NODE="186"
#!   TARGET_NODE="129"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 POINT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="205"
#!   SOURCE_NODE="189"
#!   TARGET_NODE="200"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 POINT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="193"
#!   SOURCE_NODE="191"
#!   TARGET_NODE="182"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="199"
#!   SOURCE_NODE="197"
#!   TARGET_NODE="129"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 1 LINE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="201"
#!   SOURCE_NODE="197"
#!   TARGET_NODE="200"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 1 LINE"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="204"
#!   SOURCE_NODE="202"
#!   TARGET_NODE="186"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="194"
#!   SOURCE_NODE="202"
#!   TARGET_NODE="200"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 POINT"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="216"
#!   SOURCE_NODE="207"
#!   TARGET_NODE="210"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="217"
#!   SOURCE_NODE="208"
#!   TARGET_NODE="207"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="218"
#!   SOURCE_NODE="209"
#!   TARGET_NODE="208"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 INPUT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="219"
#!   SOURCE_NODE="210"
#!   TARGET_NODE="212"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="235"
#!   SOURCE_NODE="211"
#!   TARGET_NODE="232"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="233"
#!   SOURCE_NODE="213"
#!   TARGET_NODE="211"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="220"
#!   SOURCE_NODE="214"
#!   TARGET_NODE="189"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="231"
#!   SOURCE_NODE="221"
#!   TARGET_NODE="87"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Passed"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="222"
#!   SOURCE_NODE="227"
#!   TARGET_NODE="221"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="230"
#!   SOURCE_NODE="228"
#!   TARGET_NODE="187"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="236"
#!   SOURCE_NODE="232"
#!   TARGET_NODE="227"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="53"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="52"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="56"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="55"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="54"
#!   SOURCE_NODE="52"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 1 Area"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="96"
#!   SOURCE_NODE="93"
#!   TARGET_NODE="95"
#!   SOURCE_PORT_DESC="fo 1 REMNANTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="103"
#!   SOURCE_NODE="100"
#!   TARGET_NODE="102"
#!   SOURCE_PORT_DESC="fo 1 LINE"
#!   TARGET_PORT_DESC="fi 0 LINE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="108"
#!   SOURCE_NODE="102"
#!   TARGET_NODE="107"
#!   SOURCE_PORT_DESC="fo 1 LINE"
#!   TARGET_PORT_DESC="fi 0 LINE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="128"
#!   SOURCE_NODE="116"
#!   TARGET_NODE="127"
#!   SOURCE_PORT_DESC="fo 1 LINE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="159"
#!   SOURCE_NODE="129"
#!   TARGET_NODE="158"
#!   SOURCE_PORT_DESC="fo 1 LINE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="170"
#!   SOURCE_NODE="129"
#!   TARGET_NODE="169"
#!   SOURCE_PORT_DESC="fo 1 LINE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="165"
#!   SOURCE_NODE="158"
#!   TARGET_NODE="163"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="175"
#!   SOURCE_NODE="172"
#!   TARGET_NODE="171"
#!   SOURCE_PORT_DESC="fo 1 COMPLETE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="215"
#!   SOURCE_NODE="187"
#!   TARGET_NODE="214"
#!   SOURCE_PORT_DESC="fo 1 COMPLETE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="226"
#!   SOURCE_NODE="200"
#!   TARGET_NODE="206"
#!   SOURCE_PORT_DESC="fo 1 LINE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="223"
#!   SOURCE_NODE="206"
#!   TARGET_NODE="209"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="224"
#!   SOURCE_NODE="212"
#!   TARGET_NODE="213"
#!   SOURCE_PORT_DESC="fo 1 COMPLETE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="86"
#!   SOURCE_NODE="71"
#!   TARGET_NODE="70"
#!   SOURCE_PORT_DESC="fo 3 UNUSED_SUPPLIER"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO file_2 $(FME_MF_DIR)测试\梁溪河测线点.shp

DEFAULT_MACRO file $(FME_MF_DIR)测试\梁溪河边缘测深线.shp

DEFAULT_MACRO MarkerInterval 10

DEFAULT_MACRO TOLERANCE 5

DEFAULT_MACRO dir $(FME_MF_DIR)成果

# ============================================================================
INCLUDE [ if {{$(file_2$encode)} == {}} { puts_real {Parameter 'file_2' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(file$encode)} == {}} { puts_real {Parameter 'file' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(TOLERANCE$encode)} == {}} { puts_real {Parameter 'TOLERANCE' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)提取、修正中心线工具.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 提取、修正中心线工具
MACRO FME_VIEWER_APP fmedatainspector
# -------------------------------------------------------------------------

INCLUDE [ if {[info exists env(FME_TEMP)] && [file isdirectory $env(FME_TEMP)]} {set gVisualizerTemp $env(FME_TEMP)}  elseif {[info exists env(TEMP)] && [file isdirectory $env(TEMP)]}  {set gVisualizerTemp $env(TEMP)}  elseif { $tcl_platform(platform) == "unix" } {set gVisualizerTemp "/tmp"} else {set gVisualizerTemp c:};  regsub -all {[ ,&]} {$(WORKSPACE_NAME)} {_} wsname; set gVisualizerTemp $gVisualizerTemp/${wsname}_[clock format [clock seconds] -format %H%M%S];  puts "MACRO WORKSPACE_TEMP_DIR $gVisualizerTemp"; 
MACRO VISUALIZER_FEATURE_FILE $(WORKSPACE_TEMP_DIR)/inspector.ffs
MACRO VISUALIZER_SCHEMA_FILE $(WORKSPACE_TEMP_DIR)/inspector.fsc
MACRO VISUALIZER_CREATE_SPATIAL_INDEX YES
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Creator CREATED Splitter"   INPUT FEATURE_TYPE Creator_CREATED   OUTPUT FEATURE_TYPE Creator_CREATED_0_GJAgmIF9G6c=   OUTPUT FEATURE_TYPE Creator_CREATED_1_Exx7idfjvCA=
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED_0_GJAgmIF9G6c= Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED_0_GJAgmIF9G6c=   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED_1_Exx7idfjvCA= Brancher -1 6"   INPUT FEATURE_TYPE Creator_CREATED_1_Exx7idfjvCA=   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(file$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED_0_GJAgmIF9G6c=    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_<OTHER>           }
# -------------------------------------------------------------------------
FACTORY_DEF {*} GeometryFilterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { GeometryFilter }    INPUT  FEATURE_TYPE FeatureReader_<OTHER>    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    FILTER_TYPES { Point,Area }    INSTANCES { INSTANTIATE }    BREAK_AGG { Yes }    OUTPUT { <UNFILTERED> FEATURE_TYPE GeometryFilter_<UNFILTERED>  }    OUTPUT { Point FEATURE_TYPE GeometryFilter_Point  }    OUTPUT { Area FEATURE_TYPE GeometryFilter_Area  }
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter Point Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_Point
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter <UNFILTERED> Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_<UNFILTERED>
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE GeometryFilter_Area    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { CenterlineReplacer }    INPUT  FEATURE_TYPE Reprojector_REPROJECTED    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @ConvertToLine(APPROX_CENTER_LINE, ___fme_rejection_code___, FILTER_COLLINEAR_POINTS) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CenterlineReplacer_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(___fme_rejection_code___) != ""    OUTPUT { FAILED FEATURE_TYPE CenterlineReplacer_CENTERLINE        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "CenterlineReplacer CENTERLINE Splitter"   INPUT FEATURE_TYPE CenterlineReplacer_CENTERLINE   OUTPUT FEATURE_TYPE CenterlineReplacer_CENTERLINE_0_m7erO6aY+fE=   OUTPUT FEATURE_TYPE CenterlineReplacer_CENTERLINE_1_IXR7sqC48zM=   OUTPUT FEATURE_TYPE CenterlineReplacer_CENTERLINE_2_pS3E8PKmUlQ=
# -------------------------------------------------------------------------
FACTORY_DEF {*} ChoppingFactory    FACTORY_NAME { Chopper }    INPUT  FEATURE_TYPE CenterlineReplacer_CENTERLINE_0_m7erO6aY+fE=    MODE { VERTEX }    MAX_VERTICES { "1" }    APPROX_LENGTH { "<Unused>" }    REMNANT_ATTRIBUTE { "_remnant" }    CHOP_POLYGONS { <Unused> }    REJECT_INVALID_GEOM Yes    DEAGGREGATE_INPUT { Deaggregate }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }
# -------------------------------------------------------------------------
# The Snipper takes the constant "end" to mean the last vertex of the line,
# but Workbench won't let us put an alpha string into the "last vertex" textfield.
# We'll interpret an ending vertex of -1 as being the same as "end".
# This is used only in the distance and percentage.
Tcl2 proc Snipper_fixIndex {numIndex} {     if {$numIndex < 0} {         return "end";     } else {         return $numIndex;     } }
INCLUDE [          if { ({VERTEX} == {DISTANCE} || {VERTEX} == {PERCENTAGE}) } {             puts {MACRO Snipper_snipFunc @Snip(@Tcl2("Snipper_fixIndex <Unused>"),@Tcl2("Snipper_fixIndex <Unused>"),VERTEX,<Unused>,OUTPUT_REMNANT)};          } elseif { ({VERTEX} == {MEASURE_RELATIVE_FROM_START} || {VERTEX} == {MEASURE_REAL_VALUES}) } {             puts {MACRO Snipper_snipFunc @Snip("<Unused>", "<Unused>", "<Unused>", VERTEX,OUTPUT_REMNANT)};          } elseif { ({VERTEX} == {VERTEX}) } {             puts {MACRO Snipper_snipFunc @Snip("2","2",VERTEX,OUTPUT_REMNANT)};          }          ]
FACTORY_DEF {*} TestFactory    FACTORY_NAME  { Snipper_modeFilter }    INPUT  FEATURE_TYPE CenterlineReplacer_CENTERLINE_2_pS3E8PKmUlQ=    TEST { VERTEX == "VERTEX" }    OUTPUT { PASSED FEATURE_TYPE ___TO_VERTEXFILTER___ }    OUTPUT { FAILED FEATURE_TYPE ___TO_LOCATIONFILTER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME  { Snipper_vertexFilter }    INPUT FEATURE_TYPE ___TO_VERTEXFILTER___    TEST { "2" TYPE INT ENCODED }    TEST { "2" TYPE INT ENCODED }    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE ___TO_DEAGGFILTER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME  { Snipper_locationVerifier }    INPUT FEATURE_TYPE ___TO_LOCATIONFILTER___    TEST { "<Unused>" != "" }    TEST { "@EvaluateExpression(FDIV,FLOAT,<at>double<openparen><Unused><closeparen>,Snipper)" TYPE DOUBLE ENCODED }    TEST { "<Unused>" != "" }    TEST { "@EvaluateExpression(FDIV,FLOAT,<at>double<openparen><Unused><closeparen>,Snipper)" TYPE DOUBLE ENCODED }    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE ___TO_DEAGGFILTER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME  { Snipper_DeaggFilter }    INPUT FEATURE_TYPE ___TO_DEAGGFILTER___    TEST { YES == "YES" }    TEST @Value(fme_geometry) == "fme_aggregate"    TEST @Value(fme_type) != "fme_solid"    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE ____DEAGGREGATE_INPUT____ }    OUTPUT { FAILED FEATURE_TYPE ___AGG_FILTER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME  { Snipper_Aggfilter }    INPUT FEATURE_TYPE ___AGG_FILTER___    TEST @Value(fme_geometry) != "fme_aggregate"    OUTPUT { PASSED FEATURE_TYPE ___TO_TYPEFILTER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME  { Snipper_Typefilter }    INPUT FEATURE_TYPE ___TO_TYPEFILTER___    TEST @Value(fme_type) == "fme_line"    TEST @Value(fme_type) == "fme_arc"    TEST @Value(fme_geometry) == "fme_polygon"    BOOLEAN_OPERATOR OR    OUTPUT { PASSED FEATURE_TYPE ___TO_INPUT___ }
FACTORY_DEF {*} DeaggregateFactory    FACTORY_NAME { Snipper_DeaggInput }    INPUT FEATURE_TYPE ____DEAGGREGATE_INPUT____    RECURSIVE Yes    SPLIT_COMPOSITES No    SPLIT_MULTIS No    SET_FME_TYPE Yes    GEOMETRY_NAME_FIELD _geometry_name    OUTPUT { POINT FEATURE_TYPE ___TO_MULTITYPEFILTER___ }    OUTPUT { LINE FEATURE_TYPE ___TO_MULTITYPEFILTER___ }    OUTPUT { POLYGON FEATURE_TYPE ___TO_MULTITYPEFILTER___ }    OUTPUT { DONUT FEATURE_TYPE ___TO_MULTITYPEFILTER___ }    OUTPUT { AGGREGATE FEATURE_TYPE ___TO_MULTITYPEFILTER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME  { Snipper_MultiTypefilter }    INPUT FEATURE_TYPE ___TO_MULTITYPEFILTER___    TEST @Value(fme_type) == "fme_line"    TEST @Value(fme_type) == "fme_area"    BOOLEAN_OPERATOR OR    OUTPUT { PASSED FEATURE_TYPE ____DEAGGREGATE_MULTIS____ }
FACTORY_DEF {*} DeaggregateFactory    FACTORY_NAME { Snipper_DeaggMultis }    INPUT FEATURE_TYPE ____DEAGGREGATE_MULTIS____    RECURSIVE Yes    SPLIT_COMPOSITES No    SPLIT_MULTIS Yes    SET_FME_TYPE Yes    GEOMETRY_NAME_FIELD _geometry_name    OUTPUT { LINE FEATURE_TYPE ___DEAGG_NULLNUKER___          $(Snipper_snipFunc) }    OUTPUT { POLYGON FEATURE_TYPE ___DEAGG_NULLNUKER___          $(Snipper_snipFunc) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Snipper_DeaggNullNuker }    INPUT FEATURE_TYPE ___DEAGG_NULLNUKER___    TEST @Value(fme_geometry) == "fme_undefined"    TEST @Value(fme_type) == "fme_no_geom"    BOOLEAN_OPERATOR AND    OUTPUT { FAILED FEATURE_TYPE ____TO_DEAGGREGATOR____ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Snipper_Input }    INPUT FEATURE_TYPE ___TO_INPUT___    OUTPUT { FEATURE_TYPE ____TO_DEAGGREGATOR____         $(Snipper_snipFunc) }
FACTORY_DEF {*} DeaggregateFactory    FACTORY_NAME { Snipper_SplitRemnants }    INPUT FEATURE_TYPE ____TO_DEAGGREGATOR____    RECURSIVE No    SPLIT_COMPOSITES No    SET_FME_TYPE Yes    PART_NUMBER_FIELD _part_number    GEOMETRY_NAME_FIELD _geometry_name    OUTPUT { POINT FEATURE_TYPE ___TO_NULLNUKER___ }    OUTPUT { LINE FEATURE_TYPE ___TO_NULLNUKER___ }    OUTPUT { POLYGON FEATURE_TYPE ___TO_NULLNUKER___ }    OUTPUT { DONUT FEATURE_TYPE ___TO_NULLNUKER___ }    OUTPUT { AGGREGATE FEATURE_TYPE ___TO_NULLNUKER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Snipper_NullNuker }    INPUT FEATURE_TYPE ___TO_NULLNUKER___    TEST @Value(_part_number) == 0    TEST @NumCoords() == 0    BOOLEAN_OPERATOR AND    OUTPUT { FAILED FEATURE_TYPE ___TO_OUTPUTTER___ }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Snipper_Outputter }    INPUT FEATURE_TYPE ___TO_OUTPUTTER___    TEST @Value(_part_number) == 0    TEST @Tcl2("FME_AttributeExists _part_number") == 0    BOOLEAN_OPERATOR OR    OUTPUT { FAILED FEATURE_TYPE Snipper_REMNANTS       @RemoveAttributes(_part_number)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_3 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Global }    DOMAIN { "counter" }    COUNT_ATTR { "<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Snipper_REMNANTS    OUTPUT { OUTPUT FEATURE_TYPE Counter_3_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_3_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker INPUT Input Collector"   INPUT FEATURE_TYPE Counter_3_OUTPUT   OUTPUT FEATURE_TYPE DistanceMarker_INPUT
MACRO DistanceMarker_WORKSPACE_NAME DistanceMarker
MACRO $(DistanceMarker_WORKSPACE_NAME)_TRANSFORMER_GROUP 
MACRO $(DistanceMarker_WORKSPACE_NAME)_XFORMER_NAME DistanceMarker
MACRO $(DistanceMarker_WORKSPACE_NAME)___COMPOUND_PARAMETERS 
MACRO $(DistanceMarker_WORKSPACE_NAME)_MarkerInterval $(MarkerInterval)
MACRO $(DistanceMarker_WORKSPACE_NAME)_BegorEndpoints Yes
MACRO $(DistanceMarker_WORKSPACE_NAME)_DEAG_YES_NO Yes
INCLUDE_CUSTOM_TRANSFORMER_VER DistanceMarker:1
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker OUTPUT Output Renamer/Nuker"   INPUT FEATURE_TYPE DistanceMarker_OUTPUT   OUTPUT FEATURE_TYPE DistanceMarker_OUTPUT
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker <Rejected> Output Renamer/Nuker"   INPUT FEATURE_TYPE DistanceMarker_<Rejected>
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE DistanceMarker_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "point_<u4e2d><u5fc3><u7ebf><u6298><u70b9>" "SET_TO" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_2 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE AttributeCreator_2_OUTPUT    OUTPUT { OUTPUT FEATURE_TYPE Counter_2_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_2_<REJECTED>        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Counter_2 OUTPUT Splitter"   INPUT FEATURE_TYPE Counter_2_OUTPUT   OUTPUT FEATURE_TYPE Counter_2_OUTPUT_0_6BtuCrDson4=   OUTPUT FEATURE_TYPE Counter_2_OUTPUT_1_rlbO72IVDeA=
# -------------------------------------------------------------------------
INCLUDE [    set supplyXFunc {};    if { {<Unused>} != {} }    {       set supplyXFunc {@Coordinate(REJECTABLE_WITH_FLAG,x,"<Unused>",FLATTEN_AGGREGATE,YES)};    };    puts "MACRO CoordinateExtractor_2_SUPPLY_X ${supplyXFunc}"; ]
INCLUDE [    set supplyYFunc {};    if { {<Unused>} != {} }    {       set supplyYFunc {@Coordinate(REJECTABLE_WITH_FLAG,y,"<Unused>",FLATTEN_AGGREGATE,NO)};    };    puts "MACRO CoordinateExtractor_2_SUPPLY_Y ${supplyYFunc}"; ]
INCLUDE [    set supplyZDefaultFunc {};    if { {} != {} }    {       set supplyZDefaultFunc {@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING,"<Unused>",,FLOAT)};    };    puts "MACRO CoordinateExtractor_2_SUPPLY_Z_DEFAULT ${supplyZDefaultFunc}"; ]
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_TESTZDEFAULT }    INPUT  FEATURE_TYPE Counter_2_OUTPUT_0_6BtuCrDson4=    TEST { "" = "" ENCODED }    TEST { "" TYPE NUM ENCODED }    BOOLEAN_OPERATOR OR    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_TESTMODE_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_TESTMODE }    INPUT { FEATURE_TYPE CoordinateExtractor_2_TESTMODE_INPUT }    TEST { "All Coordinates" == "All Coordinates" }    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_LIST_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_LIST }    INPUT { FEATURE_TYPE CoordinateExtractor_2_LIST_INPUT }    TEST @Dimension() == 2    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_OUTPUT         @ZValue("")         @Coordinate(x,ALL,"中心线折点xy"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"中心线折点xy"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"中心线折点xy"{}.z,FLATTEN_AGGREGATE,NO)         @Dimension(2)          }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_OUTPUT         @Coordinate(x,ALL,"中心线折点xy"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"中心线折点xy"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"中心线折点xy"{}.z,FLATTEN_AGGREGATE,NO)          }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_INPUT }    TEST { "<Unused>" TYPE INT ENCODED }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_X_INPUT }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_X }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_X_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_2_SUPPLY_X) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Y_INPUT }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_Y }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Y_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_2_SUPPLY_Y) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_ROUTER_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_Z_ROUTER }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_ROUTER_INPUT }    TEST { "<Unused>" != "" ENCODED }    TEST @Dimension() == 3    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_OUTPUT       $(CoordinateExtractor_2_SUPPLY_Z_DEFAULT)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_Z }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_INPUT }    FUNCTION_DEFINITION { @Coordinate(REJECTABLE_WITH_FLAG,z,"<Unused>",FLATTEN_AGGREGATE,NO) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder }    INPUT  FEATURE_TYPE CoordinateExtractor_2_OUTPUT    LIST_NAME { "中心线折点xy{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE ListExploder_ELEMENTS    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>x" "SET_TO" "<at>Value<openparen>x<closeparen>" "real64" }      ATTR_ACTION { "" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>y" "SET_TO" "<at>Value<openparen>y<closeparen>" "real64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper }    INPUT  FEATURE_TYPE AttributeCreator_3_OUTPUT    KEEP_ATTRS { <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,<u4e2d><u5fc3><u7ebf><u6298><u70b9>x,<u4e2d><u5fc3><u7ebf><u6298><u70b9>y,point_<u4e2d><u5fc3><u7ebf><u6298><u70b9>,<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_OUTPUT        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AttributeKeeper OUTPUT Splitter"   INPUT FEATURE_TYPE AttributeKeeper_OUTPUT   OUTPUT FEATURE_TYPE AttributeKeeper_OUTPUT_0_rpABwRdi1ws=   OUTPUT FEATURE_TYPE AttributeKeeper_OUTPUT_1_ZImEmLS5GM8=   OUTPUT FEATURE_TYPE AttributeKeeper_OUTPUT_2_TmNRcQ2YPyY=
# -------------------------------------------------------------------------
# Assumption is that the workspace temp dir has been already set and created.  See controldefs.cpp - ControlDefs::writeVisualizerTempDir
DEFAULT_MACRO WORKSPACE_TEMP_DIR
INCLUDE [    set safeName "[regsub -all {[^a-zA-Z0-9]} {AttributeKeeper_Output} _]_[expr round(rand() * 1000000)]_[clock clicks  -milliseconds]";    puts "MACRO SAFE_FFS_NAME $safeName"; ]
FACTORY_DEF {*} InspectorFactory    FACTORY_NAME { AttributeKeeper_Output_Prepper }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeKeeper_OUTPUT_2_TmNRcQ2YPyY=    GROUP_ATTRIBUTE __inspector_feature_type_attr__    GROUP_ATTRIBUTE_PREFIX { AttributeKeeper_Output }    RASTER_REDUCTION { NoReduction }    POINT_CLOUD_REDUCTION { NO_THINNING }    OUTPUT { RESULT FEATURE_TYPE __viewme__       }
# Now route all the features into the recorder, changing their
# feature type to the transformer name so that they view nicely
DEFAULT_MACRO VISUALIZER_FEATURE_FILE $(WORKSPACE_TEMP_DIR)/$(SAFE_FFS_NAME).ffs
DEFAULT_MACRO VISUALIZER_CREATE_SPATIAL_INDEX NO
# [PR#45549] The Data Inspector will limit the maximum number of features
# it reads on WIN32, so we will correspondingly limit the number of
# features we record here using the MAX_FEATURES_TO_RECORD directive.
FACTORY_DEF {*} RecorderFactory    FACTORY_NAME { AttributeKeeper_Output_Recorder }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT FEATURE_TYPE __viewme__       _wb_termination_feature Yes       @FeatureType(TERMINATOR_@Value(_wb_termination_xformer))    INPUT FEATURE_TYPE __viewme__       @FeatureType(@Value(__inspector_feature_type_attr__))       @RemoveAttributes(__inspector_feature_type_attr__)    FEATURE_FILE { "$(VISUALIZER_FEATURE_FILE)" }    STORE_SCANNED_SCHEMA YES    NORMALIZE_LIST_ATTRIBUTES_ON_SCHEMA YES    CREATE_SPATIAL_INDEX { "$(VISUALIZER_CREATE_SPATIAL_INDEX)" }    INSPECTOR { "$(VISUALIZER_CREATE_SPATIAL_INDEX)" }    FSC_SCHEMA { <u4e2d><u5fc3><u7ebf><u6298><u70b9>x,real64,<u4e2d><u5fc3><u7ebf><u6298><u70b9>y,real64,point_<u4e2d><u5fc3><u7ebf><u6298><u70b9>,buffer,<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7>,int64,<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,int64 }    MODE RECORD    STORE_SCANNED_METADATA    RECORD_DIRECTLY_TO_DISK YES    MAX_FEATURES_TO_RECORD WIN32 2000000
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_2_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(file_2$encode),FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Creator_CREATED_1_Exx7idfjvCA=    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_2_<OTHER>           }
# -------------------------------------------------------------------------
FACTORY_DEF {*} GeometryFilterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { GeometryFilter_2 }    INPUT  FEATURE_TYPE FeatureReader_2_<OTHER>    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    FILTER_TYPES { Point,Area }    INSTANCES { INSTANTIATE }    BREAK_AGG { Yes }    OUTPUT { <UNFILTERED> FEATURE_TYPE GeometryFilter_2_<UNFILTERED>  }    OUTPUT { Point FEATURE_TYPE GeometryFilter_2_Point  }    OUTPUT { Area FEATURE_TYPE GeometryFilter_2_Area  }
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter_2 Area Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_2_Area
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter_2 <UNFILTERED> Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_2_<UNFILTERED>
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector_2 }    INPUT  FEATURE_TYPE GeometryFilter_2_Point    OUTPUT { FEATURE_TYPE Reprojector_2_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector_2,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u9608><u503c><u5185><u5e8f><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Reprojector_2_REPROJECTED    OUTPUT { OUTPUT FEATURE_TYPE Counter_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Counter_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "point_<u9608><u503c><u5185>" "SET_TO" "<u9608><u503c><u5185>" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    set supplyXFunc {};    if { {<Unused>} != {} }    {       set supplyXFunc {@Coordinate(REJECTABLE_WITH_FLAG,x,"<Unused>",FLATTEN_AGGREGATE,YES)};    };    puts "MACRO CoordinateExtractor_SUPPLY_X ${supplyXFunc}"; ]
INCLUDE [    set supplyYFunc {};    if { {<Unused>} != {} }    {       set supplyYFunc {@Coordinate(REJECTABLE_WITH_FLAG,y,"<Unused>",FLATTEN_AGGREGATE,NO)};    };    puts "MACRO CoordinateExtractor_SUPPLY_Y ${supplyYFunc}"; ]
INCLUDE [    set supplyZDefaultFunc {};    if { {} != {} }    {       set supplyZDefaultFunc {@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING,"<Unused>",,FLOAT)};    };    puts "MACRO CoordinateExtractor_SUPPLY_Z_DEFAULT ${supplyZDefaultFunc}"; ]
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_TESTZDEFAULT }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    TEST { "" = "" ENCODED }    TEST { "" TYPE NUM ENCODED }    BOOLEAN_OPERATOR OR    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_TESTMODE_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_TESTMODE }    INPUT { FEATURE_TYPE CoordinateExtractor_TESTMODE_INPUT }    TEST { "All Coordinates" == "All Coordinates" }    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_LIST_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_SPECIFIC_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_LIST }    INPUT { FEATURE_TYPE CoordinateExtractor_LIST_INPUT }    TEST @Dimension() == 2    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_OUTPUT         @ZValue("")         @Coordinate(x,ALL,"阈值内xy"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"阈值内xy"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"阈值内xy"{}.z,FLATTEN_AGGREGATE,NO)         @Dimension(2)          }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_OUTPUT         @Coordinate(x,ALL,"阈值内xy"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"阈值内xy"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"阈值内xy"{}.z,FLATTEN_AGGREGATE,NO)          }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_INPUT }    TEST { "<Unused>" TYPE INT ENCODED }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_SPECIFIC_X_INPUT }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_X }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_X_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_SUPPLY_X) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_SPECIFIC_Y_INPUT }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_Y }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_Y_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_SUPPLY_Y) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_ROUTER_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_Z_ROUTER }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_ROUTER_INPUT }    TEST { "<Unused>" != "" ENCODED }    TEST @Dimension() == 3    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_OUTPUT       $(CoordinateExtractor_SUPPLY_Z_DEFAULT)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_Z }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_INPUT }    FUNCTION_DEFINITION { @Coordinate(REJECTABLE_WITH_FLAG,z,"<Unused>",FLATTEN_AGGREGATE,NO) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder_2 }    INPUT  FEATURE_TYPE CoordinateExtractor_OUTPUT    LIST_NAME { "阈值内xy{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_2_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_4 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE ListExploder_2_ELEMENTS    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u9608><u503c><u5185>x" "SET_TO" "<at>Value<openparen>x<closeparen>" "real64" }      ATTR_ACTION { "" "<u9608><u503c><u5185>y" "SET_TO" "<at>Value<openparen>y<closeparen>" "real64" }      ATTR_ACTION { "" "<u9608><u503c><u5185>z" "SET_TO" "<at>Value<openparen>z<closeparen>" "real64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_4_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_2 }    INPUT  FEATURE_TYPE AttributeCreator_4_OUTPUT    KEEP_ATTRS { <u9608><u503c><u5185><u5e8f><u53f7>,<u9608><u503c><u5185>x,<u9608><u503c><u5185>y,point_<u9608><u503c><u5185>,<u9608><u503c><u5185>z }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_2_OUTPUT        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AttributeKeeper_2 OUTPUT Splitter"   INPUT FEATURE_TYPE AttributeKeeper_2_OUTPUT   OUTPUT FEATURE_TYPE AttributeKeeper_2_OUTPUT_0_muAz4GqwTdk=   OUTPUT FEATURE_TYPE AttributeKeeper_2_OUTPUT_1_eKOY+pR0mH0=
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { PointOnPointOverlayer }    INPUT POINT FEATURE_TYPE AttributeKeeper_OUTPUT_0_rpABwRdi1ws=    INPUT POINT FEATURE_TYPE AttributeKeeper_2_OUTPUT_0_muAz4GqwTdk=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    TOLERANCE { $(TOLERANCE) }    DEAGGREGATE_INPUT { Yes }    MERGE_ATTRS { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    INCOMING_PREFIX { "<Unused>" }    LIST_NAME { "点" }    LIST_ATTRS_TO_INCLUDE { <u9608><u503c><u5185>x <u9608><u503c><u5185>y <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7> <u4e2d><u5fc3><u7ebf><u6298><u70b9>x <u4e2d><u5fc3><u7ebf><u6298><u70b9>y point_<u9608><u503c><u5185> point_<u4e2d><u5fc3><u7ebf><u6298><u70b9> <u9608><u503c><u5185>z <u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7> }    LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    OVERLAP_COUNT_ATTRIBUTE { "_overlaps" }    OVERLAY_TYPE POINT_ON_POINT    OUTPUT { POINT    FEATURE_TYPE PointOnPointOverlayer_POINT         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_7 }    INPUT  FEATURE_TYPE PointOnPointOverlayer_POINT    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>_overlaps<closeparen>,Tester_7)" != 0 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_7_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder_5 }    INPUT  FEATURE_TYPE Tester_7_PASSED    LIST_NAME { "点{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_5_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_4 }    INPUT  FEATURE_TYPE ListExploder_5_ELEMENTS    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u9608><u503c><u5185>z<closeparen>,Tester_4)" != "" ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_4_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_5 }    INPUT  FEATURE_TYPE Tester_4_PASSED    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>point_<u4e2d><u5fc3><u7ebf><u6298><u70b9><closeparen>,Tester_5)" != "" ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_5_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_8 }    INPUT  FEATURE_TYPE Tester_5_PASSED    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u9608><u503c><u5185><u5e8f><u53f7><closeparen>,Tester_8)" = "" ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_8_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_5 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Tester_8_PASSED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u8ddd><u79bb>" "SET_TO" "<at>Evaluate<openparen><at>sqrt<openparen><at>abs<openparen><openparen><at>Value<openparen><u9608><u503c><u5185>x<closeparen>-<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9>x<closeparen><closeparen>*<openparen><at>Value<openparen><u9608><u503c><u5185>x<closeparen>-<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9>x<closeparen><closeparen>-<openparen><at>Value<openparen><u9608><u503c><u5185>y<closeparen>-<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9>y<closeparen><closeparen>*<openparen><at>Value<openparen><u9608><u503c><u5185>y<closeparen>-<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9>y<closeparen><closeparen><closeparen><closeparen><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_5_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter }    INPUT  FEATURE_TYPE AttributeCreator_5_OUTPUT    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    SORT_BY { <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7> NATURAL ASCENDING <u8ddd><u79bb> NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_SORTED          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE Sorter_SORTED    KEY_ATTRIBUTES { <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7> }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { AttributeManager }    INPUT  FEATURE_TYPE DuplicateFilter_UNIQUE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "<u8ddd><u79bb>" "<u8ddd><u79bb>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u4e2d><u5fc3><u7ebf><u6298><u70b9>x" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>x" "<at>Value<openparen><u9608><u503c><u5185>x<closeparen>" "real64" "SET_TO" }      ATTR_ACTION { "<u4e2d><u5fc3><u7ebf><u6298><u70b9>y" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>y" "<at>Value<openparen><u9608><u503c><u5185>y<closeparen>" "real64" "SET_TO" }      ATTR_ACTION { "" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>z" "<at>Value<openparen><u9608><u503c><u5185>z<closeparen>" "real64" "SET_TO" }      ATTR_ACTION { "point_<u4e2d><u5fc3><u7ebf><u6298><u70b9>" "point_<u4e2d><u5fc3><u7ebf><u6298><u70b9>" "" "buffer" "NO_OP" }      ATTR_ACTION { "<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>" "<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>" "" "int64" "NO_OP" }      ATTR_ACTION { "<u9608><u503c><u5185>x" "<u9608><u503c><u5185>x" "" "real64" "NO_OP" }      ATTR_ACTION { "<u9608><u503c><u5185>y" "<u9608><u503c><u5185>y" "" "real64" "NO_OP" }      ATTR_ACTION { "<u9608><u503c><u5185>z" "<u9608><u503c><u5185>z" "" "real64" "NO_OP" }      ATTR_ACTION { "point_<u9608><u503c><u5185>" "point_<u9608><u503c><u5185>" "" "buffer" "NO_OP" }      ATTR_ACTION { "<u9608><u503c><u5185><u5e8f><u53f7>" "<u9608><u503c><u5185><u5e8f><u53f7>" "" "int64" "NO_OP" }      ATTR_ACTION { "_overlaps" "_overlaps" "" "uint32" "NO_OP" }      ATTR_ACTION { "_element_index" "_element_index" "" "uint32" "NO_OP" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeManager_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_3 }    INPUT  FEATURE_TYPE AttributeManager_OUTPUT    KEEP_ATTRS { <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,<u4e2d><u5fc3><u7ebf><u6298><u70b9>x,<u4e2d><u5fc3><u7ebf><u6298><u70b9>y,<u4e2d><u5fc3><u7ebf><u6298><u70b9>z,<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE AttributeKeeper_3_OUTPUT    KEY_ATTRIBUTES { <u4e2d><u5fc3><u7ebf><u6298><u70b9>x <u4e2d><u5fc3><u7ebf><u6298><u70b9>y }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_2_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} VertexCreatorFactory    FACTORY_NAME { VertexCreator }    INPUT  FEATURE_TYPE DuplicateFilter_2_UNIQUE    MODE { REPLACE }    INDEX { "<Unused>" }    CONTINUE_ON_ERROR YES    XVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9>x<closeparen>,VertexCreator)" }    YVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9>y<closeparen>,VertexCreator)" }    ZVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9>z<closeparen>,VertexCreator)" }    USE_EXISTING_Z YES    ALLOW_DUPLICATES { "<Unused>" }    CLOSE_LINES { "<Unused>" }    ADD_MODE_VERSION 5    MISSING_VAL_MODE { <Unused> }    COMPUTE_MEASURES_MODE { CONTINUOUS }    COMMAND_PARM_EVALUATION SINGLE_PASS    OUTPUT { OUTPUT FEATURE_TYPE VertexCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE VertexCreator_OUTPUT    TEST { 1 = 1 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Tester PASSED Splitter"   INPUT FEATURE_TYPE Tester_PASSED   OUTPUT FEATURE_TYPE Tester_PASSED_0_M1wqjkih4e0=   OUTPUT FEATURE_TYPE Tester_PASSED_1_8t6d0F2OYOU=   OUTPUT FEATURE_TYPE Tester_PASSED_2_EXStyIAekEM=   OUTPUT FEATURE_TYPE Tester_PASSED_3_gh4Am8DbgQI=
# -------------------------------------------------------------------------
INCLUDE [if { {ATTRIBUTES} == {ATTRIBUTES} } {                puts "MACRO FeatureMerger_REFERENCE_INFO ATTRIBUTES";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {POLYGONS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_POLYS";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {AGGREGATES}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {LINESFROMPOINTS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_LINES_FROM_POINTS";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {POLYGONS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_POLYS";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {AGGREGATES}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {LINESFROMPOINTS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_LINES_FROM_POINTS";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} } {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} } {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_AGGREGATES";             }          else {}; ]
FACTORY_DEF {*} ReferenceFactory    FACTORY_NAME { FeatureMerger }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    INPUT REFERENCER FEATURE_TYPE Tester_PASSED_0_M1wqjkih4e0=    INPUT REFERENCEE FEATURE_TYPE AttributeKeeper_OUTPUT_1_ZImEmLS5GM8=    REFERENCE_INFO { $(FeatureMerger_REFERENCE_INFO) }    REFERENCE_TABLE { @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7><closeparen>,FeatureMerger) @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7><closeparen>,FeatureMerger) AUTO }    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "REQUESTOR_IF_CONFLICT" }    IGNORE_NULLS { "No" }    HANDLE_NULL_MISSING_KEYS_LIKE_FME2013 { No }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    MERGE_ATTRIBUTES Yes    MANAGE_FME_TYPE Yes    MODE COMPLETE    PROCESS_DUPLICATE_REFERENCEES { NO }    REFERENCEES_FIRST { No }    REJECT_INVALID_GEOM YES    CLEANING_TOLERANCE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    COMPARE_WHITESPACE Yes    OUTPUT { UNREFERENCED FEATURE_TYPE FeatureMerger_UNUSED_SUPPLIER         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE Tester_PASSED_1_8t6d0F2OYOU=    INPUT  FEATURE_TYPE FeatureMerger_UNUSED_SUPPLIER    TEST { 1 = 1 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter_2 }    INPUT  FEATURE_TYPE Tester_2_PASSED    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    SORT_BY { <u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7> NATURAL ASCENDING <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7> NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_2_SORTED          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ConnectionFactory     FACTORY_NAME { LineBuilder }     INPUT  FEATURE_TYPE Sorter_2_SORTED     GROUP_BY { 大线段范围序号 }     FLUSH_WHEN_GROUPS_CHANGE { No }     ACCUM_INPUT_ATTRS { One }     LIST_ATTRS_TO_INCLUDE { <Unused> }     LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }     REMOVE_DUPLICATES { NO }     OUTPUT { LINE      FEATURE_TYPE LineBuilder_LINE           }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { LineOnLineOverlayer }    INPUT LINE FEATURE_TYPE LineBuilder_LINE    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    OVERLAP_COUNT_ATTRIBUTE { "_overlaps" }    ACCUMULATE_ATTRIBUTES { "ONE" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    MODE COMPLETE    OVERLAY_TYPE LINE_ON_LINE    LINE_ON_LINE_SELF_INTERSECTION    DEAGGREGATE_INPUT { Yes }    SEPARATE_COLLINEAR_SEGMENTS { No }    CLEANING_TOLERANCE { AUTO }    OUTPUT_REMNANTS    OUTPUT { LINE     FEATURE_TYPE LineOnLineOverlayer_LINE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} NetworkFactory    FACTORY_NAME { NetworkTopologyCalculator }    INPUT LINE FEATURE_TYPE LineOnLineOverlayer_LINE    REJECT_INVALID_GEOM Yes    COMPUTE_NETWORK_TOPO YES    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    NETWORK_ID_ATTR { "_network_id" }    OUTPUT { NETWORK FEATURE_TYPE NetworkTopologyCalculator_NETWORK         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "NetworkTopologyCalculator NETWORK Splitter"   INPUT FEATURE_TYPE NetworkTopologyCalculator_NETWORK   OUTPUT FEATURE_TYPE NetworkTopologyCalculator_NETWORK_0_aWkt8MY42aU=   OUTPUT FEATURE_TYPE NetworkTopologyCalculator_NETWORK_1_u5UBBT2JD9s=
# -------------------------------------------------------------------------
FACTORY_DEF {*} AggregateFactory    FACTORY_NAME { Aggregator_2 }    INPUT  FEATURE_TYPE NetworkTopologyCalculator_NETWORK_0_aWkt8MY42aU=    MODE { ONE_LEVEL }    GROUP_BY { _network_id }    FLUSH_WHEN_GROUPS_CHANGE { No }    REMOVE_GEOMETRY { NO }    ACCUMULATE_ATTRIBUTES { None }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    SEPARATOR { <comma> }    PRODUCE_MULTIS { YES }    ALL_ATTRS_LIST_GROUP_BY_MODE ADD    OUTPUT { AGGREGATE FEATURE_TYPE Aggregator_2_AGGREGATE          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_5 }    INPUT  FEATURE_TYPE NetworkTopologyCalculator_NETWORK_1_u5UBBT2JD9s=    KEEP_ATTRS {  }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { No }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_5_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter_3 }    INPUT  FEATURE_TYPE Tester_PASSED_2_EXStyIAekEM=    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    SORT_BY { <u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7> NATURAL ASCENDING <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7> NATURAL ASCENDING }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Junction }    INPUT  FEATURE_TYPE Tester_PASSED_3_gh4Am8DbgQI=    OUTPUT { FEATURE_TYPE Junction_Output         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_4 }    INPUT  FEATURE_TYPE Junction_Output    KEEP_ATTRS { <u4e2d><u5fc3><u7ebf><u6298><u70b9>z }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_4_OUTPUT        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AttributeKeeper_4 OUTPUT Splitter"   INPUT FEATURE_TYPE AttributeKeeper_4_OUTPUT   OUTPUT FEATURE_TYPE AttributeKeeper_4_OUTPUT_0_jy5SkD4CpT4=   OUTPUT FEATURE_TYPE AttributeKeeper_4_OUTPUT_1_pp4hXAU/r7M=
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { PointOnLineOverlayer }    INPUT LINE FEATURE_TYPE AttributeKeeper_5_OUTPUT    INPUT POINT FEATURE_TYPE AttributeKeeper_4_OUTPUT_0_jy5SkD4CpT4=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    TOLERANCE { 0.000001 }    OVERLAP_COUNT_ATTRIBUTE { "_overlaps" }    DEAGGREGATE_INPUT { Yes }    MERGE_ATTRS { "NO" }    MERGE_MEASURES { Yes }    MEASURE_TYPE { Continuous }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    INCOMING_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    CANDIDATE_LIST_NAME { "高程点" }    CANDIDATE_LIST_ATTRS_TO_INCLUDE { <u4e2d><u5fc3><u7ebf><u6298><u70b9>z }    CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    MODE COMPLETE    OVERLAY_TYPE POINT_ON_LINE    OUTPUT { LINE     FEATURE_TYPE PointOnLineOverlayer_LINE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_6 }    INPUT  FEATURE_TYPE PointOnLineOverlayer_LINE    KEEP_ATTRS {  }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { No }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_6_OUTPUT        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AttributeKeeper_6 OUTPUT Splitter"   INPUT FEATURE_TYPE AttributeKeeper_6_OUTPUT   OUTPUT FEATURE_TYPE AttributeKeeper_6_OUTPUT_0_hhnydhW1mFs=   OUTPUT FEATURE_TYPE AttributeKeeper_6_OUTPUT_1_r5q+zB4dbgA=
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Junction_3 }    INPUT  FEATURE_TYPE AttributeKeeper_6_OUTPUT_0_hhnydhW1mFs=    OUTPUT { FEATURE_TYPE Junction_3_Output         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Junction_3 Output Splitter"   INPUT FEATURE_TYPE Junction_3_Output   OUTPUT FEATURE_TYPE Junction_3_Output_0_YXOQ6dTB2Y4=   OUTPUT FEATURE_TYPE Junction_3_Output_1_fk258e3GCLQ=
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { LineOnLineOverlayer_2 }    INPUT LINE FEATURE_TYPE AttributeKeeper_6_OUTPUT_1_r5q+zB4dbgA=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    OVERLAP_COUNT_ATTRIBUTE { "_overlaps" }    ACCUMULATE_ATTRIBUTES { "ONE" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    MODE COMPLETE    OVERLAY_TYPE LINE_ON_LINE    LINE_ON_LINE_SELF_INTERSECTION    DEAGGREGATE_INPUT { Yes }    SEPARATE_COLLINEAR_SEGMENTS { No }    CLEANING_TOLERANCE { AUTO }    OUTPUT_REMNANTS    OUTPUT { POINT    FEATURE_TYPE LineOnLineOverlayer_2_POINT         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_3 }    INPUT  FEATURE_TYPE LineOnLineOverlayer_2_POINT    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>_overlaps<closeparen>,Tester_3)" >= 3 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_3_PASSED         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Tester_3 PASSED Splitter"   INPUT FEATURE_TYPE Tester_3_PASSED   OUTPUT FEATURE_TYPE Tester_3_PASSED_0_Yr8lcIT7MIQ=   OUTPUT FEATURE_TYPE Tester_3_PASSED_1_sQ/Bfv8obsI=
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_6 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Tester_3_PASSED_0_Yr8lcIT7MIQ=    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>z" "SET_TO" "0" "uint64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_6_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Junction_4 }    INPUT  FEATURE_TYPE AttributeKeeper_4_OUTPUT_1_pp4hXAU/r7M=    OUTPUT { FEATURE_TYPE Junction_4_Output         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Junction_4 Output Splitter"   INPUT FEATURE_TYPE Junction_4_Output   OUTPUT FEATURE_TYPE Junction_4_Output_0_4BjyOFNFnMw=   OUTPUT FEATURE_TYPE Junction_4_Output_1_hv2PJraAnxk=
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Junction_2 }    INPUT  FEATURE_TYPE AttributeCreator_6_OUTPUT    INPUT  FEATURE_TYPE Junction_4_Output_0_4BjyOFNFnMw=    OUTPUT { FEATURE_TYPE Junction_2_Output         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { PointOnLineOverlayer_2 }    INPUT LINE FEATURE_TYPE Junction_3_Output_0_YXOQ6dTB2Y4=    INPUT POINT FEATURE_TYPE Junction_2_Output    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    TOLERANCE { 0.0001 }    OVERLAP_COUNT_ATTRIBUTE { "_overlaps" }    DEAGGREGATE_INPUT { Yes }    MERGE_ATTRS { "NO" }    MERGE_MEASURES { Yes }    MEASURE_TYPE { Continuous }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    INCOMING_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    CANDIDATE_LIST_NAME { "Z" }    CANDIDATE_LIST_ATTRS_TO_INCLUDE { <u4e2d><u5fc3><u7ebf><u6298><u70b9>z }    CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    MODE COMPLETE    OVERLAY_TYPE POINT_ON_LINE    OUTPUT { LINE     FEATURE_TYPE PointOnLineOverlayer_2_LINE         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "PointOnLineOverlayer_2 LINE Splitter"   INPUT FEATURE_TYPE PointOnLineOverlayer_2_LINE   OUTPUT FEATURE_TYPE PointOnLineOverlayer_2_LINE_0_vItoyOse+Ew=   OUTPUT FEATURE_TYPE PointOnLineOverlayer_2_LINE_1_diChHra66Tk=
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_9 }    INPUT  FEATURE_TYPE PointOnLineOverlayer_2_LINE_0_vItoyOse+Ew=    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>_overlaps<closeparen>,Tester_9)" = 1 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_9_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_5 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Global }    DOMAIN { "counter" }    COUNT_ATTR { "<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Tester_9_FAILED    OUTPUT { OUTPUT FEATURE_TYPE Counter_5_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_5_<REJECTED>        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Counter_5 OUTPUT Splitter"   INPUT FEATURE_TYPE Counter_5_OUTPUT   OUTPUT FEATURE_TYPE Counter_5_OUTPUT_0_3rq3s096lyU=   OUTPUT FEATURE_TYPE Counter_5_OUTPUT_1_PZdXF1jBFUQ=
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker_2 INPUT Input Collector"   INPUT FEATURE_TYPE Counter_5_OUTPUT_0_3rq3s096lyU=   OUTPUT FEATURE_TYPE DistanceMarker_2_INPUT
MACRO DistanceMarker_WORKSPACE_NAME DistanceMarker_2
MACRO $(DistanceMarker_WORKSPACE_NAME)_TRANSFORMER_GROUP 
MACRO $(DistanceMarker_WORKSPACE_NAME)_XFORMER_NAME DistanceMarker_2
MACRO $(DistanceMarker_WORKSPACE_NAME)___COMPOUND_PARAMETERS 
MACRO $(DistanceMarker_WORKSPACE_NAME)_MarkerInterval $(MarkerInterval)
MACRO $(DistanceMarker_WORKSPACE_NAME)_BegorEndpoints Yes
MACRO $(DistanceMarker_WORKSPACE_NAME)_DEAG_YES_NO Yes
INCLUDE_CUSTOM_TRANSFORMER_VER DistanceMarker:1
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker_2 OUTPUT Output Renamer/Nuker"   INPUT FEATURE_TYPE DistanceMarker_2_OUTPUT   OUTPUT FEATURE_TYPE DistanceMarker_2_OUTPUT
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker_2 <Rejected> Output Renamer/Nuker"   INPUT FEATURE_TYPE DistanceMarker_2_<Rejected>
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_4 }    GROUP_BY { 大线段范围序号 }    FLUSH_WHEN_GROUPS_CHANGE { No }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE DistanceMarker_2_OUTPUT    OUTPUT { OUTPUT FEATURE_TYPE Counter_4_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_4_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_7 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Counter_4_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u9ad8><u7a0b>min" "SET_TO" "<at>Value<openparen>Z<opencurly>0<closecurly>.<u4e2d><u5fc3><u7ebf><u6298><u70b9>z<closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u9ad8><u7a0b>max" "SET_TO" "<at>Value<openparen>Z<opencurly>1<closecurly>.<u4e2d><u5fc3><u7ebf><u6298><u70b9>z<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_7_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator }    INPUT  FEATURE_TYPE AttributeCreator_7_OUTPUT    GROUP_BY { 大线段范围序号 }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,NUMERIC_MODE,,MAX,,,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { <u9ad8><u7a0b>min,varchar<openparen>200<closeparen>,<u9ad8><u7a0b>max,varchar<openparen>200<closeparen>,_overlaps,uint32,<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7>,int64,<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,int64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { COMPLETE FEATURE_TYPE StatisticsCalculator_COMPLETE        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_8 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_COMPLETE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u9ad8><u7a0b>" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'<at>Evaluate<openparen><openparen><at>Value<openparen><u9ad8><u7a0b>max<closeparen>-<at>Value<openparen><u9ad8><u7a0b>min<closeparen><closeparen><solidus><at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>.max<closeparen>*<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7><closeparen>+<at>Value<openparen><u9ad8><u7a0b>min<closeparen><closeparen>'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7><closeparen> = 0'<at>Value<openparen><u9ad8><u7a0b>min<closeparen>'FME_NUM_CONDITIONS2___" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_8_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_9 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Counter_5_OUTPUT_1_PZdXF1jBFUQ=    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u9ad8><u7a0b>min" "SET_TO" "<at>Value<openparen>Z<opencurly>0<closecurly>.<u4e2d><u5fc3><u7ebf><u6298><u70b9>z<closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u9ad8><u7a0b>max" "SET_TO" "<at>Value<openparen>Z<opencurly>1<closecurly>.<u4e2d><u5fc3><u7ebf><u6298><u70b9>z<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_9_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { PointOnLineOverlayer_3 }    INPUT LINE FEATURE_TYPE AttributeCreator_9_OUTPUT    INPUT POINT FEATURE_TYPE Tester_3_PASSED_1_sQ/Bfv8obsI=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    TOLERANCE { 0.00001 }    OVERLAP_COUNT_ATTRIBUTE { "_overlaps" }    DEAGGREGATE_INPUT { Yes }    MERGE_ATTRS { "NO" }    MERGE_MEASURES { Yes }    MEASURE_TYPE { Continuous }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    INCOMING_PREFIX { "<Unused>" }    LIST_NAME { "折点高程" }    LIST_ATTRS_TO_INCLUDE { <u9ad8><u7a0b>max <u9ad8><u7a0b>min }    LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    CANDIDATE_LIST_NAME { "<Unused>" }    CANDIDATE_LIST_ATTRS_TO_INCLUDE { <Unused> }    CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    MODE COMPLETE    OVERLAY_TYPE POINT_ON_LINE    OUTPUT { POINT    FEATURE_TYPE PointOnLineOverlayer_3_POINT         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_6 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "_count" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE PointOnLineOverlayer_3_POINT    OUTPUT { OUTPUT FEATURE_TYPE Counter_6_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_6_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder_3 }    INPUT  FEATURE_TYPE Counter_6_OUTPUT    LIST_NAME { "折点高程{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_3_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_13 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE ListExploder_3_ELEMENTS    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u5468><u56f4><u9ad8><u7a0b>" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'0'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Value<openparen><u9ad8><u7a0b>max<closeparen> != _FME_BLANK_STRING_'<at>Value<openparen><u9ad8><u7a0b>max<closeparen>'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Value<openparen><u9ad8><u7a0b>min<closeparen> != _FME_BLANK_STRING_'<at>Value<openparen><u9ad8><u7a0b>min<closeparen>'FME_NUM_CONDITIONS3___" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_13_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator_2 }    INPUT  FEATURE_TYPE AttributeCreator_13_OUTPUT    GROUP_BY { _count }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { <u5468><u56f4><u9ad8><u7a0b>,NUMERIC_MODE,,,,,MEAN,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { <u5468><u56f4><u9ad8><u7a0b>,varchar<openparen>200<closeparen>,_overlaps,uint32,<u9ad8><u7a0b>min,varchar<openparen>200<closeparen>,<u9ad8><u7a0b>max,varchar<openparen>200<closeparen>,_count,int64,_element_index,uint32 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { COMPLETE FEATURE_TYPE StatisticsCalculator_2_COMPLETE        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE StatisticsCalculator_2_COMPLETE    KEY_ATTRIBUTES { _count }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_3_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_10 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE DuplicateFilter_3_UNIQUE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4e2d><u5fc3><u7ebf><u6298><u70b9>z" "SET_TO" "<at>Value<openparen><u5468><u56f4><u9ad8><u7a0b>.mean<closeparen>" "real64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_10_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_10 }    INPUT  FEATURE_TYPE AttributeCreator_8_OUTPUT    INPUT  FEATURE_TYPE PointOnLineOverlayer_2_LINE_1_diChHra66Tk=    TEST { 1 = 1 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_10_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_7 }    INPUT  FEATURE_TYPE Tester_10_PASSED    KEEP_ATTRS { <u9ad8><u7a0b>,<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7>,<u9ad8><u7a0b>max,<u9ad8><u7a0b>min }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_7_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_11 }    INPUT  FEATURE_TYPE AttributeKeeper_7_OUTPUT    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7><closeparen>,Tester_11)" = 2 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { PointOnLineOverlayer_4 }    INPUT LINE FEATURE_TYPE Junction_3_Output_1_fk258e3GCLQ=    INPUT POINT FEATURE_TYPE AttributeCreator_10_OUTPUT    INPUT POINT FEATURE_TYPE Junction_4_Output_1_hv2PJraAnxk=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    TOLERANCE { 0.0001 }    OVERLAP_COUNT_ATTRIBUTE { "_overlaps" }    DEAGGREGATE_INPUT { Yes }    MERGE_ATTRS { "NO" }    MERGE_MEASURES { Yes }    MEASURE_TYPE { Continuous }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    INCOMING_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    CANDIDATE_LIST_NAME { "Z" }    CANDIDATE_LIST_ATTRS_TO_INCLUDE { <u4e2d><u5fc3><u7ebf><u6298><u70b9>z }    CANDIDATE_LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    MODE COMPLETE    OVERLAY_TYPE POINT_ON_LINE    OUTPUT { LINE     FEATURE_TYPE PointOnLineOverlayer_4_LINE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_12 }    INPUT  FEATURE_TYPE PointOnLineOverlayer_4_LINE    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>_overlaps<closeparen>,Tester_12)" = 1 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_12_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_8 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Global }    DOMAIN { "counter" }    COUNT_ATTR { "<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Tester_12_FAILED    OUTPUT { OUTPUT FEATURE_TYPE Counter_8_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_8_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker_3 INPUT Input Collector"   INPUT FEATURE_TYPE Counter_8_OUTPUT   OUTPUT FEATURE_TYPE DistanceMarker_3_INPUT
MACRO DistanceMarker_WORKSPACE_NAME DistanceMarker_3
MACRO $(DistanceMarker_WORKSPACE_NAME)_TRANSFORMER_GROUP 
MACRO $(DistanceMarker_WORKSPACE_NAME)_XFORMER_NAME DistanceMarker_3
MACRO $(DistanceMarker_WORKSPACE_NAME)___COMPOUND_PARAMETERS 
MACRO $(DistanceMarker_WORKSPACE_NAME)_MarkerInterval $(MarkerInterval)
MACRO $(DistanceMarker_WORKSPACE_NAME)_BegorEndpoints Yes
MACRO $(DistanceMarker_WORKSPACE_NAME)_DEAG_YES_NO Yes
INCLUDE_CUSTOM_TRANSFORMER_VER DistanceMarker:1
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker_3 OUTPUT Output Renamer/Nuker"   INPUT FEATURE_TYPE DistanceMarker_3_OUTPUT   OUTPUT FEATURE_TYPE DistanceMarker_3_OUTPUT
FACTORY_DEF * TeeFactory   FACTORY_NAME "DistanceMarker_3 <Rejected> Output Renamer/Nuker"   INPUT FEATURE_TYPE DistanceMarker_3_<Rejected>
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_7 }    GROUP_BY { 大线段范围序号 }    FLUSH_WHEN_GROUPS_CHANGE { No }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE DistanceMarker_3_OUTPUT    OUTPUT { OUTPUT FEATURE_TYPE Counter_7_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_7_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_11 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Counter_7_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u9ad8><u7a0b>min" "SET_TO" "<at>Value<openparen>Z<opencurly>0<closecurly>.<u4e2d><u5fc3><u7ebf><u6298><u70b9>z<closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u9ad8><u7a0b>max" "SET_TO" "<at>Value<openparen>Z<opencurly>1<closecurly>.<u4e2d><u5fc3><u7ebf><u6298><u70b9>z<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_11_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator_3 }    INPUT  FEATURE_TYPE AttributeCreator_11_OUTPUT    GROUP_BY { 大线段范围序号 }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { <u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,NUMERIC_MODE,,MAX,,,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { <u9ad8><u7a0b>min,varchar<openparen>200<closeparen>,<u9ad8><u7a0b>max,varchar<openparen>200<closeparen>,_overlaps,uint32,<u5927><u7ebf><u6bb5><u8303><u56f4><u5e8f><u53f7>,int64,<u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>,int64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { COMPLETE FEATURE_TYPE StatisticsCalculator_3_COMPLETE        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_12 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_3_COMPLETE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u9ad8><u7a0b>" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'<at>Evaluate<openparen><openparen><at>Value<openparen><u9ad8><u7a0b>max<closeparen>-<at>Value<openparen><u9ad8><u7a0b>min<closeparen><closeparen><solidus><at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7>.max<closeparen>*<at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7><closeparen>+<at>Value<openparen><u9ad8><u7a0b>min<closeparen><closeparen>'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Value<openparen><u4e2d><u5fc3><u7ebf><u6298><u70b9><u5e8f><u53f7><closeparen> = 0'<at>Value<openparen><u9ad8><u7a0b>min<closeparen>'FME_NUM_CONDITIONS2___" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_12_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    set supplyXFunc {};    if { {<Unused>} != {} }    {       set supplyXFunc {@Coordinate(REJECTABLE_WITH_FLAG,x,"<Unused>",FLATTEN_AGGREGATE,YES)};    };    puts "MACRO CoordinateExtractor_3_SUPPLY_X ${supplyXFunc}"; ]
INCLUDE [    set supplyYFunc {};    if { {<Unused>} != {} }    {       set supplyYFunc {@Coordinate(REJECTABLE_WITH_FLAG,y,"<Unused>",FLATTEN_AGGREGATE,NO)};    };    puts "MACRO CoordinateExtractor_3_SUPPLY_Y ${supplyYFunc}"; ]
INCLUDE [    set supplyZDefaultFunc {};    if { {} != {} }    {       set supplyZDefaultFunc {@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING,"<Unused>",,FLOAT)};    };    puts "MACRO CoordinateExtractor_3_SUPPLY_Z_DEFAULT ${supplyZDefaultFunc}"; ]
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_TESTZDEFAULT }    INPUT  FEATURE_TYPE AttributeCreator_12_OUTPUT    TEST { "" = "" ENCODED }    TEST { "" TYPE NUM ENCODED }    BOOLEAN_OPERATOR OR    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_TESTMODE_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_TESTMODE }    INPUT { FEATURE_TYPE CoordinateExtractor_3_TESTMODE_INPUT }    TEST { "All Coordinates" == "All Coordinates" }    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_LIST_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_LIST }    INPUT { FEATURE_TYPE CoordinateExtractor_3_LIST_INPUT }    TEST @Dimension() == 2    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_OUTPUT         @ZValue("")         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)         @Dimension(2)          }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_OUTPUT         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)          }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_INPUT }    TEST { "<Unused>" TYPE INT ENCODED }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_X_INPUT }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_X }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_X_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_3_SUPPLY_X) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Y_INPUT }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_Y }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Y_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_3_SUPPLY_Y) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_ROUTER_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_Z_ROUTER }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_ROUTER_INPUT }    TEST { "<Unused>" != "" ENCODED }    TEST @Dimension() == 3    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_OUTPUT       $(CoordinateExtractor_3_SUPPLY_Z_DEFAULT)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_Z }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_INPUT }    FUNCTION_DEFINITION { @Coordinate(REJECTABLE_WITH_FLAG,z,"<Unused>",FLATTEN_AGGREGATE,NO) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder_4 }    INPUT  FEATURE_TYPE CoordinateExtractor_3_OUTPUT    LIST_NAME { "_indices{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_4_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} VertexCreatorFactory    FACTORY_NAME { VertexCreator_2 }    INPUT  FEATURE_TYPE ListExploder_4_ELEMENTS    MODE { REPLACE }    INDEX { "<Unused>" }    CONTINUE_ON_ERROR YES    XVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen>x<closeparen>,VertexCreator_2)" }    YVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen>y<closeparen>,VertexCreator_2)" }    ZVAL { "@EvaluateExpression(FDIV,FLOAT,<at>Value<openparen><u9ad8><u7a0b><closeparen>,VertexCreator_2)" }    USE_EXISTING_Z YES    ALLOW_DUPLICATES { "<Unused>" }    CLOSE_LINES { "<Unused>" }    ADD_MODE_VERSION 5    MISSING_VAL_MODE { <Unused> }    COMPUTE_MEASURES_MODE { CONTINUOUS }    COMMAND_PARM_EVALUATION SINGLE_PASS    OUTPUT { OUTPUT FEATURE_TYPE VertexCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_8 }    INPUT  FEATURE_TYPE VertexCreator_2_OUTPUT    KEEP_ATTRS { <u9ad8><u7a0b> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_8_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>utf-8<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma><comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<u4e2d><u5fc3><u7ebf><u5e73><u6ed1><u70b9>_point:Passed,ftp_feature_type_name,<u4e2d><u5fc3><u7ebf><u5e73><u6ed1><u70b9>_point,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,<lt>u9ad8<gt><lt>u7a0b<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO;<u4e2d><u5fc3><u7ebf>_line:Coerced,ftp_feature_type_name,<u4e2d><u5fc3><u7ebf>_line,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO;<u4fee><u6b63><u9ad8><u7a0b><u70b9><u540e><u4e2d><u5fc3><u7ebf>_line:Aggregate,ftp_feature_type_name,<u4fee><u6b63><u9ad8><u7a0b><u70b9><u540e><u4e2d><u5fc3><u7ebf>_line,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,Q__network<comma>double,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,utf-8,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Passed FEATURE_TYPE AttributeKeeper_8_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u4e2d><u5fc3><u7ebf><u5e73><u6ed1><u70b9>_point,FeatureWriter))    INPUT Coerced FEATURE_TYPE CenterlineReplacer_CENTERLINE_1_IXR7sqC48zM=  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u4e2d><u5fc3><u7ebf>_line,FeatureWriter))    INPUT Aggregate FEATURE_TYPE Aggregator_2_AGGREGATE  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u4fee><u6b63><u9ad8><u7a0b><u70b9><u540e><u4e2d><u5fc3><u7ebf>_line,FeatureWriter)) @CopyAttributes(ENCODED,Q__network,_network_id)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_6 }    INPUT  FEATURE_TYPE Counter_2_OUTPUT_1_rlbO72IVDeA=    INPUT  FEATURE_TYPE AttributeKeeper_2_OUTPUT_1_eKOY+pR0mH0=    TEST { 1 = 1 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

# -------------------------------------------------------------------------

DEFAULT_MACRO FME_LAUNCH_VIEWER_APP NO
FACTORY_DEF * CreationFactory    FACTORY_NAME VisualizerProcessor   CREATE_AT_END   OUTPUT FEATURE_TYPE __NUKEME__   @TCL("if {{$(FME_LAUNCH_VIEWER_APP)} == {YES}} {  if [file exists {$(WORKSPACE_TEMP_DIR)}] {set files [glob -nocomplain -directory  {$(WORKSPACE_TEMP_DIR)} -tails *.ffs];   if {[llength $files] == 1} {    set theFile {$(WORKSPACE_TEMP_DIR)/};   append theFile [lindex $files 0];   eval FME_Execute System \173\042$(FME_HOME_UNIX)/fmedatainspector\042 --visualizer --single-application --delete-on-close --no-source-prompt -f FFS \042$theFile\042 & \175;  } elseif { [llength $files] == 0 } {  catch { file delete -force {$(WORKSPACE_TEMP_DIR)} };  } else {  set filesNoExt {}; set lastfile {}; foreach {fileName} [lsort $files] { set fileNoExt [string trimright $fileName {.ffs}]; if [regexp {(.*)_[0-9]*$} $fileNoExt wholething fileNoExtBase] {  if { $fileNoExtBase == $lastfile } { continue } }; set lastfile $fileNoExt; lappend filesNoExt $fileNoExt };  eval FME_Execute System \173\042$(FME_HOME_UNIX)/fmedatainspector\042 --visualizer --single-application --delete-on-close --no-source-prompt -d {$(WORKSPACE_TEMP_DIR)} -a ffs -f FFS $filesNoExt & \175; }; }; }; ")
FACTORY_DEF * TeeFactory    FACTORY_NAME VisualizerCleaner   INPUT FEATURE_TYPE __NUKEME__
