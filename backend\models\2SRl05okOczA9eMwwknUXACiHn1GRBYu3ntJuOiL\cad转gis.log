2025-06-16 16:28:05|   0.0|  0.0|INFORM|Current FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-06-16 16:28:05|   0.0|  0.0|INFORM|System Time: 20250616082805 UTC
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Workspace was last saved in FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME_HOME is 'C:\Program Files\FME\'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME ESRI ArcGIS Server Edition (floating)
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Permanent License.
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Machine host name is: DESKTOP-9BLU554
2025-06-16 16:28:05|   0.0|  0.0|INFORM|OS Locale Name     : zh_CN
2025-06-16 16:28:05|   0.0|  0.0|INFORM|OS Locale Encoding : GBK
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Process Encoding   : UTF-8
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME API version: '4.0 20230426'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: FME_BASE is 'no'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: FME_MF_DIR is 'E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL/'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: FME_MF_NAME is 'cad2gis.fmw'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: FME_PRODUCT_NAME is 'FME(R) 2023.1.0.0'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Operating System: Microsoft Windows 10 64-bit  (Build 19045)
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Platform: WIN64
2025-06-16 16:28:05|   0.0|  0.0|INFORM|System Status: 205.46 GB of disk space available in the FME temporary folder (C:\Users\<USER>\AppData\Local\Temp)
2025-06-16 16:28:05|   0.0|  0.0|INFORM|System Status: 15.74 GB of physical memory available
2025-06-16 16:28:05|   0.0|  0.0|INFORM|System Status: 62.96 GB of virtual memory available
2025-06-16 16:28:05|   0.0|  0.0|INFORM|START - ProcessID: 14896, peak process memory usage: 44988 kB, current process memory usage: 44988 kB
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Command line arguments are `C:\Program Files\FME\fme.exe' `E:\GeoStream_Integration\frontend\backend\models\2SRl05okOczA9eMwwknUXACiHn1GRBYu3ntJuOiL\cad2gis.fmw' `--dwg_path' `E:\GeoStream_Integration\frontend\backend\temp\93bVRaomtXLolTsuENfKGKJjeD6JwT3dw0sGtjff\zz' `--output_Choice' `['点（Point）', '富点（MultiPoint）', '面（Area）', '文本（Text）', '线（Line）', '富文本（MultiText）', '曲线（Curve）', '圆弧（Arc）', '椭圆（Ellipse）', '其他（Other）']'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Connection Storage: 'C:\Users\<USER>\AppData\Roaming\Safe Software\FME\'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Shared folders for formats are : C:\Program Files\FME\datasources;C:\Users\<USER>\Documents\FME\Formats
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Shared folders for transformers are : C:\Users\<USER>\AppData\Roaming\Safe Software\FME\Packages\23619-win64\transformers;C:\Program Files\FME\transformers;C:\Users\<USER>\AppData\Roaming\Safe Software\FME\FME Store\Transformers
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Shared folders for coordinate systems are : C:\Users\<USER>\Documents\FME\CoordinateSystems
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Shared folders for coordinate system exceptions are : C:\Users\<USER>\Documents\FME\CoordinateSystemExceptions
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Shared folders for coordinate system grid overrides are : C:\Users\<USER>\Documents\FME\CoordinateSystemGridOverrides
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Shared folders for CS-MAP transformation exceptions are : C:\Users\<USER>\Documents\FME\CsmapTransformationExceptions
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Shared folders for transformer categories are : C:\Users\<USER>\Documents\FME\TransformerCategories
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Reader Keyword is `MULTI_READER'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Writer Keyword is `MULTI_DEST'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Writer Group Definition Keyword is `MULTI_DEST_DEF'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Reader type is `MULTI_READER'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Writer type is `MULTI_WRITER'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Current working folder is `E:\GeoStream_Integration\frontend\backend'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Temporary folder is `C:\Users\<USER>\AppData\Local\Temp', set from environment variable `TEMP'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Cache folder is 'C:\Users\<USER>\AppData\Local\Temp'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: FME_HOME is `C:\Program Files\FME\'
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Start freeing memory when the process exceeds 47.22 GB
2025-06-16 16:28:05|   0.0|  0.0|INFORM|FME Configuration: Stop freeing memory when the process is below 35.42 GB
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Creating writer for format: 
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Creating reader for format: 
2025-06-16 16:28:05|   0.0|  0.0|INFORM|MULTI_READER(MULTI_READER): Will fail with first member reader failure
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Using Multi Reader with keyword `MULTI_READER' to read multiple datasets
2025-06-16 16:28:05|   0.0|  0.0|INFORM|Using MultiWriter with keyword `MULTI_DEST' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-06-16 16:28:05|   0.1|  0.1|INFORM|Loaded module 'Geometry_func' from file 'C:\Program Files\FME\plugins/Geometry_func.dll'
2025-06-16 16:28:05|   0.1|  0.0|INFORM|FME API version of module 'Geometry_func' matches current internal version (4.0 20230426)
2025-06-16 16:28:05|   0.1|  0.0|INFORM|Loaded module 'QueryFactory' from file 'C:\Program Files\FME\plugins/QueryFactory.dll'
2025-06-16 16:28:05|   0.1|  0.0|INFORM|FME API version of module 'QueryFactory' matches current internal version (4.0 20230426)
2025-06-16 16:28:05|   0.1|  0.0|INFORM|Loaded module 'GeometryFilterFactory' from file 'C:\Program Files\FME\plugins/GeometryFilterFactory.dll'
2025-06-16 16:28:05|   0.1|  0.0|INFORM|FME API version of module 'GeometryFilterFactory' matches current internal version (4.0 20230426)
2025-06-16 16:28:05|   0.1|  0.0|INFORM|Loaded module 'GQueryFactory' from file 'C:\Program Files\FME\plugins/GQueryFactory.dll'
2025-06-16 16:28:05|   0.1|  0.0|INFORM|FME API version of module 'GQueryFactory' matches current internal version (4.0 20230426)
2025-06-16 16:28:05|   0.1|  0.0|INFORM|Emptying factory pipeline
2025-06-16 16:28:05|   0.1|  0.0|STATS |Creator_XML_Creator (CreationFactory): Created 1 features
2025-06-16 16:28:05|   0.1|  0.0|STATS |Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-06-16 16:28:05|   0.1|  0.0|STATS |Creator_CREATED Brancher -1 33 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-06-16 16:28:05|   0.1|  0.0|STATS |_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_2 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-06-16 16:28:05|   0.1|  0.0|STATS |AttributeExposer_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-06-16 16:28:05|   0.1|  0.0|STATS |GeometryFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_12 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_7 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_8 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_9 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_13 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_5 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_10 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_14 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_15 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_16 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_17 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_18 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_19 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_6 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_11 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_20 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_21 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_22 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_23 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_24 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_25 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_26 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_27 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_28 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_29 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_30 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_31 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_32 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_33 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_34 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_35 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_36 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_37 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_38 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_39 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_40 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_41 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_42 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_43 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Tester_44 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-06-16 16:28:05|   0.1|  0.0|STATS |Destination Feature Type Routing Correlator (RoutingFactory): Tested 0 input feature(s), wrote 0 output feature(s): 0 matched merge filters, 0 were routed to output, 0 could not be routed.
2025-06-16 16:28:05|   0.1|  0.0|STATS |Final Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-06-16 16:28:05|   0.1|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-16 16:28:05|   0.1|  0.0|STATS |                            Features Read Summary
2025-06-16 16:28:05|   0.1|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-16 16:28:05|   0.1|  0.0|STATS |==============================================================================
2025-06-16 16:28:05|   0.1|  0.0|STATS |Total Features Read                                                          0
2025-06-16 16:28:05|   0.1|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-16 16:28:05|   0.1|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-16 16:28:05|   0.1|  0.0|STATS |                           Features Written Summary
2025-06-16 16:28:05|   0.1|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-16 16:28:05|   0.1|  0.0|STATS |==============================================================================
2025-06-16 16:28:05|   0.1|  0.0|STATS |Total Features Written                                                       0
2025-06-16 16:28:05|   0.1|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-06-16 16:28:05|   0.1|  0.0|INFORM|Translation was SUCCESSFUL with 0 warning(s) (0 feature(s) output)
2025-06-16 16:28:05|   0.1|  0.0|INFORM|FME Session Duration: 0.1 seconds. (CPU: 0.0s user, 0.1s system)
2025-06-16 16:28:05|   0.1|  0.0|INFORM|END - ProcessID: 14896, peak process memory usage: 58384 kB, current process memory usage: 58384 kB
