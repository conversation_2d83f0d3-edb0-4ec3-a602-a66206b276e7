import{d as ot,u as rt,r as _,M as ve,O as ge,c as h,e as t,w as s,f as i,Y as ee,P as M,Z as H,b as n,J as it,A as c,m as b,D as te,C as U,s as D,a7 as ut,n as dt,B as ct,F as p,a8 as ae,H as se,I as le,a4 as pt,a9 as _t,aa as ft,R as mt,ab as vt,a1 as gt,Q as yt,i as T,E as u,T as bt,l as v,ac as ht,_ as wt}from"./index-CdoJMuEX.js";const xt={class:"tools-container"},kt={key:"quality"},Dt={class:"steps-progress"},Vt={class:"step-content-container"},Ct={class:"start-container"},$t={class:"start-content"},Mt={class:"step-content"},Ut={class:"license-toolbar"},Lt={style:{display:"none"}},zt={class:"step-footer"},Bt={class:"error-message"},Gt={class:"error-message"},qt={class:"error-message"},Ht={class:"error-message"},Tt={class:"error-message"},St={class:"dialog-footer"},jt={class:"step-content"},Yt={class:"upload-section"},Rt={key:0,class:"upload-progress"},Ft={class:"progress-text"},Et={key:0,class:"gdb-info"},It={class:"card-header"},Nt={class:"info-content"},Xt={class:"info-item"},At={class:"value"},Pt={class:"info-item"},Qt={class:"value"},Ot={key:0,class:"warning-tip"},Jt={key:1,class:"layer-selection"},Wt={class:"card-header"},Zt={class:"header-actions"},Kt={class:"selection-info"},ea={class:"layer-list"},ta={class:"layer-content"},aa={class:"layer-header"},sa={class:"layer-name"},la={class:"name-text"},na={class:"layer-stats"},oa={class:"feature-count"},ra={key:0,class:"layer-fields"},ia={key:0,class:"more-fields"},ua={class:"step-footer"},da={class:"step-content"},ca={class:"step-footer"},pa={key:"history"},_a={class:"table-container"},fa={style:{display:"flex","justify-content":"center"}},ma={class:"error-log-content"},va={class:"dialog-footer"},ga=4,ya=ot({__name:"QualityView",setup(ba){const L=rt(),ye=a=>window.location.pathname.startsWith("/gsi/")?`/gsi${a}`:`${T.defaults.baseURL}${a}`,y=_(0),F=_([]),Q=_(!1),C=_(null),z=_(!1),E=_(!1),S=_(),O=_(!1),ne=_([]),f=_({tool_name:"数据质检",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),be={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},he=a=>a.getTime()<Date.now()-864e5;function we(){z.value=!0,ke()}function xe(){E.value=!0,I().finally(()=>{setTimeout(()=>{E.value=!1},600)})}async function ke(){var e;const a=(e=L.user)==null?void 0:e.username;if(!a){u.error("未登录，无法获取审批人");return}try{const o=await T.get("/api/admin-users",{headers:{"X-Username":a}});o.data.success?ne.value=o.data.data:u.error("获取审批人失败")}catch{u.error("获取审批人失败")}}async function De(){S.value&&await S.value.validate(async a=>{var e;if(a){O.value=!0;try{const o=(e=L.user)==null?void 0:e.username,r=await T.post("/api/quality/tools/apply",{fmw_id:"quality",fmw_name:"数据质检",tool_name:"数据质检",applicant:o,reason:f.value.reason,end_date:f.value.end_date,usage_count:f.value.usage_count,user_project:f.value.user_project,reviewer:f.value.approver},{headers:{"Content-Type":"application/json","X-Username":o}});r.data.success?(u.success("申请提交成功"),z.value=!1,oe(),await I()):u.error(r.data.message||"申请提交失败")}catch{u.error("申请提交失败")}finally{O.value=!1}}})}function oe(){S.value&&S.value.resetFields(),Object.assign(f.value,{tool_name:"数据质检",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})}const Ve=ve(()=>{var a;return{"X-Username":((a=L.user)==null?void 0:a.username)||""}}),x=_([]),k=_([]),j=_(!1),V=_(0),B=_(""),G=_({path:"",name:"",gdb_count:0});function J(){if(y.value===1&&!C.value){u.warning("请先选择一个质检许可");return}if(y.value===2&&k.value.length===0){u.warning("请至少选择一个待质检图层");return}y.value++}function W(){y.value>0&&y.value--}async function I(){var a;Q.value=!0;try{const e=(a=L.user)==null?void 0:a.username;if(!e){u.error("未登录，无法获取许可列表"),F.value=[];return}const o=await T.post("/api/quality/tools/my-approvals",{},{headers:{"X-Username":e}});if(o.data.success){F.value=o.data.data;const r=F.value.find(d=>{if(Y(d.status).text!=="已通过")return!1;const m=new Date(d.end_date),w=new Date;return w.setHours(0,0,0,0),!(m<w||d.count>=d.usage_count)});C.value=r?r.id:null}else u.error(o.data.message||"获取许可列表失败")}catch{u.error("获取许可列表失败")}finally{Q.value=!1}}ge(y,a=>{a===1&&I()});function Y(a){return{pending:{type:"info",text:"审批中"},approved:{type:"success",text:"已通过"},rejected:{type:"danger",text:"已驳回"},expired:{type:"warning",text:"已过期"},exhausted:{type:"warning",text:"已耗尽"},available:{type:"success",text:"可用"}}[a]||{type:"default",text:a}}function re({row:a}){if(Y(a.status).text!=="已通过")return"disabled-row";const e=new Date(a.end_date),o=new Date;return o.setHours(0,0,0,0),e<o||a.count>=a.usage_count?"disabled-row":a.id===C.value?"clickable-row selected-row":"clickable-row"}function Ce(a){if(Y(a.status).text!=="已通过")return;const e=new Date(a.end_date),o=new Date;o.setHours(0,0,0,0),!(e<o)&&(a.count>=a.usage_count||(C.value=a.id))}function $e(a){var r;const e=(r=a.name.split(".").pop())==null?void 0:r.toLowerCase();if(!["zip","rar","7z"].includes(e))return u.error("只支持zip、rar、7z格式的压缩包"),!1;const o=10*1024*1024*1024;return a.size>o?(u.error("文件大小不能超过10GB"),!1):(j.value=!0,V.value=0,B.value="开始上传...",x.value=[],k.value=[],G.value={path:"",name:"",gdb_count:0},!0)}function Me(a){V.value=Math.round(a.loaded/a.total*100),V.value<100?B.value=`上传中... ${V.value}%`:B.value="解析GDB文件中..."}function Ue(a){j.value=!1,V.value=100,B.value="上传完成",a.success&&a.data&&a.data.layers?(x.value=a.data.layers.map(e=>({...e,key:e.name,label:e.name})),k.value=[],G.value={path:a.data.gdb_path,name:a.data.gdb_path.split(/[/\\]/).pop()||"",gdb_count:a.data.gdb_count||1},u.success(`GDB解析成功，发现 ${x.value.length} 个图层`)):u.error(a.message||"GDB解析失败")}function Le(a){j.value=!1,V.value=0,B.value="",console.error("GDB上传错误详情:",a),u.error("GDB上传失败："+(a.message||"未知错误"))}function ze(){k.value=x.value.map(a=>a.name),u.success(`已选择全部 ${x.value.length} 个图层`)}function Be(){k.value=[],u.info("已清空图层选择")}function Ge(a){switch(a){case"point":return"Location";case"line":return"Connection";case"polygon":return"Grid";default:return"Document"}}function qe(a){switch(a){case"point":return"success";case"line":return"warning";case"polygon":return"info";default:return""}}ve(()=>x.value.reduce((a,e)=>a+(e.feature_count||0),0));const R=_("quality");function He(a){a.name==="history"&&de()}const N=_([]),Z=_(!1),X=_(!1),ie=_("");function ue(a,e="yyyy-MM-dd HH:mm"){if(!a)return"--";try{const o=new Date(a),r=d=>d<10?"0"+d:d;return e==="yyyy-MM-dd HH:mm:ss"?`${o.getFullYear()}-${r(o.getMonth()+1)}-${r(o.getDate())} ${r(o.getHours())}:${r(o.getMinutes())}:${r(o.getSeconds())}`:`${o.getFullYear()}-${r(o.getMonth()+1)}-${r(o.getDate())} ${r(o.getHours())}:${r(o.getMinutes())}`}catch{return"--"}}function Te(a){if(!a)return"--";const e=Math.floor(Number(a)/1e3);if(e<60)return`${e}s`;const o=Math.floor(e/60),r=e%60;return`${o}m${r}s`}function Se(a){return{running:"warning",success:"success",completed:"success",pending:"info",failed:"danger"}[a]||"info"}function je(a){return{running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"}[a]||a}async function de(){var a;Z.value=!0;try{const e=(a=L.user)==null?void 0:a.username,o=await T.post("/api/quality/tools/run_records",{fmw_id:"quality",username:e},{headers:{"X-Username":e}});o.data.success?N.value=o.data.data.records:u.error(o.data.message||"获取历史记录失败")}catch{u.error("获取历史记录失败")}finally{Z.value=!1}}function Ye(a){try{const e=`tools/quality/output/${a.task_id}/${a.file_name}`,o=`/api/tools/download_result?file_path=${encodeURIComponent(e)}`,r=document.createElement("a");r.style.display="none",document.body.appendChild(r),r.href=o,r.download=a.file_name,r.click(),setTimeout(()=>{document.body.removeChild(r)},100),u.success("开始下载")}catch{u.error("下载失败，请稍后重试")}}function Re(a){a.error_message?(ie.value=a.error_message,X.value=!0):u.warning("暂无错误信息")}async function Fe(a){var e;try{await bt.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=N.value.findIndex(r=>r.task_id===a.task_id);o!==-1&&N.value.splice(o,1),await T.post("/api/quality/tools/delete_result",{task_id:a.task_id},{headers:{"X-Username":(e=L.user)==null?void 0:e.username}}),u.success("删除成功")}catch(o){o!=="cancel"&&u.error("删除失败")}}function Ee(){u.info("后续实现：提交质检任务"),R.value="history",de()}return ge(y,a=>{a===1&&I()}),(a,e)=>{const o=i("el-step"),r=i("el-steps"),d=i("el-icon"),m=i("el-button"),w=i("el-skeleton-item"),Ie=i("el-skeleton"),g=i("el-table-column"),A=i("el-tag"),Ne=i("el-radio"),ce=i("el-table"),K=i("el-input"),q=i("el-form-item"),Xe=i("el-date-picker"),Ae=i("el-input-number"),Pe=i("el-option"),Qe=i("el-select"),Oe=i("el-form"),pe=i("el-dialog"),Je=i("el-upload"),We=i("el-progress"),_e=i("el-col"),Ze=i("el-row"),Ke=i("el-alert"),P=i("el-card"),et=i("el-checkbox"),tt=i("el-checkbox-group"),fe=i("el-tab-pane"),at=i("el-tooltip"),st=i("el-button-group"),lt=i("el-tabs"),nt=yt("loading");return v(),h("div",xt,[t(lt,{modelValue:R.value,"onUpdate:modelValue":e[11]||(e[11]=l=>R.value=l),class:"cad-tabs",onTabClick:He},{default:s(()=>[t(fe,{label:"质检工具",name:"quality"},{default:s(()=>[t(ee,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(n("div",kt,[t(P,{class:"license-card",style:{"margin-top":"0"}},{default:s(()=>[t(r,{active:y.value,"finish-status":"success",simple:"",class:"quality-steps"},{default:s(()=>[t(o,{title:"开始"}),t(o,{title:"选择许可"}),t(o,{title:"上传GDB"}),t(o,{title:"质检配置"})]),_:1},8,["active"]),n("div",Dt,[n("div",{class:"progress-bar",style:it({width:`${y.value/(ga-1)*100}%`})},null,4)]),n("div",Vt,[t(ee,{name:"step-fade",mode:"out-in"},{default:s(()=>[(v(),h("div",{key:y.value},[M(n("div",Ct,[n("div",$t,[t(m,{type:"primary",size:"large",class:"start-button",onClick:J},{default:s(()=>[t(d,null,{default:s(()=>[t(b(te))]),_:1}),e[14]||(e[14]=c(" 开始质检 "))]),_:1})])],512),[[H,y.value===0]]),M(n("div",Mt,[n("div",Ut,[t(m,{type:"primary",onClick:we},{default:s(()=>[t(d,null,{default:s(()=>[t(b(ut))]),_:1}),e[15]||(e[15]=c(" 申请许可 "))]),_:1}),t(m,{type:"primary",onClick:xe,disabled:E.value},{default:s(()=>[t(d,{class:dt({"refresh-rotate":E.value})},{default:s(()=>[t(b(ct))]),_:1},8,["class"]),e[16]||(e[16]=c(" 刷新许可 "))]),_:1},8,["disabled"])]),Q.value?(v(),U(Ie,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:s(()=>[t(w,{variant:"text",style:{width:"80px","margin-right":"16px"}}),t(w,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(w,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(w,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(w,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(w,{variant:"text",style:{width:"180px","margin-right":"16px"}}),t(w,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(w,{variant:"text",style:{width:"80px"}})]),_:1})):D("",!0),t(ce,{data:F.value,style:{width:"100%"},border:"",onRowClick:Ce,"row-class-name":re,class:"quality-table"},{default:s(()=>[t(g,{type:"index",label:"序号",width:"80",align:"center"}),t(g,{prop:"user_project",label:"项目","min-width":"150","show-overflow-tooltip":""}),t(g,{prop:"reason",label:"原因","min-width":"150","show-overflow-tooltip":""}),t(g,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),t(g,{prop:"count",label:"已用次数",width:"100",align:"center"}),t(g,{prop:"end_date",label:"截止时间",width:"180",align:"center"}),t(g,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[t(A,{type:Y(l.status).type},{default:s(()=>[c(p(Y(l.status).text),1)]),_:2},1032,["type"])]),_:1}),t(g,{label:"选择",width:"80",align:"center"},{default:s(({row:l})=>[t(Ne,{modelValue:C.value,"onUpdate:modelValue":e[0]||(e[0]=$=>C.value=$),label:l.id,disabled:re({row:l})==="disabled-row",class:"custom-radio"},{default:s(()=>[n("span",Lt,p(l.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"]),n("div",zt,[t(m,{onClick:W},{default:s(()=>[t(d,null,{default:s(()=>[t(b(ae))]),_:1}),e[17]||(e[17]=c(" 上一步 "))]),_:1}),t(m,{type:"primary",onClick:J,disabled:!C.value},{default:s(()=>[e[18]||(e[18]=c(" 下一步 ")),t(d,null,{default:s(()=>[t(b(te))]),_:1})]),_:1},8,["disabled"])]),t(pe,{modelValue:z.value,"onUpdate:modelValue":e[8]||(e[8]=l=>z.value=l),title:"申请许可-数据质检",width:"500px","close-on-click-modal":!1,onClose:e[9]||(e[9]=l=>z.value=!1),onAfterClose:oe},{footer:s(()=>[n("span",St,[t(m,{onClick:e[7]||(e[7]=l=>z.value=!1)},{default:s(()=>e[19]||(e[19]=[c("取消")])),_:1}),t(m,{type:"primary",loading:O.value,onClick:De},{default:s(()=>e[20]||(e[20]=[c("提交")])),_:1},8,["loading"])])]),default:s(()=>[t(Oe,{ref_key:"licenseFormRef",ref:S,model:f.value,rules:be,"label-width":"100px",class:"apply-form",size:"small"},{default:s(()=>[t(q,{label:"工具名称",prop:"tool_name"},{default:s(()=>[t(K,{modelValue:f.value.tool_name,"onUpdate:modelValue":e[1]||(e[1]=l=>f.value.tool_name=l),value:"数据质检",disabled:""},null,8,["modelValue"])]),_:1}),t(q,{label:"使用项目",prop:"user_project"},{error:s(({error:l})=>[n("span",Bt,p(l),1)]),default:s(()=>[t(K,{modelValue:f.value.user_project,"onUpdate:modelValue":e[2]||(e[2]=l=>f.value.user_project=l),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),t(q,{label:"申请原因",prop:"reason"},{error:s(({error:l})=>[n("span",Gt,p(l),1)]),default:s(()=>[t(K,{modelValue:f.value.reason,"onUpdate:modelValue":e[3]||(e[3]=l=>f.value.reason=l),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),t(q,{label:"有效期",prop:"end_date"},{error:s(({error:l})=>[n("span",qt,p(l),1)]),default:s(()=>[t(Xe,{modelValue:f.value.end_date,"onUpdate:modelValue":e[4]||(e[4]=l=>f.value.end_date=l),type:"date",placeholder:"请选择有效期","disabled-date":he,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),t(q,{label:"申请次数",prop:"usage_count"},{error:s(({error:l})=>[n("span",Ht,p(l),1)]),default:s(()=>[t(Ae,{modelValue:f.value.usage_count,"onUpdate:modelValue":e[5]||(e[5]=l=>f.value.usage_count=l),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(q,{label:"审批人",prop:"approver"},{error:s(({error:l})=>[n("span",Tt,p(l),1)]),default:s(()=>[t(Qe,{modelValue:f.value.approver,"onUpdate:modelValue":e[6]||(e[6]=l=>f.value.approver=l),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:s(()=>[(v(!0),h(se,null,le(ne.value,l=>(v(),U(Pe,{key:l.username,label:l.real_name,value:l.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],512),[[H,y.value===1]]),M(n("div",jt,[e[31]||(e[31]=n("h2",{class:"step-title"},"上传GDB压缩包并选择待质检图层",-1)),n("div",Yt,[t(Je,{class:"gdb-upload",drag:"",action:ye("/api/quality/upload-gdb"),headers:Ve.value,"show-file-list":!1,"before-upload":$e,"on-success":Ue,"on-error":Le,"on-progress":Me,accept:".zip,.rar,.7z",disabled:j.value},{tip:s(()=>e[21]||(e[21]=[n("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包，单个文件不超过10GB ",-1)])),default:s(()=>[t(d,{class:"el-icon--upload"},{default:s(()=>[t(b(pt))]),_:1}),e[22]||(e[22]=n("div",{class:"el-upload__text"},[c(" 将GDB压缩包拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["action","headers","disabled"]),j.value?(v(),h("div",Rt,[t(We,{percentage:V.value,status:V.value===100?"success":void 0},null,8,["percentage","status"]),n("p",Ft,p(B.value),1)])):D("",!0)]),G.value.path?(v(),h("div",Et,[t(P,{class:"info-card"},{header:s(()=>[n("div",It,[t(d,null,{default:s(()=>[t(b(_t))]),_:1}),e[23]||(e[23]=n("span",null,"GDB文件信息",-1))])]),default:s(()=>[n("div",Nt,[t(Ze,{gutter:20},{default:s(()=>[t(_e,{span:12},{default:s(()=>[n("div",Xt,[e[24]||(e[24]=n("span",{class:"label"},"文件路径：",-1)),n("span",At,p(G.value.name),1)])]),_:1}),t(_e,{span:12},{default:s(()=>[n("div",Pt,[e[25]||(e[25]=n("span",{class:"label"},"图层数量：",-1)),n("span",Qt,p(x.value.length)+" 个",1)])]),_:1})]),_:1}),G.value.gdb_count>1?(v(),h("div",Ot,[t(Ke,{title:"检测到多个GDB文件夹",description:`压缩包中共发现 ${G.value.gdb_count} 个GDB文件夹，当前使用第一个进行解析`,type:"warning","show-icon":"",closable:!1},null,8,["description"])])):D("",!0)])]),_:1})])):D("",!0),x.value.length>0?(v(),h("div",Jt,[t(P,{class:"layer-card"},{header:s(()=>[n("div",Wt,[t(d,null,{default:s(()=>[t(b(ft))]),_:1}),e[28]||(e[28]=n("span",null,"图层选择",-1)),n("div",Zt,[t(m,{size:"small",onClick:ze},{default:s(()=>e[26]||(e[26]=[c("全选")])),_:1}),t(m,{size:"small",onClick:Be},{default:s(()=>e[27]||(e[27]=[c("清空")])),_:1}),n("span",Kt,"已选择 "+p(k.value.length)+" / "+p(x.value.length)+" 个图层",1)])])]),default:s(()=>[n("div",ea,[t(tt,{modelValue:k.value,"onUpdate:modelValue":e[10]||(e[10]=l=>k.value=l),class:"layer-checkbox-group"},{default:s(()=>[(v(!0),h(se,null,le(x.value,l=>(v(),h("div",{key:l.name,class:"layer-item"},[t(et,{label:l.name,class:"layer-checkbox"},{default:s(()=>{var $;return[n("div",ta,[n("div",aa,[n("div",sa,[t(d,{class:"layer-icon"},{default:s(()=>[(v(),U(ht(Ge(l.type))))]),_:2},1024),n("span",la,p(l.name),1),t(A,{type:qe(l.type),size:"small",class:"geometry-tag"},{default:s(()=>[c(p(l.geometry_type),1)]),_:2},1032,["type"])]),n("div",na,[n("span",oa,p((($=l.feature_count)==null?void 0:$.toLocaleString())||0)+" 个要素",1)])]),l.fields&&l.fields.length>0?(v(),h("div",ra,[e[29]||(e[29]=n("span",{class:"fields-label"},"字段：",-1)),(v(!0),h(se,null,le(l.fields.slice(0,4),me=>(v(),U(A,{key:me,size:"small",class:"field-tag",effect:"plain"},{default:s(()=>[c(p(me),1)]),_:2},1024))),128)),l.fields.length>4?(v(),h("span",ia," +"+p(l.fields.length-4)+" 个字段 ",1)):D("",!0)])):D("",!0)])]}),_:2},1032,["label"])]))),128))]),_:1},8,["modelValue"])])]),_:1})])):D("",!0),n("div",ua,[t(m,{onClick:W},{default:s(()=>[t(d,null,{default:s(()=>[t(b(ae))]),_:1}),e[30]||(e[30]=c(" 上一步 "))]),_:1}),t(m,{type:"primary",disabled:k.value.length===0,onClick:J},{default:s(()=>[c(" 下一步（"+p(k.value.length)+"个图层） ",1),t(d,null,{default:s(()=>[t(b(te))]),_:1})]),_:1},8,["disabled"])])],512),[[H,y.value===2]]),M(n("div",da,[e[34]||(e[34]=n("h2",{class:"step-title"},"质检项配置",-1)),e[35]||(e[35]=n("p",null,"支持配置表模板下载、上传、在线配置、历史配置读取等功能。后续详细实现。",-1)),n("div",ca,[t(m,{onClick:W},{default:s(()=>[t(d,null,{default:s(()=>[t(b(ae))]),_:1}),e[32]||(e[32]=c(" 上一步 "))]),_:1}),t(m,{type:"primary",onClick:Ee},{default:s(()=>e[33]||(e[33]=[c("提交任务")])),_:1})])],512),[[H,y.value===3]])]))]),_:1})])]),_:1})],512),[[H,R.value==="quality"]])]),_:1})]),_:1}),t(fe,{label:"历史记录",name:"history"},{default:s(()=>[t(ee,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(n("div",pa,[t(P,{class:"history-card",style:{"margin-top":"0px"}},{default:s(()=>[n("div",_a,[M((v(),U(ce,{data:N.value,style:{width:"100%"}},{default:s(()=>[t(g,{type:"index",label:"序号",width:"80",align:"center"}),t(g,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:s(({row:l})=>[t(at,{content:ue(l.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:s(()=>[n("span",null,p(ue(l.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(g,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[t(A,{type:Se(l.status)},{default:s(()=>[c(p(je(l.status)),1)]),_:2},1032,["type"])]),_:1}),t(g,{prop:"time_consuming",label:"运行耗时",width:"120",align:"center"},{default:s(({row:l})=>[c(p(Te(l.time_consuming)),1)]),_:1}),t(g,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),t(g,{prop:"up_nums",label:"质检文件数量","min-width":"130",align:"center","show-overflow-tooltip":""}),t(g,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:l})=>[n("div",fa,[t(st,null,{default:s(()=>[l.status==="success"&&l.up_nums>0&&l.file_size!=="0.0MB"?(v(),U(m,{key:0,type:"success",size:"small",onClick:$=>Ye(l),disabled:l.status!=="success"},{default:s(()=>[t(d,null,{default:s(()=>[t(b(mt))]),_:1}),e[36]||(e[36]=c(" 下载 "))]),_:2},1032,["onClick","disabled"])):D("",!0),l.error_message?(v(),U(m,{key:1,type:"info",size:"small",onClick:$=>Re(l)},{default:s(()=>[t(d,null,{default:s(()=>[t(b(vt))]),_:1}),e[37]||(e[37]=c(" 日志 "))]),_:2},1032,["onClick"])):D("",!0),t(m,{type:"danger",size:"small",onClick:$=>Fe(l)},{default:s(()=>[t(d,null,{default:s(()=>[t(b(gt))]),_:1}),e[38]||(e[38]=c(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[nt,Z.value]])])]),_:1})],512),[[H,R.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(pe,{modelValue:X.value,"onUpdate:modelValue":e[13]||(e[13]=l=>X.value=l),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:s(()=>[n("span",va,[t(m,{onClick:e[12]||(e[12]=l=>X.value=!1)},{default:s(()=>e[39]||(e[39]=[c("关闭")])),_:1})])]),default:s(()=>[n("div",ma,[n("pre",null,p(ie.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"])])}}}),wa=wt(ya,[["__scopeId","data-v-e1764363"]]);export{wa as default};
