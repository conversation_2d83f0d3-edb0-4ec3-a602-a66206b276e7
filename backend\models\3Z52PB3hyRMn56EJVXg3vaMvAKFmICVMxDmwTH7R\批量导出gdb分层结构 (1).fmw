#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2025\0108\批量导出图层名、字段名\none2none.fmw
#          --dir "$(FME_MF_DIR)无锡最终成果"
#          --dir_2 "$(FME_MF_DIR)图元基础信息字典"
#          --PARAMETER "C:\Users\<USER>\Desktop"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="9139.46 1180.39"
#!   DOC_TOP_LEFT="153.127 -1583.52"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="4f6c74eb-5560-4d18-80fb-c2aa1d9ae76c"
#!   FME_DOCUMENT_PRIORGUID=""
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-01-08T17:23:01"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="NO"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="4915.67 -190.627"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="@Value(path_windows)"
#! />
#! <DATASET
#!   NAME="FeatureReader_4"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_4"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/数据库导出.xlsx"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 带提取数据库压缩包（rar）"
#!   DEFAULT_VALUE="$(FME_MF_DIR)无锡最终成果"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 图元基础信息字典excel压缩包（rar）"
#!   DEFAULT_VALUE="$(FME_MF_DIR)图元基础信息字典"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER 保存路径"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJhY2Nlc3NNb2RlIjoicmVhZCIsImRlZmF1bHRWYWx1ZSI6IiQoRk1FX01GX0RJUinml6DplKHmnIDnu4jmiJDmnpwiLCJpbmNsdWRlV2ViQnJvd3NlciI6dHJ1ZSwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiZGlyIiwicHJvbXB0Ijoi5bim5o+Q5Y+W5pWw5o2u5bqT5Y6L57yp5YyF77yIcmFy77yJIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjp0cnVlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6ZmFsc2UsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiYWNjZXNzTW9kZSI6InJlYWQiLCJkZWZhdWx0VmFsdWUiOiIkKEZNRV9NRl9ESVIp5Zu+5YWD5Z+656GA5L+h5oGv5a2X5YW4IiwiaW5jbHVkZVdlYkJyb3dzZXIiOnRydWUsIml0ZW1zVG9TZWxlY3QiOiJmb2xkZXJzIiwibmFtZSI6ImRpcl8yIiwicHJvbXB0Ijoi5Zu+5YWD5Z+656GA5L+h5oGv5a2X5YW4ZXhjZWzljovnvKnljIXvvIhyYXLvvIkiLCJyZXF1aXJlZCI6dHJ1ZSwic2VsZWN0TXVsdGlwbGUiOnRydWUsInNob3dQcm9tcHQiOnRydWUsInN1cHBvcnRlZFZhbHVlVHlwZXMiOlsiZXhwcmVzc2lvbiIsImdsb2JhbFBhcmFtZXRlciJdLCJ0eXBlIjoiZmlsZSIsInZhbGlkYXRlRXhpc3RlbmNlIjpmYWxzZSwidmFsdWVUeXBlIjoic3RyaW5nIn0seyJhY2Nlc3NNb2RlIjoid3JpdGUiLCJhbGxvd1VSTCI6ZmFsc2UsImRlZmF1bHRWYWx1ZSI6IkM6XFxVc2Vyc1xcZGp6XFxEZXNrdG9wIiwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiUEFSQU1FVEVSIiwicHJvbXB0Ijoi5L+d5a2Y6Lev5b6EIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjpmYWxzZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOnRydWUsInZhbHVlVHlwZSI6InN0cmluZyJ9XX0="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)无锡最终成果"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 带提取数据库压缩包（rar）"
#! />
#!     <INFO NAME="dir_2" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)图元基础信息字典"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 图元基础信息字典excel压缩包（rar）"
#! />
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="153.12653126531291 -550.00550005500065"
#!   BOUNDING_RECT="153.12653126531291 -550.00550005500065 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="800.12728127281343 -403.12903129031309"
#!   BOUNDING_RECT="800.12728127281343 -403.12903129031309 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="1362.513625136251 -550.00550005500065"
#!   BOUNDING_RECT="1362.513625136251 -550.00550005500065 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = gdb"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1956.269562695627 -463.12903129031309"
#!   BOUNDING_RECT="1956.269562695627 -463.12903129031309 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="GEODATABASE_FILE_GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS GEODATABASE_FILE%Source&quot;,GEODATABASE_FILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,GEODATABASE_FILE_MERGE_FEAT_LINKED_ANNOS,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Merge&lt;space&gt;Feature&lt;space&gt;Linked&lt;space&gt;Annotations:,GEODATABASE_FILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,GEODATABASE_FILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,GEODATABASE_FILE_REMOVE_FEATURE_DATASET,&quot;OPTIONAL CHECKBOX YES%NO&quot;,GEODATABASE_FILE&lt;space&gt;Remove&lt;space&gt;Feature&lt;space&gt;Dataset,GEODATABASE_FILE_SIMPLE_DONUT_GEOMETRY,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,yes%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,no&quot;,GEODATABASE_FILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,GEODATABASE_FILE_WHERE_WWJD,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED Mode,WHERE&quot;,GEODATABASE_FILE&lt;space&gt;WHERE&lt;space&gt;Clause:,GEODATABASE_FILE_DISABLE_FEATURE_DATASET_ATTRIBUTE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_TRANSLATE_SPATIAL_DATA_ONLY,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Spatial&lt;space&gt;Data&lt;space&gt;Only,GEODATABASE_FILE_GEOMETRY,&quot;OPTIONAL DISCLOSUREGROUP SIMPLE_DONUT_GEOMETRY&quot;,GEODATABASE_FILE&lt;space&gt;Geometry,GEODATABASE_FILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,GEODATABASE_FILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,GEODATABASE_FILE_BEGIN_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;Before&lt;space&gt;Read,GEODATABASE_FILE_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_READ_THREE_POINT_ARCS,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Read&lt;space&gt;as&lt;space&gt;Three&lt;space&gt;Point&lt;space&gt;Arcs:,GEODATABASE_FILE_STRIP_GUID_GLOBALID_BRACES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Strip&lt;space&gt;braces&lt;space&gt;off&lt;space&gt;GlobalID&lt;space&gt;and&lt;space&gt;GUID:,GEODATABASE_FILE_CREATE_FEATURE_TABLES_FROM_DATA,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_IGNORE_NETWORK_INFO,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Network&lt;space&gt;Info,GEODATABASE_FILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS%ALIAS_MODE%REMOVE_MAIN_PREFIX&quot;,GEODATABASE_FILE&lt;space&gt;Schema&lt;space&gt;Attributes,GEODATABASE_FILE_FEATURE_READ_MODE,&quot;OPTIONAL CHOICE Features%Metadata&quot;,GEODATABASE_FILE&lt;space&gt;Feature&lt;space&gt;Read&lt;space&gt;Mode:,GEODATABASE_FILE_RESOLVE_DOMAINS,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Resolve&lt;space&gt;Domains,GEODATABASE_FILE_CACHE_MULTIPATCH_TEXTURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Cache&lt;space&gt;Multipatch&lt;space&gt;Textures:,GEODATABASE_FILE_SPLIT_COMPLEX_EDGES,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Split&lt;space&gt;Complex&lt;space&gt;Edges,GEODATABASE_FILE_TABLELIST,&quot;IGNORE TEXT&quot;,GEODATABASE_FILE&lt;space&gt;Tables:,GEODATABASE_FILE_END_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;After&lt;space&gt;Read,GEODATABASE_FILE_GEODB_SHARED_RDR_ADV_PARM_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SPLIT_COMPLEX_ANNOS%CACHE_MULTIPATCH_TEXTURES%CHECK_SIMPLE_GEOM%STRIP_GUID_GLOBALID_BRACES%MERGE_FEAT_LINKED_ANNOS%READ_THREE_POINT_ARCS%BEGIN_SQL{0}%END_SQL{0}%GEOMETRY&quot;,GEODATABASE_FILE&lt;space&gt;Advanced,GEODATABASE_FILE_RESOLVE_SUBTYPE_NAMES,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Resolve&lt;space&gt;Subtypes,GEODATABASE_FILE_ALIAS_MODE,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%Replace&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;With&lt;space&gt;Aliases,SCHEMA%Expose&lt;space&gt;Aliases&lt;space&gt;as&lt;space&gt;Metadata&lt;space&gt;Attributes&lt;space&gt;&lt;openparen&gt;&lt;lt&gt;name&lt;gt&gt;_alias&lt;closeparen&gt;,ON_DATA_FEATURES&quot;,GEODATABASE_FILE&lt;space&gt;Alias&lt;space&gt;Mode:,GEODATABASE_FILE_SPLIT_COMPLEX_ANNOS,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Split&lt;space&gt;Complex&lt;space&gt;Annotations:,GEODATABASE_FILE_IGNORE_RELATIONSHIP_INFO,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Relationship&lt;space&gt;Info,GEODATABASE_FILE_CHECK_SIMPLE_GEOM,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Check&lt;space&gt;for&lt;space&gt;Simple&lt;space&gt;Geometry:,GEODATABASE_FILE_SPLIT_MULTI_PART_ANNOS,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Split&lt;space&gt;Multi-Part&lt;space&gt;Annotations"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ALIAS_MODE" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_BEGIN_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_CACHE_MULTIPATCH_TEXTURES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_CHECK_SIMPLE_GEOM" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_CREATE_FEATURE_TABLES_FROM_DATA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DISABLE_FEATURE_DATASET_ATTRIBUTE" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_END_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FEATURE_READ_MODE" PARM_VALUE="Metadata"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODB_SHARED_RDR_ADV_PARM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEOMETRY" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_NETWORK_INFO" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_RELATIONSHIP_INFO" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_MERGE_FEAT_LINKED_ANNOS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_READ_THREE_POINT_ARCS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_REMOVE_FEATURE_DATASET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_RESOLVE_DOMAINS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_RESOLVE_SUBTYPE_NAMES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLE_DONUT_GEOMETRY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SPLIT_COMPLEX_ANNOS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SPLIT_COMPLEX_EDGES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SPLIT_MULTI_PART_ANNOS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_STRIP_GUID_GLOBALID_BRACES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TABLELIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSLATE_SPATIAL_DATA_ONLY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_WHERE_WWJD" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="153.12653126531291 -1187.511875118751"
#!   BOUNDING_RECT="153.12653126531291 -1187.511875118751 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="10"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="800.12728127281343 -1096.8859688596885"
#!   BOUNDING_RECT="800.12728127281343 -1096.8859688596885 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir_2)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="1362.513625136251 -1187.511875118751"
#!   BOUNDING_RECT="1362.513625136251 -1187.511875118751 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = xlsx"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="14"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1981.2698126981272 -1096.8859688596885"
#!   BOUNDING_RECT="1981.2698126981272 -1096.8859688596885 503.13003130031257 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;u56fe&gt;&lt;u5143&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u5b57&gt;&lt;u5178&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="A" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性00" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性01" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性02" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性03" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="G" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="Sheet1,&quot;TYID,图元标识码&quot;,Sheet2,,Sheet3,&quot;英文名称,英文名称00,数据库,中文名称,第一排序,F,G&quot;,关联关系表结构（通用）,&quot;关联表,关联表00,关联表01,E,关系表,关系表00,关系表01&quot;,图元基础信息字典,&quot;A,注意所有实体基础信息表都需要有对应字段，没有专有属性,注意所有实体基础信息表都需要有对应字段，没有专有属性00,注意所有实体基础信息表都需要有对应字段，没有专有属性01,注意所有实体基础信息表都需要有对应字段，没有专有属性02,注意所有实体基础信息表都需要有对应字段，没有专有属性03,G&quot;,实体分类代码结构,&quot;分类代码,分类名称,父级代码,父级名称,一级类代码,一级类名称,大类,级别,tree,缓冲范围&quot;,实体基础信息字典,&quot;实体信息表英文名称,实体信息表中文名称,字段英文名称,字段中文名称,对应数据库表名,备注,G&quot;,数据资源清单,&quot;一级图层,二级图层,中文对照名,三级图层,中文对照名00,数据类型,服务类型,服务地址,图层序列,数据库,数据表名称&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="Sheet1,&quot;varchar(4),varchar(15)&quot;,Sheet2,,Sheet3,&quot;varchar(9),varchar(7),varchar(6),varchar(30),int16,varchar(5),varchar(15)&quot;,关联关系表结构（通用）,&quot;varchar(18),varchar(18),buffer,buffer,varchar(18),varchar(18),buffer&quot;,图元基础信息字典,&quot;varchar(18),varchar(61),varchar(63),varchar(33),varchar(21),varchar(6),buffer&quot;,实体分类代码结构,&quot;int32,varchar(48),int32,buffer,int32,varchar(27),varchar(18),varchar(9),buffer,buffer&quot;,实体基础信息字典,&quot;varchar(13),varchar(43),varchar(6),varchar(18),varchar(6),buffer,buffer&quot;,数据资源清单,&quot;varchar(4),varchar(9),varchar(33),varchar(13),varchar(33),varchar(6),varchar(6),varchar(76),int16,buffer,varchar(20)&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%Sheet1%Sheet2%Sheet3%关联关系表结构（通用）%图元基础信息字典%实体分类代码结构%实体基础信息字典%数据资源清单"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="&lt;u56fe&gt;&lt;u5143&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u5b57&gt;&lt;u5178&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="Sheet1,,Sheet2,,Sheet3,,关联关系表结构（通用）,,图元基础信息字典,,实体分类代码结构,,实体基础信息字典,,数据资源清单,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXR_SKIP_EMPTY_ROWS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_USE_CUSTOM_SCHEMA,&quot;OPTIONAL RADIO_GROUP 2%Automatic,NO%Manual,YES&quot;,XLSXR&lt;space&gt;Attribute&lt;space&gt;Definition,XLSXR_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP APPLY_FILTERS%SCAN_MAX_FEATURES%TRIM_ATTR_NAME_WHITESPACE%TRIM_ATTR_NAME_CHARACTERS%READ_BLANK_AS%EXPAND_MERGED_CELLS%READ_RASTER_MODE%READ_FORM_CONTROLS%SCAN_FOR_GEOMETRIC_TYPES&quot;,XLSXR&lt;space&gt;Advanced,XLSXR_READ_FORM_CONTROLS,&quot;OPTIONAL CHECKBOX CELL_VALUE%NONE&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Form&lt;space&gt;Control&lt;space&gt;as&lt;space&gt;Cell&lt;space&gt;Values:,XLSXR_CONFIGURATION_DATASET,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_TRIM_ATTR_NAME_WHITESPACE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Whitespace&lt;space&gt;From&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_EXCEL_COL_NAMES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_CASE_SENSITIVE_FEATURE_TYPES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_READ_BLANK_AS,&quot;OPTIONAL CHOICE Missing%Null&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Cells&lt;space&gt;As:,XLSXR_SCHEMA_HANDLING_REVISION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SCAN_MAX_FEATURES,&quot;OPTIONAL RANGE_SLIDER 0%MAX%0&quot;,XLSXR&lt;space&gt;Maximum&lt;space&gt;Rows&lt;space&gt;to&lt;space&gt;Scan:,XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_CREATE_FEATURE_TABLES_FROM_DATA,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP XLSXR_EXPOSE_FORMAT_ATTRS&quot;,XLSXR&lt;space&gt;Schema&lt;space&gt;Attributes,XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS XLSXR%Source&quot;,XLSXR&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,XLSXR_READ_RASTER_MODE,&quot;OPTIONAL LOOKUP_CHOICE None%Attribute%Geometry&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Embedded&lt;space&gt;Images&lt;space&gt;As:,XLSXR_FORCE_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_APPLY_FILTERS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Apply&lt;space&gt;Filter&lt;openparen&gt;s&lt;closeparen&gt;:,XLSXR_SCHEMA,&quot;OPTIONAL STRING&quot;,XLSXR&lt;space&gt;To&lt;space&gt;be&lt;space&gt;populated,XLSXR_TABLELIST,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SCAN_FOR_GEOMETRIC_TYPES,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Scan&lt;space&gt;For&lt;space&gt;Geometric&lt;space&gt;Types:,XLSXR_ALLOW_DOLLAR_SIGNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_TRIM_ATTR_NAME_CHARACTERS,&quot;OPTIONAL TEXT_EDIT_ENCODED FME_INCLUDEBROWSE%NO&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Characters&lt;space&gt;from&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_STRIP_SHEETNAME_SPACES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_EXPAND_MERGED_CELLS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Expand&lt;space&gt;Merged&lt;space&gt;Cells:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_4"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ALLOW_DOLLAR_SIGNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_APPLY_FILTERS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CASE_SENSITIVE_FEATURE_TYPES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CONFIGURATION_DATASET" PARM_VALUE="$(FME_MF_DIR)图元基础信息字典\新吴实景三维——新规范信息表.xlsx"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CREATE_FEATURE_TABLES_FROM_DATA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXCEL_COL_NAMES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPAND_MERGED_CELLS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_FORCE_DATETIME" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_BLANK_AS" PARM_VALUE="Missing"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_FORM_CONTROLS" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_RASTER_MODE" PARM_VALUE="None"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_FOR_GEOMETRIC_TYPES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_MAX_FEATURES" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA" PARM_VALUE="&lt;u6570&gt;&lt;u636e&gt;&lt;u8d44&gt;&lt;u6e90&gt;&lt;u6e05&gt;&lt;u5355&gt;,0&lt;comma&gt;&lt;u4e00&gt;&lt;u7ea7&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;comma&gt;char&lt;comma&gt;4&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u4e8c&gt;&lt;u7ea7&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;comma&gt;char&lt;comma&gt;9&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u5bf9&gt;&lt;u7167&gt;&lt;u540d&gt;&lt;comma&gt;char&lt;comma&gt;33&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;&lt;u4e09&gt;&lt;u7ea7&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;comma&gt;char&lt;comma&gt;13&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u5bf9&gt;&lt;u7167&gt;&lt;u540d&gt;00&lt;comma&gt;char&lt;comma&gt;33&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;comma&gt;char&lt;comma&gt;6&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;&lt;u670d&gt;&lt;u52a1&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;comma&gt;char&lt;comma&gt;6&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;7&lt;comma&gt;&lt;u670d&gt;&lt;u52a1&gt;&lt;u5730&gt;&lt;u5740&gt;&lt;comma&gt;char&lt;comma&gt;76&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;8&lt;comma&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u5e8f&gt;&lt;u5217&gt;&lt;comma&gt;number&lt;comma&gt;2&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;9&lt;comma&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;10&lt;comma&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u8868&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;20&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;E:&lt;lt&gt;backslash&lt;gt&gt;YC&lt;lt&gt;backslash&lt;gt&gt;&lt;lt&gt;u6bcf&lt;gt&gt;&lt;lt&gt;u65e5&lt;gt&gt;&lt;lt&gt;u4efb&lt;gt&gt;&lt;lt&gt;u52a1&lt;gt&gt;&lt;lt&gt;backslash&lt;gt&gt;2025&lt;lt&gt;backslash&lt;gt&gt;0108&lt;lt&gt;backslash&lt;gt&gt;&lt;lt&gt;u6279&lt;gt&gt;&lt;lt&gt;u91cf&lt;gt&gt;&lt;lt&gt;u5bfc&lt;gt&gt;&lt;lt&gt;u51fa&lt;gt&gt;&lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5c42&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u3001&lt;gt&gt;&lt;lt&gt;u5b57&lt;gt&gt;&lt;lt&gt;u6bb5&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;backslash&lt;gt&gt;&lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5143&lt;gt&gt;&lt;lt&gt;u57fa&lt;gt&gt;&lt;lt&gt;u7840&lt;gt&gt;&lt;lt&gt;u4fe1&lt;gt&gt;&lt;lt&gt;u606f&lt;gt&gt;&lt;lt&gt;u5b57&lt;gt&gt;&lt;lt&gt;u5178&lt;gt&gt;&lt;lt&gt;backslash&lt;gt&gt;&lt;lt&gt;u65b0&lt;gt&gt;&lt;lt&gt;u5434&lt;gt&gt;&lt;lt&gt;u5b9e&lt;gt&gt;&lt;lt&gt;u666f&lt;gt&gt;&lt;lt&gt;u4e09&lt;gt&gt;&lt;lt&gt;u7ef4&lt;gt&gt;&lt;lt&gt;u2014&lt;gt&gt;&lt;lt&gt;u2014&lt;gt&gt;&lt;lt&gt;u65b0&lt;gt&gt;&lt;lt&gt;u89c4&lt;gt&gt;&lt;lt&gt;u8303&lt;gt&gt;&lt;lt&gt;u4fe1&lt;gt&gt;&lt;lt&gt;u606f&lt;gt&gt;&lt;lt&gt;u8868&lt;gt&gt;.xlsx&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;10&lt;comma&gt;165&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u5206&gt;&lt;u7c7b&gt;&lt;u4ee3&gt;&lt;u7801&gt;&lt;u7ed3&gt;&lt;u6784&gt;,0&lt;comma&gt;&lt;u5206&gt;&lt;u7c7b&gt;&lt;u4ee3&gt;&lt;u7801&gt;&lt;comma&gt;number&lt;comma&gt;6&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u5206&gt;&lt;u7c7b&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;48&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u7236&gt;&lt;u7ea7&gt;&lt;u4ee3&gt;&lt;u7801&gt;&lt;comma&gt;number&lt;comma&gt;6&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;&lt;u7236&gt;&lt;u7ea7&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;&lt;u4e00&gt;&lt;u7ea7&gt;&lt;u7c7b&gt;&lt;u4ee3&gt;&lt;u7801&gt;&lt;comma&gt;number&lt;comma&gt;6&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;&lt;u4e00&gt;&lt;u7ea7&gt;&lt;u7c7b&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;27&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;&lt;u5927&gt;&lt;u7c7b&gt;&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;7&lt;comma&gt;&lt;u7ea7&gt;&lt;u522b&gt;&lt;comma&gt;char&lt;comma&gt;9&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;8&lt;comma&gt;tree&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;9&lt;comma&gt;&lt;u7f13&gt;&lt;u51b2&gt;&lt;u8303&gt;&lt;u56f4&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;9&lt;comma&gt;584&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,&lt;u56fe&gt;&lt;u5143&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u5b57&gt;&lt;u5178&gt;,0&lt;comma&gt;A&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;comma&gt;char&lt;comma&gt;61&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;00&lt;comma&gt;char&lt;comma&gt;63&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;01&lt;comma&gt;char&lt;comma&gt;33&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;02&lt;comma&gt;char&lt;comma&gt;21&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;03&lt;comma&gt;char&lt;comma&gt;6&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;G&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;6&lt;comma&gt;1099&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u5b57&gt;&lt;u5178&gt;,0&lt;comma&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;13&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;43&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;6&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u8868&gt;&lt;u540d&gt;&lt;comma&gt;char&lt;comma&gt;6&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;&lt;u5907&gt;&lt;u6ce8&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;G&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;6&lt;comma&gt;730&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,&lt;u5173&gt;&lt;u8054&gt;&lt;u5173&gt;&lt;u7cfb&gt;&lt;u8868&gt;&lt;u7ed3&gt;&lt;u6784&gt;&lt;uff08&gt;&lt;u901a&gt;&lt;u7528&gt;&lt;uff09&gt;,1&lt;comma&gt;&lt;u5173&gt;&lt;u8054&gt;&lt;u8868&gt;&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u5173&gt;&lt;u8054&gt;&lt;u8868&gt;00&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;&lt;u5173&gt;&lt;u8054&gt;&lt;u8868&gt;01&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;E&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;&lt;u5173&gt;&lt;u7cfb&gt;&lt;u8868&gt;&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;&lt;u5173&gt;&lt;u7cfb&gt;&lt;u8868&gt;00&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;7&lt;comma&gt;&lt;u5173&gt;&lt;u7cfb&gt;&lt;u8868&gt;01&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,2&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;2&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;1&lt;comma&gt;1&lt;comma&gt;7&lt;comma&gt;8&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,Sheet1,0&lt;comma&gt;TYID&lt;comma&gt;char&lt;comma&gt;4&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u56fe&gt;&lt;u5143&gt;&lt;u6807&gt;&lt;u8bc6&gt;&lt;u7801&gt;&lt;comma&gt;char&lt;comma&gt;15&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;1&lt;comma&gt;7&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,Sheet3,0&lt;comma&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;9&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;00&lt;comma&gt;char&lt;comma&gt;7&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;comma&gt;char&lt;comma&gt;6&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;30&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;&lt;u7b2c&gt;&lt;u4e00&gt;&lt;u6392&gt;&lt;u5e8f&gt;&lt;comma&gt;number&lt;comma&gt;3&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;F&lt;comma&gt;char&lt;comma&gt;5&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;G&lt;comma&gt;char&lt;comma&gt;15&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;6&lt;comma&gt;1097&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO,Sheet2,,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;18446744073709551615&lt;comma&gt;18446744073709551615&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA_HANDLING_REVISION" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SKIP_EMPTY_ROWS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_STRIP_SHEETNAME_SPACES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TABLELIST" PARM_VALUE="数据资源清单 实体分类代码结构 图元基础信息字典 实体基础信息字典 关联关系表结构（通用） Sheet1 Sheet3 Sheet2"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_CHARACTERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_WHITESPACE" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_USE_CUSTOM_SCHEMA" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="16"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="2675.0267502675024 -1187.511875118751"
#!   BOUNDING_RECT="2675.0267502675024 -1187.511875118751 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="A" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性00" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性01" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性02" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="注意所有实体基础信息表都需要有对应字段，没有专有属性03" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="G" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;A&lt;closeparen&gt; varchar&lt;openparen&gt;18&lt;closeparen&gt;  &lt;u56fe&gt;&lt;u5143&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;61&lt;closeparen&gt;  &lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;00&lt;closeparen&gt; varchar&lt;openparen&gt;63&lt;closeparen&gt;  &lt;u5b57&gt;&lt;u6bb5&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;01&lt;closeparen&gt; varchar&lt;openparen&gt;33&lt;closeparen&gt;  &lt;u5bf9&gt;&lt;u5e94&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u8868&gt;&lt;u540d&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6ce8&gt;&lt;u610f&gt;&lt;u6240&gt;&lt;u6709&gt;&lt;u5b9e&gt;&lt;u4f53&gt;&lt;u57fa&gt;&lt;u7840&gt;&lt;u4fe1&gt;&lt;u606f&gt;&lt;u8868&gt;&lt;u90fd&gt;&lt;u9700&gt;&lt;u8981&gt;&lt;u6709&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;uff0c&gt;&lt;u6ca1&gt;&lt;u6709&gt;&lt;u4e13&gt;&lt;u6709&gt;&lt;u5c5e&gt;&lt;u6027&gt;02&lt;closeparen&gt; varchar&lt;openparen&gt;21&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="3215.6571565715653 -1187.511875118751"
#!   BOUNDING_RECT="3215.6571565715653 -1187.511875118751 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u8868&gt;&lt;u540d&gt;,&lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u56fe&gt;&lt;u5143&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="20"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="3771.9127191271914 -1187.511875118751"
#!   BOUNDING_RECT="3771.9127191271914 -1187.511875118751 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u8868&gt;&lt;u540d&gt;&lt;closeparen&gt; = &lt;u5bf9&gt;&lt;u5e94&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u8868&gt;&lt;u540d&gt;&#10;TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;closeparen&gt; = &quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="22"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="3265.6576565765658 -610.00550005500065"
#!   BOUNDING_RECT="3265.6576565765658 -610.00550005500065 430 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u6240&gt;&lt;u5c5e&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u96c6&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt; SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt; != _FME_BLANK_STRING_&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;1&lt;closecurly&gt;&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS2___&quot; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="24"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="2675.0267502675024 -610.00550005500065"
#!   BOUNDING_RECT="2675.0267502675024 -610.00550005500065 430 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="fme_feature_type_name"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="&lt;solidus&gt;"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="27"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="3821.9132191321914 -550.00550005500065"
#!   BOUNDING_RECT="3821.9132191321914 -550.00550005500065 430 71"
#!   ORDER="500000000000015"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="native_data_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="attribute{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="29"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="4393.7939379393792 -584.38084380843793"
#!   BOUNDING_RECT="4393.7939379393792 -584.38084380843793 430 71"
#!   ORDER="500000000000016"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="native_data_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;name&lt;closeparen&gt; = OBJECTID&#10;CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;name&lt;closeparen&gt; CONTAINS SHAPE_Area&#10;CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;name&lt;closeparen&gt; CONTAINS SHAPE_Length"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="CASE_INSENSITIVE_TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="31"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="5018.8001880018792 -610.00550005500065"
#!   BOUNDING_RECT="5018.8001880018792 -610.00550005500065 430 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u6240&gt;&lt;u5c5e&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u96c6&gt;,&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;,name"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="33"
#!   TYPE="FeatureMerger"
#!   VERSION="20"
#!   POSITION="5653.1815318153176 -768.75768757687592"
#!   BOUNDING_RECT="5653.1815318153176 -768.75768757687592 562.50562505625021 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="MERGED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNMERGED_REQUESTOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="USED_SUPPLIER"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="numReferences" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="UNUSED_SUPPLIER"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Supplier"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Requestor"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEOM_TYPE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="JOIN_ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="JOIN_KEYS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt; &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;closeparen&gt; AUTO"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MERGE_COUNT_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MERGE_TYPE" PARM_VALUE="Attributes Only"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="PROCESS_DUPS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REJECT_NULL_MISSING_KEYS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SUPPLIERS_FIRST" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SUPPLIER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureMerger"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="34"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="4350.0435004350047 -978.13478134781326"
#!   BOUNDING_RECT="4350.0435004350047 -978.13478134781326 430 71"
#!   ORDER="500000000000019"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="&lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; &lt;u56fe&gt;&lt;u5143&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="36"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="4393.7939379393792 -1350.010500105001"
#!   BOUNDING_RECT="4393.7939379393792 -1350.010500105001 430 71"
#!   ORDER="500000000000019"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; &lt;u5b57&gt;&lt;u6bb5&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="40"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="4981.2998129981279 -978.13478134781326"
#!   BOUNDING_RECT="4981.2998129981279 -978.13478134781326 430 71"
#!   ORDER="500000000000020"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u56fe&gt;&lt;u5143&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="43"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="5050.6776650871961 -1350.010500105001"
#!   BOUNDING_RECT="5050.6776650871961 -1350.010500105001 430 71"
#!   ORDER="500000000000020"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="45"
#!   TYPE="FeatureMerger"
#!   VERSION="20"
#!   POSITION="6403.8190442109499 -1209.3840938409382"
#!   BOUNDING_RECT="6403.8190442109499 -1209.3840938409382 665.00164399593177 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="MERGED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNMERGED_REQUESTOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="USED_SUPPLIER"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="numReferences" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="UNUSED_SUPPLIER"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Supplier"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Requestor"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEOM_TYPE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="JOIN_ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="JOIN_KEYS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;name&lt;closeparen&gt; &lt;at&gt;Value&lt;openparen&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;closeparen&gt; AUTO"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MERGE_COUNT_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MERGE_TYPE" PARM_VALUE="Attributes Only"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="PROCESS_DUPS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="REJECT_NULL_MISSING_KEYS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SUPPLIERS_FIRST" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SUPPLIER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureMerger_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="49"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="7225.0722507225073 -1415.6391563915638"
#!   BOUNDING_RECT="7225.0722507225073 -1415.6391563915638 430 71"
#!   ORDER="500000000000022"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="所属数据集" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u56fe&gt;&lt;u5c42&gt;&lt;u540d&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;name&lt;closeparen&gt; buffer  &lt;u5bf9&gt;&lt;u5e94&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u8868&gt;&lt;u540d&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6240&gt;&lt;u5c5e&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u96c6&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="51"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="7843.8284382843804 -1415.6391563915638"
#!   BOUNDING_RECT="7843.8284382843804 -1415.6391563915638 430 71"
#!   ORDER="500000000000023"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="&lt;u5bf9&gt;&lt;u5e94&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u8868&gt;&lt;u540d&gt;,&lt;u56fe&gt;&lt;u5143&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u56fe&gt;&lt;u5143&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u82f1&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;,&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u4e2d&gt;&lt;u6587&gt;&lt;u540d&gt;&lt;u79f0&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="54"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="8862.5886258862611 -1378.1387813878137"
#!   BOUNDING_RECT="8862.5886258862611 -1378.1387813878137 430 71"
#!   ORDER="500000000000024"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER)&lt;backslash&gt;&lt;u6570&gt;&lt;u636e&gt;&lt;u5e93&gt;&lt;u5bfc&gt;&lt;u51fa&gt;.xlsx"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_FILE&lt;comma&gt;No&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;comma&gt;TEMPLATE_SHEET&lt;comma&gt;&lt;comma&gt;REMOVE_UNCHANGED_TEMPLATE_SHEET&lt;comma&gt;No&lt;comma&gt;MULTIPLE_TEMPLATE_SHEETS&lt;comma&gt;Yes&lt;comma&gt;INSERT_IGNORE_DB_OP&lt;comma&gt;Yes&lt;comma&gt;DROP_TABLE&lt;comma&gt;No&lt;comma&gt;TRUNCATE_TABLE&lt;comma&gt;No&lt;comma&gt;FIELD_NAMES_OUT&lt;comma&gt;Yes&lt;comma&gt;FIELD_NAMES_FORMATTING&lt;comma&gt;Yes&lt;comma&gt;WRITER_MODE&lt;comma&gt;Insert&lt;comma&gt;RASTER_FORMAT&lt;comma&gt;PNG&lt;comma&gt;PROTECT_SHEET&lt;comma&gt;NO&lt;comma&gt;PROTECT_SHEET_PASSWORD&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_LEVEL&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_PERMISSIONS&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING&lt;comma&gt;yes&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE&lt;comma&gt;CUSTOM_NUMBER_FORMATTING&lt;comma&gt;ENABLE_NATIVE&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXW_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_INSERT_IGNORE_DB_OP,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_WRITER_MODE,&quot;OPTIONAL CHOICE Insert%Update%Delete&quot;,XLSXW&lt;space&gt;Default&lt;space&gt;Feature&lt;space&gt;Type&lt;space&gt;Writer&lt;space&gt;Mode:,XLSXW_OVERWRITE_FILE,&quot;OPTIONAL ACTIVECHOICE Yes%No,TEMPLATEFILE,TEMPLATE_SHEET,REMOVE_UNCHANGED_TEMPLATE_SHEET&quot;,XLSXW&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;File:,XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_FIELD_NAMES_FORMATTING,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:,XLSXW_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXW&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,XLSXW_RASTER_FORMAT,&quot;OPTIONAL CHOICE BMP%JPEG%PNG&quot;,XLSXW&lt;space&gt;Raster&lt;space&gt;Format:,XLSXW_CUSTOM_NUMBER_FORMATTING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_MULTIPLE_TEMPLATE_SHEETS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_TRUNCATE_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_DROP_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_PROTECT_SHEET,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP PROTECT_SHEET_PASSWORD%PROTECT_SHEET_LEVEL%PROTECT_SHEET_PERMISSIONS&quot;,XLSXW&lt;space&gt;Protect&lt;space&gt;Sheet,XLSXW_FIELD_NAMES_OUT,&quot;OPTIONAL ACTIVECHOICE Yes%No,FIELD_NAMES_FORMATTING,++FIELD_NAMES_FORMATTING+No&quot;,XLSXW&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="sheet1:Output,ftp_feature_type_name,sheet1,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5143&lt;gt&gt;&lt;lt&gt;u82f1&lt;gt&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;11&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5143&lt;gt&gt;&lt;lt&gt;u4e2d&lt;gt&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;25&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;11&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5b57&lt;gt&gt;&lt;lt&gt;u6bb5&lt;gt&gt;&lt;lt&gt;u82f1&lt;gt&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;11&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5b57&lt;gt&gt;&lt;lt&gt;u6bb5&lt;gt&gt;&lt;lt&gt;u4e2d&lt;gt&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;11&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5bf9&lt;gt&gt;&lt;lt&gt;u5e94&lt;gt&gt;&lt;lt&gt;u6570&lt;gt&gt;&lt;lt&gt;u636e&lt;gt&gt;&lt;lt&gt;u5e93&lt;gt&gt;&lt;lt&gt;u8868&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;11&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_xml&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_json&lt;comma&gt;&lt;quote&gt;auto&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;datetime&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_datetime&lt;comma&gt;&lt;quote&gt;time&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_time&lt;comma&gt;&lt;quote&gt;date&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_date&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;boolean&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;openbracket&gt;&lt;closebracket&gt;*&lt;backslash&gt;&lt;backslash&gt;?:&lt;apos&gt;,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none&lt;comma&gt;fme_no_geom&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point&lt;comma&gt;xlsx_point&lt;comma&gt;fme_point&lt;comma&gt;xlsx_none&lt;comma&gt;fme_line&lt;comma&gt;xlsx_none&lt;comma&gt;fme_polygon&lt;comma&gt;xlsx_none&lt;comma&gt;fme_text&lt;comma&gt;xlsx_none&lt;comma&gt;fme_ellipse&lt;comma&gt;xlsx_none&lt;comma&gt;fme_arc&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_collection&lt;comma&gt;xlsx_none&lt;comma&gt;fme_surface&lt;comma&gt;xlsx_none&lt;comma&gt;fme_solid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_raster&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point_cloud&lt;comma&gt;xlsx_none&lt;comma&gt;fme_voxel_grid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;xlsx_layer_group&lt;space&gt;xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group&lt;space&gt;Sheet&lt;space&gt;Settings&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_truncate_group&lt;space&gt;xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet&lt;space&gt;Drop&lt;solidus&gt;Truncate&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_rowcolumn_group&lt;space&gt;xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Position&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVEDISCLOSUREGROUP&lt;space&gt;xlsx_protect_sheet&lt;space&gt;xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions&lt;space&gt;Protect&lt;space&gt;Sheet&lt;quote&gt;&lt;comma&gt;NO&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_template_group&lt;space&gt;xlsx_template_sheet%xlsx_remove_unchanged_template_sheet&lt;space&gt;Template&lt;space&gt;Options&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_advanced_group&lt;space&gt;xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_drop_sheet&lt;space&gt;Yes%No&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_trunc_sheet&lt;space&gt;Yes%No&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_sheet_order&lt;space&gt;1%MAX&lt;space&gt;Sheet&lt;space&gt;Order&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_freeze_end_row&lt;space&gt;1%MAX&lt;space&gt;Freeze&lt;space&gt;First&lt;space&gt;Row&lt;openparen&gt;s&lt;closeparen&gt;&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_field_names_out&lt;space&gt;Yes%No&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;++xlsx_field_names_formatting+No&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_field_names_formatting&lt;space&gt;Yes%No&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_names_are_positions&lt;space&gt;Yes%No&lt;space&gt;Use&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;As&lt;space&gt;Column&lt;space&gt;Positions:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_start_col&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_start_row&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_offset_col&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_raster_type&lt;space&gt;BMP%JPEG%PNG&lt;space&gt;Raster&lt;space&gt;Format:&lt;quote&gt;&lt;comma&gt;PNG&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;PASSWORD_ENCODED&lt;space&gt;xlsx_protect_sheet_password&lt;space&gt;Password:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;xlsx_protect_sheet_level&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_DEFAULT&lt;comma&gt;xlsx_protect_sheet_permissions%View&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_ALL&lt;comma&gt;xlsx_protect_sheet_permissions%Specific&lt;lt&gt;space&lt;gt&gt;Permissions&lt;space&gt;Protection&lt;space&gt;Level:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;LOOKUP_LISTBOX&lt;space&gt;xlsx_protect_sheet_permissions&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;locked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_LOCKED_CELLS%Select&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_UNLOCKED_CELLS%Format&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_FORMAT_CELLS%Format&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_FORMAT_COLUMNS%Format&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_FORMAT_ROWS%Insert&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_INSERT_COLUMNS%Insert&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_INSERT_ROWS%Add&lt;lt&gt;space&lt;gt&gt;hyperlinks&lt;lt&gt;space&lt;gt&gt;to&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_INSERT_HYPERLINKS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_DELETE_COLUMNS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_DELETE_ROWS%Sort&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;solidus&gt;rows&lt;solidus&gt;columns&lt;comma&gt;PROT_SORT%Use&lt;lt&gt;space&lt;gt&gt;Autofilter&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_AUTOFILTER%Use&lt;lt&gt;space&lt;gt&gt;PivotTable&lt;lt&gt;space&lt;gt&gt;&lt;amp&gt;&lt;lt&gt;space&lt;gt&gt;PivotChart&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_PIVOTTABLES%Edit&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;objects&lt;comma&gt;PROT_OBJECTS%Edit&lt;lt&gt;space&lt;gt&gt;unprotected&lt;lt&gt;space&lt;gt&gt;scenarios&lt;comma&gt;PROT_SCENARIOS&lt;space&gt;Specific&lt;space&gt;Permissions:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_table_writer_mode&lt;space&gt;Insert&lt;comma&gt;+xlsx_row_id_column+&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;%Update&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id%Delete&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id&lt;space&gt;Writer&lt;space&gt;Mode:&lt;quote&gt;&lt;comma&gt;Insert&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ATTR&lt;space&gt;xlsx_row_id_column&lt;space&gt;ALLOW_NEW&lt;space&gt;Row&lt;space&gt;Number&lt;space&gt;Attribute:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT_EDIT&lt;space&gt;xlsx_template_sheet&lt;space&gt;Template&lt;space&gt;Sheet:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_remove_unchanged_template_sheet&lt;space&gt;Yes%No&lt;space&gt;Remove&lt;space&gt;Template&lt;space&gt;Sheet&lt;space&gt;if&lt;space&gt;Unchanged:&lt;quote&gt;&lt;comma&gt;No,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;NO&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;No&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,DEFAULT_READER&lt;comma&gt;XLSXR&lt;comma&gt;ALLOW_DATASET_CONFLICT&lt;comma&gt;YES&lt;comma&gt;MIME_TYPE&lt;comma&gt;&lt;quote&gt;application&lt;solidus&gt;vnd.openxmlformats-officedocument.spreadsheetml.sheet&lt;space&gt;ADD_DISPOSITION&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;DEFAULT_ATTR_TYPE&lt;comma&gt;auto&lt;comma&gt;USER_ATTRIBUTES_COLUMNS&lt;comma&gt;&lt;quote&gt;Width&lt;comma&gt;Cell&lt;space&gt;Width%Precision&lt;comma&gt;Formatting&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Sheet&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Sheet1&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;name&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Microsoft&lt;space&gt;Excel&lt;space&gt;file&lt;quote&gt;,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_CUSTOM_NUMBER_FORMATTING" PARM_VALUE="ENABLE_NATIVE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DROP_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_FORMATTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_OUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_INSERT_IGNORE_DB_OP" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_MULTIPLE_TEMPLATE_SHEETS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXW_OVERWRITE_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_PROTECT_SHEET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_RASTER_FORMAT" PARM_VALUE="PNG"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_TRUNCATE_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_WRITER_MODE" PARM_VALUE="Insert"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="56"
#!   TYPE="ExcelStyler"
#!   VERSION="1"
#!   POSITION="8346.9584695846988 -1512.5151251512514"
#!   BOUNDING_RECT="8346.9584695846988 -1512.5151251512514 430 71"
#!   ORDER="500000000000025"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ExcelStyled"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="图元英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段英文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="对应数据库表名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图元中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="字段中文名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="xlsx_row_formatting" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_STYLE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BACKGROUND_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_COLOR" PARM_VALUE="0,0,0"/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_STYLE" PARM_VALUE="BORDERSTYLE_THIN"/>
#!     <XFORM_PARM PARM_NAME="CELL_PROTECTION_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILL_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_NAME" PARM_VALUE="&lt;u5b8b&gt;&lt;u4f53&gt;&lt;comma&gt;11"/>
#!     <XFORM_PARM PARM_NAME="HIDDEN" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="HORIZONTAL_ALIGNMENT" PARM_VALUE="center"/>
#!     <XFORM_PARM PARM_NAME="INDENT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="LOCKED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NUMBER_FORMAT_STRING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NUMBER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATTERN_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATTERN_STYLE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ROW_HEIGHT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ROW_OR_CELL" PARM_VALUE="row"/>
#!     <XFORM_PARM PARM_NAME="SELECTION_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEXT_ALIGNMENT_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="TEXT_CONTROL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEXT_ORIENTATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VERTICAL_ALIGNMENT" PARM_VALUE="center"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ExcelStyler"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="10"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="8"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="24"
#!   SOURCE_PORT_DESC="fo 0 &lt;lt&gt;SCHEMA&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="14"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="19"
#!   SOURCE_NODE="16"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="21"
#!   SOURCE_NODE="18"
#!   TARGET_NODE="20"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="28"
#!   SOURCE_NODE="22"
#!   TARGET_NODE="27"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="26"
#!   SOURCE_NODE="24"
#!   TARGET_NODE="22"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="30"
#!   SOURCE_NODE="27"
#!   TARGET_NODE="29"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="38"
#!   SOURCE_NODE="31"
#!   TARGET_NODE="33"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 REQUESTOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="46"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="45"
#!   SOURCE_PORT_DESC="fo 0 MERGED"
#!   TARGET_PORT_DESC="fi 0 REQUESTOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="41"
#!   SOURCE_NODE="34"
#!   TARGET_NODE="40"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="44"
#!   SOURCE_NODE="36"
#!   TARGET_NODE="43"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="42"
#!   SOURCE_NODE="40"
#!   TARGET_NODE="33"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 SUPPLIER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="48"
#!   SOURCE_NODE="43"
#!   TARGET_NODE="45"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 SUPPLIER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="50"
#!   SOURCE_NODE="45"
#!   TARGET_NODE="49"
#!   SOURCE_PORT_DESC="fo 0 MERGED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="52"
#!   SOURCE_NODE="49"
#!   TARGET_NODE="51"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="57"
#!   SOURCE_NODE="51"
#!   TARGET_NODE="56"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="58"
#!   SOURCE_NODE="56"
#!   TARGET_NODE="54"
#!   SOURCE_PORT_DESC="fo 0 ExcelStyled"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="35"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="34"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="37"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="36"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="32"
#!   SOURCE_NODE="29"
#!   TARGET_NODE="31"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="47"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="45"
#!   SOURCE_PORT_DESC="fo 1 UNMERGED_REQUESTOR"
#!   TARGET_PORT_DESC="fi 0 REQUESTOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="53"
#!   SOURCE_NODE="45"
#!   TARGET_NODE="49"
#!   SOURCE_PORT_DESC="fo 1 UNMERGED_REQUESTOR"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="6"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="13"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="17"
#!   SOURCE_NODE="14"
#!   TARGET_NODE="16"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5143&lt;gt&gt;&lt;lt&gt;u57fa&lt;gt&gt;&lt;lt&gt;u7840&lt;gt&gt;&lt;lt&gt;u4fe1&lt;gt&gt;&lt;lt&gt;u606f&lt;gt&gt;&lt;lt&gt;u5b57&lt;gt&gt;&lt;lt&gt;u5178&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO dir $(FME_MF_DIR)无锡最终成果

DEFAULT_MACRO dir_2 $(FME_MF_DIR)图元基础信息字典

DEFAULT_MACRO PARAMETER C:\Users\<USER>\Desktop

# ============================================================================
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir_2$encode)} == {}} { puts_real {Parameter 'dir_2' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)none2none.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME none2none
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_2_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_2_XML     NOT_ACTIVATED
MACRO Creator_2_CLASSIC NOT_ACTIVATED
MACRO Creator_2_2D3D    2D_GEOMETRY
MACRO Creator_2_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_2_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2_2D3D 3D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_2: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_2_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
FACTORY_DEF {$(Creator_2_XML)} CreationFactory    FACTORY_NAME { Creator_2_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_2_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_2_CLASSIC_Creator }    $(Creator_2_2D3D) { $(Creator_2_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_2_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_2_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_2_CREATED        fme_feature_type Creator_2         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_2_CREATED Brancher -1 11"   INPUT FEATURE_TYPE Creator_2_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_3_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,$(dir_2$encode),FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE Creator_2_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT PATH FEATURE_TYPE FeatureReader_3_PATH
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE FeatureReader_3_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester_2)" = xlsx ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_4_OUTPUT_PORTS_ENCODED <u56fe><u5143><u57fa><u7840><u4fe1><u606f><u5b57><u5178>
MACRO FeatureReader_4_DIRECTIVES ADVANCED,,ALLOW_DOLLAR_SIGNS,YES,APPLY_FILTERS,No,CASE_SENSITIVE_FEATURE_TYPES,YES,CONFIGURATION_DATASET,$(FME_MF_DIR$encode)<u56fe><u5143><u57fa><u7840><u4fe1><u606f><u5b57><u5178><backslash><u65b0><u5434><u5b9e><u666f><u4e09><u7ef4><u2014><u2014><u65b0><u89c4><u8303><u4fe1><u606f><u8868>.xlsx,CREATE_FEATURE_TABLES_FROM_DATA,Yes,EXCEL_COL_NAMES,YES,EXPAND_MERGED_CELLS,Yes,EXPOSE_ATTRS_GROUP,,FORCE_DATETIME,NO,QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,Yes,READ_BLANK_AS,Missing,READ_FORM_CONTROLS,NONE,READ_RASTER_MODE,None,REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,YES,SCAN_FOR_GEOMETRIC_TYPES,Yes,SCAN_MAX_FEATURES,1000,SCHEMA,<lt>u6570<gt><lt>u636e<gt><lt>u8d44<gt><lt>u6e90<gt><lt>u6e05<gt><lt>u5355<gt><comma>0<lt>comma<gt><lt>u4e00<gt><lt>u7ea7<gt><lt>u56fe<gt><lt>u5c42<gt><lt>comma<gt>char<lt>comma<gt>4<lt>comma<gt><lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u4e8c<gt><lt>u7ea7<gt><lt>u56fe<gt><lt>u5c42<gt><lt>comma<gt>char<lt>comma<gt>9<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u4e2d<gt><lt>u6587<gt><lt>u5bf9<gt><lt>u7167<gt><lt>u540d<gt><lt>comma<gt>char<lt>comma<gt>33<lt>comma<gt><lt>comma<gt><lt>comma<gt>3<lt>comma<gt><lt>u4e09<gt><lt>u7ea7<gt><lt>u56fe<gt><lt>u5c42<gt><lt>comma<gt>char<lt>comma<gt>13<lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt><lt>u4e2d<gt><lt>u6587<gt><lt>u5bf9<gt><lt>u7167<gt><lt>u540d<gt>00<lt>comma<gt>char<lt>comma<gt>33<lt>comma<gt><lt>comma<gt><lt>comma<gt>5<lt>comma<gt><lt>u6570<gt><lt>u636e<gt><lt>u7c7b<gt><lt>u578b<gt><lt>comma<gt>char<lt>comma<gt>6<lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt><lt>u670d<gt><lt>u52a1<gt><lt>u7c7b<gt><lt>u578b<gt><lt>comma<gt>char<lt>comma<gt>6<lt>comma<gt><lt>comma<gt><lt>comma<gt>7<lt>comma<gt><lt>u670d<gt><lt>u52a1<gt><lt>u5730<gt><lt>u5740<gt><lt>comma<gt>char<lt>comma<gt>76<lt>comma<gt><lt>comma<gt><lt>comma<gt>8<lt>comma<gt><lt>u56fe<gt><lt>u5c42<gt><lt>u5e8f<gt><lt>u5217<gt><lt>comma<gt>number<lt>comma<gt>2<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>9<lt>comma<gt><lt>u6570<gt><lt>u636e<gt><lt>u5e93<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>10<lt>comma<gt><lt>u6570<gt><lt>u636e<gt><lt>u8868<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>20<lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt>E:<lt>lt<gt>backslash<lt>gt<gt>YC<lt>lt<gt>backslash<lt>gt<gt><lt>lt<gt>u6bcf<lt>gt<gt><lt>lt<gt>u65e5<lt>gt<gt><lt>lt<gt>u4efb<lt>gt<gt><lt>lt<gt>u52a1<lt>gt<gt><lt>lt<gt>backslash<lt>gt<gt>2025<lt>lt<gt>backslash<lt>gt<gt>0108<lt>lt<gt>backslash<lt>gt<gt><lt>lt<gt>u6279<lt>gt<gt><lt>lt<gt>u91cf<lt>gt<gt><lt>lt<gt>u5bfc<lt>gt<gt><lt>lt<gt>u51fa<lt>gt<gt><lt>lt<gt>u56fe<lt>gt<gt><lt>lt<gt>u5c42<lt>gt<gt><lt>lt<gt>u540d<lt>gt<gt><lt>lt<gt>u3001<lt>gt<gt><lt>lt<gt>u5b57<lt>gt<gt><lt>lt<gt>u6bb5<lt>gt<gt><lt>lt<gt>u540d<lt>gt<gt><lt>lt<gt>backslash<lt>gt<gt><lt>lt<gt>u56fe<lt>gt<gt><lt>lt<gt>u5143<lt>gt<gt><lt>lt<gt>u57fa<lt>gt<gt><lt>lt<gt>u7840<lt>gt<gt><lt>lt<gt>u4fe1<lt>gt<gt><lt>lt<gt>u606f<lt>gt<gt><lt>lt<gt>u5b57<lt>gt<gt><lt>lt<gt>u5178<lt>gt<gt><lt>lt<gt>backslash<lt>gt<gt><lt>lt<gt>u65b0<lt>gt<gt><lt>lt<gt>u5434<lt>gt<gt><lt>lt<gt>u5b9e<lt>gt<gt><lt>lt<gt>u666f<lt>gt<gt><lt>lt<gt>u4e09<lt>gt<gt><lt>lt<gt>u7ef4<lt>gt<gt><lt>lt<gt>u2014<lt>gt<gt><lt>lt<gt>u2014<lt>gt<gt><lt>lt<gt>u65b0<lt>gt<gt><lt>lt<gt>u89c4<lt>gt<gt><lt>lt<gt>u8303<lt>gt<gt><lt>lt<gt>u4fe1<lt>gt<gt><lt>lt<gt>u606f<lt>gt<gt><lt>lt<gt>u8868<lt>gt<gt>.xlsx<lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>10<lt>comma<gt>165<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma><lt>u5b9e<gt><lt>u4f53<gt><lt>u5206<gt><lt>u7c7b<gt><lt>u4ee3<gt><lt>u7801<gt><lt>u7ed3<gt><lt>u6784<gt><comma>0<lt>comma<gt><lt>u5206<gt><lt>u7c7b<gt><lt>u4ee3<gt><lt>u7801<gt><lt>comma<gt>number<lt>comma<gt>6<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u5206<gt><lt>u7c7b<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>48<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u7236<gt><lt>u7ea7<gt><lt>u4ee3<gt><lt>u7801<gt><lt>comma<gt>number<lt>comma<gt>6<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>3<lt>comma<gt><lt>u7236<gt><lt>u7ea7<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt><lt>u4e00<gt><lt>u7ea7<gt><lt>u7c7b<gt><lt>u4ee3<gt><lt>u7801<gt><lt>comma<gt>number<lt>comma<gt>6<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>5<lt>comma<gt><lt>u4e00<gt><lt>u7ea7<gt><lt>u7c7b<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>27<lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt><lt>u5927<gt><lt>u7c7b<gt><lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>7<lt>comma<gt><lt>u7ea7<gt><lt>u522b<gt><lt>comma<gt>char<lt>comma<gt>9<lt>comma<gt><lt>comma<gt><lt>comma<gt>8<lt>comma<gt>tree<lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>9<lt>comma<gt><lt>u7f13<gt><lt>u51b2<gt><lt>u8303<gt><lt>u56f4<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt><lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>9<lt>comma<gt>584<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma><lt>u56fe<gt><lt>u5143<gt><lt>u57fa<gt><lt>u7840<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u5b57<gt><lt>u5178<gt><comma>0<lt>comma<gt>A<lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u6ce8<gt><lt>u610f<gt><lt>u6240<gt><lt>u6709<gt><lt>u5b9e<gt><lt>u4f53<gt><lt>u57fa<gt><lt>u7840<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u8868<gt><lt>u90fd<gt><lt>u9700<gt><lt>u8981<gt><lt>u6709<gt><lt>u5bf9<gt><lt>u5e94<gt><lt>u5b57<gt><lt>u6bb5<gt><lt>uff0c<gt><lt>u6ca1<gt><lt>u6709<gt><lt>u4e13<gt><lt>u6709<gt><lt>u5c5e<gt><lt>u6027<gt><lt>comma<gt>char<lt>comma<gt>61<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u6ce8<gt><lt>u610f<gt><lt>u6240<gt><lt>u6709<gt><lt>u5b9e<gt><lt>u4f53<gt><lt>u57fa<gt><lt>u7840<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u8868<gt><lt>u90fd<gt><lt>u9700<gt><lt>u8981<gt><lt>u6709<gt><lt>u5bf9<gt><lt>u5e94<gt><lt>u5b57<gt><lt>u6bb5<gt><lt>uff0c<gt><lt>u6ca1<gt><lt>u6709<gt><lt>u4e13<gt><lt>u6709<gt><lt>u5c5e<gt><lt>u6027<gt>00<lt>comma<gt>char<lt>comma<gt>63<lt>comma<gt><lt>comma<gt><lt>comma<gt>3<lt>comma<gt><lt>u6ce8<gt><lt>u610f<gt><lt>u6240<gt><lt>u6709<gt><lt>u5b9e<gt><lt>u4f53<gt><lt>u57fa<gt><lt>u7840<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u8868<gt><lt>u90fd<gt><lt>u9700<gt><lt>u8981<gt><lt>u6709<gt><lt>u5bf9<gt><lt>u5e94<gt><lt>u5b57<gt><lt>u6bb5<gt><lt>uff0c<gt><lt>u6ca1<gt><lt>u6709<gt><lt>u4e13<gt><lt>u6709<gt><lt>u5c5e<gt><lt>u6027<gt>01<lt>comma<gt>char<lt>comma<gt>33<lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt><lt>u6ce8<gt><lt>u610f<gt><lt>u6240<gt><lt>u6709<gt><lt>u5b9e<gt><lt>u4f53<gt><lt>u57fa<gt><lt>u7840<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u8868<gt><lt>u90fd<gt><lt>u9700<gt><lt>u8981<gt><lt>u6709<gt><lt>u5bf9<gt><lt>u5e94<gt><lt>u5b57<gt><lt>u6bb5<gt><lt>uff0c<gt><lt>u6ca1<gt><lt>u6709<gt><lt>u4e13<gt><lt>u6709<gt><lt>u5c5e<gt><lt>u6027<gt>02<lt>comma<gt>char<lt>comma<gt>21<lt>comma<gt><lt>comma<gt><lt>comma<gt>5<lt>comma<gt><lt>u6ce8<gt><lt>u610f<gt><lt>u6240<gt><lt>u6709<gt><lt>u5b9e<gt><lt>u4f53<gt><lt>u57fa<gt><lt>u7840<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u8868<gt><lt>u90fd<gt><lt>u9700<gt><lt>u8981<gt><lt>u6709<gt><lt>u5bf9<gt><lt>u5e94<gt><lt>u5b57<gt><lt>u6bb5<gt><lt>uff0c<gt><lt>u6ca1<gt><lt>u6709<gt><lt>u4e13<gt><lt>u6709<gt><lt>u5c5e<gt><lt>u6027<gt>03<lt>comma<gt>char<lt>comma<gt>6<lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt>G<lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt><lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>6<lt>comma<gt>1099<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma><lt>u5b9e<gt><lt>u4f53<gt><lt>u57fa<gt><lt>u7840<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u5b57<gt><lt>u5178<gt><comma>0<lt>comma<gt><lt>u5b9e<gt><lt>u4f53<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u8868<gt><lt>u82f1<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>13<lt>comma<gt><lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u5b9e<gt><lt>u4f53<gt><lt>u4fe1<gt><lt>u606f<gt><lt>u8868<gt><lt>u4e2d<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>43<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u5b57<gt><lt>u6bb5<gt><lt>u82f1<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>6<lt>comma<gt><lt>comma<gt><lt>comma<gt>3<lt>comma<gt><lt>u5b57<gt><lt>u6bb5<gt><lt>u4e2d<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt><lt>u5bf9<gt><lt>u5e94<gt><lt>u6570<gt><lt>u636e<gt><lt>u5e93<gt><lt>u8868<gt><lt>u540d<gt><lt>comma<gt>char<lt>comma<gt>6<lt>comma<gt><lt>comma<gt><lt>comma<gt>5<lt>comma<gt><lt>u5907<gt><lt>u6ce8<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt>G<lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt><lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>6<lt>comma<gt>730<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma><lt>u5173<gt><lt>u8054<gt><lt>u5173<gt><lt>u7cfb<gt><lt>u8868<gt><lt>u7ed3<gt><lt>u6784<gt><lt>uff08<gt><lt>u901a<gt><lt>u7528<gt><lt>uff09<gt><comma>1<lt>comma<gt><lt>u5173<gt><lt>u8054<gt><lt>u8868<gt><lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u5173<gt><lt>u8054<gt><lt>u8868<gt>00<lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>3<lt>comma<gt><lt>u5173<gt><lt>u8054<gt><lt>u8868<gt>01<lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt>E<lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>5<lt>comma<gt><lt>u5173<gt><lt>u7cfb<gt><lt>u8868<gt><lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt><lt>u5173<gt><lt>u7cfb<gt><lt>u8868<gt>00<lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>7<lt>comma<gt><lt>u5173<gt><lt>u7cfb<gt><lt>u8868<gt>01<lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>2<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>2<lt>comma<gt><lt>comma<gt><lt>quote<gt>1<lt>comma<gt>1<lt>comma<gt>7<lt>comma<gt>8<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma>Sheet1<comma>0<lt>comma<gt>TYID<lt>comma<gt>char<lt>comma<gt>4<lt>comma<gt><lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u56fe<gt><lt>u5143<gt><lt>u6807<gt><lt>u8bc6<gt><lt>u7801<gt><lt>comma<gt>char<lt>comma<gt>15<lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt><lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>1<lt>comma<gt>7<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma>Sheet3<comma>0<lt>comma<gt><lt>u82f1<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>9<lt>comma<gt><lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u82f1<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt>00<lt>comma<gt>char<lt>comma<gt>7<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u6570<gt><lt>u636e<gt><lt>u5e93<gt><lt>comma<gt>char<lt>comma<gt>6<lt>comma<gt><lt>comma<gt><lt>comma<gt>3<lt>comma<gt><lt>u4e2d<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>30<lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt><lt>u7b2c<gt><lt>u4e00<gt><lt>u6392<gt><lt>u5e8f<gt><lt>comma<gt>number<lt>comma<gt>3<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>5<lt>comma<gt>F<lt>comma<gt>char<lt>comma<gt>5<lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt>G<lt>comma<gt>char<lt>comma<gt>15<lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt><lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>6<lt>comma<gt>1097<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO<comma>Sheet2<comma><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt><lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>18446744073709551615<lt>comma<gt>18446744073709551615<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO,SCHEMA_HANDLING_REVISION,2,SKIP_EMPTY_ROWS,YES,STRIP_SHEETNAME_SPACES,YES,TABLELIST,<u6570><u636e><u8d44><u6e90><u6e05><u5355><space><u5b9e><u4f53><u5206><u7c7b><u4ee3><u7801><u7ed3><u6784><space><u56fe><u5143><u57fa><u7840><u4fe1><u606f><u5b57><u5178><space><u5b9e><u4f53><u57fa><u7840><u4fe1><u606f><u5b57><u5178><space><u5173><u8054><u5173><u7cfb><u8868><u7ed3><u6784><uff08><u901a><u7528><uff09><space>Sheet1<space>Sheet3<space>Sheet2,TRIM_ATTR_NAME_CHARACTERS,,TRIM_ATTR_NAME_WHITESPACE,Yes,USE_CUSTOM_SCHEMA,NO,XLSXR_EXPOSE_FORMAT_ATTRS,
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_4_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_4_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_4_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_4 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_4)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_4 }    INPUT  FEATURE_TYPE Tester_2_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_4_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { XLSXR  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_4)" }    QUERYFCT_IDS        { "<u56fe><u5143><u57fa><u7840><u4fe1><u606f><u5b57><u5178>" }    READER_DIRECTIVES   { METAFILE,XLSXR }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_4_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_4_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT <u56fe><u5143><u57fa><u7840><u4fe1><u606f><u5b57><u5178> FEATURE_TYPE FeatureReader_4_<u56fe><u5143><u57fa><u7840><u4fe1><u606f><u5b57><u5178>
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE FeatureReader_4_<u56fe><u5143><u57fa><u7840><u4fe1><u606f><u5b57><u5178>    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5143><u82f1><u6587><u540d><u79f0>" "SET_TO" "<at>Value<openparen>A<closeparen>" "varchar<openparen>18<closeparen>" }      ATTR_ACTION { "" "<u56fe><u5143><u4e2d><u6587><u540d><u79f0>" "SET_TO" "<at>Value<openparen><u6ce8><u610f><u6240><u6709><u5b9e><u4f53><u57fa><u7840><u4fe1><u606f><u8868><u90fd><u9700><u8981><u6709><u5bf9><u5e94><u5b57><u6bb5><uff0c><u6ca1><u6709><u4e13><u6709><u5c5e><u6027><closeparen>" "varchar<openparen>61<closeparen>" }      ATTR_ACTION { "" "<u5b57><u6bb5><u82f1><u6587><u540d><u79f0>" "SET_TO" "<at>Value<openparen><u6ce8><u610f><u6240><u6709><u5b9e><u4f53><u57fa><u7840><u4fe1><u606f><u8868><u90fd><u9700><u8981><u6709><u5bf9><u5e94><u5b57><u6bb5><uff0c><u6ca1><u6709><u4e13><u6709><u5c5e><u6027>00<closeparen>" "varchar<openparen>63<closeparen>" }      ATTR_ACTION { "" "<u5b57><u6bb5><u4e2d><u6587><u540d><u79f0>" "SET_TO" "<at>Value<openparen><u6ce8><u610f><u6240><u6709><u5b9e><u4f53><u57fa><u7840><u4fe1><u606f><u8868><u90fd><u9700><u8981><u6709><u5bf9><u5e94><u5b57><u6bb5><uff0c><u6ca1><u6709><u4e13><u6709><u5c5e><u6027>01<closeparen>" "varchar<openparen>33<closeparen>" }      ATTR_ACTION { "" "<u5bf9><u5e94><u6570><u636e><u5e93><u8868><u540d>" "SET_TO" "<at>Value<openparen><u6ce8><u610f><u6240><u6709><u5b9e><u4f53><u57fa><u7840><u4fe1><u606f><u8868><u90fd><u9700><u8981><u6709><u5bf9><u5e94><u5b57><u6bb5><uff0c><u6ca1><u6709><u4e13><u6709><u5c5e><u6027>02<closeparen>" "varchar<openparen>21<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    KEEP_ATTRS { <u5bf9><u5e94><u6570><u636e><u5e93><u8868><u540d>,<u56fe><u5143><u82f1><u6587><u540d><u79f0>,<u56fe><u5143><u4e2d><u6587><u540d><u79f0>,<u5b57><u6bb5><u82f1><u6587><u540d><u79f0>,<u5b57><u6bb5><u4e2d><u6587><u540d><u79f0> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_3 }    INPUT  FEATURE_TYPE AttributeKeeper_OUTPUT    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u5bf9><u5e94><u6570><u636e><u5e93><u8868><u540d><closeparen>,Tester_3)" = <u5bf9><u5e94><u6570><u636e><u5e93><u8868><u540d> ENCODED } TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u56fe><u5143><u82f1><u6587><u540d><u79f0><closeparen>,Tester_3)" = "" ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_3_FAILED         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Tester_3 FAILED Splitter"   INPUT FEATURE_TYPE Tester_3_FAILED   OUTPUT FEATURE_TYPE Tester_3_FAILED_0_0RJaczaGQVk=   OUTPUT FEATURE_TYPE Tester_3_FAILED_1_LdKgKi9SYDo=
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE Tester_3_FAILED_0_0RJaczaGQVk=    KEY_ATTRIBUTES { <u56fe><u5143><u82f1><u6587><u540d><u79f0> <u56fe><u5143><u4e2d><u6587><u540d><u79f0> }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_3 }    INPUT  FEATURE_TYPE DuplicateFilter_UNIQUE    KEEP_ATTRS { <u56fe><u5143><u82f1><u6587><u540d><u79f0>,<u56fe><u5143><u4e2d><u6587><u540d><u79f0> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE Tester_3_FAILED_1_LdKgKi9SYDo=    KEY_ATTRIBUTES { <u5b57><u6bb5><u82f1><u6587><u540d><u79f0> <u5b57><u6bb5><u4e2d><u6587><u540d><u79f0> }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_2_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_4 }    INPUT  FEATURE_TYPE DuplicateFilter_2_UNIQUE    KEEP_ATTRS { <u5b57><u6bb5><u82f1><u6587><u540d><u79f0>,<u5b57><u6bb5><u4e2d><u6587><u540d><u79f0> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_4_OUTPUT        }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = gdb ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_2_DIRECTIVES ALIAS_MODE,NONE,BEGIN_SQL{0},,CACHE_MULTIPATCH_TEXTURES,yes,CHECK_SIMPLE_GEOM,no,CREATE_FEATURE_TABLES_FROM_DATA,Yes,DISABLE_FEATURE_DATASET_ATTRIBUTE,Yes,END_SQL{0},,EXPOSE_ATTRS_GROUP,,FEATURE_READ_MODE,Metadata,GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS,,GEODB_SHARED_RDR_ADV_PARM_GROUP,,GEOMETRY,,IGNORE_NETWORK_INFO,yes,IGNORE_RELATIONSHIP_INFO,yes,MERGE_FEAT_LINKED_ANNOS,no,NETWORK_AUTHENTICATION,,QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,Yes,READ_THREE_POINT_ARCS,no,REMOVE_FEATURE_DATASET,NO,RESOLVE_DOMAINS,no,RESOLVE_SUBTYPE_NAMES,yes,SIMPLE_DONUT_GEOMETRY,no,SPLIT_COMPLEX_ANNOS,no,SPLIT_COMPLEX_EDGES,no,SPLIT_MULTI_PART_ANNOS,no,STRIP_GUID_GLOBALID_BRACES,no,TABLELIST,,TRANSLATE_SPATIAL_DATA_ONLY,no,USE_SEARCH_ENVELOPE,NO,WHERE_WWJD,
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Tester_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { GEODATABASE_FILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,GEODATABASE_FILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { SCHEMA FEATURE_TYPE FeatureReader_2_<SCHEMA>           }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_fde96bdb_b415_46f4_a92e_0b4b1b6c64d529} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {<solidus>}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {fme_feature_type_name}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {fme_feature_type_name}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter }    INPUT  FEATURE_TYPE FeatureReader_2_<SCHEMA>    OUTPUT { FEATURE_TYPE AttributeSplitter_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeSplitter_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u6240><u5c5e><u6570><u636e><u96c6>" "SET_TO" "<at>Value<openparen>_list<opencurly>0<closecurly><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u56fe><u5c42><u540d>" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'<at>Value<openparen>_list<opencurly>0<closecurly><closeparen>'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Value<openparen>_list<opencurly>1<closecurly><closeparen> != _FME_BLANK_STRING_'<at>Value<openparen>_list<opencurly>1<closecurly><closeparen>'FME_NUM_CONDITIONS2___" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder }    INPUT  FEATURE_TYPE AttributeCreator_2_OUTPUT    LIST_NAME { "attribute{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_4 }    INPUT  FEATURE_TYPE ListExploder_ELEMENTS    CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>name<closeparen>,Tester_4)" = OBJECTID ENCODED } CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>name<closeparen>,Tester_4)" CONTAINS SHAPE_Area ENCODED } CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>name<closeparen>,Tester_4)" CONTAINS SHAPE_Length ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_4_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_2 }    INPUT  FEATURE_TYPE Tester_4_FAILED    KEEP_ATTRS { <u6240><u5c5e><u6570><u636e><u96c6>,<u56fe><u5c42><u540d>,name }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_2_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [if { {ATTRIBUTES} == {ATTRIBUTES} } {                puts "MACRO FeatureMerger_REFERENCE_INFO ATTRIBUTES";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {POLYGONS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_POLYS";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {AGGREGATES}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {LINESFROMPOINTS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_LINES_FROM_POINTS";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {POLYGONS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_POLYS";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {AGGREGATES}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {LINESFROMPOINTS}} {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_LINES_FROM_POINTS";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} } {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} } {                puts "MACRO FeatureMerger_REFERENCE_INFO GEOM_AND_ATTR_BUILD_AGGREGATES";             }          else {}; ]
FACTORY_DEF {*} ReferenceFactory    FACTORY_NAME { FeatureMerger }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    INPUT REFERENCER FEATURE_TYPE AttributeKeeper_2_OUTPUT    INPUT REFERENCEE FEATURE_TYPE AttributeKeeper_3_OUTPUT    REFERENCE_INFO { $(FeatureMerger_REFERENCE_INFO) }    REFERENCE_TABLE { @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u56fe><u5c42><u540d><closeparen>,FeatureMerger) @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u56fe><u5143><u82f1><u6587><u540d><u79f0><closeparen>,FeatureMerger) AUTO }    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "REQUESTOR_IF_CONFLICT" }    IGNORE_NULLS { "No" }    HANDLE_NULL_MISSING_KEYS_LIKE_FME2013 { No }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    MERGE_ATTRIBUTES Yes    MANAGE_FME_TYPE Yes    MODE COMPLETE    PROCESS_DUPLICATE_REFERENCEES { NO }    REFERENCEES_FIRST { No }    REJECT_INVALID_GEOM YES    CLEANING_TOLERANCE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    COMPARE_WHITESPACE Yes    OUTPUT { COMPLETE FEATURE_TYPE FeatureMerger_MERGED         }    OUTPUT { INCOMPLETE FEATURE_TYPE FeatureMerger_UNMERGED_REQUESTOR         }
# -------------------------------------------------------------------------
INCLUDE [if { {ATTRIBUTES} == {ATTRIBUTES} } {                puts "MACRO FeatureMerger_2_REFERENCE_INFO ATTRIBUTES";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {POLYGONS}} {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_BUILD_POLYS";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {AGGREGATES}} {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} && {<Unused>} == {LINESFROMPOINTS}} {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_BUILD_LINES_FROM_POINTS";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {POLYGONS}} {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_AND_ATTR_BUILD_POLYS";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {AGGREGATES}} {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_AND_ATTR_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} && {<Unused>} == {LINESFROMPOINTS}} {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_AND_ATTR_BUILD_LINES_FROM_POINTS";             }          elseif { {ATTRIBUTES} == {GEOM_BUILD} } {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_BUILD_AGGREGATES";             }          elseif { {ATTRIBUTES} == {GEOM_AND_ATTRS} } {                puts "MACRO FeatureMerger_2_REFERENCE_INFO GEOM_AND_ATTR_BUILD_AGGREGATES";             }          else {}; ]
FACTORY_DEF {*} ReferenceFactory    FACTORY_NAME { FeatureMerger_2 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    INPUT REFERENCER FEATURE_TYPE FeatureMerger_MERGED    INPUT REFERENCER FEATURE_TYPE FeatureMerger_UNMERGED_REQUESTOR    INPUT REFERENCEE FEATURE_TYPE AttributeKeeper_4_OUTPUT    REFERENCE_INFO { $(FeatureMerger_2_REFERENCE_INFO) }    REFERENCE_TABLE { @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>name<closeparen>,FeatureMerger_2) @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u5b57><u6bb5><u82f1><u6587><u540d><u79f0><closeparen>,FeatureMerger_2) AUTO }    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "REQUESTOR_IF_CONFLICT" }    IGNORE_NULLS { "No" }    HANDLE_NULL_MISSING_KEYS_LIKE_FME2013 { No }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    MERGE_ATTRIBUTES Yes    MANAGE_FME_TYPE Yes    MODE COMPLETE    PROCESS_DUPLICATE_REFERENCEES { NO }    REFERENCEES_FIRST { No }    REJECT_INVALID_GEOM YES    CLEANING_TOLERANCE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    COMPARE_WHITESPACE Yes    OUTPUT { COMPLETE FEATURE_TYPE FeatureMerger_2_MERGED         }    OUTPUT { INCOMPLETE FEATURE_TYPE FeatureMerger_2_UNMERGED_REQUESTOR         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE FeatureMerger_2_MERGED    INPUT  FEATURE_TYPE FeatureMerger_2_UNMERGED_REQUESTOR    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u56fe><u5143><u82f1><u6587><u540d><u79f0>" "SET_TO" "<at>Value<openparen><u56fe><u5c42><u540d><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u5b57><u6bb5><u82f1><u6587><u540d><u79f0>" "SET_TO" "<at>Value<openparen>name<closeparen>" "buffer" }      ATTR_ACTION { "" "<u5bf9><u5e94><u6570><u636e><u5e93><u8868><u540d>" "SET_TO" "<at>Value<openparen><u6240><u5c5e><u6570><u636e><u96c6><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_5 }    INPUT  FEATURE_TYPE AttributeCreator_3_OUTPUT    KEEP_ATTRS { <u5bf9><u5e94><u6570><u636e><u5e93><u8868><u540d>,<u56fe><u5143><u82f1><u6587><u540d><u79f0>,<u56fe><u5143><u4e2d><u6587><u540d><u79f0>,<u5b57><u6bb5><u82f1><u6587><u540d><u79f0>,<u5b57><u6bb5><u4e2d><u6587><u540d><u79f0> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_5_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ExcelStyleFactory    FACTORY_NAME { ExcelStyler_Styler }    INPUT  FEATURE_TYPE AttributeKeeper_5_OUTPUT    ROW_OR_CELL { "row" }    NUMBER_FORMAT_STRING { "" }    FONT_NAME { "<u5b8b><u4f53><comma>11" }    FONT_COLOR { "" }    PATTERN_COLOR { "" }    BACKGROUND_COLOR { "" }    PATTERN_STYLE { "" }    HORIZONTAL_ALIGNMENT { "center" }    VERTICAL_ALIGNMENT { "center" }    INDENT { "" }    TEXT_ORIENTATION { "" }    TEXT_CONTROL { "" }    CELL_BORDER_COLOR { "0,0,0" }    CELL_BORDER_STYLE { "BORDERSTYLE_THIN" }    ROW_HEIGHT { "" }    ATTRS_TO_STYLE { <Unused> }    HIDDEN { "" }    LOCKED { "" }    OUTPUT { ExcelStyled FEATURE_TYPE ExcelStyler_ExcelStyled        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode)<backslash><u6570><u636e><u5e93><u5bfc><u51fa>.xlsx,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { XLSXW }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_FILE<comma>No<comma>TEMPLATEFILE<comma><comma>TEMPLATE_SHEET<comma><comma>REMOVE_UNCHANGED_TEMPLATE_SHEET<comma>No<comma>MULTIPLE_TEMPLATE_SHEETS<comma>Yes<comma>INSERT_IGNORE_DB_OP<comma>Yes<comma>DROP_TABLE<comma>No<comma>TRUNCATE_TABLE<comma>No<comma>FIELD_NAMES_OUT<comma>Yes<comma>FIELD_NAMES_FORMATTING<comma>Yes<comma>WRITER_MODE<comma>Insert<comma>RASTER_FORMAT<comma>PNG<comma>PROTECT_SHEET<comma>NO<comma>PROTECT_SHEET_PASSWORD<comma><lt>Unused<gt><comma>PROTECT_SHEET_LEVEL<comma><lt>Unused<gt><comma>PROTECT_SHEET_PERMISSIONS<comma><lt>Unused<gt><comma>STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING<comma>yes<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE<comma>CUSTOM_NUMBER_FORMATTING<comma>ENABLE_NATIVE<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,XLSXW" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,<quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varchar<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varbinary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_char<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binarybuffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_xml<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_json<comma><quote>auto<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>datetime<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_datetime<comma><quote>time<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_time<comma><quote>date<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_date<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int8<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint8<comma><quote>boolean<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<openbracket><closebracket>*<backslash><backslash>?:<apos>,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none<comma>fme_no_geom<comma>xlsx_none<comma>fme_point<comma>xlsx_point<comma>fme_point<comma>xlsx_none<comma>fme_line<comma>xlsx_none<comma>fme_polygon<comma>xlsx_none<comma>fme_text<comma>xlsx_none<comma>fme_ellipse<comma>xlsx_none<comma>fme_arc<comma>xlsx_none<comma>fme_rectangle<comma>xlsx_none<comma>fme_rounded_rectangle<comma>xlsx_none<comma>fme_collection<comma>xlsx_none<comma>fme_surface<comma>xlsx_none<comma>fme_solid<comma>xlsx_none<comma>fme_raster<comma>xlsx_none<comma>fme_point_cloud<comma>xlsx_none<comma>fme_voxel_grid<comma>xlsx_none<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>xlsx_layer_group<space>xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group<space>Sheet<space>Settings<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_truncate_group<space>xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet<space>Drop<solidus>Truncate<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_rowcolumn_group<space>xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row<space>Start<space>Position<quote><comma><comma><quote>GUI<space>ACTIVEDISCLOSUREGROUP<space>xlsx_protect_sheet<space>xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions<space>Protect<space>Sheet<quote><comma>NO<comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_template_group<space>xlsx_template_sheet%xlsx_remove_unchanged_template_sheet<space>Template<space>Options<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_advanced_group<space>xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type<space>Advanced<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_drop_sheet<space>Yes%No<space>Drop<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>CHOICE<space>xlsx_trunc_sheet<space>Yes%No<space>Truncate<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_sheet_order<space>1%MAX<space>Sheet<space>Order<space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_freeze_end_row<space>1%MAX<space>Freeze<space>First<space>Row<openparen>s<closeparen><space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_field_names_out<space>Yes%No<comma>xlsx_field_names_formatting<comma>++xlsx_field_names_formatting+No<space>Output<space>Field<space>Names:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_field_names_formatting<space>Yes%No<space>Format<space>Field<space>Names<space>Row:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_names_are_positions<space>Yes%No<space>Use<space>Attribute<space>Names<space>As<space>Column<space>Positions:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_start_col<space>Named<space>Range<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_start_row<space>Named<space>Range<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_offset_col<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_offset_row<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_raster_type<space>BMP%JPEG%PNG<space>Raster<space>Format:<quote><comma>PNG<comma><quote>GUI<space>OPTIONAL<space>PASSWORD_ENCODED<space>xlsx_protect_sheet_password<space>Password:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>xlsx_protect_sheet_level<space>Select<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_DEFAULT<comma>xlsx_protect_sheet_permissions%View<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_ALL<comma>xlsx_protect_sheet_permissions%Specific<lt>space<gt>Permissions<space>Protection<space>Level:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>OPTIONAL<space>LOOKUP_LISTBOX<space>xlsx_protect_sheet_permissions<space>Select<lt>space<gt>locked<lt>space<gt>cells<comma>PROT_SEL_LOCKED_CELLS%Select<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_SEL_UNLOCKED_CELLS%Format<lt>space<gt>cells<comma>PROT_FORMAT_CELLS%Format<lt>space<gt>columns<comma>PROT_FORMAT_COLUMNS%Format<lt>space<gt>rows<comma>PROT_FORMAT_ROWS%Insert<lt>space<gt>columns<comma>PROT_INSERT_COLUMNS%Insert<lt>space<gt>rows<comma>PROT_INSERT_ROWS%Add<lt>space<gt>hyperlinks<lt>space<gt>to<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_INSERT_HYPERLINKS%Delete<lt>space<gt>unlocked<lt>space<gt>columns<comma>PROT_DELETE_COLUMNS%Delete<lt>space<gt>unlocked<lt>space<gt>rows<comma>PROT_DELETE_ROWS%Sort<lt>space<gt>unlocked<lt>space<gt>cells<solidus>rows<solidus>columns<comma>PROT_SORT%Use<lt>space<gt>Autofilter<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_AUTOFILTER%Use<lt>space<gt>PivotTable<lt>space<gt><amp><lt>space<gt>PivotChart<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_PIVOTTABLES%Edit<lt>space<gt>unlocked<lt>space<gt>objects<comma>PROT_OBJECTS%Edit<lt>space<gt>unprotected<lt>space<gt>scenarios<comma>PROT_SCENARIOS<space>Specific<space>Permissions:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_table_writer_mode<space>Insert<comma>+xlsx_row_id_column+<quote><quote><quote><quote>%Update<comma>+xlsx_row_id_column+xlsx_row_id%Delete<comma>+xlsx_row_id_column+xlsx_row_id<space>Writer<space>Mode:<quote><comma>Insert<comma><quote>GUI<space>OPTIONAL<space>ATTR<space>xlsx_row_id_column<space>ALLOW_NEW<space>Row<space>Number<space>Attribute:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT_EDIT<space>xlsx_template_sheet<space>Template<space>Sheet:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_remove_unchanged_template_sheet<space>Yes%No<space>Remove<space>Template<space>Sheet<space>if<space>Unchanged:<quote><comma>No,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_freeze_end_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_names_are_positions<comma>No<comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_start_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_start_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_raster_type<comma>PNG<comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_protect_sheet<comma><quote><quote><quote>NO<quote><quote><quote><comma>xlsx_protect_sheet_level<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_password<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_permissions<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_template_sheet<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_remove_unchanged_template_sheet<comma><quote><quote><quote>No<quote><quote><quote>,WRITER_FORMAT_PARAMETER,DEFAULT_READER<comma>XLSXR<comma>ALLOW_DATASET_CONFLICT<comma>YES<comma>MIME_TYPE<comma><quote>application<solidus>vnd.openxmlformats-officedocument.spreadsheetml.sheet<space>ADD_DISPOSITION<quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>DEFAULT_ATTR_TYPE<comma>auto<comma>USER_ATTRIBUTES_COLUMNS<comma><quote>Width<comma>Cell<space>Width%Precision<comma>Formatting<quote><comma>FEATURE_TYPE_NAME<comma>Sheet<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Sheet1<comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>name<space>for<space>the<space>Microsoft<space>Excel<space>file<quote>,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "sheet1:Output,ftp_feature_type_name,sheet1,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u56fe<gt><lt>u5143<gt><lt>u82f1<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>11<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u56fe<gt><lt>u5143<gt><lt>u4e2d<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>25<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>11<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u5b57<gt><lt>u6bb5<gt><lt>u82f1<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>11<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u5b57<gt><lt>u6bb5<gt><lt>u4e2d<gt><lt>u6587<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>11<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u5bf9<gt><lt>u5e94<gt><lt>u6570<gt><lt>u636e<gt><lt>u5e93<gt><lt>u8868<gt><lt>u540d<gt><comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>11<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma><comma>,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No" }    WRITER_PARAMS { "COORDINATE_SYSTEM_GRANULARITY,FEATURE,CUSTOM_NUMBER_FORMATTING,ENABLE_NATIVE,DESTINATION_DATASETTYPE_VALIDATION,Yes,DROP_TABLE,No,FIELD_NAMES_FORMATTING,Yes,FIELD_NAMES_OUT,Yes,INSERT_IGNORE_DB_OP,Yes,MULTIPLE_TEMPLATE_SHEETS,Yes,NETWORK_AUTHENTICATION,,OVERWRITE_FILE,No,PROTECT_SHEET,NO,RASTER_FORMAT,PNG,STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,yes,TRUNCATE_TABLE,No,WRITER_MODE,Insert" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE ExcelStyler_ExcelStyled  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,sheet1,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

