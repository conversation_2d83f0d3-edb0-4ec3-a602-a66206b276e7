{"version": 3, "file": "use-checkbox-status.mjs", "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-status.ts"], "sourcesContent": ["import { computed, inject, ref, toRaw } from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { useFormSize } from '@element-plus/components/form'\nimport { isArray, isBoolean, isObject, isPropAbsent } from '@element-plus/utils'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { ComponentInternalInstance } from 'vue'\nimport type { CheckboxProps } from '../checkbox'\nimport type { CheckboxModel } from '../composables'\n\nexport const useCheckboxStatus = (\n  props: CheckboxProps,\n  slots: ComponentInternalInstance['slots'],\n  { model }: Pick<CheckboxModel, 'model'>\n) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const isFocused = ref(false)\n  const actualValue = computed(() => {\n    // In version 2.x, if there's no props.value, props.label will act as props.value\n    // In version 3.x, remove this computed value, use props.value instead.\n    if (!isPropAbsent(props.value)) {\n      return props.value\n    }\n    return props.label\n  })\n  const isChecked = computed<boolean>(() => {\n    const value = model.value\n    if (isBoolean(value)) {\n      return value\n    } else if (isArray(value)) {\n      if (isObject(actualValue.value)) {\n        return value.map(toRaw).some((o) => isEqual(o, actualValue.value))\n      } else {\n        return value.map(toRaw).includes(actualValue.value)\n      }\n    } else if (value !== null && value !== undefined) {\n      return value === props.trueValue || value === props.trueLabel\n    } else {\n      return !!value\n    }\n  })\n\n  const checkboxButtonSize = useFormSize(\n    computed(() => checkboxGroup?.size?.value),\n    {\n      prop: true,\n    }\n  )\n  const checkboxSize = useFormSize(computed(() => checkboxGroup?.size?.value))\n\n  const hasOwnLabel = computed<boolean>(() => {\n    return !!slots.default || !isPropAbsent(actualValue.value)\n  })\n\n  return {\n    checkboxButtonSize,\n    isChecked,\n    isFocused,\n    checkboxSize,\n    hasOwnLabel,\n    actualValue,\n  }\n}\n\nexport type CheckboxStatus = ReturnType<typeof useCheckboxStatus>\n"], "names": [], "mappings": ";;;;;;;AAKY,MAAC,iBAAiB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK;AAC9D,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,CAAC;AAChE,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACpC,MAAM,OAAO,KAAK,CAAC,KAAK,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC9B,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;AAC1B,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AAC/B,MAAM,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AACvC,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3E,OAAO,MAAM;AACb,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5D,OAAO;AACP,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;AACnD,MAAM,OAAO,KAAK,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK,KAAK,KAAK,CAAC,SAAS,CAAC;AACpE,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,CAAC,KAAK,CAAC;AACrB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM;AACxD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AAClG,GAAG,CAAC,EAAE;AACN,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM;AAClD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AAClG,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/D,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,kBAAkB;AACtB,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}