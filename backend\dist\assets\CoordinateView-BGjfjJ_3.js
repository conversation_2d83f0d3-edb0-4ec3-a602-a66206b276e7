import{d as Oa,u as Ma,r as u,I as P,K as ne,o as Se,a as Ot,a6 as Ra,c as I,e as t,w as s,f as v,i as D,E as i,X as Ke,M,Y as B,b as n,L as Fa,y as d,l as h,z as et,s as C,a7 as za,x as $e,U as Pa,A as m,a8 as tt,a3 as ye,$ as Ea,v as re,a9 as ja,a0 as at,N as Ia,O as Mt,F as lt,G as st,Q as Ae,k as y,a5 as J,H as Ba,_ as Na}from"./index-Dn7OnccA.js";import{f as qa}from"./format-CBpsKyOP.js";const Ga={class:"tools-container"},Ha={key:"convert"},Wa={class:"steps-progress"},Xa={class:"start-container"},Ya={class:"start-content"},Ja={class:"step-content"},Za={style:{"margin-bottom":"20px","margin-top":"20px","padding-left":"50px","text-align":"left",display:"flex",gap:"12px","align-items":"center"}},Qa={style:{display:"none"}},Ka={class:"step-footer"},el={class:"step-content"},tl={class:"upload-container"},al={class:"upload-sections"},ll={class:"upload-section"},sl={class:"section-title"},ol={class:"el-upload-list__item custom-upload-item"},nl={class:"el-upload-list__item-name"},rl={class:"custom-status-label"},ul={class:"upload-section"},il={class:"section-title"},dl={class:"el-upload-list__item custom-upload-item"},cl={class:"el-upload-list__item-name",style:{"text-align":"left",display:"block"}},pl={class:"custom-status-label"},fl={class:"file-list-table"},ml={class:"table-header",style:{"justify-content":"flex-start","flex-direction":"row","align-items":"center"}},vl={class:"license-tags",style:{display:"flex","align-items":"center","margin-top":"0px"}},_l={class:"step-footer"},gl={class:"step-content"},yl={class:"output-settings-container"},hl={class:"info-confirmation"},bl={class:"section-card"},wl={class:"section-card"},Cl={class:"output-types"},xl={class:"section-card"},kl={class:"geometry-types"},Vl={class:"button-group"},Tl={class:"tip-text"},Dl={class:"step-footer"},Ll={key:"history"},Ul={class:"table-container"},Sl={style:{display:"flex","justify-content":"center"}},$l={key:0,class:"message-content"},Al={key:6,class:"color-picker-wrapper"},Ol={class:"color-value"},Ml={key:1,class:"no-params"},Rl={class:"dialog-footer"},Fl={class:"dialog-footer"},zl={class:"dialog-footer"},Pl={style:{display:"flex","justify-content":"center"}},El={class:"error-log-content"},jl={class:"dialog-footer"},Il={class:"error-message"},Bl={class:"error-message"},Nl={class:"error-message"},ql={class:"error-message"},Gl={class:"error-message"},Hl={class:"dialog-footer"},Wl=5e3,Xl=Oa({__name:"CoordinateView",setup(Yl){const b=Ma(),Rt=u(!1),Z=u([]),Q=u([]),ot=u(""),nt=u(""),K=u("convert"),Ft=u(1),zt=u(1),Pt=u(1),rt=u(10),ut=u(10);u(10);const Oe=u(0);u(0);const $=u(localStorage.getItem("token")),N={GET_TOOLS_LIST:"/api/Coordinate/tools/list",GET_TOOLS_COUNT:"/api/Coordinate/tools/count",UPLOAD_TOOL:"/api/Coordinate/tools/upload",DELETE_TOOL:"/api/Coordinate/tools/delete",APPLY_TOOL:"/api/Coordinate/tools/apply",APPROVE_TOOL:"/api/Coordinate/tools/approve",REJECT_TOOL:"/api/Coordinate/tools/reject",GET_MY_APPLICATIONS:"/api/tools/my-applications",GET_MY_APPROVALS:"/api/Coordinate/tools/my-approvals",RUN_TOOL:"/api/Coordinate/tools/run",GET_RUN_RECORDS:"/api/Coordinate/tools/run_records",UPLOAD_FILE:"/api/upload",DOWNLOAD_RESULT:"/api/tools/download_result"},Et=a=>{a.name==="history"&&me()},jt=P(()=>Z.value.filter(a=>a.fmw_name.toLowerCase().includes(ot.value.toLowerCase())||(a.user_project||"").toLowerCase().includes(ot.value.toLowerCase()))),It=P(()=>Q.value.filter(a=>a.fmw_name.toLowerCase().includes(nt.value.toLowerCase())||a.project.toLowerCase().includes(nt.value.toLowerCase())));P(()=>{const a=(Pt.value-1)*rt.value,e=a+rt.value;return jt.value.slice(a,e)}),P(()=>{const a=(zt.value-1)*ut.value,e=a+ut.value;return It.value.slice(a,e)});const he=async()=>{var a;try{if(!((a=b.user)!=null&&a.username)){Z.value=[];return}const e=await D.post(N.GET_TOOLS_LIST,{username:b.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});e.data.success?Z.value=e.data.data.map(o=>({...o,user_project:o.user_project||o.project||"未指定项目",created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(i.error(e.data.message||"获取工具列表失败"),Z.value=[])}catch(e){console.error("获取工具列表失败:",e),Z.value=[]}},Bt=async()=>{var a;try{if(!((a=b.user)!=null&&a.username)){Q.value=[];return}const e=await D.get("/api/tools/my-applications",{params:{source:"CoordinateView",fmw_id:"coordinatetransformation"},headers:{"X-Username":b.user.username}});e.data.success?Q.value=e.data.data.filter(o=>o.status==="已通过").map(o=>({...o,count:parseInt(o.count)||0,usage_count:parseInt(o.usage_count)||0,remaining_count:(parseInt(o.usage_count)||0)-(parseInt(o.count)||0),user_project:o.user_project||"未指定项目",end_date:o.end_date||null,created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(i.error(e.data.message||"获取申请列表失败"),Q.value=[])}catch{Q.value=[]}},be=(a,e="yyyy-MM-dd HH:mm")=>{if(!a)return"--";try{return qa(new Date(a),e)}catch{return"--"}};ne(()=>b.user,a=>{a!=null&&a.username?he():(Z.value=[],Q.value=[])},{immediate:!0});const ue=u(!1),R=u(null),ee=u([]),g=u({}),S=u(null),Me=u({}),Nt=u(!1);u(!1);const Re=u(!1),ie=u([]);ne(Re,a=>{a||(Ft.value=1)});const qt=a=>({running:"warning",success:"success",failed:"danger"})[a]||"info",Gt=a=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[a]||a,it=a=>{if(!a)return"-";const e=Math.floor(a/60),o=a%60;return`${e}分${o}秒`},Ht=async a=>{try{await Ae.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=ie.value.findIndex(o=>o.task_id===a.task_id);e!==-1&&(ie.value.splice(e,1),Oe.value--),D.post("/api/Coordinate/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}}).then(o=>{o.data.success?i.success("删除成功"):(ie.value.splice(e,0,a),Oe.value++,i.error(o.data.message||"删除失败"))}).catch(o=>{ie.value.splice(e,0,a),Oe.value++,i.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&i.error("删除失败，请稍后重试")}};Se(()=>{he(),W.value=Ne(),R.value={fmw_id:"coordinatetransformation",fmw_name:"坐标转换",fmw_path:"tools/coordinatetransformation/coordinatetransformation.fmw"}}),Ot(()=>{});const de=u(!1),ce=u(!1),L=u({fmw_name:"",project:"",description:"",file:null}),q=u({fmw_id:"",file:null}),Wt={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},G=u(null),H=u(null),Xt=()=>{ue.value=!1},Yt=()=>{var a;g.value={},ee.value=[],S.value&&S.value.resetFields(),(a=S.value)!=null&&a.$el&&S.value.$el.querySelectorAll(".el-upload").forEach(o=>{var c;const p=(c=o.__vueParentComponent)==null?void 0:c.ctx;p&&typeof p.clearFiles=="function"&&p.clearFiles()})},Jt=()=>{de.value=!1},Zt=()=>{E.value&&E.value.resetFields(),L.value={fmw_name:"",project:"",description:"",file:null},G.value&&typeof G.value.clearFiles=="function"&&G.value.clearFiles()},Qt=()=>{ce.value=!1},Kt=()=>{we.value&&we.value.resetFields(),q.value={fmw_id:"",file:null},H.value&&typeof H.value.clearFiles=="function"&&H.value.clearFiles()},E=u(),we=u();ne(()=>g.value,a=>{const e={};ee.value.forEach(o=>{Fe(o)&&Me.value[o.prop]&&(e[o.prop]=Me.value[o.prop])}),S.value&&(S.value.clearValidate(),S.value.rules=e)},{deep:!0});const te=u(!1),ea=()=>{Ae.confirm("确定要提交任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{te.value=!0,fa().finally(()=>{te.value=!1})})},ta=async()=>{var a;te.value=!0;try{if(!R.value){i.error("工具信息不完整"),te.value=!1;return}const e=ee.value.filter(r=>Fe(r));console.log("可见的表单项:",e.map(r=>r.prop));for(const r of e)if(r.required&&!g.value[r.prop]){let _="";r.type==="file"||r.type==="upload"?_=`请上传${r.label}`:r.type==="select"||r.type==="dropdown"||r.type==="listbox"?_=`请选择${r.label}`:_=`请填写${r.label}`,i.error(_);return}const p={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:R.value.fmw_id,fmw_name:R.value.fmw_name,fmw_path:R.value.fmw_path,params:{}};for(const r of e){const _=g.value[r.prop];_!=null&&!r.prop.endsWith("_value")&&(r.type==="color"?p.params[r.prop]=g.value[`${r.prop}_value`]:p.params[r.prop]=_)}if(console.log("提交的请求数据:",p),Nt.value)try{const r=await D.post(N.UPDATE_COUNT,{id:R.value.id,username:b.user.username});if(!r.data.success){i.error(r.data.message||"更新使用次数失败");return}}catch(r){console.error("更新使用次数失败:",r),i.error("更新使用次数失败，请稍后重试");return}const c=await D.post("/api/Coordinate/run_fme",p,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(c.data.success){try{const r=await D.post("/api/Coordinate/tools/update-run-times",{},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});r.data.success||console.error("更新运行次数失败:",r.data.message)}catch(r){console.error("更新运行次数失败:",r)}i.success("任务提交成功"),g.value={},ee.value=[],S.value&&S.value.resetFields(),(a=S.value)!=null&&a.$el&&S.value.$el.querySelectorAll(".el-upload").forEach(_=>{var f;const k=(f=_.__vueParentComponent)==null?void 0:f.ctx;k&&typeof k.clearFiles=="function"&&k.clearFiles()}),ue.value=!1,window.location.reload(),await Bt(),K.value="history",bt()}else i.error(c.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),i.error("提交失败，请稍后重试")}finally{te.value=!1}},aa=async()=>{if(E.value)try{if(await E.value.validate(),!L.value.file){i.error("请上传工具文件");return}const a=new FormData;a.append("file",L.value.file);const e=await D.post(`${N.UPLOAD_FILE}`,a,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(!e.data.success){i.error(e.data.message||"文件上传失败");return}const o={fmw_id,fmw_name:L.value.fmw_name,project:L.value.project,description:L.value.description,fmw_path:e.data.data.path,file_path:e.data.data.path,data:new Date().toISOString()},p=await D.post(`${N.UPLOAD_TOOL}`,o,{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});p.data.success?(i.success("工具上传成功"),de.value=!1,L.value={fmw_name:"",project:"",description:"",file:null},E.value&&E.value.resetFields(),G.value&&typeof G.value.clearFiles=="function"&&G.value.clearFiles(),await he()):i.error(p.data.message||"上传失败")}catch(a){console.error("上传工具失败:",a),i.error("参数未填写完整")}},la=a=>a.name.toLowerCase().endsWith(".fmw")?a.size/1024/1024<50?!0:(i.error("文件大小不能超过50MB"),!1):(i.error("只能上传FMW文件"),!1),sa=a=>(q.value.file=a.raw,!1),oa=async()=>{if(!q.value.file){i.error("请选择更新文件");return}try{const a=new FormData;a.append("file",q.value.file);const e=await D.post(`${N.UPLOAD_FILE}`,a,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(!e.data.success){i.error(e.data.message||"文件上传失败");return}const o={fmw_id:q.value.fmw_id,file_path:e.data.data.path},p=await D.post(`${N.UPDATE_TOOL}`,o,{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});p.data.success?(i.success("工具更新成功"),ce.value=!1,q.value={fmw_id:"",file:null},we.value&&we.value.resetFields(),H.value&&typeof H.value.clearFiles=="function"&&H.value.clearFiles(),await he()):i.error(p.data.message||"更新失败")}catch(a){console.error("更新工具失败:",a),i.error("更新失败，请检查网络连接")}},na=(a,e)=>{if(!e){g.value[a]="rgb(255, 255, 255)";return}g.value[a]=e;const o=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(o){const[,p,c,r]=o;g.value[`${a}_value`]=`${p},${c},${r}`}else g.value[`${a}_value`]="255,255,255"},Fe=a=>{var o;if(!((o=a.component)!=null&&o.visibility))return!0;const e=a.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const p of e.if){const{condition:c,then:r}=p;let _=!1;if(c.allOf)_=c.allOf.every(k=>{if(k.equals){const{parameter:f,value:z}=k.equals;return g.value[f]===z}else if(k.isEnabled){const{parameter:f}=k.isEnabled;return!!g.value[f]}return!1});else if(c.equals){const{parameter:k,value:f}=c.equals;_=g.value[k]===f}else if(c.isEnabled){const{parameter:k}=c.isEnabled;_=!!g.value[k]}if(_)return r==="visibleEnabled"||r==="visibleDisabled"}return!1},Ce=u(!1),dt=u(""),ae=u(!1),pe=u(),ze=u(!1),ct=u([]),x=u({tool_name:"坐标转换",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),ra={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},ua=a=>a.getTime()<Date.now()-864e5,ia=()=>{ae.value=!0,da()},da=async()=>{try{const a=await D.get("/api/admin-users");a.data.success?ct.value=a.data.data:i.error("获取审批人失败")}catch{i.error("获取审批人失败")}},ca=async()=>{pe.value&&await pe.value.validate(async a=>{if(a){ze.value=!0;try{const e=await D.post("/api/Coordinate/tools/apply",{fmw_id:"coordinatetransformation",fmw_name:"坐标转换",applicant:b.user.username,reason:x.value.reason,end_date:x.value.end_date,usage_count:x.value.usage_count,user_project:x.value.user_project,reviewer:x.value.approver},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});e.data.success?(i.success("申请提交成功"),ae.value=!1,pt(),await ke()):i.error(e.data.message||"申请提交失败")}catch{i.error("申请提交失败")}finally{ze.value=!1}}})},pt=()=>{pe.value&&pe.value.resetFields(),Object.assign(x.value,{tool_name:"坐标转换",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})},xe=u([]),Pe=u(!1),F=u(null),V=u(0),ft=u(0),Ee=()=>{V.value--},je=()=>{V.value,V.value++};ne(V,a=>{setTimeout(()=>{ft.value=a},300)});const ke=async()=>{Pe.value=!0;try{const a=await D.get("/api/tools/my-applications",{params:{source:"CoordinateView",fmw_id:"coordinatetransformation"},headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(a.data.success){xe.value=a.data.data;const e=xe.value.find(o=>mt(o));e&&(F.value=e.id)}else i.error(a.data.message||"获取许可列表失败")}catch(a){console.error("获取许可列表失败:",a),i.error("获取许可列表失败")}finally{Pe.value=!1}};Se(()=>{ke()});function fe(a){return{审批中:{type:"info",text:"审批中"},已通过:{type:"success",text:"已通过"},已驳回:{type:"danger",text:"已驳回"},已过期:{type:"warning",text:"已过期"},已耗尽:{type:"warning",text:"已耗尽"},可用:{type:"success",text:"可用"}}[a]||{type:"default",text:a}}const le=u(!1),pa=Ra(async()=>{if(!le.value){le.value=!0;try{await ke()}finally{le.value=!1}}},1e3,{leading:!0,trailing:!1}),fa=async()=>{if(V.value===3)try{const a=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,e={点:"Point",多点:"Multipoint",线:"Line",面:"Area",文本:"Text",多文本:"Multitext",曲线:"Curve",圆弧:"Arc",椭圆:"Ellipse",其他:"Other"},o={task_id:a,fmw_id:"coordinatetransformation",fmw_name:"坐标转换",fmw_path:"tools/coordinatetransformation/coordinatetransformation.fmw",params:{trans_form:"dwg gdb shp",input_date:`temp/${W.value}`,source_coor:"苏州独立",target_coor:"EPSG:4528",VERSION_2:"Release2007",save_path:`tools/coordinatetransformation/output/${a}`},up_nums:T.value.length},p=sessionStorage.getItem("user");if(!p){i.error("未登录,请先登录");return}try{const c=JSON.parse(p);if(!c.username){i.error("用户信息不完整,请重新登录");return}const r=await fetch("/api/Coordinate/run_fme",{method:"POST",headers:{"Content-Type":"application/json","X-Username":c.username},body:JSON.stringify(o)}).then(_=>_.json());if(r.success){const _=await fetch("/api/Coordinate/tools/update_usacount",{method:"POST",headers:{"Content-Type":"application/json","X-Username":c.username},body:JSON.stringify({id:F.value,username:c.username,file_count:T.value.length})}).then(k=>k.json());_.success||console.error("更新使用次数失败:",_.message),i.success("任务提交成功"),await new Promise(k=>setTimeout(k,1e3)),await me("coordinatetransformation"),bt(),V.value=0,F.value=null,T.value=[],j.value=[],W.value=Ne(),Ie.value=[],Be.value=!1,le.value=!1,await ke(),K.value="history"}else i.error(r.message||"任务提交失败")}catch(c){console.error("解析用户信息失败:",c),i.error("用户信息解析失败,请重新登录")}}catch(a){console.error("提交任务失败:",a),i.error("任务提交失败，请重试")}else{if(V.value===2&&!ha.value){i.warning("请先上传DWG文件");return}V.value++}},ma=a=>{if(fe(a.status).text!=="已通过")return;const e=new Date(a.end_date),o=new Date;if(o.setHours(0,0,0,0),e<o){i.warning("该许可已过期");return}if(a.count>=a.usage_count){i.warning("该许可使用次数已达上限");return}if(V.value===2&&T.value.length>0&&(a.usage_count||0)-(a.count||0)-T.value.length<0){i.warning(`当前选择的许可剩余次数不足，无法处理 ${T.value.length} 个文件`);return}F.value=a.id},va=({row:a})=>{if(fe(a.status).text!=="已通过")return"disabled-row";const e=new Date(a.end_date),o=new Date;return o.setHours(0,0,0,0),e<o||a.count>=a.usage_count?"disabled-row":"clickable-row"},mt=a=>{if(fe(a.status).text!=="已通过")return!1;const e=new Date(a.end_date),o=new Date;return o.setHours(0,0,0,0),!(e<o||a.count>=a.usage_count)},Ie=u([]),T=u([]),Be=u(!1),W=u(""),Ne=()=>{const a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return Array.from({length:40},()=>a.charAt(Math.floor(Math.random()*a.length))).join("")};Se(()=>{W.value=Ne()});const _a=a=>a.name.toLowerCase().endsWith(".dwg")?!0:(i.error("只能上传DWG格式的文件！"),!1),ga=a=>[".zip",".rar",".7z"].some(o=>a.name.toLowerCase().endsWith(o))?!0:(i.error("只能上传ZIP、RAR、7Z格式的压缩包！"),!1),vt=(a,e)=>{a.success?([".zip",".rar",".7z"].some(p=>e.name.toLowerCase().endsWith(p))&&(e.extracting=!1,e.parsing=!1,e.parsed=!0,e.dwgCount=a.files.length),T.value=[...T.value,...a.files],i.success("文件上传成功")):i.error(a.message||"文件上传失败")},_t=a=>{console.error("Upload error:",a),i.error("文件上传失败")},qe=async a=>{try{const e=await fetch("/api/Coordinate/delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({folderId:W.value,fileName:a.name})}).then(o=>o.json());if(e.success){const o=T.value.findIndex(p=>p.name===a.name);o!==-1&&T.value.splice(o,1),i.success("文件删除成功")}else i.error(e.message||"文件删除失败")}catch(e){console.error("Delete error:",e),i.error("文件删除失败")}},ya=a=>{Ae.confirm(`确定要移除文件 "${a.name}" 吗？`,"移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{qe({name:a.name})}).catch(()=>{})},gt=a=>{if(!a)return"-";if(typeof a=="string")return a;const e=["B","KB","MB","GB"];let o=0,p=a;for(;p>=1024&&o<e.length-1;)p/=1024,o++;return`${p.toFixed(2)} ${e[o]}`},ha=P(()=>T.value.length>0),se=P(()=>F.value?xe.value.find(a=>a.id===F.value):null),Ge=P(()=>{var a,e;return(((a=se.value)==null?void 0:a.usage_count)||0)-(((e=se.value)==null?void 0:e.count)||0)-T.value.length}),He=P(()=>Ge.value>=0),j=u([]),We=["点（Point）","富点（MultiPoint）","线（Line）","面（Area）","文本（Text）","富文本（MultiText）","曲线（Curve）","圆弧（Arc）","椭圆（Ellipse）","其他（Other）"],ba=()=>{j.value=[...We]},wa=()=>{const a=new Set(j.value);j.value=We.filter(e=>!a.has(e))},X=u([]),Xe=u(!1),yt=u(1),ht=u(10),Ve=u(0),Te=u(null),Ye=u(!1),Ca=a=>({running:"warning",success:"success",failed:"danger"})[a]||"info",xa=a=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[a]||a,me=async(a=(e=>(e=R.value)==null?void 0:e.fmw_id)()||"coordinatetransformation")=>{try{Xe.value=!0;const o=await D.post("/api/Coordinate/tools/run_records",{fmw_id:a,username:b.user.username,page:yt.value,page_size:ht.value},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});o.data.success?(X.value=o.data.data.records,Ve.value=o.data.data.pagination.total):i.error(o.data.message||"获取历史记录失败")}catch(o){console.error("获取历史记录失败:",o),i.error("获取历史记录失败")}finally{Xe.value=!1}},bt=()=>{Ye.value||(Ye.value=!0,Te.value=setInterval(async()=>{X.value.some(e=>e.status==="running"||e.status==="pending")?(console.log("检测到运行中的任务，正在更新状态..."),await me()):(console.log("没有运行中的任务，停止轮询"),wt())},Wl))},wt=()=>{Te.value&&(clearInterval(Te.value),Te.value=null),Ye.value=!1},ka=async a=>{try{await Ae.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=X.value.findIndex(o=>o.task_id===a.task_id);e!==-1&&(X.value.splice(e,1),Ve.value--),D.post("/api/Coordinate/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}}).then(o=>{o.data.success?i.success("删除成功"):(X.value.splice(e,0,a),Ve.value++,i.error(o.data.message||"删除失败"))}).catch(o=>{X.value.splice(e,0,a),Ve.value++,i.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&(console.error("删除历史记录失败:",e),i.error("删除历史记录失败"))}},Ct=async a=>{try{const e=`tools/coordinatetransformation/output/${a.task_id}/${a.file_name}`,o=`${N.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`,p=document.createElement("a");p.style.display="none",document.body.appendChild(p),p.href=o,p.download=a.file_name,p.click(),setTimeout(()=>{document.body.removeChild(p)},100),i.success("开始下载")}catch(e){console.error("下载历史记录失败:",e),i.error("下载失败，请稍后重试")}},xt=async a=>{try{a.error_message?(dt.value=a.error_message,Ce.value=!0):i.warning("暂无错误信息")}catch(e){console.error("显示日志失败:",e),i.error("显示日志失败")}};ne([yt,ht],()=>{me()}),Se(()=>{me()}),Ot(()=>{wt()});const De=u(null);ne(V,async()=>{if(await Ba(),De.value){const a=De.value.querySelector('.step-content[v-show="true"]');a&&(De.value.style.height=`${a.scrollHeight}px`)}});const Va=P(()=>j.value.length>0);return(a,e)=>{var $t,At;const o=v("el-step"),p=v("el-steps"),c=v("el-icon"),r=v("el-button"),_=v("el-skeleton-item"),k=v("el-skeleton"),f=v("el-table-column"),z=v("el-tag"),Ta=v("el-radio"),ve=v("el-table"),Je=v("ArrowLeft"),_e=v("el-upload"),Le=v("el-descriptions-item"),Da=v("el-descriptions"),La=v("el-alert"),A=v("el-checkbox"),Ua=v("el-checkbox-group"),kt=v("el-card"),Vt=v("el-tab-pane"),Sa=v("el-tooltip"),Tt=v("el-button-group"),$a=v("el-tabs"),Dt=v("el-option"),Lt=v("el-select"),Ut=v("el-input-number"),St=v("el-date-picker"),Aa=v("el-color-picker"),Y=v("el-input"),O=v("el-form-item"),Ze=v("el-form"),oe=v("el-dialog"),Qe=Ia("loading");return y(),I("div",Ga,[t($a,{modelValue:K.value,"onUpdate:modelValue":e[2]||(e[2]=l=>K.value=l),class:"cad-tabs",onTabClick:Et},{default:s(()=>[t(Vt,{label:"转换工具",name:"convert"},{default:s(()=>[t(Ke,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(n("div",Ha,[t(kt,{class:"license-card",style:{"margin-top":"0"}},{default:s(()=>[t(p,{active:V.value,"finish-status":"success",simple:""},{default:s(()=>[t(o,{title:"开始"}),t(o,{title:"选择许可"}),t(o,{title:"上传文件"}),t(o,{title:"设置输出类型"})]),_:1},8,["active"]),n("div",Wa,[n("div",{class:"progress-bar",style:Fa({width:`${ft.value/4*100}%`})},null,4)]),n("div",{class:"step-content-container",ref_key:"stepContentRef",ref:De},[t(Ke,{name:"step-fade",mode:"out-in"},{default:s(()=>[(y(),I("div",{key:V.value},[M(n("div",Xa,[n("div",Ya,[t(r,{type:"primary",size:"large",class:"start-button",onClick:je},{default:s(()=>[t(c,null,{default:s(()=>[t(h(et))]),_:1}),e[24]||(e[24]=d(" 开始转换 "))]),_:1})])],512),[[B,V.value===0]]),M(n("div",Ja,[n("div",Za,[t(r,{type:"primary",onClick:ia},{default:s(()=>[t(c,null,{default:s(()=>[t(h(za))]),_:1}),e[25]||(e[25]=d(" 申请许可 "))]),_:1}),t(r,{type:"primary",onClick:h(pa),disabled:le.value},{default:s(()=>[t(c,{class:$e({"refresh-rotate":le.value})},{default:s(()=>[t(h(Pa))]),_:1},8,["class"]),e[26]||(e[26]=d(" 刷新许可 "))]),_:1},8,["onClick","disabled"])]),Pe.value?(y(),C(k,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:s(()=>[t(_,{variant:"text",style:{width:"80px","margin-right":"16px"}}),t(_,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(_,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(_,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(_,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(_,{variant:"text",style:{width:"180px","margin-right":"16px"}}),t(_,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(_,{variant:"text",style:{width:"80px"}})]),_:1})):(y(),C(ve,{key:1,data:xe.value,style:{width:"100%"},onRowClick:ma,"row-class-name":va},{default:s(()=>[t(f,{type:"index",label:"序号",width:"80",align:"center"}),t(f,{prop:"user_project",label:"项目","min-width":"150","show-overflow-tooltip":""}),t(f,{prop:"reason",label:"原因","min-width":"150","show-overflow-tooltip":""}),t(f,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),t(f,{prop:"count",label:"已用次数",width:"100",align:"center"}),t(f,{prop:"end_date",label:"截止时间",width:"180",align:"center"},{default:s(({row:l})=>[d(m(be(l.end_date,"yyyy-MM-dd")),1)]),_:1}),t(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[t(z,{type:fe(l.status).type},{default:s(()=>[d(m(fe(l.status).text),1)]),_:2},1032,["type"])]),_:1}),t(f,{label:"选择",width:"80",align:"center"},{default:s(({row:l})=>[t(Ta,{modelValue:F.value,"onUpdate:modelValue":e[0]||(e[0]=U=>F.value=U),label:l.id,disabled:!mt(l),class:"custom-radio"},{default:s(()=>[n("span",Qa,m(l.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"])),n("div",Ka,[t(r,{onClick:Ee},{default:s(()=>[t(c,null,{default:s(()=>[t(Je)]),_:1}),e[27]||(e[27]=d(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:je,disabled:!F.value},{default:s(()=>[e[28]||(e[28]=d(" 下一步 ")),t(c,null,{default:s(()=>[t(h(et))]),_:1})]),_:1},8,["disabled"])])],512),[[B,V.value===1]]),M(n("div",el,[n("div",tl,[e[39]||(e[39]=n("div",{style:{"margin-bottom":"20px",display:"flex","justify-content":"space-between","align-items":"center"}},[n("h3",{style:{margin:"0"}},"上传文件")],-1)),n("div",al,[n("div",ll,[n("div",sl,[t(c,null,{default:s(()=>[t(h(tt))]),_:1}),e[29]||(e[29]=n("span",null,"上传DWG文件",-1))]),e[32]||(e[32]=n("div",{class:"section-desc"},"支持直接上传单个或多个DWG文件",-1)),t(_e,{class:"upload-component",drag:"",action:"/api/Coordinate/upload","auto-upload":!0,"on-success":vt,"on-error":_t,"on-remove":qe,"file-list":Ie.value,"before-upload":_a,accept:".dwg",multiple:"","show-file-list":!0,data:{folderId:W.value}},{tip:s(()=>e[30]||(e[30]=[n("div",{class:"el-upload__tip"}," 支持 .dwg 格式文件 ",-1)])),file:s(({file:l})=>[n("div",ol,[n("span",nl,m(l.name),1),n("span",rl,m(l.status==="success"?"上传成功":l.status==="uploading"?`上传中 ${Math.round(l.percentage)}%`:"上传中"),1)])]),default:s(()=>[t(c,{class:"el-icon--upload"},{default:s(()=>[t(h(ye))]),_:1}),e[31]||(e[31]=n("div",{class:"el-upload__text"},[d(" 将DWG文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["file-list","data"])]),n("div",ul,[n("div",il,[t(c,null,{default:s(()=>[t(h(Ea))]),_:1}),e[33]||(e[33]=n("span",null,"上传压缩包",-1))]),e[36]||(e[36]=n("div",{class:"section-desc"},"支持上传包含DWG文件的ZIP/RAR/7Z压缩包，将自动解压并提取DWG文件",-1)),t(_e,{class:"upload-component",drag:"",action:"/api/Coordinate/upload","auto-upload":!0,"on-success":vt,"on-error":_t,"on-remove":qe,"file-list":Ie.value,"before-upload":ga,accept:".zip,.rar,.7z",multiple:"","show-file-list":!0,data:{folderId:W.value}},{tip:s(()=>e[34]||(e[34]=[n("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包 ",-1)])),file:s(({file:l})=>[n("div",dl,[n("span",cl,m(l.name),1),n("span",pl,m(l.status==="success"?l.extracting?"解压中":l.parsing?"解析中":l.parsed?`找到${l.dwgCount||0}个DWG文件`:"解压中":l.status==="uploading"?`上传中 ${Math.round(l.percentage)}%`:"上传中"),1)])]),default:s(()=>[t(c,{class:"el-icon--upload"},{default:s(()=>[t(h(ye))]),_:1}),e[35]||(e[35]=n("div",{class:"el-upload__text"},[d(" 将压缩包拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["file-list","data"])])]),n("div",fl,[n("div",ml,[e[37]||(e[37]=n("span",{class:"table-title"},"解析后CAD文件",-1)),n("div",vl,[t(z,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var l;return[d("当前许可次数："+m(((l=se.value)==null?void 0:l.usage_count)||0),1)]}),_:1}),t(z,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var l;return[d("已用次数："+m(((l=se.value)==null?void 0:l.count)||0),1)]}),_:1}),t(z,{type:"warning",style:{"margin-right":"10px"}},{default:s(()=>[d("消耗次数："+m(T.value.length),1)]),_:1}),t(z,{type:He.value?"success":"danger",style:{"margin-right":"10px"}},{default:s(()=>[d(" 消耗后剩余次数："+m(Ge.value)+" ",1),He.value?re("",!0):(y(),C(c,{key:0,style:{"margin-left":"4px"}},{default:s(()=>[t(h(ja))]),_:1}))]),_:1},8,["type"])])]),M((y(),C(ve,{data:T.value,style:{width:"100%"},border:""},{default:s(()=>[t(f,{type:"index",label:"序号",width:"80",align:"center"}),t(f,{prop:"name",label:"文件名","min-width":"200","show-overflow-tooltip":""}),t(f,{prop:"size",label:"文件大小",width:"120",align:"center"},{default:s(({row:l})=>[d(m(gt(l.size)),1)]),_:1}),t(f,{label:"操作",width:"120",align:"center"},{default:s(({row:l})=>[t(r,{type:"danger",size:"small",onClick:U=>ya(l),disabled:Be.value},{default:s(()=>[t(c,null,{default:s(()=>[t(h(at))]),_:1}),e[38]||(e[38]=d(" 移除 "))]),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[Qe,Be.value]])])]),n("div",_l,[t(r,{onClick:Ee},{default:s(()=>[t(c,null,{default:s(()=>[t(Je)]),_:1}),e[40]||(e[40]=d(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:je,disabled:T.value.length===0},{default:s(()=>[e[41]||(e[41]=d(" 下一步 ")),t(c,null,{default:s(()=>[t(h(et))]),_:1})]),_:1},8,["disabled"])])],512),[[B,V.value===2]]),M(n("div",gl,[n("div",yl,[n("div",hl,[n("div",bl,[e[42]||(e[42]=n("h3",null,"待转换文件",-1)),t(ve,{data:T.value,style:{width:"100%"},size:"small"},{default:s(()=>[t(f,{prop:"name",label:"文件名"}),t(f,{prop:"size",label:"大小",width:"120"},{default:s(l=>[d(m(gt(l.row.size)),1)]),_:1})]),_:1},8,["data"])]),n("div",wl,[e[43]||(e[43]=n("h3",null,"许可信息",-1)),t(Da,{column:1,border:""},{default:s(()=>[t(Le,{label:"当前许可次数"},{default:s(()=>{var l;return[d(m(((l=se.value)==null?void 0:l.usage_count)||0),1)]}),_:1}),t(Le,{label:"已用次数"},{default:s(()=>{var l;return[d(m(((l=se.value)==null?void 0:l.count)||0),1)]}),_:1}),t(Le,{label:"消耗次数"},{default:s(()=>[d(m(T.value.length),1)]),_:1}),t(Le,{label:"消耗后剩余次数"},{default:s(()=>[n("span",{class:$e({"text-danger":!He.value})},m(Ge.value),3)]),_:1})]),_:1})])]),n("div",Cl,[n("div",xl,[e[56]||(e[56]=n("h3",null,"选择输出Geometry类型",-1)),n("div",kl,[n("div",Vl,[t(r,{type:"primary",onClick:ba},{default:s(()=>e[44]||(e[44]=[d("全选")])),_:1}),t(r,{onClick:wa},{default:s(()=>e[45]||(e[45]=[d("反选")])),_:1})]),M(n("div",Tl,[t(La,{title:"为了避免图形丢失，建议选择全部类型",type:"warning",closable:!1,"show-icon":""})],512),[[B,j.value.length!==We.length]]),t(Ua,{modelValue:j.value,"onUpdate:modelValue":e[1]||(e[1]=l=>j.value=l)},{default:s(()=>[t(A,{label:"点（Point）"},{default:s(()=>e[46]||(e[46]=[d("点（Point）")])),_:1}),t(A,{label:"富点（MultiPoint）"},{default:s(()=>e[47]||(e[47]=[d("富点（MultiPoint）")])),_:1}),t(A,{label:"线（Line）"},{default:s(()=>e[48]||(e[48]=[d("线（Line）")])),_:1}),t(A,{label:"面（Area）"},{default:s(()=>e[49]||(e[49]=[d("面（Area）")])),_:1}),t(A,{label:"文本（Text）"},{default:s(()=>e[50]||(e[50]=[d("文本（Text）")])),_:1}),t(A,{label:"富文本（MultiText）"},{default:s(()=>e[51]||(e[51]=[d("富文本（MultiText）")])),_:1}),t(A,{label:"曲线（Curve）"},{default:s(()=>e[52]||(e[52]=[d("曲线（Curve）")])),_:1}),t(A,{label:"圆弧（Arc）"},{default:s(()=>e[53]||(e[53]=[d("圆弧（Arc）")])),_:1}),t(A,{label:"椭圆（Ellipse）"},{default:s(()=>e[54]||(e[54]=[d("椭圆（Ellipse）")])),_:1}),t(A,{label:"其他（Other）"},{default:s(()=>e[55]||(e[55]=[d("其他（Other）")])),_:1})]),_:1},8,["modelValue"])])])])]),n("div",Dl,[t(r,{onClick:Ee},{default:s(()=>[t(c,null,{default:s(()=>[t(Je)]),_:1}),e[57]||(e[57]=d(" 上一步 "))]),_:1}),V.value===3?(y(),C(r,{key:0,type:"primary",loading:te.value,onClick:ea,disabled:!Va.value},{default:s(()=>e[58]||(e[58]=[d("提交任务")])),_:1},8,["loading","disabled"])):re("",!0)])],512),[[B,V.value===3]])]))]),_:1})],512)]),_:1})],512),[[B,K.value==="convert"]])]),_:1})]),_:1}),t(Vt,{label:"历史记录",name:"history"},{default:s(()=>[t(Ke,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(n("div",Ll,[t(kt,{class:"history-card",style:{"margin-top":"0px"}},{header:s(()=>e[59]||(e[59]=[])),default:s(()=>[n("div",Ul,[M((y(),C(ve,{data:X.value,style:{width:"100%"}},{default:s(()=>[t(f,{type:"index",label:"序号",width:"80",align:"center"}),t(f,{prop:"submit_time",label:"提交时间",width:"350",align:"center"},{default:s(({row:l})=>[t(Sa,{content:be(l.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:s(()=>[n("span",null,m(be(l.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[t(z,{type:Ca(l.status)},{default:s(()=>[d(m(xa(l.status)),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"time_consuming",label:"运行耗时",width:"250",align:"center"},{default:s(({row:l})=>[d(m(it(l.time_consuming)),1)]),_:1}),t(f,{prop:"file_size",label:"文件大小",width:"150",align:"center"},{default:s(({row:l})=>[d(m(l.file_size),1)]),_:1}),t(f,{prop:"up_nums",label:"转换文件数量","min-width":"130",align:"center","show-overflow-tooltip":""}),t(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:l})=>[n("div",Sl,[t(Tt,null,{default:s(()=>[l.status==="success"&&l.up_nums>0&&l.file_size!=="0.0MB"?(y(),C(r,{key:0,type:"success",size:"small",onClick:U=>Ct(l),disabled:l.status!=="success"},{default:s(()=>[t(c,null,{default:s(()=>[t(h(Mt))]),_:1}),e[60]||(e[60]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):re("",!0),l.error_message?(y(),C(r,{key:1,type:"info",size:"small",onClick:U=>xt(l)},{default:s(()=>[t(c,null,{default:s(()=>[t(h(tt))]),_:1}),e[61]||(e[61]=d(" 日志 "))]),_:2},1032,["onClick"])):re("",!0),t(r,{type:"danger",size:"small",onClick:U=>ka(l)},{default:s(()=>[t(c,null,{default:s(()=>[t(h(at))]),_:1}),e[62]||(e[62]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Qe,Xe.value]])])]),_:1})],512),[[B,K.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(oe,{modelValue:ue.value,"onUpdate:modelValue":e[4]||(e[4]=l=>ue.value=l),title:`运行工具 - ${($t=R.value)==null?void 0:$t.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:Xt,onAfterClose:Yt},{footer:s(()=>[n("span",Rl,[t(r,{onClick:e[3]||(e[3]=l=>ue.value=!1)},{default:s(()=>e[64]||(e[64]=[d("取消")])),_:1}),t(r,{type:"primary",onClick:ta},{default:s(()=>e[65]||(e[65]=[d("提交任务")])),_:1})])]),default:s(()=>[t(Ze,{ref_key:"formRef",ref:S,model:g.value,rules:Me.value,"label-width":"200px",size:"small",class:"run-form"},{default:s(()=>[ee.value.length>0?(y(!0),I(lt,{key:0},st(ee.value,l=>M((y(),C(O,{key:l.prop,label:l.type==="message"?"":l.label,prop:l.prop,required:l.required,class:$e({"message-form-item":l.type==="message"})},{default:s(()=>{var U,ge,Ue;return[l.type==="message"?(y(),I("div",$l,m(l.component.content),1)):l.type==="upload"?(y(),C(_e,J({key:1,ref_for:!0},l.component.props,{class:["upload-area",{"is-error":((Ue=(ge=(U=S.value)==null?void 0:U.fields)==null?void 0:ge.find(w=>w.prop===l.prop))==null?void 0:Ue.validateState)==="error"}],drag:""}),{default:s(()=>[t(c,{class:"el-icon--upload"},{default:s(()=>[t(h(ye))]),_:1}),e[63]||(e[63]=n("div",{class:"el-upload__text"},[d(" 拖拽文件到此处"),n("br"),d("或"),n("em",null,"点击上传")],-1))]),_:2},1040,["class"])):l.type==="select"?(y(),C(Lt,J({key:2,modelValue:g.value[l.prop],"onUpdate:modelValue":w=>g.value[l.prop]=w,ref_for:!0},l.component.props,{style:{width:"100%"}}),{default:s(()=>[(y(!0),I(lt,null,st(l.component.options,w=>(y(),C(Dt,{key:w.value,label:w.label,value:w.value,title:w.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue"])):l.type==="number"?(y(),C(Ut,J({key:3,modelValue:g.value[l.prop],"onUpdate:modelValue":w=>g.value[l.prop]=w,ref_for:!0},l.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="datetime"?(y(),C(St,J({key:4,modelValue:g.value[l.prop],"onUpdate:modelValue":w=>g.value[l.prop]=w,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"])):l.type==="checkbox"?(y(),C(A,J({key:5,modelValue:g.value[l.prop],"onUpdate:modelValue":w=>g.value[l.prop]=w,ref_for:!0},l.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="color"?(y(),I("div",Al,[t(Aa,J({modelValue:g.value[l.prop],"onUpdate:modelValue":w=>g.value[l.prop]=w,ref_for:!0},l.component.props,{onChange:w=>na(l.prop,w),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),n("span",Ol,m(g.value[`${l.prop}_value`]||"255,255,255"),1)])):(y(),C(Y,J({key:7,modelValue:g.value[l.prop],"onUpdate:modelValue":w=>g.value[l.prop]=w,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[B,Fe(l)]])),128)):(y(),I("div",Ml," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),t(oe,{modelValue:de.value,"onUpdate:modelValue":e[9]||(e[9]=l=>de.value=l),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:Jt,onAfterClose:Zt},{footer:s(()=>[n("span",Fl,[t(r,{onClick:e[8]||(e[8]=l=>de.value=!1)},{default:s(()=>e[68]||(e[68]=[d("取消")])),_:1}),t(r,{type:"primary",onClick:aa},{default:s(()=>e[69]||(e[69]=[d("确定")])),_:1})])]),default:s(()=>[t(Ze,{ref_key:"uploadFormRef",ref:E,model:L.value,rules:Wt,"label-width":"100px",size:"small"},{default:s(()=>[t(O,{label:"工具名称",prop:"fmw_name"},{default:s(()=>[t(Y,{modelValue:L.value.fmw_name,"onUpdate:modelValue":e[5]||(e[5]=l=>L.value.fmw_name=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(O,{label:"所属项目",prop:"project"},{default:s(()=>[t(Y,{modelValue:L.value.project,"onUpdate:modelValue":e[6]||(e[6]=l=>L.value.project=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(O,{label:"工具描述",prop:"description"},{default:s(()=>[t(Y,{modelValue:L.value.description,"onUpdate:modelValue":e[7]||(e[7]=l=>L.value.description=l),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),t(O,{label:"工具文件",prop:"file"},{default:s(()=>{var l,U,ge;return[t(_e,{ref_key:"uploadRef",ref:G,class:$e(["upload-demo",{"is-error":((ge=(U=(l=E.value)==null?void 0:l.fields)==null?void 0:U.find(Ue=>Ue.prop==="file"))==null?void 0:ge.validateState)==="error"}]),drag:"","auto-upload":!1,"on-change":a.handleFileChange,"before-upload":la,limit:1,accept:".fmw","file-list":[]},{tip:s(()=>e[66]||(e[66]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[t(c,{class:"el-icon--upload"},{default:s(()=>[t(h(ye))]),_:1}),e[67]||(e[67]=n("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["class","on-change"])]}),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(oe,{modelValue:ce.value,"onUpdate:modelValue":e[11]||(e[11]=l=>ce.value=l),title:"更新工具",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:Qt,onAfterClose:Kt},{footer:s(()=>[n("span",zl,[t(r,{onClick:e[10]||(e[10]=l=>ce.value=!1)},{default:s(()=>e[72]||(e[72]=[d("取消")])),_:1}),t(r,{type:"primary",onClick:oa,disabled:!q.value.file},{default:s(()=>e[73]||(e[73]=[d("确认更新")])),_:1},8,["disabled"])])]),default:s(()=>[t(_e,{ref_key:"updateUploadRef",ref:H,class:"upload-demo","auto-upload":!1,"on-change":sa,limit:1,accept:".fmw",drag:""},{tip:s(()=>e[70]||(e[70]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[t(c,{class:"el-icon--upload"},{default:s(()=>[t(h(ye))]),_:1}),e[71]||(e[71]=n("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},512)]),_:1},8,["modelValue"]),t(oe,{modelValue:Re.value,"onUpdate:modelValue":e[12]||(e[12]=l=>Re.value=l),title:`运行成果 - ${(At=R.value)==null?void 0:At.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:s(()=>[M((y(),C(ve,{data:ie.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:s(()=>[t(f,{type:"index",label:"序号",width:"80",align:"center"}),t(f,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:s(({row:l})=>[d(m(be(l.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),t(f,{prop:"status",label:"运行状态",width:"100",align:"center"},{default:s(({row:l})=>[t(z,{type:qt(l.status)},{default:s(()=>[d(m(Gt(l.status)),1)]),_:2},1032,["type"])]),_:1}),t(f,{prop:"time_consuming",label:"运行耗时",width:"100",align:"center"},{default:s(({row:l})=>[d(m(it(l.time_consuming)),1)]),_:1}),t(f,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),t(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:l})=>[n("div",Pl,[t(Tt,null,{default:s(()=>[l.status==="success"&&l.up_nums>0&&l.file_size!=="0.0MB"?(y(),C(r,{key:0,type:"success",size:"small",onClick:U=>Ct(l),disabled:l.status!=="success"},{default:s(()=>[t(c,null,{default:s(()=>[t(h(Mt))]),_:1}),e[74]||(e[74]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):re("",!0),l.error_message?(y(),C(r,{key:1,type:"info",size:"small",onClick:U=>xt(l)},{default:s(()=>[t(c,null,{default:s(()=>[t(h(tt))]),_:1}),e[75]||(e[75]=d(" 日志 "))]),_:2},1032,["onClick"])):re("",!0),t(r,{type:"danger",size:"small",onClick:U=>Ht(l)},{default:s(()=>[t(c,null,{default:s(()=>[t(h(at))]),_:1}),e[76]||(e[76]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Qe,Rt.value]])]),_:1},8,["modelValue","title"]),t(oe,{modelValue:Ce.value,"onUpdate:modelValue":e[14]||(e[14]=l=>Ce.value=l),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:s(()=>[n("span",jl,[t(r,{onClick:e[13]||(e[13]=l=>Ce.value=!1)},{default:s(()=>e[77]||(e[77]=[d("关闭")])),_:1})])]),default:s(()=>[n("div",El,[n("pre",null,m(dt.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"]),t(oe,{modelValue:ae.value,"onUpdate:modelValue":e[22]||(e[22]=l=>ae.value=l),title:"申请许可-坐标转换",width:"500px","close-on-click-modal":!1,onClose:e[23]||(e[23]=l=>ae.value=!1),onAfterClose:pt},{footer:s(()=>[n("span",Hl,[t(r,{onClick:e[21]||(e[21]=l=>ae.value=!1)},{default:s(()=>e[78]||(e[78]=[d("取消")])),_:1}),t(r,{type:"primary",loading:ze.value,onClick:ca},{default:s(()=>e[79]||(e[79]=[d("提交")])),_:1},8,["loading"])])]),default:s(()=>[t(Ze,{ref_key:"licenseFormRef",ref:pe,model:x.value,rules:ra,"label-width":"100px",class:"apply-form",size:"small"},{default:s(()=>[t(O,{label:"工具名称",prop:"tool_name"},{default:s(()=>[t(Y,{modelValue:x.value.tool_name,"onUpdate:modelValue":e[15]||(e[15]=l=>x.value.tool_name=l),value:"坐标转换",disabled:""},null,8,["modelValue"])]),_:1}),t(O,{label:"使用项目",prop:"user_project"},{error:s(({error:l})=>[n("span",Il,m(l),1)]),default:s(()=>[t(Y,{modelValue:x.value.user_project,"onUpdate:modelValue":e[16]||(e[16]=l=>x.value.user_project=l),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),t(O,{label:"申请原因",prop:"reason"},{error:s(({error:l})=>[n("span",Bl,m(l),1)]),default:s(()=>[t(Y,{modelValue:x.value.reason,"onUpdate:modelValue":e[17]||(e[17]=l=>x.value.reason=l),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),t(O,{label:"有效期",prop:"end_date"},{error:s(({error:l})=>[n("span",Nl,m(l),1)]),default:s(()=>[t(St,{modelValue:x.value.end_date,"onUpdate:modelValue":e[18]||(e[18]=l=>x.value.end_date=l),type:"date",placeholder:"请选择有效期","disabled-date":ua,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),t(O,{label:"申请次数",prop:"usage_count"},{error:s(({error:l})=>[n("span",ql,m(l),1)]),default:s(()=>[t(Ut,{modelValue:x.value.usage_count,"onUpdate:modelValue":e[19]||(e[19]=l=>x.value.usage_count=l),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(O,{label:"审批人",prop:"approver"},{error:s(({error:l})=>[n("span",Gl,m(l),1)]),default:s(()=>[t(Lt,{modelValue:x.value.approver,"onUpdate:modelValue":e[20]||(e[20]=l=>x.value.approver=l),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:s(()=>[(y(!0),I(lt,null,st(ct.value,l=>(y(),C(Dt,{key:l.username,label:l.real_name,value:l.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Kl=Na(Xl,[["__scopeId","data-v-710efbba"]]);export{Kl as default};
