2025-07-17 13:27:52|   0.0|  0.0|INFORM|Current FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-07-17 13:27:52|   0.0|  0.0|INFORM|System Time: 20250717052752 UTC
2025-07-17 13:27:52|   0.0|  0.0|INFORM|Workspace was last saved in FME version: FME 2023.1.0.0 (20230825 - Build 23619 - WIN64)
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME_HOME is 'C:\Program Files\FME\'
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME ESRI ArcGIS Server Edition (floating)
2025-07-17 13:27:52|   0.0|  0.0|INFORM|Permanent License.
2025-07-17 13:27:52|   0.0|  0.0|INFORM|Machine host name is: DESKTOP-9BLU554
2025-07-17 13:27:52|   0.0|  0.0|INFORM|OS Locale Name     : zh_CN
2025-07-17 13:27:52|   0.0|  0.0|INFORM|OS Locale Encoding : GBK
2025-07-17 13:27:52|   0.0|  0.0|INFORM|Process Encoding   : UTF-8
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME API version: '4.0 20230426'
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME Configuration: FME_BASE is 'no'
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME Configuration: FME_MF_DIR is 'E:\GeoStream_Integration\frontend\backend\tools\coordinatetransformation/'
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME Configuration: FME_MF_NAME is 'coordinatetransformation.fmw'
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME Configuration: FME_PRODUCT_NAME is 'FME(R) 2023.1.0.0'
2025-07-17 13:27:52|   0.0|  0.0|INFORM|Operating System: Microsoft Windows 10 64-bit  (Build 19045)
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME Platform: WIN64
2025-07-17 13:27:52|   0.0|  0.0|INFORM|System Status: 181.54 GB of disk space available in the FME temporary folder (C:\Users\<USER>\AppData\Local\Temp)
2025-07-17 13:27:52|   0.0|  0.0|INFORM|System Status: 15.74 GB of physical memory available
2025-07-17 13:27:52|   0.0|  0.0|INFORM|System Status: 62.96 GB of virtual memory available
2025-07-17 13:27:52|   0.0|  0.0|INFORM|START - ProcessID: 19216, peak process memory usage: 84640 kB, current process memory usage: 84116 kB
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME Configuration: Command line arguments are `C:\Program Files\FME\fme.exe' `E:\GeoStream_Integration\frontend\backend\tools\coordinatetransformation\coordinatetransformation.fmw' `--trans_form' `gdb' `--input_date' `E:\GeoStream_Integration\frontend\backend\temp\vj4XHplGuqMdQE1cZWxvlqJZRaH4ht017ZL1vDR6' `--source_coor' `苏州独立' `--target_coor' `EPSG:4528' `--save_path' `E:\GeoStream_Integration\frontend\backend\tools\coordinatetransformation\output\task_1752730066599_phe5znfqt'
2025-07-17 13:27:52|   0.0|  0.0|INFORM|FME Configuration: Connection Storage: 'C:\Users\<USER>\AppData\Roaming\Safe Software\FME\'
2025-07-17 13:27:52|   0.1|  0.1|INFORM|Shared folders for formats are : C:\Program Files\FME\datasources;C:\Users\<USER>\Documents\FME\Formats
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Shared folders for transformers are : C:\Users\<USER>\AppData\Roaming\Safe Software\FME\Packages\23619-win64\transformers;C:\Program Files\FME\transformers;C:\Users\<USER>\AppData\Roaming\Safe Software\FME\FME Store\Transformers
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Shared folders for coordinate systems are : C:\Users\<USER>\Documents\FME\CoordinateSystems
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Shared folders for coordinate system exceptions are : C:\Users\<USER>\Documents\FME\CoordinateSystemExceptions
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Shared folders for coordinate system grid overrides are : C:\Users\<USER>\Documents\FME\CoordinateSystemGridOverrides
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Shared folders for CS-MAP transformation exceptions are : C:\Users\<USER>\Documents\FME\CsmapTransformationExceptions
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Shared folders for transformer categories are : C:\Users\<USER>\Documents\FME\TransformerCategories
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Mapping File Identifier is: 坐标转换
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Reader Keyword is `MULTI_READER'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Writer Keyword is `MULTI_DEST'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Writer Group Definition Keyword is `MULTI_DEST_DEF'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Reader type is `MULTI_READER'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Writer type is `MULTI_WRITER'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: No destination coordinate system set
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Current working folder is `E:\GeoStream_Integration\frontend\backend'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Temporary folder is `C:\Users\<USER>\AppData\Local\Temp', set from environment variable `TEMP'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Cache folder is 'C:\Users\<USER>\AppData\Local\Temp'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: FME_HOME is `C:\Program Files\FME\'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Start freeing memory when the process exceeds 47.22 GB
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME Configuration: Stop freeing memory when the process is below 35.42 GB
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Creating writer for format: 
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Creating reader for format: 
2025-07-17 13:27:52|   0.1|  0.0|INFORM|MULTI_READER(MULTI_READER): Will fail with first member reader failure
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Using Multi Reader with keyword `MULTI_READER' to read multiple datasets
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Using MultiWriter with keyword `MULTI_DEST' to output data (ID_ATTRIBUTE is `multi_writer_id')
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Loaded module 'Geometry_func' from file 'C:\Program Files\FME\plugins/Geometry_func.dll'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME API version of module 'Geometry_func' matches current internal version (4.0 20230426)
2025-07-17 13:27:52|   0.1|  0.0|INFORM|Loaded module 'AttributeKeeperFactory' from file 'C:\Program Files\FME\plugins/AttributeKeeperFactory.dll'
2025-07-17 13:27:52|   0.1|  0.0|INFORM|FME API version of module 'AttributeKeeperFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:52|   0.2|  0.1|INFORM|Loaded module 'FeatureJoinerFactory' from file 'C:\Program Files\FME\plugins/FeatureJoinerFactory.dll'
2025-07-17 13:27:52|   0.2|  0.0|INFORM|FME API version of module 'FeatureJoinerFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:52|   0.2|  0.0|INFORM|Loaded module 'QueryFactory' from file 'C:\Program Files\FME\plugins/QueryFactory.dll'
2025-07-17 13:27:52|   0.2|  0.0|INFORM|FME API version of module 'QueryFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:52|   0.3|  0.1|INFORM|Loaded module 'PythonFactory' from file 'C:\Program Files\FME\plugins/PythonFactory.dll'
2025-07-17 13:27:52|   0.3|  0.0|INFORM|FME API version of module 'PythonFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:52|   0.3|  0.0|INFORM|Using Python interpreter from `C:\Program Files\ArcGIS\Pro\bin\Python\envs\arcgispro-py3\python39.dll' with PYTHONHOME `C:\Program Files\ArcGIS\Pro\bin\Python\envs\arcgispro-py3'
2025-07-17 13:27:52|   0.3|  0.0|INFORM|Python version 3.9 loaded successfully
2025-07-17 13:27:53|   1.0|  0.7|INFORM|ArcGIS for the Python interpreter initialized in 2.9772367 seconds
2025-07-17 13:27:53|   1.0|  0.0|INFORM|Adding folder `E:\GeoStream_Integration\frontend\backend\tools\coordinatetransformation\' to the python path
2025-07-17 13:27:54|   1.5|  0.5|INFORM|... Last line repeated 6 times ...
2025-07-17 13:27:54|   1.5|  0.0|INFORM|Loaded module 'GeometryFilterFactory' from file 'C:\Program Files\FME\plugins/GeometryFilterFactory.dll'
2025-07-17 13:27:54|   1.5|  0.0|INFORM|FME API version of module 'GeometryFilterFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:54|   1.5|  0.0|INFORM|Loaded module 'GQueryFactory' from file 'C:\Program Files\FME\plugins/GQueryFactory.dll'
2025-07-17 13:27:54|   1.5|  0.0|INFORM|FME API version of module 'GQueryFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:54|   1.5|  0.0|INFORM|Loaded module 'DuplicateRemoverFactory' from file 'C:\Program Files\FME\plugins/DuplicateRemoverFactory.dll'
2025-07-17 13:27:54|   1.5|  0.0|INFORM|FME API version of module 'DuplicateRemoverFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:54|   1.6|  0.1|INFORM|Loaded module 'StringReplacerFactory' from file 'C:\Program Files\FME\plugins/StringReplacerFactory.dll'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|FME API version of module 'StringReplacerFactory' matches current internal version (4.0 20230426)
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Emptying factory pipeline
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Loaded module 'PATH' from file 'C:\Program Files\FME\plugins/PATH.dll'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|FME API version of module 'PATH' matches current internal version (4.0 20230426)
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Performing query against PATH dataset `E:\GeoStream_Integration\frontend\backend\temp\vj4XHplGuqMdQE1cZWxvlqJZRaH4ht017ZL1vDR6'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Creating reader for format: Directory and File Pathnames
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `PATH'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Path Reader: Opening the PATH Reader on folder 'E:\GeoStream_Integration\frontend\backend\temp\vj4XHplGuqMdQE1cZWxvlqJZRaH4ht017ZL1vDR6'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Path Reader: Using Glob Pattern '*'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Path Reader: Allowed Path Type set to 'ANY'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Path Reader: Recurse into subdirectories 'false'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Path Reader: Hidden Files and Folders set to 'INCLUDE'
2025-07-17 13:27:54|   1.6|  0.0|INFORM|Path Reader: Retrieve file properties 'false'
2025-07-17 13:27:54|   1.8|  0.2|INFORM|Creating reader for format: Esri Geodatabase (File Geodb)
2025-07-17 13:27:54|   2.0|  0.2|INFORM|Trying to find a DYNAMIC plugin for reader named `GEODATABASE_FILE'
2025-07-17 13:27:54|   2.0|  0.0|INFORM|Loaded module 'GEODATABASE_FILE' from file 'C:\Program Files\FME\plugins/..\geodatabase9.dll'
2025-07-17 13:27:54|   2.0|  0.0|INFORM|FME API version of module '..\geodatabase9' matches current internal version (4.0 20230426)
2025-07-17 13:27:54|   2.0|  0.0|INFORM|Performing query against GEODATABASE_FILE dataset `E:\GeoStream_Integration\frontend\backend\temp\vj4XHplGuqMdQE1cZWxvlqJZRaH4ht017ZL1vDR6\G高速与铁路szdl(转换后).gdb'
2025-07-17 13:27:54|   2.0|  0.0|INFORM|Creating reader for format: Esri Geodatabase (File Geodb)
2025-07-17 13:27:54|   2.0|  0.0|INFORM|Trying to find a DYNAMIC plugin for reader named `GEODATABASE_FILE'
2025-07-17 13:27:54|   2.0|  0.0|INFORM|Opening the Esri Geodatabase reader
2025-07-17 13:27:54|   2.0|  0.0|INFORM|An ArcGIS license is already checked out. The product checked out is 'Advanced'
2025-07-17 13:27:54|   2.0|  0.0|INFORM|Installed ArcGIS version is '3.1.6'
2025-07-17 13:27:55|   2.4|  0.4|INFORM|Connected to the File Geodatabase at 'E:\GeoStream_Integration\frontend\backend\temp\vj4XHplGuqMdQE1cZWxvlqJZRaH4ht017ZL1vDR6\G高速与铁路szdl(转换后).gdb'
2025-07-17 13:27:55|   2.4|  0.0|INFORM|File Geodatabase release: '10.0'
2025-07-17 13:27:55|   2.9|  0.5|INFORM|The OGC definition of the FME coordinate system '_CGCS2000/GK3d-40_FME_0' is 'PROJCS["CGCS2000_3_Degree_GK_Zone_40",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",40500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",120.0],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]]'
2025-07-17 13:27:55|   2.9|  0.0|INFORM|Closing the Esri Geodatabase reader
2025-07-17 13:27:55|   2.9|  0.0|INFORM|Merged 2 schema features read from 1 datasets into 2 resulting feature types
2025-07-17 13:27:55|   3.0|  0.1|STATS |Creator_XML_Creator (CreationFactory): Created 1 features
2025-07-17 13:27:55|   3.0|  0.0|STATS |Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:55|   3.0|  0.0|STATS |Creator_CREATED Brancher -1 33 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:55|   3.1|  0.1|STATS |转换参数管理_投影参数_Creator_XML_Creator (CreationFactory): Created 1 features
2025-07-17 13:27:55|   3.1|  0.0|STATS |转换参数管理_投影参数_Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Creator CREATED Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Creator_CREATED_0_gxemOtFBUzM= Brancher 0 5 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Creator_CREATED_1_FfVvzeJX5nU= Brancher 0 7 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 2 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Junction_4 (TeeFactory): Cloned 2 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Junction (TeeFactory): Cloned 2 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Junction Output Splitter (TeeFactory): Cloned 2 input feature(s) into 4 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Tester (TestFactory): Tested 2 input feature(s) -- 1 feature(s) passed and 1 feature(s) failed
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Tester PASSED Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Junction_2 (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Junction_2 Output Splitter (TeeFactory): Cloned 1 input feature(s) into 5 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_3 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_5 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_6 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_7 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_8 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Tester_2 (TestFactory): Tested 2 input feature(s) -- 1 feature(s) passed and 1 feature(s) failed
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Tester_2 PASSED Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Junction_3 (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_Junction_3 Output Splitter (TeeFactory): Cloned 1 input feature(s) into 5 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_4 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_9 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_10 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_11 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_VariableSetter_12 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_源投影参数1752572398 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_目标投影参数1752572398 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数 源投影参数 Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数 目标投影参数 Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_源投影参数 Brancher 7 20 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_投影参数_目标投影参数 Brancher 7 22 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Creator_XML_Creator (CreationFactory): Created 1 features
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Creator CREATED Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Creator_CREATED_0_tQxYRRNwcRc= Brancher 6 6 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Creator_CREATED_1_AD5BqYMbTQ0= Brancher 6 46 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 2 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Tester (TestFactory): Tested 2 input feature(s) -- 1 feature(s) passed and 1 feature(s) failed
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Junction (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_Junction Output Splitter (TeeFactory): Cloned 1 input feature(s) into 9 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter_2 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter_3 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter_4 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter_5 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter_6 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter_7 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_VariableSetter_8 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_输出1752572398 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数 输出 Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_七参数_输出 Brancher 7 24 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Creator_XML_Creator (CreationFactory): Created 1 features
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Creator_Cloner (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Creator CREATED Splitter (TeeFactory): Cloned 1 input feature(s) into 3 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Creator_CREATED_0_FKwjjOZAaEM= Brancher 3 6 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Creator_CREATED_1_RheniYvDvRY= Brancher 3 8 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Creator_CREATED_2_n2Caa3toiBU= Brancher 3 12 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 3 input feature(s) into 3 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Junction_2 (TeeFactory): Cloned 3 input feature(s) into 3 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Junction (TeeFactory): Cloned 3 input feature(s) into 3 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Junction Output Splitter (TeeFactory): Cloned 3 input feature(s) into 6 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Tester (TestFactory): Tested 3 input feature(s) -- 1 feature(s) passed and 2 feature(s) failed
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Tester PASSED Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_VariableSetter OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Tester_3 (TestFactory): Tested 3 input feature(s) -- 1 feature(s) passed and 2 feature(s) failed
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_Tester_3 PASSED Splitter (TeeFactory): Cloned 1 input feature(s) into 2 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_VariableSetter_2 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_源坐标系椭球参数1752572398 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_目标坐标系椭球参数1752572398 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数 源坐标系椭球参数 Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数 目标坐标系椭球参数 Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_源坐标系椭球参数 Brancher 7 7 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_椭球参数_目标坐标系椭球参数 Brancher 7 13 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:27:56|   3.1|  0.0|STATS |转换参数管理_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 5 input feature(s) into 5 output feature(s)
2025-07-17 13:27:56|   3.1|  0.0|INFORM|转换参数管理_FeatureJoiner_2 (FeatureJoinerFactory): Splitting bulk features into individual features
2025-07-17 13:27:56|   3.1|  0.0|INFORM|转换参数管理_FeatureJoiner_3 (FeatureJoinerFactory): Splitting bulk features into individual features
2025-07-17 13:27:56|   3.1|  0.0|INFORM|转换参数管理_FeatureJoiner_4 (FeatureJoinerFactory): Splitting bulk features into individual features
2025-07-17 13:27:56|   3.1|  0.0|INFORM|Tester_5 (TestFactory): Splitting bulk features into individual features
2025-07-17 13:27:56|   3.1|  0.0|INFORM|Tester_6 (TestFactory): Splitting bulk features into individual features
2025-07-17 13:27:56|   3.1|  0.0|INFORM|Tester_7 (TestFactory): Splitting bulk features into individual features
2025-07-17 13:27:59|   5.3|  2.2|WARN  |PythonFactory script changed the current FME process locale from 'Chinese (Simplified)_China.utf8' to 'Chinese (Simplified)_China.936' and FME has restored it back to 'Chinese (Simplified)_China.utf8'. It is undefined behavior to change locale and doing so may cause unexpected errors. For more information, visit http://fme.ly/PythonLocaleError
2025-07-17 13:28:04|   8.4|  3.1|WARN  |PythonFactory script changed the current FME process locale from 'Chinese (Simplified)_China.utf8' to 'Chinese (Simplified)_China.936' and FME has restored it back to 'Chinese (Simplified)_China.utf8'. It is undefined behavior to change locale and doing so may cause unexpected errors. For more information, visit http://fme.ly/PythonLocaleError
2025-07-17 13:28:04|   8.4|  0.0|INFORM|Tester_7 (TestFactory): Processed 1 of 1 features
2025-07-17 13:28:04|   8.4|  0.0|STATS |转换参数管理_转换参数1752572398 Output Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |转换参数管理 转换参数 Output Renamer/Nuker (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |转换参数管理 转换参数 Splitter (TeeFactory): Cloned 1 input feature(s) into 4 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |转换参数管理_转换参数_0_Mgx0ta8t5JI= Brancher -1 41 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:28:04|   8.4|  0.0|STATS |转换参数管理_转换参数_1_H868GXGBxc8= Brancher -1 43 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:28:04|   8.4|  0.0|STATS |转换参数管理_转换参数_2_sJD6Sz/T4jM= Brancher -1 48 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:28:04|   8.4|  0.0|STATS |转换参数管理_转换参数_3_yeJGayPEH3U= Brancher -1 49 (BranchingFactory): Branched 1 input feature -- 1 feature routed to the target factory, and 0 features routed to the fallback factory.
2025-07-17 13:28:04|   8.4|  0.0|STATS |_CREATOR_BRANCH_TARGET (TeeFactory): Cloned 5 input feature(s) into 5 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |Tester_5 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-07-17 13:28:04|   8.4|  0.0|STATS |Tester_6 (TestFactory): Tested 1 input feature(s) -- 0 feature(s) passed and 1 feature(s) failed
2025-07-17 13:28:04|   8.4|  0.0|STATS |Tester_7 (TestFactory): Tested 1 input feature(s) -- 1 feature(s) passed and 0 feature(s) failed
2025-07-17 13:28:04|   8.4|  0.0|INFORM|Path Reader: Closing the PATH Reader
2025-07-17 13:28:04|   8.4|  0.0|STATS |TestFilter_INPUT (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |TestFilter (TestFactory): Tested 1 input feature(s) -- 1 feature(s) passed and 0 feature(s) failed
2025-07-17 13:28:04|   8.4|  0.0|STATS |TestFilter <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |Tester_3 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换 cad Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换 转换参数 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换_cad1752572398 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换_转换参数1752572398 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换_AttributeExposer_3 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换_AttributeExposer_2 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换_GeometryFilter_2 Curve Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.4|  0.0|STATS |cad七参转换_GeometryFilter_2 <UNFILTERED> Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.1|STATS |cad七参转换_GeometryRemover (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |cad七参转换_Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:28:04|   8.5|  0.0|STATS |cad七参转换_Sorter (SortFactory): Finished sorting a total of 0 features.
2025-07-17 13:28:04|   8.5|  0.0|STATS |cad七参转换_PythonCaller_5 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |Tester (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:28:04|   8.5|  0.0|STATS |shp七参转换 矢量 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |shp七参转换 转换参数 Input Collector (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |shp七参转换_矢量1752572398 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |shp七参转换_转换参数1752572398 Input Splitter (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |shp七参转换_AttributeExposer_5 (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |shp七参转换_AttributeExposer (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |shp七参转换_PythonCaller_4 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |Tester_2 (TestFactory): Tested 1 input feature(s) -- 1 feature(s) passed and 0 feature(s) failed
2025-07-17 13:28:04|   8.5|  0.0|STATS |gdb七参转换 矢量 Input Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |gdb七参转换 转换参数 Input Collector (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |gdb七参转换_矢量1752572398 Input Splitter (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |gdb七参转换_转换参数1752572398 Input Splitter (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |gdb七参转换_AttributeExposer_5 (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:04|   8.5|  0.0|STATS |gdb七参转换_AttributeExposer (TeeFactory): Cloned 1 input feature(s) into 1 output feature(s)
2025-07-17 13:28:17|  17.8|  9.3|STATS |gdb七参转换_FeatureMerger (ReferenceFactory): Total Results: 2 Complete Feature(s), 0 Incomplete Feature(s), 0 Referenced Feature(s), 0 Unreferenced Feature(s), 0 Referencer Feature(s) (NO REFERENCES), 0 Referencee Feature(s) (NO Referencee fields defined), and 0 Duplicate Referencee Feature(s)
2025-07-17 13:28:17|  17.9|  0.1|STATS |gdb七参转换_PythonCaller_4 OUTPUT Transformer Output Nuker (TeeFactory): Cloned 2 input feature(s) into 0 output feature(s)
2025-07-17 13:28:17|  17.9|  0.0|STATS |Tester_4 (TestFactory): Tested 0 input feature(s) -- 0 feature(s) passed and 0 feature(s) failed
2025-07-17 13:28:17|  17.9|  0.0|STATS |点对七参转换 (Disabled) Nuker (TeeFactory): Cloned 1 input feature(s) into 0 output feature(s)
2025-07-17 13:28:17|  17.9|  0.0|STATS |Destination Feature Type Routing Correlator (RoutingFactory): Tested 0 input feature(s), wrote 0 output feature(s): 0 matched merge filters, 0 were routed to output, 0 could not be routed.
2025-07-17 13:28:17|  17.9|  0.0|STATS |Final Output Nuker (TeeFactory): Cloned 0 input feature(s) into 0 output feature(s)
2025-07-17 13:28:17|  17.9|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|INFORM|                     'GlobalVariable' Final State Summary
2025-07-17 13:28:17|  17.9|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|INFORM|D                                                             6.**************
2025-07-17 13:28:17|  17.9|  0.0|INFORM|T_x                                                           -147.***********
2025-07-17 13:28:17|  17.9|  0.0|INFORM|T_y                                                           -218.***********
2025-07-17 13:28:17|  17.9|  0.0|INFORM|T_z                                                           -85.241065251626
2025-07-17 13:28:17|  17.9|  0.0|INFORM|alpha                                                          1.5283254709543
2025-07-17 13:28:17|  17.9|  0.0|INFORM|beta                                                          -4.0029760535540
2025-07-17 13:28:17|  17.9|  0.0|INFORM|gamma                                                         3.57048376697048
2025-07-17 13:28:17|  17.9|  0.0|INFORM|par_7                                                         -147.***********
2025-07-17 13:28:17|  17.9|  0.0|INFORM|source_ellipsoid                                                     Beijing54
2025-07-17 13:28:17|  17.9|  0.0|INFORM|source_ellps                                                             krass
2025-07-17 13:28:17|  17.9|  0.0|INFORM|source_k                                                                     1
2025-07-17 13:28:17|  17.9|  0.0|INFORM|source_lon_0                                                  120.583333333333
2025-07-17 13:28:17|  17.9|  0.0|INFORM|source_x_0                                                               50805
2025-07-17 13:28:17|  17.9|  0.0|INFORM|source_y_0                                                            -3421129
2025-07-17 13:28:17|  17.9|  0.0|INFORM|target_ellipsoid                                                      CGCS2000
2025-07-17 13:28:17|  17.9|  0.0|INFORM|target_ellps                                                             GRS80
2025-07-17 13:28:17|  17.9|  0.0|INFORM|target_k                                                                     1
2025-07-17 13:28:17|  17.9|  0.0|INFORM|target_lon_0                                                               120
2025-07-17 13:28:17|  17.9|  0.0|INFORM|target_x_0                                                            40500000
2025-07-17 13:28:17|  17.9|  0.0|INFORM|target_y_0                                                                   0
2025-07-17 13:28:17|  17.9|  0.0|INFORM|==============================================================================
2025-07-17 13:28:17|  17.9|  0.0|INFORM|Total Number of Global Variables:                                           20
2025-07-17 13:28:17|  17.9|  0.0|INFORM|=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|STATS |                            Features Read Summary
2025-07-17 13:28:17|  17.9|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|STATS |==============================================================================
2025-07-17 13:28:17|  17.9|  0.0|STATS |Total Features Read                                                          0
2025-07-17 13:28:17|  17.9|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|STATS |                           Features Written Summary
2025-07-17 13:28:17|  17.9|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|STATS |==============================================================================
2025-07-17 13:28:17|  17.9|  0.0|STATS |Total Features Written                                                       0
2025-07-17 13:28:17|  17.9|  0.0|STATS |=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
2025-07-17 13:28:17|  17.9|  0.0|INFORM|Translation was SUCCESSFUL with 2 warning(s) (0 feature(s) output)
2025-07-17 13:28:17|  17.9|  0.0|INFORM|FME Session Duration: 25.8 seconds. (CPU: 11.6s user, 6.3s system)
2025-07-17 13:28:17|  17.9|  0.0|INFORM|END - ProcessID: 19216, peak process memory usage: 391116 kB, current process memory usage: 382864 kB
