#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\GeoStream_Integration\frontend\fmw参数解析.fmw
#          --PARAMETER02 ""
#          --PARAMETER_20 ""
#          --PARAMETER_34 ""
#          --PARAMETER_14 ""
#          --PARAMETER_55 ""
#          --PARAMETER_46 ""
#          --PARAMETER_42 ""
#          --PARAMETER_8 ""
#          --PARAMETER_9 ""
#          --PARAMETER_12 ""
#          --PARAMETER_11 ""
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="0 0"
#!   DOC_TOP_LEFT="0 0"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="56b2eb95-acd2-433d-9f25-7b965b876a2e"
#!   FME_DOCUMENT_PRIORGUID="4b8a2be9-535b-4b20-8969-e91fae1cd8be,47c267fb-8cb3-4274-a643-a2a3268aaafe,6dd9ba3e-53c9-4afb-ab83-04dc963aef7b,03fef34f-f493-4d42-8630-5395ac9452b9,8896324e-6f77-4cef-97ea-a8b680642c74"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-05-15T10:55:38"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="-3371.91 971.885"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL NAMEDMESSAGE 消息框 这是消息测试说如果二哥如果热感 发DVD吧我他很儿童好问题回头今天是星期几啊，粉丝哦给你那地方空JK风我你的健康你放假可能手机看了你的空很健康他那就看不上你发帖人开始皇岗口岸乳房保健看看他价格和卡斯特今年看见跟你说疼看饭后嗯头那个是看到你发个呢就看看来你认识；他年后上课那天结构"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI CHOICE PARAMETER02 a%b%c 1选项参数"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI COLOR_PICK PARAMETER_20 2颜色参数"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DATETIME PARAMETER_34 {} 3日期选择参数"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_14 &quot;&quot; 4文件参数"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_55 &quot;&quot; 5文件夹参数"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI STRING PARAMETER_46 6文本框"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI LITERAL CHECKBOX PARAMETER_42 YES%NO 7YESORNO测试"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI CHOICE PARAMETER_8 a%b 8条件显示测试"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_9 &quot;&quot; 9选择a显示"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_12 &quot;&quot; 10选择b显示"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME PARAMETER_11 11写出"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="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"
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="消息框" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL NAMEDMESSAGE 消息框 这是消息测试说如果二哥如果热感 发DVD吧我他很儿童好问题回头今天是星期几啊，粉丝哦给你那地方空JK风我你的健康你放假可能手机看了你的空很健康他那就看不上你发帖人开始皇岗口岸乳房保健看看他价格和卡斯特今年看见跟你说疼看饭后嗯头那个是看到你发个呢就看看来你认识；他年后上课那天结构"
#! />
#!     <INFO NAME="PARAMETER02" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI CHOICE PARAMETER02 a%b%c 1选项参数"
#! />
#!     <INFO NAME="PARAMETER_20" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI COLOR_PICK PARAMETER_20 2颜色参数"
#! />
#!     <INFO NAME="PARAMETER_34" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DATETIME PARAMETER_34 {} 3日期选择参数"
#! />
#!     <INFO NAME="PARAMETER_14" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_14 &quot;&quot; 4文件参数"
#! />
#!     <INFO NAME="PARAMETER_55" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_55 &quot;&quot; 5文件夹参数"
#! />
#!     <INFO NAME="PARAMETER_46" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI STRING PARAMETER_46 6文本框"
#! />
#!     <INFO NAME="PARAMETER_42" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI LITERAL CHECKBOX PARAMETER_42 YES%NO 7YESORNO测试"
#! />
#!     <INFO NAME="PARAMETER_8" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI CHOICE PARAMETER_8 a%b 8条件显示测试"
#! />
#!     <INFO NAME="PARAMETER_9" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_9 &quot;&quot; 9选择a显示"
#! />
#!     <INFO NAME="PARAMETER_12" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI FILENAME_MUSTEXIST PARAMETER_12 &quot;&quot; 10选择b显示"
#! />
#!     <INFO NAME="PARAMETER_11" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME PARAMETER_11 11写出"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO 消息框 

DEFAULT_MACRO PARAMETER02 

DEFAULT_MACRO PARAMETER_20 

DEFAULT_MACRO PARAMETER_34 

DEFAULT_MACRO PARAMETER_14 

DEFAULT_MACRO PARAMETER_55 

DEFAULT_MACRO PARAMETER_46 

DEFAULT_MACRO PARAMETER_42 

DEFAULT_MACRO PARAMETER_8 

DEFAULT_MACRO PARAMETER_9 

DEFAULT_MACRO PARAMETER_12 

DEFAULT_MACRO PARAMETER_11 

# ============================================================================
INCLUDE [ if {{$(PARAMETER02$encode)} == {}} { puts_real {Parameter 'PARAMETER02' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_20$encode)} == {}} { puts_real {Parameter 'PARAMETER_20' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_34$encode)} == {}} { puts_real {Parameter 'PARAMETER_34' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_14$encode)} == {}} { puts_real {Parameter 'PARAMETER_14' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_55$encode)} == {}} { puts_real {Parameter 'PARAMETER_55' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_46$encode)} == {}} { puts_real {Parameter 'PARAMETER_46' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_42$encode)} == {}} { puts_real {Parameter 'PARAMETER_42' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_8$encode)} == {}} { puts_real {Parameter 'PARAMETER_8' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_11$encode)} == {}} { puts_real {Parameter 'PARAMETER_11' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)fmw参数解析.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME fmw参数解析
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

