#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2024\12\1217\水资源调查\图斑自西北向东南编号.fmw
#          --dir "$(FME_MF_DIR)标准"
#          --PARAMETER "$(FME_MF_DIR)成果"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="17414 3393.88"
#!   DOC_TOP_LEFT="-3459.41 -3552"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="bcb72073-0ff9-489d-ab39-13e9134f9be6"
#!   FME_DOCUMENT_PRIORGUID="********-55e2-46a4-94b2-3dd41323378b,40781b8a-57ff-4bea-a426-c94e46511f72,c8ade6af-c04c-42e4-858f-64c1f03f7045,5b61fb6b-5345-4fb3-a627-49df7aae2ff8,d4be11e7-164b-4697-a895-1345b0a0b36c,a46da355-31f4-4ae9-a696-a2afd4760aeb"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2024-12-17T18:17:54"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="9583.43 -1199.72"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="87"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/&lt;u7f16&gt;&lt;u53f7&gt;&lt;u540e&gt;.gdb"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER gdb压缩包"
#!   DEFAULT_VALUE="$(FME_MF_DIR)标准"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER 保存路径"
#!   DEFAULT_VALUE="$(FME_MF_DIR)成果"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJhY2Nlc3NNb2RlIjoicmVhZCIsImRlZmF1bHRWYWx1ZSI6IiQoRk1FX01GX0RJUinmoIflh4YiLCJpbmNsdWRlV2ViQnJvd3NlciI6dHJ1ZSwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiZGlyIiwicHJvbXB0IjoiZ2Ri5Y6L57yp5YyFIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjp0cnVlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6ZmFsc2UsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiYWNjZXNzTW9kZSI6IndyaXRlIiwiYWxsb3dVUkwiOmZhbHNlLCJkZWZhdWx0VmFsdWUiOiIkKEZNRV9NRl9ESVIp5oiQ5p6cIiwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiUEFSQU1FVEVSIiwicHJvbXB0Ijoi5L+d5a2Y6Lev5b6EIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjpmYWxzZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOnRydWUsInZhbHVlVHlwZSI6InN0cmluZyJ9XX0="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)标准"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER gdb压缩包"
#! />
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)成果"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-3459.4095940959423 -784.3828438284379"
#!   BOUNDING_RECT="-3459.4095940959423 -784.3828438284379 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-1121.0600611753243 -1318.9068672295916"
#!   BOUNDING_RECT="-1121.0600611753243 -1318.9068672295916 540.63040630406317 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;u5143&gt;&lt;u6570&gt;&lt;u636e&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="作业员" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="所属单位" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="调查开始日期" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="调查完成日期" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="问题及处理意见" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="备注" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="FSQ&lt;solidus&gt;&lt;u6881&gt;&lt;u6eaa&gt;&lt;u533a&gt;&lt;u9700&gt;&lt;u5916&gt;&lt;u4e1a&gt;&lt;u8c03&gt;&lt;u67e5&gt;&lt;u56fe&gt;&lt;u6591&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_1" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Leng" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="FSQ&lt;solidus&gt;&lt;u6881&gt;&lt;u6eaa&gt;&lt;u533a&gt;DFFH&lt;u56fe&gt;&lt;u6591&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="DLBM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="HYDANAME" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="HYDLNAME" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GJBJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SKDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Length" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="FSQ&lt;solidus&gt;HL_FS"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <OUTPUT_FEAT NAME="FSQ&lt;solidus&gt;HP_FS"/>
#!     <FEAT_COLLAPSED COLLAPSED="5"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="5" />
#!     <OUTPUT_FEAT NAME="FSQ&lt;solidus&gt;SK_FS"/>
#!     <FEAT_COLLAPSED COLLAPSED="6"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SKMC" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SKDM" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SKBM" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="6" />
#!     <OUTPUT_FEAT NAME="KT"/>
#!     <FEAT_COLLAPSED COLLAPSED="7"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="7" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="8"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="8" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="9"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="9" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="10"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="10" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="FSQ/HL_FS,&quot;OBJECTID,UID,YSDM,HLMC,HLDM,HLBM,XJXZQDM,XJXZQMC,SMMJ,SSJSYDDL,HLDJ,FKSQ,LYMC,LYBM,SJNF,BZ,SHAPE_Length,SHAPE_Area,挂接号&quot;,FSQ/HP_FS,&quot;OBJECTID,UID,YSDM,HPMC,HPDM,HPBM,XJXZQDM,XJXZQMC,SMMJ,SSJSYDDL,FKSQ,LYMC,LYBM,SJNF,BZ,SHAPE_Length,SHAPE_Area&quot;,FSQ/SK_FS,&quot;OBJECTID,UID,YSDM,SKMC,SKDM,SKBM,XJXZQDM,XJXZQMC,SMMJ,SSJSYDDL,FKSQ,LYMC,LYBM,SJNF,BZ,SHAPE_Length,SHAPE_Area&quot;,FSQ/梁溪区DFFH图斑,&quot;OBJECTID,DLBM,DLMC,HYDANAME,HYDLNAME,GJBJ,HLDM,HPDM,SKDM,HLDJ,Shape_Length,Shape_Area&quot;,FSQ/梁溪区需外业调查图斑,&quot;OBJECTID_1,OBJECTID,UID,YSDM,HLMC,HLDM,HLBM,XJXZQDM,XJXZQMC,SMMJ,SSJSYDDL,HLDJ,FKSQ,LYMC,LYBM,SJNF,BZ,SHAPE_Leng,Shape_Length,Shape_Area&quot;,KT,&quot;OBJECTID,UID,YSDM,KTBM,GTBGDCKTBM,XJXZQDM,XJXZQMC,SMMJ,SSJSYDDL,LYMC,LYBM,SJNF,BZ,SHAPE_Length,SHAPE_Area&quot;,元数据,&quot;OBJECTID,作业员,所属单位,调查开始日期,调查完成日期,问题及处理意见,备注,SHAPE_Length,SHAPE_Area&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="FSQ/HL_FS,&quot;int32,varchar(21),varchar(10),varchar(50),varchar(14),varchar(20),varchar(6),varchar(50),real64,varchar(6),varchar(6),varchar(6),varchar(50),varchar(16),varchar(4),varchar(255),real64,real64,varchar(20)&quot;,FSQ/HP_FS,&quot;int32,varchar(21),varchar(10),varchar(50),varchar(8),varchar(20),varchar(6),varchar(50),real64,varchar(6),varchar(6),varchar(50),varchar(16),varchar(4),varchar(255),real64,real64&quot;,FSQ/SK_FS,&quot;int32,varchar(21),varchar(10),varchar(50),varchar(11),varchar(20),varchar(6),varchar(50),real64,varchar(6),varchar(6),varchar(50),varchar(16),varchar(4),varchar(255),real64,real64&quot;,FSQ/梁溪区DFFH图斑,&quot;int32,varchar(5),varchar(60),varchar(254),varchar(254),varchar(254),varchar(50),varchar(50),varchar(50),varchar(50),real64,real64&quot;,FSQ/梁溪区需外业调查图斑,&quot;int32,int32,varchar(21),varchar(10),varchar(50),varchar(14),varchar(20),varchar(6),varchar(50),real64,varchar(6),varchar(6),varchar(6),varchar(50),varchar(16),varchar(4),varchar(254),real64,real64,real64&quot;,KT,&quot;int32,varchar(21),varchar(10),varchar(20),varchar(20),varchar(6),varchar(50),real64,varchar(6),varchar(50),varchar(16),varchar(4),varchar(255),real64,real64&quot;,元数据,&quot;int32,varchar(16),varchar(64),varchar(10),varchar(10),varchar(255),varchar(255),real64,real64&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%FSQ/HL_FS%FSQ/HP_FS%FSQ/SK_FS%FSQ/梁溪区DFFH图斑%FSQ/梁溪区需外业调查图斑%KT%元数据"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="&lt;u5143&gt;&lt;u6570&gt;&lt;u636e&gt; FSQ&lt;solidus&gt;&lt;u6881&gt;&lt;u6eaa&gt;&lt;u533a&gt;&lt;u9700&gt;&lt;u5916&gt;&lt;u4e1a&gt;&lt;u8c03&gt;&lt;u67e5&gt;&lt;u56fe&gt;&lt;u6591&gt; FSQ&lt;solidus&gt;&lt;u6881&gt;&lt;u6eaa&gt;&lt;u533a&gt;DFFH&lt;u56fe&gt;&lt;u6591&gt; FSQ&lt;solidus&gt;HL_FS FSQ&lt;solidus&gt;HP_FS FSQ&lt;solidus&gt;SK_FS KT"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="FSQ/HL_FS,,FSQ/HP_FS,,FSQ/SK_FS,,FSQ/梁溪区DFFH图斑,,FSQ/梁溪区需外业调查图斑,,KT,,元数据,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,GEODATABASE_FILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="GEODATABASE_FILE_GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS GEODATABASE_FILE%Source&quot;,GEODATABASE_FILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,GEODATABASE_FILE_IGNORE_RELATIONSHIP_INFO,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Relationship&lt;space&gt;Info,GEODATABASE_FILE_CHECK_SIMPLE_GEOM,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Check&lt;space&gt;for&lt;space&gt;Simple&lt;space&gt;Geometry:,GEODATABASE_FILE_SIMPLE_DONUT_GEOMETRY,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,yes%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,no&quot;,GEODATABASE_FILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,GEODATABASE_FILE_READ_THREE_POINT_ARCS,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Read&lt;space&gt;as&lt;space&gt;Three&lt;space&gt;Point&lt;space&gt;Arcs:,GEODATABASE_FILE_STRIP_GUID_GLOBALID_BRACES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Strip&lt;space&gt;braces&lt;space&gt;off&lt;space&gt;GlobalID&lt;space&gt;and&lt;space&gt;GUID:,GEODATABASE_FILE_FEATURE_READ_MODE,&quot;OPTIONAL CHOICE Features%Metadata&quot;,GEODATABASE_FILE&lt;space&gt;Feature&lt;space&gt;Read&lt;space&gt;Mode:,GEODATABASE_FILE_GEOMETRY,&quot;OPTIONAL DISCLOSUREGROUP SIMPLE_DONUT_GEOMETRY&quot;,GEODATABASE_FILE&lt;space&gt;Geometry,GEODATABASE_FILE_CACHE_MULTIPATCH_TEXTURES,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Cache&lt;space&gt;Multipatch&lt;space&gt;Textures:,GEODATABASE_FILE_IGNORE_NETWORK_INFO,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Ignore&lt;space&gt;Network&lt;space&gt;Info,GEODATABASE_FILE_WHERE_WWJD,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED Mode,WHERE&quot;,GEODATABASE_FILE&lt;space&gt;WHERE&lt;space&gt;Clause:,GEODATABASE_FILE_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_MERGE_FEAT_LINKED_ANNOS,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Merge&lt;space&gt;Feature&lt;space&gt;Linked&lt;space&gt;Annotations:,GEODATABASE_FILE_REMOVE_FEATURE_DATASET,&quot;OPTIONAL CHECKBOX YES%NO&quot;,GEODATABASE_FILE&lt;space&gt;Remove&lt;space&gt;Feature&lt;space&gt;Dataset,GEODATABASE_FILE_GEODB_SHARED_RDR_ADV_PARM_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SPLIT_COMPLEX_ANNOS%CACHE_MULTIPATCH_TEXTURES%CHECK_SIMPLE_GEOM%STRIP_GUID_GLOBALID_BRACES%MERGE_FEAT_LINKED_ANNOS%READ_THREE_POINT_ARCS%BEGIN_SQL{0}%END_SQL{0}%GEOMETRY&quot;,GEODATABASE_FILE&lt;space&gt;Advanced,GEODATABASE_FILE_BEGIN_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;Before&lt;space&gt;Read,GEODATABASE_FILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,GEODATABASE_FILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,GEODATABASE_FILE_ALIAS_MODE,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%Replace&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;With&lt;space&gt;Aliases,SCHEMA%Expose&lt;space&gt;Aliases&lt;space&gt;as&lt;space&gt;Metadata&lt;space&gt;Attributes&lt;space&gt;&lt;openparen&gt;&lt;lt&gt;name&lt;gt&gt;_alias&lt;closeparen&gt;,ON_DATA_FEATURES&quot;,GEODATABASE_FILE&lt;space&gt;Alias&lt;space&gt;Mode:,GEODATABASE_FILE_DISABLE_FEATURE_DATASET_ATTRIBUTE,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_RESOLVE_DOMAINS,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Resolve&lt;space&gt;Domains,GEODATABASE_FILE_CREATE_FEATURE_TABLES_FROM_DATA,&quot;OPTIONAL NO_EDIT TEXT&quot;,GEODATABASE_FILE&lt;space&gt;,GEODATABASE_FILE_END_SQL{0},&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,GEODATABASE_FILE&quot;,GEODATABASE_FILE&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;After&lt;space&gt;Read,GEODATABASE_FILE_TABLELIST,&quot;IGNORE TEXT&quot;,GEODATABASE_FILE&lt;space&gt;Tables:,GEODATABASE_FILE_RESOLVE_SUBTYPE_NAMES,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Resolve&lt;space&gt;Subtypes,GEODATABASE_FILE_SPLIT_COMPLEX_ANNOS,&quot;OPTIONAL CHOICE yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Split&lt;space&gt;Complex&lt;space&gt;Annotations:,GEODATABASE_FILE_SPLIT_MULTI_PART_ANNOS,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Split&lt;space&gt;Multi-Part&lt;space&gt;Annotations,GEODATABASE_FILE_TRANSLATE_SPATIAL_DATA_ONLY,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Spatial&lt;space&gt;Data&lt;space&gt;Only,GEODATABASE_FILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS%ALIAS_MODE%REMOVE_MAIN_PREFIX&quot;,GEODATABASE_FILE&lt;space&gt;Schema&lt;space&gt;Attributes,GEODATABASE_FILE_SPLIT_COMPLEX_EDGES,&quot;OPTIONAL CHECKBOX yes%no&quot;,GEODATABASE_FILE&lt;space&gt;Split&lt;space&gt;Complex&lt;space&gt;Edges,GEODATABASE_FILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,GEODATABASE_FILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_ALIAS_MODE" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_BEGIN_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_CACHE_MULTIPATCH_TEXTURES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_CHECK_SIMPLE_GEOM" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_CREATE_FEATURE_TABLES_FROM_DATA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_DISABLE_FEATURE_DATASET_ATTRIBUTE" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_END_SQL{0}" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_FEATURE_READ_MODE" PARM_VALUE="Features"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEODB_SHARED_RDR_ADV_PARM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_GEOMETRY" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_NETWORK_INFO" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_IGNORE_RELATIONSHIP_INFO" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_MERGE_FEAT_LINKED_ANNOS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_READ_THREE_POINT_ARCS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_REMOVE_FEATURE_DATASET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_RESOLVE_DOMAINS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_RESOLVE_SUBTYPE_NAMES" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SIMPLE_DONUT_GEOMETRY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SPLIT_COMPLEX_ANNOS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SPLIT_COMPLEX_EDGES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_SPLIT_MULTI_PART_ANNOS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_STRIP_GUID_GLOBALID_BRACES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TABLELIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_TRANSLATE_SPATIAL_DATA_ONLY" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GEODATABASE_FILE_WHERE_WWJD" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="4828.1732817328148 -427.00503577484506"
#!   BOUNDING_RECT="4828.1732817328148 -427.00503577484506 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; tran_x SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;1&lt;solidus&gt;&lt;at&gt;sqrt&lt;openparen&gt;2&lt;closeparen&gt;*&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  tran_y SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;1&lt;solidus&gt;&lt;at&gt;sqrt&lt;openparen&gt;2&lt;closeparen&gt;*&lt;openparen&gt;-&lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="21"
#!   TYPE="GeometryRemover"
#!   VERSION="0"
#!   POSITION="2934.4043440434398 -218.12778127781246"
#!   BOUNDING_RECT="2934.4043440434398 -218.12778127781246 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_COORDSYS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryRemover"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="26"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="5459.4295942959434 -378.75318753187503"
#!   BOUNDING_RECT="5459.4295942959434 -378.75318753187503 430 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="x NATURAL ASCENDING tran_y NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="28"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="6790.6929069290682 -520.75597328422009"
#!   BOUNDING_RECT="6790.6929069290682 -520.75597328422009 430 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="35"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="1063.139127394692 -312.50312503125019"
#!   BOUNDING_RECT="1063.139127394692 -312.50312503125019 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="37"
#!   TYPE="Junction"
#!   VERSION="0"
#!   POSITION="-161.99924999249868 -559.38059380593813"
#!   BOUNDING_RECT="-161.99924999249868 -559.38059380593813 61 61"
#!   ORDER="500000000000015"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Junction"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="43"
#!   TYPE="FeatureJoiner"
#!   VERSION="1001"
#!   POSITION="8130.1251609007304 -970.62674135513248"
#!   BOUNDING_RECT="8130.1251609007304 -970.62674135513248 430 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="JOINED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNJOINED_LEFT"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="UNJOINED_RIGHT"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Prefer Left"/>
#!     <XFORM_PARM PARM_NAME="GEOMETRY_HANDLING" PARM_VALUE="Prefer Left"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="JOIN_KEYS" PARM_VALUE="_count _count AUTO"/>
#!     <XFORM_PARM PARM_NAME="JOIN_MODE" PARM_VALUE="Inner"/>
#!     <XFORM_PARM PARM_NAME="JOIN_MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="JOIN_ON_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureJoiner"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="48"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="8853.3231823546339 -1270.6267413551325"
#!   BOUNDING_RECT="8853.3231823546339 -1270.6267413551325 430 71"
#!   ORDER="500000000000019"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u987a&gt;&lt;u5e8f&gt;&lt;u7801&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt; int64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="50"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="13524.606422834648 -2316.4625967984207"
#!   BOUNDING_RECT="13524.606422834648 -2316.4625967984207 430 71"
#!   ORDER="500000000000020"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER)&lt;backslash&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;u540e&gt;.gdb"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_BEGIN_SQL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE_TYPE"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_ENABLE_LOAD_ONLY_MODE" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_END_SQL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_FEATURE_DATASET_HANDLING" PARM_VALUE="WRITE"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_OVERWRITE_GEODB" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_WRITE_UNZONED_UTC" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="FILEGDB"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_GEODB&lt;comma&gt;NO&lt;comma&gt;DATASET_TEMPLATE&lt;comma&gt;&lt;comma&gt;WRITE_UNZONED_UTC&lt;comma&gt;No&lt;comma&gt;FEATURE_DATASET_HANDLING&lt;comma&gt;WRITE&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE_TYPE&lt;comma&gt;ADVANCED&lt;comma&gt;&lt;comma&gt;BEGIN_SQL&lt;comma&gt;&lt;comma&gt;END_SQL&lt;comma&gt;&lt;comma&gt;ENABLE_LOAD_ONLY_MODE&lt;comma&gt;YES,METAFILE,FILEGDB"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="FILEGDB_BEGIN_SQL,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,FILEGDB&quot;,FILEGDB&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;Before&lt;space&gt;Write,FILEGDB_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,FILEGDB&lt;space&gt;,FILEGDB_FEATURE_DATASET_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Write&lt;space&gt;Feature&lt;space&gt;Dataset,WRITE%Warn&lt;space&gt;and&lt;space&gt;Ignore&lt;space&gt;Feature&lt;space&gt;Dataset,IGNORE%Error&lt;space&gt;and&lt;space&gt;End&lt;space&gt;Translation,END&quot;,FILEGDB&lt;space&gt;Feature&lt;space&gt;Dataset&lt;space&gt;Handling:,FILEGDB_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,FILEGDB&lt;space&gt;,FILEGDB_ENABLE_LOAD_ONLY_MODE,&quot;OPTIONAL ACTIVECHECK YES%NO&quot;,FILEGDB&lt;space&gt;Enable&lt;space&gt;Load&lt;space&gt;Only&lt;space&gt;Mode:,FILEGDB_WRITE_UNZONED_UTC,&quot;OPTIONAL NO_EDIT TEXT&quot;,FILEGDB&lt;space&gt;,FILEGDB_OVERWRITE_GEODB,&quot;OPTIONAL ACTIVECHECK YES%NO,DATASET_TEMPLATE&quot;,FILEGDB&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;Geodatabase:,FILEGDB_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP BEGIN_SQL%END_SQL%ENABLE_LOAD_ONLY_MODE&quot;,FILEGDB&lt;space&gt;Advanced,FILEGDB_END_SQL,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,FILEGDB&quot;,FILEGDB&lt;space&gt;SQL&lt;space&gt;To&lt;space&gt;Run&lt;space&gt;After&lt;space&gt;Write"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;:Output,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;,ftp_writer,FILEGDB,ftp_geometry,geodb_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u987a&lt;gt&gt;&lt;lt&gt;u5e8f&lt;gt&gt;&lt;lt&gt;u7801&lt;gt&gt;&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;UID&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;21&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;YSDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;HLMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;HLDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;14&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;HLBM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SMMJ&lt;comma&gt;double&lt;comma&gt;SSJSYDDL&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;HLDJ&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;FKSQ&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LYMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LYBM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;16&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SJNF&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;4&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;BZ&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6302&lt;gt&gt;&lt;lt&gt;u63a5&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SHAPE_Length&lt;comma&gt;double&lt;comma&gt;SHAPE_Area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;filegdb_object_id_field&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_object_id_alias&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_shape_field&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_shape_alias&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_config_keyword&lt;comma&gt;DEFAULTS&lt;comma&gt;filegdb_xy_tolerance&lt;comma&gt;&lt;comma&gt;filegdb_z_tolerance&lt;comma&gt;0.001&lt;comma&gt;filegdb_m_tolerance&lt;comma&gt;0.001;&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;:Output00,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;,ftp_writer,FILEGDB,ftp_geometry,geodb_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u987a&lt;gt&gt;&lt;lt&gt;u5e8f&lt;gt&gt;&lt;lt&gt;u7801&lt;gt&gt;&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;UID&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;21&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;YSDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;HPMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;HPDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;8&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;HPBM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SMMJ&lt;comma&gt;double&lt;comma&gt;SSJSYDDL&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;FKSQ&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LYMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LYBM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;16&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SJNF&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;4&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;BZ&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6302&lt;gt&gt;&lt;lt&gt;u63a5&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SHAPE_Length&lt;comma&gt;double&lt;comma&gt;SHAPE_Area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;filegdb_object_id_field&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_object_id_alias&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_shape_field&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_shape_alias&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_config_keyword&lt;comma&gt;DEFAULTS&lt;comma&gt;filegdb_xy_tolerance&lt;comma&gt;&lt;comma&gt;filegdb_z_tolerance&lt;comma&gt;0.001&lt;comma&gt;filegdb_m_tolerance&lt;comma&gt;0.001;&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;:Output01,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;,ftp_writer,FILEGDB,ftp_geometry,geodb_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u987a&lt;gt&gt;&lt;lt&gt;u5e8f&lt;gt&gt;&lt;lt&gt;u7801&lt;gt&gt;&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;UID&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;21&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;YSDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;KTBM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;GTBGDCKTBM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQDM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XJXZQMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SMMJ&lt;comma&gt;double&lt;comma&gt;SSJSYDDL&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;6&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LYMC&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;LYBM&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;16&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SJNF&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;4&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;BZ&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6302&lt;gt&gt;&lt;lt&gt;u63a5&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;text&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SHAPE_Length&lt;comma&gt;double&lt;comma&gt;SHAPE_Area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,fme_configuration_group&lt;comma&gt;&lt;comma&gt;fme_configuration_common_group&lt;comma&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_update_geometry&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_selection_group&lt;comma&gt;&lt;comma&gt;fme_selection_method&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;fme_table_creation_group&lt;comma&gt;&lt;comma&gt;filegdb_object_id_field&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_object_id_alias&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_shape_field&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_shape_alias&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_config_keyword&lt;comma&gt;DEFAULTS&lt;comma&gt;filegdb_xy_tolerance&lt;comma&gt;&lt;comma&gt;filegdb_z_tolerance&lt;comma&gt;0.001&lt;comma&gt;filegdb_m_tolerance&lt;comma&gt;0.001"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,&lt;backslash&gt;&lt;backslash&gt;&lt;quote&gt;:&lt;dollar&gt;?*&lt;closeparen&gt;&lt;openparen&gt;&lt;lt&gt;&lt;gt&gt;|&lt;openbracket&gt;%#&lt;space&gt;&lt;apos&gt;&lt;amp&gt;+-&lt;closebracket&gt;.^&lt;backslash&gt;&lt;opencurly&gt;&lt;backslash&gt;&lt;closecurly&gt;~&lt;comma&gt;&lt;solidus&gt;,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,text&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;text&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;text&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;text&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;text&lt;openparen&gt;2048&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;blob&lt;comma&gt;fme_binarybuffer&lt;comma&gt;blob&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;blob&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;date&lt;comma&gt;fme_datetime&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;date&lt;comma&gt;fme_time&lt;comma&gt;single&lt;comma&gt;fme_real32&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;int&lt;comma&gt;fme_int32&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;text&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_int64&lt;comma&gt;text&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;smallint&lt;comma&gt;fme_int16&lt;comma&gt;int&lt;comma&gt;fme_uint16&lt;comma&gt;smallint&lt;comma&gt;fme_int8&lt;comma&gt;smallint&lt;comma&gt;fme_uint8&lt;comma&gt;smallint&lt;comma&gt;fme_boolean&lt;comma&gt;objectid&lt;comma&gt;fme_int32&lt;comma&gt;guid&lt;comma&gt;fme_buffer&lt;comma&gt;globalid&lt;comma&gt;fme_buffer,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,&lt;backslash&gt;&lt;backslash&gt;&lt;quote&gt;:&lt;dollar&gt;?*&lt;closeparen&gt;&lt;openparen&gt;&lt;lt&gt;&lt;gt&gt;|&lt;openbracket&gt;%#&lt;space&gt;&lt;apos&gt;&lt;amp&gt;+-&lt;closebracket&gt;.^&lt;backslash&gt;&lt;opencurly&gt;&lt;backslash&gt;&lt;closecurly&gt;~&lt;comma&gt;,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;FILEGDB.fmf,FORMAT_NAME,FILEGDB,GEOM_MAP,geodb_point&lt;comma&gt;fme_point&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_line&lt;comma&gt;geodb_multipoint&lt;comma&gt;fme_point&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;geodb_point&lt;comma&gt;fme_text&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_arc&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;geodb_no_geom&lt;comma&gt;fme_no_geom&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_raster&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_surface&lt;comma&gt;geodb_polyline&lt;comma&gt;fme_solid&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;geodb_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;geodb_no_geom&lt;comma&gt;fme_feature_table&lt;comma&gt;geodb_no_geom&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,FILEGDB,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_group&lt;space&gt;fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;fme_configuration_common_group&lt;space&gt;fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group&lt;space&gt;General&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_feature_operation&lt;space&gt;Insert&lt;comma&gt;INSERT&lt;comma&gt;fme_update_geometry&lt;comma&gt;fme_selection_group&lt;comma&gt;mie_pack%Update&lt;comma&gt;UPDATE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert&lt;comma&gt;UPSERT&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN%Delete&lt;comma&gt;DELETE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;fme_update_geometry&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;comma&gt;fme_spatial_group&lt;comma&gt;fme_advanced_group&lt;comma&gt;oracle_sequenced_cols%&lt;lt&gt;at&lt;gt&gt;Value&lt;lt&gt;openparen&lt;gt&gt;fme_db_operation&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;MULTIPLE&lt;comma&gt;++fme_table_handling+USE_EXISTING&lt;comma&gt;++fme_selection_group+FME_DISCLOSURE_OPEN&lt;space&gt;Feature&lt;space&gt;Operation&lt;quote&gt;&lt;comma&gt;INSERT&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;fme_table_handling&lt;space&gt;Use&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;USE_EXISTING&lt;comma&gt;fme_table_creation_group%Create&lt;lt&gt;space&lt;gt&gt;If&lt;lt&gt;space&lt;gt&gt;Needed&lt;comma&gt;CREATE_IF_MISSING%Drop&lt;lt&gt;space&lt;gt&gt;and&lt;lt&gt;space&lt;gt&gt;Create&lt;comma&gt;DROP_CREATE%Truncate&lt;lt&gt;space&lt;gt&gt;Existing&lt;comma&gt;TRUNCATE_EXISTING&lt;comma&gt;fme_table_creation_group&lt;space&gt;Table&lt;space&gt;Handling&lt;quote&gt;&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;fme_update_geometry&lt;space&gt;Yes&lt;comma&gt;YES%No&lt;comma&gt;NO&lt;space&gt;Update&lt;space&gt;Spatial&lt;space&gt;Column&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;YES&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_selection_group&lt;space&gt;fme_selection_method&lt;space&gt;Row&lt;space&gt;Selection&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;RADIOPARAMETERGROUP&lt;space&gt;fme_selection_method&lt;space&gt;fme_match_columns&lt;comma&gt;MATCH_COLUMNS%fme_where_builder_clause&lt;comma&gt;BUILDER&lt;space&gt;Row&lt;space&gt;Selection&lt;space&gt;Method&lt;quote&gt;&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;ATTRLIST_COMMAS&lt;space&gt;fme_match_columns&lt;space&gt;Match&lt;space&gt;Columns&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;WHOLE_LINE&lt;space&gt;TEXT_EDIT_SQL_CFG_OR_ATTR&lt;space&gt;fme_where_builder_clause&lt;space&gt;MODE&lt;comma&gt;WHERE&lt;space&gt;WHERE&lt;space&gt;Clause&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;fme_table_creation_group&lt;space&gt;filegdb_object_id_field%filegdb_object_id_alias%filegdb_shape_field%filegdb_shape_alias%filegdb_config_keyword%filegdb_xy_tolerance%filegdb_z_tolerance%filegdb_m_tolerance&lt;space&gt;Table&lt;space&gt;Creation&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;filegdb_object_id_field&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;filegdb_object_id_alias&lt;space&gt;Object&lt;space&gt;ID&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;OBJECTID&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;filegdb_shape_field&lt;space&gt;Shape&lt;space&gt;Field&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;filegdb_shape_alias&lt;space&gt;Shape&lt;space&gt;Alias&lt;quote&gt;&lt;comma&gt;SHAPE&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;TEXT&lt;space&gt;filegdb_config_keyword&lt;space&gt;Configuration&lt;space&gt;Keyword&lt;quote&gt;&lt;comma&gt;DEFAULTS&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;filegdb_xy_tolerance&lt;space&gt;XY&lt;space&gt;Tolerance&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;FLOAT&lt;space&gt;filegdb_z_tolerance&lt;space&gt;Z&lt;space&gt;Tolerance&lt;quote&gt;&lt;comma&gt;0.001&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;FLOAT&lt;space&gt;filegdb_m_tolerance&lt;space&gt;M&lt;space&gt;Tolerance&lt;quote&gt;&lt;comma&gt;0.001,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;filegdb_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;filegdb_drop_table&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;filegdb_truncate_table&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_feature_operation&lt;comma&gt;INSERT&lt;comma&gt;fme_table_handling&lt;comma&gt;CREATE_IF_MISSING&lt;comma&gt;fme_selection_method&lt;comma&gt;MATCH_COLUMNS&lt;comma&gt;fme_match_columns&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_where_builder_clause&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;fme_update_geometry&lt;comma&gt;YES&lt;comma&gt;filegdb_object_id_field&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_object_id_alias&lt;comma&gt;OBJECTID&lt;comma&gt;filegdb_shape_field&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_shape_alias&lt;comma&gt;SHAPE&lt;comma&gt;filegdb_config_keyword&lt;comma&gt;DEFAULTS&lt;comma&gt;filegdb_xy_tolerance&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;filegdb_z_tolerance&lt;comma&gt;0.001&lt;comma&gt;filegdb_m_tolerance&lt;comma&gt;0.001,WRITER_FORMAT_PARAMETER,FEATURE_TYPE_NAME&lt;comma&gt;&lt;quote&gt;Feature&lt;space&gt;Class&lt;space&gt;or&lt;space&gt;Table&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;FeatureClass1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;File&lt;space&gt;Geodatabase&lt;space&gt;folder&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;File&lt;space&gt;Geodatabase&lt;space&gt;folder&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;ATTRIBUTE_READING_HISTORIC&lt;comma&gt;ALL&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;FILEGDB_IN_BEGIN_SQL&lt;space&gt;FILEGDB_IN_END_SQL&lt;space&gt;FILEGDB_OUT_BEGIN_SQL&lt;space&gt;FILEGDB_OUT_END_SQL&lt;space&gt;FILEGDB_OUT_ENABLE_LOAD_ONLY_MODE&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="44"
#!   TYPE="AttributeRemover"
#!   VERSION="1"
#!   POSITION="10335.145741779988 -1183.9710513182722"
#!   BOUNDING_RECT="10335.145741779988 -1183.9710513182722 430 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_ATTRS" PARM_VALUE="_count,&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;,&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeRemover"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="51"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="4206.2920629206283 -318.75318753187503"
#!   BOUNDING_RECT="4206.2920629206283 -318.75318753187503 430 71"
#!   ORDER="500000000000021"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="x,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,;y,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="42"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="6590.6909069090671 -737.50737507375038"
#!   BOUNDING_RECT="6590.6909069090671 -737.50737507375038 430 71"
#!   ORDER="500000000000024"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="_count,&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="33"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="1488.1421248453196 -559.38059380593813"
#!   BOUNDING_RECT="1488.1421248453196 -559.38059380593813 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="41"
#!   TYPE="Chopper"
#!   VERSION="8"
#!   POSITION="1796.8929689296888 -158.12778127781246"
#!   BOUNDING_RECT="1796.8929689296888 -158.12778127781246 430 71"
#!   ORDER="500000000000026"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CHOPPED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNTOUCHED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="APPROX_LENGTH" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Deaggregate"/>
#!     <XFORM_PARM PARM_NAME="GRID_AREAS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_VERTICES" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="By Vertex"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="REMNANT_ATTR" PARM_VALUE="_remnant"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Chopper"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="46"
#!   TYPE="CoordinateExtractor"
#!   VERSION="4"
#!   POSITION="2381.2738127381272 -158.12778127781246"
#!   BOUNDING_RECT="2381.2738127381272 -158.12778127781246 430 71"
#!   ORDER="500000000000027"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ALL_ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IND" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTRIBUTES_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_indices"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="All Coordinates"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="CoordinateExtractor"/>
#!     <XFORM_PARM PARM_NAME="X_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Y_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_DEFAULT" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="57"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="3553.1605316053146 -190.00250002499996"
#!   BOUNDING_RECT="3553.1605316053146 -190.00250002499996 430 71"
#!   ORDER="500000000000028"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="_indices{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="53"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="6175.0617506175067 -427.00503577484506"
#!   BOUNDING_RECT="6175.0617506175067 -427.00503577484506 430 71"
#!   ORDER="500000000000029"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="68"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-2759.4025940259417 -668.7566875668756"
#!   BOUNDING_RECT="-2759.4025940259417 -668.7566875668756 430 71"
#!   ORDER="500000000000030"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="71"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-1912.5191251912522 -724.3828438284379"
#!   BOUNDING_RECT="-1912.5191251912522 -724.3828438284379 430 71"
#!   ORDER="500000000000031"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = gdb"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="70"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="7128.1962819628197 -809.38309383093838"
#!   BOUNDING_RECT="7128.1962819628197 -809.38309383093838 430 71"
#!   ORDER="500000000000032"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;PadLeft&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt;&lt;comma&gt;3&lt;comma&gt;0&lt;closeparen&gt; int64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="59"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="350.0035000350004 -451.88021880218798"
#!   BOUNDING_RECT="350.0035000350004 -451.88021880218798 430 71"
#!   ORDER="500000000000033"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="78"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="9613.************ -1183.9710513182722"
#!   BOUNDING_RECT="9613.************ -1183.9710513182722 430 71"
#!   ORDER="500000000000034"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt; NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="60"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="5742.2751048897444 -1603.2806560846477"
#!   BOUNDING_RECT="5742.2751048897444 -1603.2806560846477 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; tran_x SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;1&lt;solidus&gt;&lt;at&gt;sqrt&lt;openparen&gt;2&lt;closeparen&gt;*&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  tran_y SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;1&lt;solidus&gt;&lt;at&gt;sqrt&lt;openparen&gt;2&lt;closeparen&gt;*&lt;openparen&gt;-&lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="61"
#!   TYPE="GeometryRemover"
#!   VERSION="0"
#!   POSITION="3848.5061672003694 -1394.************"
#!   BOUNDING_RECT="3848.5061672003694 -1394.************ 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_COORDSYS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryRemover_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="81"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="6373.************ -1555.0288078416777"
#!   BOUNDING_RECT="6373.************ -1555.0288078416777 430 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="x NATURAL ASCENDING tran_y NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="82"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="7704.7947300859978 -1697.0315935940228"
#!   BOUNDING_RECT="7704.7947300859978 -1697.0315935940228 430 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="83"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="1977.2409505516216 -1488.7787453410529"
#!   BOUNDING_RECT="1977.2409505516216 -1488.7787453410529 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="84"
#!   TYPE="FeatureJoiner"
#!   VERSION="1001"
#!   POSITION="9044.2269840576591 -2146.9023616649351"
#!   BOUNDING_RECT="9044.2269840576591 -2146.9023616649351 430 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="JOINED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNJOINED_LEFT"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="UNJOINED_RIGHT"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Prefer Left"/>
#!     <XFORM_PARM PARM_NAME="GEOMETRY_HANDLING" PARM_VALUE="Prefer Left"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="JOIN_KEYS" PARM_VALUE="_count _count AUTO"/>
#!     <XFORM_PARM PARM_NAME="JOIN_MODE" PARM_VALUE="Inner"/>
#!     <XFORM_PARM PARM_NAME="JOIN_MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="JOIN_ON_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureJoiner_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="85"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="9767.4250055115626 -2446.9023616649351"
#!   BOUNDING_RECT="9767.4250055115626 -2446.9023616649351 430 71"
#!   ORDER="500000000000019"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u987a&gt;&lt;u5e8f&gt;&lt;u7801&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt; int64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="86"
#!   TYPE="AttributeRemover"
#!   VERSION="1"
#!   POSITION="11249.************ -2360.2466716280751"
#!   BOUNDING_RECT="11249.************ -2360.2466716280751 430 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_ATTRS" PARM_VALUE="_count,&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;,&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeRemover_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="87"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="5120.3938860775579 -1495.0288078416777"
#!   BOUNDING_RECT="5120.3938860775579 -1495.0288078416777 430 71"
#!   ORDER="500000000000021"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="x,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,;y,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="88"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="7504.7927300659967 -1913.782995383553"
#!   BOUNDING_RECT="7504.7927300659967 -1913.782995383553 430 71"
#!   ORDER="500000000000024"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="_count,&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="89"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="2402.2439480022495 -1735.6562141157408"
#!   BOUNDING_RECT="2402.2439480022495 -1735.6562141157408 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="90"
#!   TYPE="Chopper"
#!   VERSION="8"
#!   POSITION="2710.9947920866184 -1334.************"
#!   BOUNDING_RECT="2710.9947920866184 -1334.************ 430 71"
#!   ORDER="500000000000026"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CHOPPED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNTOUCHED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="APPROX_LENGTH" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Deaggregate"/>
#!     <XFORM_PARM PARM_NAME="GRID_AREAS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_VERTICES" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="By Vertex"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="REMNANT_ATTR" PARM_VALUE="_remnant"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Chopper_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="91"
#!   TYPE="CoordinateExtractor"
#!   VERSION="4"
#!   POSITION="3295.3756358950568 -1334.************"
#!   BOUNDING_RECT="3295.3756358950568 -1334.************ 430 71"
#!   ORDER="500000000000027"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ALL_ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IND" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTRIBUTES_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_indices"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="All Coordinates"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="CoordinateExtractor_2"/>
#!     <XFORM_PARM PARM_NAME="X_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Y_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_DEFAULT" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="92"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="4467.2623547622443 -1366.2781203348027"
#!   BOUNDING_RECT="4467.2623547622443 -1366.2781203348027 430 71"
#!   ORDER="500000000000028"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="_indices{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="93"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="7089.1635737744364 -1603.2806560846477"
#!   BOUNDING_RECT="7089.1635737744364 -1603.2806560846477 430 71"
#!   ORDER="500000000000029"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="94"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="8042.2981051197494 -1985.658714140741"
#!   BOUNDING_RECT="8042.2981051197494 -1985.658714140741 430 71"
#!   ORDER="500000000000032"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;PadLeft&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt;&lt;comma&gt;3&lt;comma&gt;0&lt;closeparen&gt; int64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="95"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="1264.10532319193 -1628.1558391119906"
#!   BOUNDING_RECT="1264.10532319193 -1628.1558391119906 430 71"
#!   ORDER="500000000000033"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="96"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="10527.************ -2360.2466716280751"
#!   BOUNDING_RECT="10527.************ -2360.2466716280751 430 71"
#!   ORDER="500000000000034"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt; NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="116"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="5992.2776049147442 -2637.3819061880577"
#!   BOUNDING_RECT="5992.2776049147442 -2637.3819061880577 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; tran_x SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;1&lt;solidus&gt;&lt;at&gt;sqrt&lt;openparen&gt;2&lt;closeparen&gt;*&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  tran_y SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;1&lt;solidus&gt;&lt;at&gt;sqrt&lt;openparen&gt;2&lt;closeparen&gt;*&lt;openparen&gt;-&lt;at&gt;Value&lt;openparen&gt;x&lt;closeparen&gt;+&lt;at&gt;Value&lt;openparen&gt;y&lt;closeparen&gt;&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="117"
#!   TYPE="GeometryRemover"
#!   VERSION="0"
#!   POSITION="4098.5086672253692 -2428.5046516910252"
#!   BOUNDING_RECT="4098.5086672253692 -2428.5046516910252 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_COORDSYS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryRemover_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="118"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="6623.5339174778728 -2589.1300579450876"
#!   BOUNDING_RECT="6623.5339174778728 -2589.1300579450876 430 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="x NATURAL ASCENDING tran_y NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="119"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="7954.7972301109967 -2731.1328436974327"
#!   BOUNDING_RECT="7954.7972301109967 -2731.1328436974327 430 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="fme_feature_type"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="120"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="2227.2434505766214 -2522.************"
#!   BOUNDING_RECT="2227.2434505766214 -2522.************ 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="121"
#!   TYPE="FeatureJoiner"
#!   VERSION="1001"
#!   POSITION="9294.************ -3181.0036117683449"
#!   BOUNDING_RECT="9294.************ -3181.0036117683449 430 71"
#!   ORDER="500000000000017"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="JOINED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNJOINED_LEFT"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="UNJOINED_RIGHT"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Prefer Left"/>
#!     <XFORM_PARM PARM_NAME="GEOMETRY_HANDLING" PARM_VALUE="Prefer Left"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="JOIN_KEYS" PARM_VALUE="_count _count AUTO"/>
#!     <XFORM_PARM PARM_NAME="JOIN_MODE" PARM_VALUE="Inner"/>
#!     <XFORM_PARM PARM_NAME="JOIN_MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="JOIN_ON_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureJoiner_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="122"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="10017.************ -3481.0036117683449"
#!   BOUNDING_RECT="10017.************ -3481.0036117683449 430 71"
#!   ORDER="500000000000019"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u987a&gt;&lt;u5e8f&gt;&lt;u7801&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt; int64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_8"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="123"
#!   TYPE="AttributeRemover"
#!   VERSION="1"
#!   POSITION="11499.************ -3394.3479217314848"
#!   BOUNDING_RECT="11499.************ -3394.3479217314848 430 71"
#!   ORDER="500000000000018"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_ATTRS" PARM_VALUE="_count,&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;,&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeRemover_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="124"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="5370.3963861025577 -2529.1300579450876"
#!   BOUNDING_RECT="5370.3963861025577 -2529.1300579450876 430 71"
#!   ORDER="500000000000021"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="x,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,;y,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="125"
#!   TYPE="AttributeKeeper"
#!   VERSION="3"
#!   POSITION="7754.7952300909965 -2947.8842454869628"
#!   BOUNDING_RECT="7754.7952300909965 -2947.8842454869628 430 71"
#!   ORDER="500000000000024"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="CREATE_BULK_MODE_FEATURES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="KEEP_ATTRS" PARM_VALUE="_count,&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="KEEP_LIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ON_ATTRIBUTE_CHANGE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeKeeper_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="126"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="2652.2464480272492 -2769.7574642191507"
#!   BOUNDING_RECT="2652.2464480272492 -2769.7574642191507 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_8"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="127"
#!   TYPE="Chopper"
#!   VERSION="8"
#!   POSITION="2960.9972921116182 -2368.5046516910252"
#!   BOUNDING_RECT="2960.9972921116182 -2368.5046516910252 430 71"
#!   ORDER="500000000000026"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CHOPPED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="UNTOUCHED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="APPROX_LENGTH" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Deaggregate"/>
#!     <XFORM_PARM PARM_NAME="GRID_AREAS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_VERTICES" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="By Vertex"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="REMNANT_ATTR" PARM_VALUE="_remnant"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Chopper_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="128"
#!   TYPE="CoordinateExtractor"
#!   VERSION="4"
#!   POSITION="3545.3781359200566 -2368.5046516910252"
#!   BOUNDING_RECT="3545.3781359200566 -2368.5046516910252 430 71"
#!   ORDER="500000000000027"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_indices{}.z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ALL_ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="IND" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTRIBUTES_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_indices"/>
#!     <XFORM_PARM PARM_NAME="MODE" PARM_VALUE="All Coordinates"/>
#!     <XFORM_PARM PARM_NAME="MODE_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="CoordinateExtractor_3"/>
#!     <XFORM_PARM PARM_NAME="X_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Y_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_ATTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="Z_DEFAULT" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="129"
#!   TYPE="ListExploder"
#!   VERSION="6"
#!   POSITION="4717.2648547872441 -2400.3793704382124"
#!   BOUNDING_RECT="4717.2648547872441 -2400.3793704382124 430 71"
#!   ORDER="500000000000028"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ELEMENTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge List Attributes"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use List Attribute Values"/>
#!     <XFORM_PARM PARM_NAME="INCOMING_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="INDEX_ATTR" PARM_VALUE="_element_index"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTR" PARM_VALUE="_indices{}"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ListExploder_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="130"
#!   TYPE="DuplicateFilter"
#!   VERSION="5"
#!   POSITION="7339.1660737994362 -2637.3819061880577"
#!   BOUNDING_RECT="7339.1660737994362 -2637.3819061880577 430 71"
#!   ORDER="500000000000029"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="UNIQUE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="DUPLICATE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="tran_x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="tran_y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_remnant" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="z" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_element_index" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="x.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.min" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="y.max" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INPUT_ORDERED_CHOICE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="KEYATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="DuplicateFilter_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="131"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="8292.3006051447483 -3019.759964244151"
#!   BOUNDING_RECT="8292.3006051447483 -3019.759964244151 430 71"
#!   ORDER="500000000000032"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;PadLeft&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u987a&gt;&lt;u5e8f&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;closeparen&gt;&lt;comma&gt;3&lt;comma&gt;0&lt;closeparen&gt; int64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_9"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="132"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="1514.1078232169298 -2662.2570892154008"
#!   BOUNDING_RECT="1514.1078232169298 -2662.2570892154008 430 71"
#!   ORDER="500000000000033"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt;"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Local"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="0"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_9"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="133"
#!   TYPE="Sorter"
#!   VERSION="3"
#!   POSITION="10777.************ -3394.3479217314848"
#!   BOUNDING_RECT="10777.************ -3394.3479217314848 430 71"
#!   ORDER="500000000000034"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SORTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="原始排序" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SORT_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SORT_PARM" PARM_VALUE="&lt;u539f&gt;&lt;u59cb&gt;&lt;u6392&gt;&lt;u5e8f&gt; NATURAL ASCENDING"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Sorter_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="155"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="12270.************ -1903.************"
#!   BOUNDING_RECT="12270.************ -1903.************ 430 71"
#!   ORDER="500000000000035"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u6302&gt;&lt;u63a5&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6302&gt;&lt;u63a5&gt;&lt;u53f7&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;20&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_10"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="158"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="12270.************ -2360.2466716280751"
#!   BOUNDING_RECT="12270.************ -2360.2466716280751 430 71"
#!   ORDER="500000000000035"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u6302&gt;&lt;u63a5&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6302&gt;&lt;u63a5&gt;&lt;u53f7&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_11"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="161"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="12457.************ -2902.7524430129351"
#!   BOUNDING_RECT="12457.************ -2902.7524430129351 430 71"
#!   ORDER="500000000000035"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="顺序码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u6302&gt;&lt;u63a5&gt;&lt;u53f7&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;&lt;u6302&gt;&lt;u63a5&gt;&lt;u53f7&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_12"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="152"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="-399.90821364152328 -1888.1893799780905"
#!   BOUNDING_RECT="-399.90821364152328 -1888.1893799780905 430 71"
#!   ORDER="500000000000036"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GTBGDCKTBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="挂接号,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="154"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="-365.86380174086344 -1738.9424703584805"
#!   BOUNDING_RECT="-365.86380174086344 -1738.9424703584805 430 71"
#!   ORDER="500000000000036"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HPBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="挂接号,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="153"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="-362.27181179797537 -1433.************"
#!   BOUNDING_RECT="-362.27181179797537 -1433.************ 430 71"
#!   ORDER="500000000000036"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="UID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XJXZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSJSYDDL" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="HLDJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="FKSQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="LYBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SJNF" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Length" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SHAPE_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="挂接号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="挂接号,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_3"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="69"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="68"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="55"
#!   SOURCE_NODE="18"
#!   TARGET_NODE="26"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="64"
#!   SOURCE_NODE="21"
#!   TARGET_NODE="57"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="66"
#!   SOURCE_NODE="26"
#!   TARGET_NODE="53"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="58"
#!   SOURCE_NODE="28"
#!   TARGET_NODE="42"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="56"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="41"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="36"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="43"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 LEFT"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="34"
#!   SOURCE_NODE="35"
#!   TARGET_NODE="33"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="76"
#!   SOURCE_NODE="37"
#!   TARGET_NODE="59"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="62"
#!   SOURCE_NODE="41"
#!   TARGET_NODE="46"
#!   SOURCE_PORT_DESC="fo 0 CHOPPED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="74"
#!   SOURCE_NODE="42"
#!   TARGET_NODE="70"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="52"
#!   SOURCE_NODE="43"
#!   TARGET_NODE="48"
#!   SOURCE_PORT_DESC="fo 0 JOINED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="156"
#!   SOURCE_NODE="44"
#!   TARGET_NODE="155"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="63"
#!   SOURCE_NODE="46"
#!   TARGET_NODE="21"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="79"
#!   SOURCE_NODE="48"
#!   TARGET_NODE="78"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="67"
#!   SOURCE_NODE="53"
#!   TARGET_NODE="28"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="65"
#!   SOURCE_NODE="57"
#!   TARGET_NODE="51"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="77"
#!   SOURCE_NODE="59"
#!   TARGET_NODE="35"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="97"
#!   SOURCE_NODE="60"
#!   TARGET_NODE="81"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="98"
#!   SOURCE_NODE="61"
#!   TARGET_NODE="92"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="75"
#!   SOURCE_NODE="70"
#!   TARGET_NODE="43"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 RIGHT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="73"
#!   SOURCE_NODE="71"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="80"
#!   SOURCE_NODE="78"
#!   TARGET_NODE="44"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="99"
#!   SOURCE_NODE="81"
#!   TARGET_NODE="93"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="100"
#!   SOURCE_NODE="82"
#!   TARGET_NODE="88"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="103"
#!   SOURCE_NODE="83"
#!   TARGET_NODE="89"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="106"
#!   SOURCE_NODE="84"
#!   TARGET_NODE="85"
#!   SOURCE_PORT_DESC="fo 0 JOINED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="108"
#!   SOURCE_NODE="85"
#!   TARGET_NODE="96"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="159"
#!   SOURCE_NODE="86"
#!   TARGET_NODE="158"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="105"
#!   SOURCE_NODE="88"
#!   TARGET_NODE="94"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="101"
#!   SOURCE_NODE="89"
#!   TARGET_NODE="90"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="102"
#!   SOURCE_NODE="89"
#!   TARGET_NODE="84"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 LEFT"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="104"
#!   SOURCE_NODE="90"
#!   TARGET_NODE="91"
#!   SOURCE_PORT_DESC="fo 0 CHOPPED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="107"
#!   SOURCE_NODE="91"
#!   TARGET_NODE="61"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="110"
#!   SOURCE_NODE="92"
#!   TARGET_NODE="87"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="109"
#!   SOURCE_NODE="93"
#!   TARGET_NODE="82"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="112"
#!   SOURCE_NODE="94"
#!   TARGET_NODE="84"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 RIGHT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="111"
#!   SOURCE_NODE="95"
#!   TARGET_NODE="83"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="113"
#!   SOURCE_NODE="96"
#!   TARGET_NODE="86"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="134"
#!   SOURCE_NODE="116"
#!   TARGET_NODE="118"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="135"
#!   SOURCE_NODE="117"
#!   TARGET_NODE="129"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="136"
#!   SOURCE_NODE="118"
#!   TARGET_NODE="130"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="137"
#!   SOURCE_NODE="119"
#!   TARGET_NODE="125"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="140"
#!   SOURCE_NODE="120"
#!   TARGET_NODE="126"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="143"
#!   SOURCE_NODE="121"
#!   TARGET_NODE="122"
#!   SOURCE_PORT_DESC="fo 0 JOINED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="145"
#!   SOURCE_NODE="122"
#!   TARGET_NODE="133"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="162"
#!   SOURCE_NODE="123"
#!   TARGET_NODE="161"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="142"
#!   SOURCE_NODE="125"
#!   TARGET_NODE="131"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="138"
#!   SOURCE_NODE="126"
#!   TARGET_NODE="127"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="139"
#!   SOURCE_NODE="126"
#!   TARGET_NODE="121"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 LEFT"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="141"
#!   SOURCE_NODE="127"
#!   TARGET_NODE="128"
#!   SOURCE_PORT_DESC="fo 0 CHOPPED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="144"
#!   SOURCE_NODE="128"
#!   TARGET_NODE="117"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="147"
#!   SOURCE_NODE="129"
#!   TARGET_NODE="124"
#!   SOURCE_PORT_DESC="fo 0 ELEMENTS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="146"
#!   SOURCE_NODE="130"
#!   TARGET_NODE="119"
#!   SOURCE_PORT_DESC="fo 0 UNIQUE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="149"
#!   SOURCE_NODE="131"
#!   TARGET_NODE="121"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 RIGHT"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="148"
#!   SOURCE_NODE="132"
#!   TARGET_NODE="120"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="150"
#!   SOURCE_NODE="133"
#!   TARGET_NODE="123"
#!   SOURCE_PORT_DESC="fo 0 SORTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="165"
#!   SOURCE_NODE="152"
#!   TARGET_NODE="132"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="169"
#!   SOURCE_NODE="153"
#!   TARGET_NODE="37"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="167"
#!   SOURCE_NODE="154"
#!   TARGET_NODE="95"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="157"
#!   SOURCE_NODE="155"
#!   TARGET_NODE="50"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="160"
#!   SOURCE_NODE="158"
#!   TARGET_NODE="50"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 Output00"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="163"
#!   SOURCE_NODE="161"
#!   TARGET_NODE="50"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 2 Output01"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="54"
#!   SOURCE_NODE="51"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 1 COMPLETE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="72"
#!   SOURCE_NODE="68"
#!   TARGET_NODE="71"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="114"
#!   SOURCE_NODE="87"
#!   TARGET_NODE="60"
#!   SOURCE_PORT_DESC="fo 1 COMPLETE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="151"
#!   SOURCE_NODE="124"
#!   TARGET_NODE="116"
#!   SOURCE_PORT_DESC="fo 1 COMPLETE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="168"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="153"
#!   SOURCE_PORT_DESC="fo 4 FSQ&lt;lt&gt;solidus&lt;gt&gt;HL_FS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="166"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="154"
#!   SOURCE_PORT_DESC="fo 5 FSQ&lt;lt&gt;solidus&lt;gt&gt;HP_FS"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="164"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="152"
#!   SOURCE_PORT_DESC="fo 7 KT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO dir $(FME_MF_DIR)标准

DEFAULT_MACRO PARAMETER $(FME_MF_DIR)成果

# ============================================================================
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)图斑自西北向东南编号.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 图斑自西北向东南编号
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 69"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_2_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,NO,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_2_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_2_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_2_<Rejected> INPUT FEATURE_TYPE FeatureReader_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_2_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = gdb ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED <u5143><u6570><u636e> FSQ<solidus><u6881><u6eaa><u533a><u9700><u5916><u4e1a><u8c03><u67e5><u56fe><u6591> FSQ<solidus><u6881><u6eaa><u533a>DFFH<u56fe><u6591> FSQ<solidus>HL_FS FSQ<solidus>HP_FS FSQ<solidus>SK_FS KT
MACRO FeatureReader_DIRECTIVES ALIAS_MODE,NONE,BEGIN_SQL{0},,CACHE_MULTIPATCH_TEXTURES,yes,CHECK_SIMPLE_GEOM,no,CREATE_FEATURE_TABLES_FROM_DATA,Yes,DISABLE_FEATURE_DATASET_ATTRIBUTE,Yes,END_SQL{0},,EXPOSE_ATTRS_GROUP,,FEATURE_READ_MODE,Features,GEODATABASE_FILE_EXPOSE_FORMAT_ATTRS,,GEODB_SHARED_RDR_ADV_PARM_GROUP,,GEOMETRY,,IGNORE_NETWORK_INFO,yes,IGNORE_RELATIONSHIP_INFO,yes,MERGE_FEAT_LINKED_ANNOS,no,NETWORK_AUTHENTICATION,,QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,Yes,READ_THREE_POINT_ARCS,no,REMOVE_FEATURE_DATASET,NO,RESOLVE_DOMAINS,no,RESOLVE_SUBTYPE_NAMES,yes,SIMPLE_DONUT_GEOMETRY,no,SPLIT_COMPLEX_ANNOS,no,SPLIT_COMPLEX_EDGES,no,SPLIT_MULTI_PART_ANNOS,no,STRIP_GUID_GLOBALID_BRACES,no,TABLELIST,,TRANSLATE_SPATIAL_DATA_ONLY,no,USE_SEARCH_ENVELOPE,NO,WHERE_WWJD,
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Tester_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { GEODATABASE_FILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "<u5143><u6570><u636e><space>FSQ<solidus><u6881><u6eaa><u533a><u9700><u5916><u4e1a><u8c03><u67e5><u56fe><u6591><space>FSQ<solidus><u6881><u6eaa><u533a>DFFH<u56fe><u6591><space>FSQ<solidus>HL_FS<space>FSQ<solidus>HP_FS<space>FSQ<solidus>SK_FS<space>KT" }    READER_DIRECTIVES   { METAFILE,GEODATABASE_FILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }    OUTPUT FSQ<solidus>HL_FS FEATURE_TYPE FeatureReader_FSQ<solidus>HL_FS    OUTPUT FSQ<solidus>HP_FS FEATURE_TYPE FeatureReader_FSQ<solidus>HP_FS    OUTPUT KT FEATURE_TYPE FeatureReader_KT
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer_3 }    INPUT  FEATURE_TYPE FeatureReader_FSQ<solidus>HL_FS    OUTPUT { FEATURE_TYPE AttributeExposer_3_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Junction }    INPUT  FEATURE_TYPE AttributeExposer_3_OUTPUT    OUTPUT { FEATURE_TYPE Junction_Output         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_3 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u539f><u59cb><u6392><u5e8f>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Junction_Output    OUTPUT { OUTPUT FEATURE_TYPE Counter_3_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_3_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE Counter_3_OUTPUT    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "_count" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Reprojector_REPROJECTED    OUTPUT { OUTPUT FEATURE_TYPE Counter_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_<REJECTED>        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Counter OUTPUT Splitter"   INPUT FEATURE_TYPE Counter_OUTPUT   OUTPUT FEATURE_TYPE Counter_OUTPUT_0_/sTFA1dnbN8=   OUTPUT FEATURE_TYPE Counter_OUTPUT_1_bU15iCfwGHA=
# -------------------------------------------------------------------------
FACTORY_DEF {*} ChoppingFactory    FACTORY_NAME { Chopper }    INPUT  FEATURE_TYPE Counter_OUTPUT_0_/sTFA1dnbN8=    MODE { VERTEX }    MAX_VERTICES { "1" }    APPROX_LENGTH { "<Unused>" }    REMNANT_ATTRIBUTE { "_remnant" }    CHOP_POLYGONS { <Unused> }    REJECT_INVALID_GEOM Yes    DEAGGREGATE_INPUT { Deaggregate }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { CHOPPED FEATURE_TYPE Chopper_CHOPPED          }    OUTPUT { REJECTED FEATURE_TYPE Chopper_<REJECTED>          }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Chopper_<Rejected> INPUT FEATURE_TYPE Chopper_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Chopper<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
INCLUDE [    set supplyXFunc {};    if { {<Unused>} != {} }    {       set supplyXFunc {@Coordinate(REJECTABLE_WITH_FLAG,x,"<Unused>",FLATTEN_AGGREGATE,YES)};    };    puts "MACRO CoordinateExtractor_SUPPLY_X ${supplyXFunc}"; ]
INCLUDE [    set supplyYFunc {};    if { {<Unused>} != {} }    {       set supplyYFunc {@Coordinate(REJECTABLE_WITH_FLAG,y,"<Unused>",FLATTEN_AGGREGATE,NO)};    };    puts "MACRO CoordinateExtractor_SUPPLY_Y ${supplyYFunc}"; ]
INCLUDE [    set supplyZDefaultFunc {};    if { {} != {} }    {       set supplyZDefaultFunc {@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING,"<Unused>",,FLOAT)};    };    puts "MACRO CoordinateExtractor_SUPPLY_Z_DEFAULT ${supplyZDefaultFunc}"; ]
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_TESTZDEFAULT }    INPUT  FEATURE_TYPE Chopper_CHOPPED    TEST { "" = "" ENCODED }    TEST { "" TYPE NUM ENCODED }    BOOLEAN_OPERATOR OR    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_TESTMODE_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_<REJECTED>       @SupplyAttributes(fme_rejection_code, INVALID_PARAMETER_DEFAULT_Z)        }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_TESTMODE }    INPUT { FEATURE_TYPE CoordinateExtractor_TESTMODE_INPUT }    TEST { "All Coordinates" == "All Coordinates" }    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_LIST_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_SPECIFIC_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_LIST }    INPUT { FEATURE_TYPE CoordinateExtractor_LIST_INPUT }    TEST @Dimension() == 2    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_OUTPUT         @ZValue("")         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)         @Dimension(2)          }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_OUTPUT         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)          }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_INPUT }    TEST { "<Unused>" TYPE INT ENCODED }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_SPECIFIC_X_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_<REJECTED>       @SupplyAttributes(fme_rejection_code, INVALID_PARAMETER_INDEX)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_X }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_X_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_SUPPLY_X) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_SPECIFIC_Y_INPUT }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_<REJECTED>        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_Y }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_Y_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_SUPPLY_Y) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_ROUTER_INPUT }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_<REJECTED>        }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_Z_ROUTER }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_ROUTER_INPUT }    TEST { "<Unused>" != "" ENCODED }    TEST @Dimension() == 3    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_OUTPUT       $(CoordinateExtractor_SUPPLY_Z_DEFAULT)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_SPECIFIC_Z }    INPUT { FEATURE_TYPE CoordinateExtractor_SPECIFIC_Z_INPUT }    FUNCTION_DEFINITION { @Coordinate(REJECTABLE_WITH_FLAG,z,"<Unused>",FLATTEN_AGGREGATE,NO) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_<REJECTED>        }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME CoordinateExtractor_<Rejected> INPUT FEATURE_TYPE CoordinateExtractor_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, CoordinateExtractor<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { GeometryRemover }    INPUT  FEATURE_TYPE CoordinateExtractor_OUTPUT    OUTPUT { FEATURE_TYPE GeometryRemover_OUTPUT         @RemoveGeometry( No )          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder }    INPUT  FEATURE_TYPE GeometryRemover_OUTPUT    LIST_NAME { "_indices{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }    OUTPUT { NOLIST FEATURE_TYPE ListExploder_<REJECTED>         @RemoveAttributes(ElementFactory.baseCloned)         fme_rejection_code MISSING_PARAMETER_LIST          }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME ListExploder_<Rejected> INPUT FEATURE_TYPE ListExploder_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, ListExploder<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator }    INPUT  FEATURE_TYPE ListExploder_ELEMENTS    GROUP_BY { _count }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { x,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,;y,NUMERIC_MODE,MIN,MAX,,,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { OBJECTID,int32,UID,varchar<openparen>21<closeparen>,YSDM,varchar<openparen>10<closeparen>,HLMC,varchar<openparen>50<closeparen>,HLDM,varchar<openparen>14<closeparen>,HLBM,varchar<openparen>20<closeparen>,XJXZQDM,varchar<openparen>6<closeparen>,XJXZQMC,varchar<openparen>50<closeparen>,SMMJ,real64,SSJSYDDL,varchar<openparen>6<closeparen>,HLDJ,varchar<openparen>6<closeparen>,FKSQ,varchar<openparen>6<closeparen>,LYMC,varchar<openparen>50<closeparen>,LYBM,varchar<openparen>16<closeparen>,SJNF,varchar<openparen>4<closeparen>,BZ,varchar<openparen>255<closeparen>,SHAPE_Length,real64,SHAPE_Area,real64,<u6302><u63a5><u53f7>,varchar<openparen>20<closeparen>,<u539f><u59cb><u6392><u5e8f>,int64,_count,int64,_remnant,char<openparen>3<closeparen>,x,real64,y,real64,z,real64,_element_index,uint32 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { COMPLETE FEATURE_TYPE StatisticsCalculator_COMPLETE        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_COMPLETE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "tran_x" "SET_TO" "<at>Evaluate<openparen>1<solidus><at>sqrt<openparen>2<closeparen>*<openparen><at>Value<openparen>x<closeparen>+<at>Value<openparen>y<closeparen><closeparen><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "tran_y" "SET_TO" "<at>Evaluate<openparen>1<solidus><at>sqrt<openparen>2<closeparen>*<openparen>-<at>Value<openparen>x<closeparen>+<at>Value<openparen>y<closeparen><closeparen><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    GROUP_BY { fme_feature_type }    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { No }    SORT_BY { x NATURAL ASCENDING tran_y NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_SORTED          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE Sorter_SORTED    KEY_ATTRIBUTES { _count }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_2 }    GROUP_BY { fme_feature_type }    FLUSH_WHEN_GROUPS_CHANGE { No }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u987a><u5e8f><u7f16><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE DuplicateFilter_UNIQUE    OUTPUT { OUTPUT FEATURE_TYPE Counter_2_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_2_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper }    INPUT  FEATURE_TYPE Counter_2_OUTPUT    KEEP_ATTRS { _count,<u987a><u5e8f><u7f16><u53f7> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeKeeper_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u987a><u5e8f><u7f16><u53f7>" "SET_TO" "<at>PadLeft<openparen><at>Value<openparen><u987a><u5e8f><u7f16><u53f7><closeparen><comma>3<comma>0<closeparen>" "int64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} FeatureJoinerFactory    FACTORY_NAME { FeatureJoiner }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    INPUT LEFT FEATURE_TYPE Counter_OUTPUT_1_bU15iCfwGHA=    INPUT RIGHT FEATURE_TYPE AttributeCreator_3_OUTPUT    JOIN_MODE { Inner }    JOIN_KEYS { _count _count AUTO }    ATTR_CONFLICT_RES { USE_LEFT }    PRESERVE_VALUES_FROM_UNJOINED_FEATURES { YES }    GEOMETRY_HANDLING { USE_LEFT }    OUTPUT { JOINED FEATURE_TYPE FeatureJoiner_JOINED        }    OUTPUT { <REJECTED> FEATURE_TYPE FeatureJoiner_<REJECTED>        }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureJoiner_<Rejected> INPUT FEATURE_TYPE FeatureJoiner_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureJoiner<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE FeatureJoiner_JOINED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u987a><u5e8f><u7801>" "SET_TO" "<at>Value<openparen><u987a><u5e8f><u7f16><u53f7><closeparen>" "int64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter_2 }    INPUT  FEATURE_TYPE AttributeCreator_2_OUTPUT    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    SORT_BY { <u539f><u59cb><u6392><u5e8f> NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_2_SORTED          }
# -------------------------------------------------------------------------
# Build the List removal function and regular expression if there was any list attributes to be removed.
# If not, then we will not have any extra list removal call to @RemoveAttributes, which speeds the
# normal, non-list removal especially when in Bulk Mode.  Note that this computation of the regular expressions is done
# once during mapping file parse time.
INCLUDE [    set listAttributeRemoveRegexps {};    set anyList {no};    foreach attr [split ""] {       set attr [FME_DecodeText $attr];       set attr [regsub "{}$" $attr "{}.*"];       set attr [regsub -all "{}" $attr "\\{\[0-9\]+\\}"];       append listAttributeRemoveRegexps ",^$attr$";       set anyList {yes};    };    if { ${anyList} == {no} } {        puts {MACRO AttributeRemover_LIST_FUNC }    } else {        puts "MACRO AttributeRemover_LIST_FUNC @RemoveAttributes(fme_pcre_match\"$listAttributeRemoveRegexps\")"    }; ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeRemover }    INPUT  FEATURE_TYPE Sorter_2_SORTED    OUTPUT { FEATURE_TYPE AttributeRemover_OUTPUT        @RemoveAttributes(fme_encoded,_count,<u987a><u5e8f><u7f16><u53f7>,<u539f><u59cb><u6392><u5e8f>)        $(AttributeRemover_LIST_FUNC)         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_10 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeRemover_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u6302><u63a5><u53f7>" "SET_TO" "<at>Value<openparen><u6302><u63a5><u53f7><closeparen>" "varchar<openparen>20<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_10_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer_2 }    INPUT  FEATURE_TYPE FeatureReader_FSQ<solidus>HP_FS    OUTPUT { FEATURE_TYPE AttributeExposer_2_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_6 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u539f><u59cb><u6392><u5e8f>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE AttributeExposer_2_OUTPUT    OUTPUT { OUTPUT FEATURE_TYPE Counter_6_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_6_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector_2 }    INPUT  FEATURE_TYPE Counter_6_OUTPUT    OUTPUT { FEATURE_TYPE Reprojector_2_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector_2,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_5 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "_count" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Reprojector_2_REPROJECTED    OUTPUT { OUTPUT FEATURE_TYPE Counter_5_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_5_<REJECTED>        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Counter_5 OUTPUT Splitter"   INPUT FEATURE_TYPE Counter_5_OUTPUT   OUTPUT FEATURE_TYPE Counter_5_OUTPUT_0_3rq3s096lyU=   OUTPUT FEATURE_TYPE Counter_5_OUTPUT_1_PZdXF1jBFUQ=
# -------------------------------------------------------------------------
FACTORY_DEF {*} ChoppingFactory    FACTORY_NAME { Chopper_2 }    INPUT  FEATURE_TYPE Counter_5_OUTPUT_0_3rq3s096lyU=    MODE { VERTEX }    MAX_VERTICES { "1" }    APPROX_LENGTH { "<Unused>" }    REMNANT_ATTRIBUTE { "_remnant" }    CHOP_POLYGONS { <Unused> }    REJECT_INVALID_GEOM Yes    DEAGGREGATE_INPUT { Deaggregate }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { CHOPPED FEATURE_TYPE Chopper_2_CHOPPED          }    OUTPUT { REJECTED FEATURE_TYPE Chopper_2_<REJECTED>          }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Chopper_2_<Rejected> INPUT FEATURE_TYPE Chopper_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Chopper_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
INCLUDE [    set supplyXFunc {};    if { {<Unused>} != {} }    {       set supplyXFunc {@Coordinate(REJECTABLE_WITH_FLAG,x,"<Unused>",FLATTEN_AGGREGATE,YES)};    };    puts "MACRO CoordinateExtractor_2_SUPPLY_X ${supplyXFunc}"; ]
INCLUDE [    set supplyYFunc {};    if { {<Unused>} != {} }    {       set supplyYFunc {@Coordinate(REJECTABLE_WITH_FLAG,y,"<Unused>",FLATTEN_AGGREGATE,NO)};    };    puts "MACRO CoordinateExtractor_2_SUPPLY_Y ${supplyYFunc}"; ]
INCLUDE [    set supplyZDefaultFunc {};    if { {} != {} }    {       set supplyZDefaultFunc {@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING,"<Unused>",,FLOAT)};    };    puts "MACRO CoordinateExtractor_2_SUPPLY_Z_DEFAULT ${supplyZDefaultFunc}"; ]
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_TESTZDEFAULT }    INPUT  FEATURE_TYPE Chopper_2_CHOPPED    TEST { "" = "" ENCODED }    TEST { "" TYPE NUM ENCODED }    BOOLEAN_OPERATOR OR    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_TESTMODE_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_<REJECTED>       @SupplyAttributes(fme_rejection_code, INVALID_PARAMETER_DEFAULT_Z)        }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_TESTMODE }    INPUT { FEATURE_TYPE CoordinateExtractor_2_TESTMODE_INPUT }    TEST { "All Coordinates" == "All Coordinates" }    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_LIST_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_LIST }    INPUT { FEATURE_TYPE CoordinateExtractor_2_LIST_INPUT }    TEST @Dimension() == 2    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_OUTPUT         @ZValue("")         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)         @Dimension(2)          }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_OUTPUT         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)          }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_INPUT }    TEST { "<Unused>" TYPE INT ENCODED }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_X_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_<REJECTED>       @SupplyAttributes(fme_rejection_code, INVALID_PARAMETER_INDEX)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_X }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_X_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_2_SUPPLY_X) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Y_INPUT }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_2_<REJECTED>        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_Y }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Y_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_2_SUPPLY_Y) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_ROUTER_INPUT }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_2_<REJECTED>        }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_Z_ROUTER }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_ROUTER_INPUT }    TEST { "<Unused>" != "" ENCODED }    TEST @Dimension() == 3    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_2_OUTPUT       $(CoordinateExtractor_2_SUPPLY_Z_DEFAULT)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_2_SPECIFIC_Z }    INPUT { FEATURE_TYPE CoordinateExtractor_2_SPECIFIC_Z_INPUT }    FUNCTION_DEFINITION { @Coordinate(REJECTABLE_WITH_FLAG,z,"<Unused>",FLATTEN_AGGREGATE,NO) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_2_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_2_<REJECTED>        }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME CoordinateExtractor_2_<Rejected> INPUT FEATURE_TYPE CoordinateExtractor_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, CoordinateExtractor_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { GeometryRemover_2 }    INPUT  FEATURE_TYPE CoordinateExtractor_2_OUTPUT    OUTPUT { FEATURE_TYPE GeometryRemover_2_OUTPUT         @RemoveGeometry( No )          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder_2 }    INPUT  FEATURE_TYPE GeometryRemover_2_OUTPUT    LIST_NAME { "_indices{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_2_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }    OUTPUT { NOLIST FEATURE_TYPE ListExploder_2_<REJECTED>         @RemoveAttributes(ElementFactory.baseCloned)         fme_rejection_code MISSING_PARAMETER_LIST          }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME ListExploder_2_<Rejected> INPUT FEATURE_TYPE ListExploder_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, ListExploder_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator_2 }    INPUT  FEATURE_TYPE ListExploder_2_ELEMENTS    GROUP_BY { _count }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { x,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,;y,NUMERIC_MODE,MIN,MAX,,,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { OBJECTID,int32,UID,varchar<openparen>21<closeparen>,YSDM,varchar<openparen>10<closeparen>,HPMC,varchar<openparen>50<closeparen>,HPDM,varchar<openparen>8<closeparen>,HPBM,varchar<openparen>20<closeparen>,XJXZQDM,varchar<openparen>6<closeparen>,XJXZQMC,varchar<openparen>50<closeparen>,SMMJ,real64,SSJSYDDL,varchar<openparen>6<closeparen>,FKSQ,varchar<openparen>6<closeparen>,LYMC,varchar<openparen>50<closeparen>,LYBM,varchar<openparen>16<closeparen>,SJNF,varchar<openparen>4<closeparen>,BZ,varchar<openparen>255<closeparen>,SHAPE_Length,real64,SHAPE_Area,real64,<u6302><u63a5><u53f7>,varchar<openparen>200<closeparen>,<u539f><u59cb><u6392><u5e8f>,int64,_count,int64,_remnant,char<openparen>3<closeparen>,x,real64,y,real64,z,real64,_element_index,uint32 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { COMPLETE FEATURE_TYPE StatisticsCalculator_2_COMPLETE        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_4 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_2_COMPLETE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "tran_x" "SET_TO" "<at>Evaluate<openparen>1<solidus><at>sqrt<openparen>2<closeparen>*<openparen><at>Value<openparen>x<closeparen>+<at>Value<openparen>y<closeparen><closeparen><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "tran_y" "SET_TO" "<at>Evaluate<openparen>1<solidus><at>sqrt<openparen>2<closeparen>*<openparen>-<at>Value<openparen>x<closeparen>+<at>Value<openparen>y<closeparen><closeparen><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_4_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter_3 }    INPUT  FEATURE_TYPE AttributeCreator_4_OUTPUT    GROUP_BY { fme_feature_type }    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { No }    SORT_BY { x NATURAL ASCENDING tran_y NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_3_SORTED          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE Sorter_3_SORTED    KEY_ATTRIBUTES { _count }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_2_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_4 }    GROUP_BY { fme_feature_type }    FLUSH_WHEN_GROUPS_CHANGE { No }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u987a><u5e8f><u7f16><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE DuplicateFilter_2_UNIQUE    OUTPUT { OUTPUT FEATURE_TYPE Counter_4_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_4_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_2 }    INPUT  FEATURE_TYPE Counter_4_OUTPUT    KEEP_ATTRS { _count,<u987a><u5e8f><u7f16><u53f7> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_6 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeKeeper_2_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u987a><u5e8f><u7f16><u53f7>" "SET_TO" "<at>PadLeft<openparen><at>Value<openparen><u987a><u5e8f><u7f16><u53f7><closeparen><comma>3<comma>0<closeparen>" "int64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_6_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} FeatureJoinerFactory    FACTORY_NAME { FeatureJoiner_2 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    INPUT LEFT FEATURE_TYPE Counter_5_OUTPUT_1_PZdXF1jBFUQ=    INPUT RIGHT FEATURE_TYPE AttributeCreator_6_OUTPUT    JOIN_MODE { Inner }    JOIN_KEYS { _count _count AUTO }    ATTR_CONFLICT_RES { USE_LEFT }    PRESERVE_VALUES_FROM_UNJOINED_FEATURES { YES }    GEOMETRY_HANDLING { USE_LEFT }    OUTPUT { JOINED FEATURE_TYPE FeatureJoiner_2_JOINED        }    OUTPUT { <REJECTED> FEATURE_TYPE FeatureJoiner_2_<REJECTED>        }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureJoiner_2_<Rejected> INPUT FEATURE_TYPE FeatureJoiner_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureJoiner_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_5 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE FeatureJoiner_2_JOINED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u987a><u5e8f><u7801>" "SET_TO" "<at>Value<openparen><u987a><u5e8f><u7f16><u53f7><closeparen>" "int64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_5_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter_4 }    INPUT  FEATURE_TYPE AttributeCreator_5_OUTPUT    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    SORT_BY { <u539f><u59cb><u6392><u5e8f> NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_4_SORTED          }
# -------------------------------------------------------------------------
# Build the List removal function and regular expression if there was any list attributes to be removed.
# If not, then we will not have any extra list removal call to @RemoveAttributes, which speeds the
# normal, non-list removal especially when in Bulk Mode.  Note that this computation of the regular expressions is done
# once during mapping file parse time.
INCLUDE [    set listAttributeRemoveRegexps {};    set anyList {no};    foreach attr [split ""] {       set attr [FME_DecodeText $attr];       set attr [regsub "{}$" $attr "{}.*"];       set attr [regsub -all "{}" $attr "\\{\[0-9\]+\\}"];       append listAttributeRemoveRegexps ",^$attr$";       set anyList {yes};    };    if { ${anyList} == {no} } {        puts {MACRO AttributeRemover_2_LIST_FUNC }    } else {        puts "MACRO AttributeRemover_2_LIST_FUNC @RemoveAttributes(fme_pcre_match\"$listAttributeRemoveRegexps\")"    }; ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeRemover_2 }    INPUT  FEATURE_TYPE Sorter_4_SORTED    OUTPUT { FEATURE_TYPE AttributeRemover_2_OUTPUT        @RemoveAttributes(fme_encoded,_count,<u987a><u5e8f><u7f16><u53f7>,<u539f><u59cb><u6392><u5e8f>)        $(AttributeRemover_2_LIST_FUNC)         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_11 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeRemover_2_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u6302><u63a5><u53f7>" "SET_TO" "<at>Value<openparen><u6302><u63a5><u53f7><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_11_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer }    INPUT  FEATURE_TYPE FeatureReader_KT    OUTPUT { FEATURE_TYPE AttributeExposer_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_9 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u539f><u59cb><u6392><u5e8f>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE AttributeExposer_OUTPUT    OUTPUT { OUTPUT FEATURE_TYPE Counter_9_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_9_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector_3 }    INPUT  FEATURE_TYPE Counter_9_OUTPUT    OUTPUT { FEATURE_TYPE Reprojector_3_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector_3,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_8 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "_count" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE Reprojector_3_REPROJECTED    OUTPUT { OUTPUT FEATURE_TYPE Counter_8_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_8_<REJECTED>        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Counter_8 OUTPUT Splitter"   INPUT FEATURE_TYPE Counter_8_OUTPUT   OUTPUT FEATURE_TYPE Counter_8_OUTPUT_0_9nGB0bMjBVE=   OUTPUT FEATURE_TYPE Counter_8_OUTPUT_1_cGtiWn39fPI=
# -------------------------------------------------------------------------
FACTORY_DEF {*} ChoppingFactory    FACTORY_NAME { Chopper_3 }    INPUT  FEATURE_TYPE Counter_8_OUTPUT_0_9nGB0bMjBVE=    MODE { VERTEX }    MAX_VERTICES { "1" }    APPROX_LENGTH { "<Unused>" }    REMNANT_ATTRIBUTE { "_remnant" }    CHOP_POLYGONS { <Unused> }    REJECT_INVALID_GEOM Yes    DEAGGREGATE_INPUT { Deaggregate }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { CHOPPED FEATURE_TYPE Chopper_3_CHOPPED          }    OUTPUT { REJECTED FEATURE_TYPE Chopper_3_<REJECTED>          }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Chopper_3_<Rejected> INPUT FEATURE_TYPE Chopper_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Chopper_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
INCLUDE [    set supplyXFunc {};    if { {<Unused>} != {} }    {       set supplyXFunc {@Coordinate(REJECTABLE_WITH_FLAG,x,"<Unused>",FLATTEN_AGGREGATE,YES)};    };    puts "MACRO CoordinateExtractor_3_SUPPLY_X ${supplyXFunc}"; ]
INCLUDE [    set supplyYFunc {};    if { {<Unused>} != {} }    {       set supplyYFunc {@Coordinate(REJECTABLE_WITH_FLAG,y,"<Unused>",FLATTEN_AGGREGATE,NO)};    };    puts "MACRO CoordinateExtractor_3_SUPPLY_Y ${supplyYFunc}"; ]
INCLUDE [    set supplyZDefaultFunc {};    if { {} != {} }    {       set supplyZDefaultFunc {@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING,"<Unused>",,FLOAT)};    };    puts "MACRO CoordinateExtractor_3_SUPPLY_Z_DEFAULT ${supplyZDefaultFunc}"; ]
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_TESTZDEFAULT }    INPUT  FEATURE_TYPE Chopper_3_CHOPPED    TEST { "" = "" ENCODED }    TEST { "" TYPE NUM ENCODED }    BOOLEAN_OPERATOR OR    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_TESTMODE_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_<REJECTED>       @SupplyAttributes(fme_rejection_code, INVALID_PARAMETER_DEFAULT_Z)        }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_TESTMODE }    INPUT { FEATURE_TYPE CoordinateExtractor_3_TESTMODE_INPUT }    TEST { "All Coordinates" == "All Coordinates" }    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_LIST_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_INPUT }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_LIST }    INPUT { FEATURE_TYPE CoordinateExtractor_3_LIST_INPUT }    TEST @Dimension() == 2    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_OUTPUT         @ZValue("")         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)         @Dimension(2)          }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_OUTPUT         @Coordinate(x,ALL,"_indices"{}.x,FLATTEN_AGGREGATE,YES)         @Coordinate(y,ALL,"_indices"{}.y,FLATTEN_AGGREGATE,NO)         @Coordinate(z,ALL,"_indices"{}.z,FLATTEN_AGGREGATE,NO)          }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_INPUT }    TEST { "<Unused>" TYPE INT ENCODED }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_X_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_<REJECTED>       @SupplyAttributes(fme_rejection_code, INVALID_PARAMETER_INDEX)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_X }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_X_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_3_SUPPLY_X) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Y_INPUT }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_3_<REJECTED>        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_Y }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Y_INPUT }    FUNCTION_DEFINITION { $(CoordinateExtractor_3_SUPPLY_Y) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_ROUTER_INPUT }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_3_<REJECTED>        }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_Z_ROUTER }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_ROUTER_INPUT }    TEST { "<Unused>" != "" ENCODED }    TEST @Dimension() == 3    BOOLEAN_OPERATOR AND    OUTPUT { PASSED FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_INPUT }    OUTPUT { FAILED FEATURE_TYPE CoordinateExtractor_3_OUTPUT       $(CoordinateExtractor_3_SUPPLY_Z_DEFAULT)        }
FACTORY_DEF {*} ExecuteFunctionFactory    FACTORY_NAME { CoordinateExtractor_3_SPECIFIC_Z }    INPUT { FEATURE_TYPE CoordinateExtractor_3_SPECIFIC_Z_INPUT }    FUNCTION_DEFINITION { @Coordinate(REJECTABLE_WITH_FLAG,z,"<Unused>",FLATTEN_AGGREGATE,NO) }    RESULT_ATTRIBUTE { <Unused> }    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { COMPLETE FEATURE_TYPE CoordinateExtractor_3_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE CoordinateExtractor_3_<REJECTED>        }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME CoordinateExtractor_3_<Rejected> INPUT FEATURE_TYPE CoordinateExtractor_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, CoordinateExtractor_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { GeometryRemover_3 }    INPUT  FEATURE_TYPE CoordinateExtractor_3_OUTPUT    OUTPUT { FEATURE_TYPE GeometryRemover_3_OUTPUT         @RemoveGeometry( No )          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ElementFactory    FACTORY_NAME { ListExploder_3 }    INPUT  FEATURE_TYPE GeometryRemover_3_OUTPUT    LIST_NAME { "_indices{}" }    ELEMENT_NUMBER_FIELD { "_element_index" }    CLONE_GEOMETRY    ATTR_ACCUM_MODE { "HANDLE_CONFLICT" }    ATTR_CONFLICT_RES { "INCOMING_IF_CONFLICT" }    INCOMING_PREFIX { "<Unused>" }    OUTPUT { ELEMENT FEATURE_TYPE ListExploder_3_ELEMENTS         @RemoveAttributes(ElementFactory.baseCloned)          }    OUTPUT { NOLIST FEATURE_TYPE ListExploder_3_<REJECTED>         @RemoveAttributes(ElementFactory.baseCloned)         fme_rejection_code MISSING_PARAMETER_LIST          }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME ListExploder_3_<Rejected> INPUT FEATURE_TYPE ListExploder_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, ListExploder_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator_3 }    INPUT  FEATURE_TYPE ListExploder_3_ELEMENTS    GROUP_BY { _count }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { x,NUMERIC_MODE,MIN,MAX,,,,,,,,,,,;y,NUMERIC_MODE,MIN,MAX,,,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { OBJECTID,int32,UID,varchar<openparen>21<closeparen>,YSDM,varchar<openparen>10<closeparen>,KTBM,varchar<openparen>20<closeparen>,GTBGDCKTBM,varchar<openparen>20<closeparen>,XJXZQDM,varchar<openparen>6<closeparen>,XJXZQMC,varchar<openparen>50<closeparen>,SMMJ,real64,SSJSYDDL,varchar<openparen>6<closeparen>,LYMC,varchar<openparen>50<closeparen>,LYBM,varchar<openparen>16<closeparen>,SJNF,varchar<openparen>4<closeparen>,BZ,varchar<openparen>255<closeparen>,SHAPE_Length,real64,SHAPE_Area,real64,<u6302><u63a5><u53f7>,varchar<openparen>200<closeparen>,<u539f><u59cb><u6392><u5e8f>,int64,_count,int64,_remnant,char<openparen>3<closeparen>,x,real64,y,real64,z,real64,_element_index,uint32 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { COMPLETE FEATURE_TYPE StatisticsCalculator_3_COMPLETE        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_7 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_3_COMPLETE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "tran_x" "SET_TO" "<at>Evaluate<openparen>1<solidus><at>sqrt<openparen>2<closeparen>*<openparen><at>Value<openparen>x<closeparen>+<at>Value<openparen>y<closeparen><closeparen><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "tran_y" "SET_TO" "<at>Evaluate<openparen>1<solidus><at>sqrt<openparen>2<closeparen>*<openparen>-<at>Value<openparen>x<closeparen>+<at>Value<openparen>y<closeparen><closeparen><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_7_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter_5 }    INPUT  FEATURE_TYPE AttributeCreator_7_OUTPUT    GROUP_BY { fme_feature_type }    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { No }    SORT_BY { x NATURAL ASCENDING tran_y NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_5_SORTED          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} DuplicateRemoverFactory    FACTORY_NAME { DuplicateFilter_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    SUPPORTS_FEATURE_TABLES    INPUT  FEATURE_TYPE Sorter_5_SORTED    KEY_ATTRIBUTES { _count }    INPUT_IS_ORDERED { NO }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { UNIQUE FEATURE_TYPE DuplicateFilter_3_UNIQUE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_7 }    GROUP_BY { fme_feature_type }    FLUSH_WHEN_GROUPS_CHANGE { No }    START { "0" }    SCOPE { Local }    DOMAIN { "<Unused>" }    COUNT_ATTR { "<u987a><u5e8f><u7f16><u53f7>" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE DuplicateFilter_3_UNIQUE    OUTPUT { OUTPUT FEATURE_TYPE Counter_7_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_7_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttributeKeeperFactory    FACTORY_NAME { AttributeKeeper_3 }    INPUT  FEATURE_TYPE Counter_7_OUTPUT    KEEP_ATTRS { _count,<u987a><u5e8f><u7f16><u53f7> }    KEEP_LISTS {  }    KEEP_FME_ATTRIBUTES Yes    BUILD_FEATURE_TABLES { NO }    OUTPUT_ON_ATTRIBUTE_CHANGE { <Unused> }    OUTPUT { OUTPUT FEATURE_TYPE AttributeKeeper_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_9 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeKeeper_3_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u987a><u5e8f><u7f16><u53f7>" "SET_TO" "<at>PadLeft<openparen><at>Value<openparen><u987a><u5e8f><u7f16><u53f7><closeparen><comma>3<comma>0<closeparen>" "int64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_9_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} FeatureJoinerFactory    FACTORY_NAME { FeatureJoiner_3 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    INPUT LEFT FEATURE_TYPE Counter_8_OUTPUT_1_cGtiWn39fPI=    INPUT RIGHT FEATURE_TYPE AttributeCreator_9_OUTPUT    JOIN_MODE { Inner }    JOIN_KEYS { _count _count AUTO }    ATTR_CONFLICT_RES { USE_LEFT }    PRESERVE_VALUES_FROM_UNJOINED_FEATURES { YES }    GEOMETRY_HANDLING { USE_LEFT }    OUTPUT { JOINED FEATURE_TYPE FeatureJoiner_3_JOINED        }    OUTPUT { <REJECTED> FEATURE_TYPE FeatureJoiner_3_<REJECTED>        }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureJoiner_3_<Rejected> INPUT FEATURE_TYPE FeatureJoiner_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureJoiner_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_8 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE FeatureJoiner_3_JOINED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u987a><u5e8f><u7801>" "SET_TO" "<at>Value<openparen><u987a><u5e8f><u7f16><u53f7><closeparen>" "int64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_8_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} SortFactory    FACTORY_NAME { Sorter_6 }    INPUT  FEATURE_TYPE AttributeCreator_8_OUTPUT    SORT_ALPHA_AS_UTF8 { YES }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    SORT_BY { <u539f><u59cb><u6392><u5e8f> NATURAL ASCENDING }    OUTPUT { SORTED FEATURE_TYPE Sorter_6_SORTED          }
# -------------------------------------------------------------------------
# Build the List removal function and regular expression if there was any list attributes to be removed.
# If not, then we will not have any extra list removal call to @RemoveAttributes, which speeds the
# normal, non-list removal especially when in Bulk Mode.  Note that this computation of the regular expressions is done
# once during mapping file parse time.
INCLUDE [    set listAttributeRemoveRegexps {};    set anyList {no};    foreach attr [split ""] {       set attr [FME_DecodeText $attr];       set attr [regsub "{}$" $attr "{}.*"];       set attr [regsub -all "{}" $attr "\\{\[0-9\]+\\}"];       append listAttributeRemoveRegexps ",^$attr$";       set anyList {yes};    };    if { ${anyList} == {no} } {        puts {MACRO AttributeRemover_3_LIST_FUNC }    } else {        puts "MACRO AttributeRemover_3_LIST_FUNC @RemoveAttributes(fme_pcre_match\"$listAttributeRemoveRegexps\")"    }; ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeRemover_3 }    INPUT  FEATURE_TYPE Sorter_6_SORTED    OUTPUT { FEATURE_TYPE AttributeRemover_3_OUTPUT        @RemoveAttributes(fme_encoded,_count,<u987a><u5e8f><u7f16><u53f7>,<u539f><u59cb><u6392><u5e8f>)        $(AttributeRemover_3_LIST_FUNC)         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_12 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AttributeRemover_3_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u6302><u63a5><u53f7>" "SET_TO" "<at>Value<openparen><u6302><u63a5><u53f7><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_12_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode)<backslash><u7f16><u53f7><u540e>.gdb,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { FILEGDB }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_GEODB<comma>NO<comma>DATASET_TEMPLATE<comma><comma>WRITE_UNZONED_UTC<comma>No<comma>FEATURE_DATASET_HANDLING<comma>WRITE<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE_TYPE<comma>ADVANCED<comma><comma>BEGIN_SQL<comma><comma>END_SQL<comma><comma>ENABLE_LOAD_ONLY_MODE<comma>YES,METAFILE,FILEGDB" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY_FIRST_NONNUMERIC,ATTRIBUTE_INVALID_CHARS,<backslash><backslash><quote>:<dollar>?*<closeparen><openparen><lt><gt>|<openbracket>%#<space><apos><amp>+-<closebracket>.^<backslash><opencurly><backslash><closecurly>~<comma><solidus>,ATTRIBUTE_LENGTH,64,ATTR_TYPE_MAP,text<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>text<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>text<openparen>2048<closeparen><comma>fme_buffer<comma>text<openparen>2048<closeparen><comma>fme_xml<comma>text<openparen>2048<closeparen><comma>fme_json<comma>blob<comma>fme_binarybuffer<comma>blob<comma>fme_varbinary<openparen>width<closeparen><comma>blob<comma>fme_binary<openparen>width<closeparen><comma>date<comma>fme_datetime<comma>date<comma>fme_date<comma>date<comma>fme_time<comma>single<comma>fme_real32<comma>double<comma>fme_real64<comma>double<comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma>int<comma>fme_int32<comma>double<comma>fme_uint32<comma>text<openparen>20<closeparen><comma>fme_int64<comma>text<openparen>20<closeparen><comma>fme_uint64<comma>smallint<comma>fme_int16<comma>int<comma>fme_uint16<comma>smallint<comma>fme_int8<comma>smallint<comma>fme_uint8<comma>smallint<comma>fme_boolean<comma>objectid<comma>fme_int32<comma>guid<comma>fme_buffer<comma>globalid<comma>fme_buffer,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY_FIRST_NONNUMERIC,FEATURE_TYPE_INVALID_CHARS,<backslash><backslash><quote>:<dollar>?*<closeparen><openparen><lt><gt>|<openbracket>%#<space><apos><amp>+-<closebracket>.^<backslash><opencurly><backslash><closecurly>~<comma>,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>FILEGDB.fmf,FORMAT_NAME,FILEGDB,GEOM_MAP,geodb_point<comma>fme_point<comma>geodb_polygon<comma>fme_polygon<comma>geodb_polygon<comma>fme_rounded_rectangle<comma>geodb_polyline<comma>fme_line<comma>geodb_multipoint<comma>fme_point<comma>geodb_polygon<comma>fme_rectangle<comma>geodb_point<comma>fme_text<comma>geodb_polyline<comma>fme_arc<comma>geodb_polygon<comma>fme_ellipse<comma>geodb_no_geom<comma>fme_no_geom<comma>geodb_polygon<comma>fme_raster<comma>geodb_polyline<comma>fme_surface<comma>geodb_polyline<comma>fme_solid<comma>geodb_polygon<comma>fme_point_cloud<comma>geodb_polygon<comma>fme_voxel_grid<comma>geodb_no_geom<comma>fme_feature_table<comma>geodb_no_geom<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,FILEGDB,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>fme_configuration_group<space>fme_configuration_common_group%fme_spatial_group%fme_advanced_group%oracle_advanced_group<space>Table<quote><comma><comma><quote>GUI<space>NAMEDGROUP<space>fme_configuration_common_group<space>fme_feature_operation%fme_table_handling%mie_pack%oracle_model%fme_update_geometry%fme_selection_group%fme_table_creation_group<space>General<quote><comma><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_feature_operation<space>Insert<comma>INSERT<comma>fme_update_geometry<comma>fme_selection_group<comma>mie_pack%Update<comma>UPDATE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Upsert<comma>UPSERT<comma>fme_where_builder_clause<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN%Delete<comma>DELETE<comma>++fme_table_handling+USE_EXISTING<comma>fme_update_geometry<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<comma>fme_spatial_group<comma>fme_advanced_group<comma>oracle_sequenced_cols%<lt>at<gt>Value<lt>openparen<gt>fme_db_operation<lt>closeparen<gt><comma>MULTIPLE<comma>++fme_table_handling+USE_EXISTING<comma>++fme_selection_group+FME_DISCLOSURE_OPEN<space>Feature<space>Operation<quote><comma>INSERT<comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>fme_table_handling<space>Use<lt>space<gt>Existing<comma>USE_EXISTING<comma>fme_table_creation_group%Create<lt>space<gt>If<lt>space<gt>Needed<comma>CREATE_IF_MISSING%Drop<lt>space<gt>and<lt>space<gt>Create<comma>DROP_CREATE%Truncate<lt>space<gt>Existing<comma>TRUNCATE_EXISTING<comma>fme_table_creation_group<space>Table<space>Handling<quote><comma>CREATE_IF_MISSING<comma><quote>GUI<space>WHOLE_LINE<space>LOOKUP_CHOICE<space>fme_update_geometry<space>Yes<comma>YES%No<comma>NO<space>Update<space>Spatial<space>Column<openparen>s<closeparen><quote><comma>YES<comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_selection_group<space>fme_selection_method<space>Row<space>Selection<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>RADIOPARAMETERGROUP<space>fme_selection_method<space>fme_match_columns<comma>MATCH_COLUMNS%fme_where_builder_clause<comma>BUILDER<space>Row<space>Selection<space>Method<quote><comma>MATCH_COLUMNS<comma><quote>GUI<space>WHOLE_LINE<space>ATTRLIST_COMMAS<space>fme_match_columns<space>Match<space>Columns<quote><comma><comma><quote>GUI<space>WHOLE_LINE<space>TEXT_EDIT_SQL_CFG_OR_ATTR<space>fme_where_builder_clause<space>MODE<comma>WHERE<space>WHERE<space>Clause<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>fme_table_creation_group<space>filegdb_object_id_field%filegdb_object_id_alias%filegdb_shape_field%filegdb_shape_alias%filegdb_config_keyword%filegdb_xy_tolerance%filegdb_z_tolerance%filegdb_m_tolerance<space>Table<space>Creation<quote><comma><comma><quote>GUI<space>TEXT<space>filegdb_object_id_field<space>Object<space>ID<space>Field<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>filegdb_object_id_alias<space>Object<space>ID<space>Alias<quote><comma>OBJECTID<comma><quote>GUI<space>TEXT<space>filegdb_shape_field<space>Shape<space>Field<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>filegdb_shape_alias<space>Shape<space>Alias<quote><comma>SHAPE<comma><quote>GUI<space>TEXT<space>filegdb_config_keyword<space>Configuration<space>Keyword<quote><comma>DEFAULTS<comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>filegdb_xy_tolerance<space>XY<space>Tolerance<quote><comma><comma><quote>GUI<space>FLOAT<space>filegdb_z_tolerance<space>Z<space>Tolerance<quote><comma>0.001<comma><quote>GUI<space>FLOAT<space>filegdb_m_tolerance<space>M<space>Tolerance<quote><comma>0.001,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>filegdb_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>filegdb_drop_table<comma><quote><quote><quote><quote><quote><quote><comma>filegdb_truncate_table<comma><quote><quote><quote><quote><quote><quote><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_selection_method<comma>MATCH_COLUMNS<comma>fme_match_columns<comma><quote><quote><quote><quote><quote><quote><comma>fme_where_builder_clause<comma><quote><quote><quote><quote><quote><quote><comma>fme_update_geometry<comma>YES<comma>filegdb_object_id_field<comma>OBJECTID<comma>filegdb_object_id_alias<comma>OBJECTID<comma>filegdb_shape_field<comma>SHAPE<comma>filegdb_shape_alias<comma>SHAPE<comma>filegdb_config_keyword<comma>DEFAULTS<comma>filegdb_xy_tolerance<comma><quote><quote><quote><quote><quote><quote><comma>filegdb_z_tolerance<comma>0.001<comma>filegdb_m_tolerance<comma>0.001,WRITER_FORMAT_PARAMETER,FEATURE_TYPE_NAME<comma><quote>Feature<space>Class<space>or<space>Table<quote><comma>FEATURE_TYPE_DEFAULT_NAME<comma>FeatureClass1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>File<space>Geodatabase<space>folder<quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>File<space>Geodatabase<space>folder<quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>ATTRIBUTE_READING_HISTORIC<comma>ALL<comma>ADVANCED_PARMS<comma><quote>FILEGDB_IN_BEGIN_SQL<space>FILEGDB_IN_END_SQL<space>FILEGDB_OUT_BEGIN_SQL<space>FILEGDB_OUT_END_SQL<space>FILEGDB_OUT_ENABLE_LOAD_ONLY_MODE<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<at>Value<openparen>fme_feature_type<closeparen>:Output,ftp_feature_type_name_exp,<at>Value<openparen>fme_feature_type<closeparen>,ftp_writer,FILEGDB,ftp_geometry,geodb_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u987a<gt><lt>u5e8f<gt><lt>u7801<gt><comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>UID<comma>text<lt>openparen<gt>21<lt>closeparen<gt><comma>YSDM<comma>text<lt>openparen<gt>10<lt>closeparen<gt><comma>HLMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>HLDM<comma>text<lt>openparen<gt>14<lt>closeparen<gt><comma>HLBM<comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>XJXZQDM<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>XJXZQMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>SMMJ<comma>double<comma>SSJSYDDL<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>HLDJ<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>FKSQ<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>LYMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>LYBM<comma>text<lt>openparen<gt>16<lt>closeparen<gt><comma>SJNF<comma>text<lt>openparen<gt>4<lt>closeparen<gt><comma>BZ<comma>text<lt>openparen<gt>255<lt>closeparen<gt><comma><lt>u6302<gt><lt>u63a5<gt><lt>u53f7<gt><comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>SHAPE_Length<comma>double<comma>SHAPE_Area<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>filegdb_object_id_field<comma>OBJECTID<comma>filegdb_object_id_alias<comma>OBJECTID<comma>filegdb_shape_field<comma>SHAPE<comma>filegdb_shape_alias<comma>SHAPE<comma>filegdb_config_keyword<comma>DEFAULTS<comma>filegdb_xy_tolerance<comma><comma>filegdb_z_tolerance<comma>0.001<comma>filegdb_m_tolerance<comma>0.001;<at>Value<openparen>fme_feature_type<closeparen>:Output00,ftp_feature_type_name_exp,<at>Value<openparen>fme_feature_type<closeparen>,ftp_writer,FILEGDB,ftp_geometry,geodb_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u987a<gt><lt>u5e8f<gt><lt>u7801<gt><comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>UID<comma>text<lt>openparen<gt>21<lt>closeparen<gt><comma>YSDM<comma>text<lt>openparen<gt>10<lt>closeparen<gt><comma>HPMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>HPDM<comma>text<lt>openparen<gt>8<lt>closeparen<gt><comma>HPBM<comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>XJXZQDM<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>XJXZQMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>SMMJ<comma>double<comma>SSJSYDDL<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>FKSQ<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>LYMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>LYBM<comma>text<lt>openparen<gt>16<lt>closeparen<gt><comma>SJNF<comma>text<lt>openparen<gt>4<lt>closeparen<gt><comma>BZ<comma>text<lt>openparen<gt>255<lt>closeparen<gt><comma><lt>u6302<gt><lt>u63a5<gt><lt>u53f7<gt><comma>text<lt>openparen<gt>200<lt>closeparen<gt><comma>SHAPE_Length<comma>double<comma>SHAPE_Area<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>filegdb_object_id_field<comma>OBJECTID<comma>filegdb_object_id_alias<comma>OBJECTID<comma>filegdb_shape_field<comma>SHAPE<comma>filegdb_shape_alias<comma>SHAPE<comma>filegdb_config_keyword<comma>DEFAULTS<comma>filegdb_xy_tolerance<comma><comma>filegdb_z_tolerance<comma>0.001<comma>filegdb_m_tolerance<comma>0.001;<at>Value<openparen>fme_feature_type<closeparen>:Output01,ftp_feature_type_name_exp,<at>Value<openparen>fme_feature_type<closeparen>,ftp_writer,FILEGDB,ftp_geometry,geodb_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u987a<gt><lt>u5e8f<gt><lt>u7801<gt><comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>UID<comma>text<lt>openparen<gt>21<lt>closeparen<gt><comma>YSDM<comma>text<lt>openparen<gt>10<lt>closeparen<gt><comma>KTBM<comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>GTBGDCKTBM<comma>text<lt>openparen<gt>20<lt>closeparen<gt><comma>XJXZQDM<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>XJXZQMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>SMMJ<comma>double<comma>SSJSYDDL<comma>text<lt>openparen<gt>6<lt>closeparen<gt><comma>LYMC<comma>text<lt>openparen<gt>50<lt>closeparen<gt><comma>LYBM<comma>text<lt>openparen<gt>16<lt>closeparen<gt><comma>SJNF<comma>text<lt>openparen<gt>4<lt>closeparen<gt><comma>BZ<comma>text<lt>openparen<gt>255<lt>closeparen<gt><comma><lt>u6302<gt><lt>u63a5<gt><lt>u53f7<gt><comma>text<lt>openparen<gt>200<lt>closeparen<gt><comma>SHAPE_Length<comma>double<comma>SHAPE_Area<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,fme_configuration_group<comma><comma>fme_configuration_common_group<comma><comma>fme_feature_operation<comma>INSERT<comma>fme_table_handling<comma>CREATE_IF_MISSING<comma>fme_update_geometry<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_selection_group<comma><comma>fme_selection_method<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_match_columns<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_where_builder_clause<comma><lt>lt<gt>Unused<lt>gt<gt><comma>fme_table_creation_group<comma><comma>filegdb_object_id_field<comma>OBJECTID<comma>filegdb_object_id_alias<comma>OBJECTID<comma>filegdb_shape_field<comma>SHAPE<comma>filegdb_shape_alias<comma>SHAPE<comma>filegdb_config_keyword<comma>DEFAULTS<comma>filegdb_xy_tolerance<comma><comma>filegdb_z_tolerance<comma>0.001<comma>filegdb_m_tolerance<comma>0.001" }    WRITER_PARAMS { "ADVANCED,,BEGIN_SQL,,COORDINATE_SYSTEM_GRANULARITY,FEATURE_TYPE,DESTINATION_DATASETTYPE_VALIDATION,Yes,ENABLE_LOAD_ONLY_MODE,YES,END_SQL,,FEATURE_DATASET_HANDLING,WRITE,OVERWRITE_GEODB,NO,WRITE_UNZONED_UTC,No" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE AttributeCreator_10_OUTPUT  @SupplyAttributes(ENCODED,fme_template_feature_type,Output)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_feature_type<closeparen>,FeatureWriter))    INPUT Output00 FEATURE_TYPE AttributeCreator_11_OUTPUT  @SupplyAttributes(ENCODED,fme_template_feature_type,Output00)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_feature_type<closeparen>,FeatureWriter))    INPUT Output01 FEATURE_TYPE AttributeCreator_12_OUTPUT  @SupplyAttributes(ENCODED,fme_template_feature_type,Output01)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_feature_type<closeparen>,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

