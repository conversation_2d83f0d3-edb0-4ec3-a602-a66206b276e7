{"version": 3, "file": "use-calendar.mjs", "sources": ["../../../../../../packages/components/calendar/src/use-calendar.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport { useLocale } from '@element-plus/hooks'\nimport { debugWarn, isArray, isDate } from '@element-plus/utils'\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type { ComputedRef, SetupContext } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { CalendarDateType, CalendarEmits, CalendarProps } from './calendar'\n\nconst adjacentMonth = (start: Dayjs, end: Dayjs): [Dayjs, Dayjs][] => {\n  const firstMonthLastDay = start.endOf('month')\n  const lastMonthFirstDay = end.startOf('month')\n\n  // Whether the last day of the first month and the first day of the last month is in the same week\n  const isSameWeek = firstMonthLastDay.isSame(lastMonthFirstDay, 'week')\n  const lastMonthStartDay = isSameWeek\n    ? lastMonthFirstDay.add(1, 'week')\n    : lastMonthFirstDay\n\n  return [\n    [start, firstMonthLastDay],\n    [lastMonthStartDay.startOf('week'), end],\n  ]\n}\n\nconst threeConsecutiveMonth = (start: Dayjs, end: Dayjs): [Dayjs, Dayjs][] => {\n  const firstMonthLastDay = start.endOf('month')\n  const secondMonthFirstDay = start.add(1, 'month').startOf('month')\n\n  // Whether the last day of the first month and the second month is in the same week\n  const secondMonthStartDay = firstMonthLastDay.isSame(\n    secondMonthFirstDay,\n    'week'\n  )\n    ? secondMonthFirstDay.add(1, 'week')\n    : secondMonthFirstDay\n\n  const secondMonthLastDay = secondMonthStartDay.endOf('month')\n  const lastMonthFirstDay = end.startOf('month')\n\n  // Whether the last day of the second month and the last day of the last month is in the same week\n  const lastMonthStartDay = secondMonthLastDay.isSame(lastMonthFirstDay, 'week')\n    ? lastMonthFirstDay.add(1, 'week')\n    : lastMonthFirstDay\n\n  return [\n    [start, firstMonthLastDay],\n    [secondMonthStartDay.startOf('week'), secondMonthLastDay],\n    [lastMonthStartDay.startOf('week'), end],\n  ]\n}\n\nexport const useCalendar = (\n  props: CalendarProps,\n  emit: SetupContext<CalendarEmits>['emit'],\n  componentName: string\n) => {\n  const { lang } = useLocale()\n\n  const selectedDay = ref<Dayjs>()\n  const now = dayjs().locale(lang.value)\n\n  const realSelectedDay = computed<Dayjs | undefined>({\n    get() {\n      if (!props.modelValue) return selectedDay.value\n      return date.value\n    },\n    set(val) {\n      if (!val) return\n      selectedDay.value = val\n      const result = val.toDate()\n\n      emit(INPUT_EVENT, result)\n      emit(UPDATE_MODEL_EVENT, result)\n    },\n  })\n\n  // if range is valid, we get a two-digit array\n  const validatedRange = computed(() => {\n    if (\n      !props.range ||\n      !isArray(props.range) ||\n      props.range.length !== 2 ||\n      props.range.some((item) => !isDate(item))\n    )\n      return []\n    const rangeArrDayjs = props.range.map((_) => dayjs(_).locale(lang.value))\n    const [startDayjs, endDayjs] = rangeArrDayjs\n    if (startDayjs.isAfter(endDayjs)) {\n      debugWarn(componentName, 'end time should be greater than start time')\n      return []\n    }\n    if (startDayjs.isSame(endDayjs, 'month')) {\n      // same month\n      return calculateValidatedDateRange(startDayjs, endDayjs)\n    } else {\n      // two months\n      if (startDayjs.add(1, 'month').month() !== endDayjs.month()) {\n        debugWarn(\n          componentName,\n          'start time and end time interval must not exceed two months'\n        )\n        return []\n      }\n      return calculateValidatedDateRange(startDayjs, endDayjs)\n    }\n  })\n\n  const date: ComputedRef<Dayjs> = computed(() => {\n    if (!props.modelValue) {\n      return (\n        realSelectedDay.value ||\n        (validatedRange.value.length ? validatedRange.value[0][0] : now)\n      )\n    } else {\n      return dayjs(props.modelValue).locale(lang.value)\n    }\n  })\n  const prevMonthDayjs = computed(() => date.value.subtract(1, 'month').date(1))\n  const nextMonthDayjs = computed(() => date.value.add(1, 'month').date(1))\n  const prevYearDayjs = computed(() => date.value.subtract(1, 'year').date(1))\n  const nextYearDayjs = computed(() => date.value.add(1, 'year').date(1))\n\n  // https://github.com/element-plus/element-plus/issues/3155\n  // Calculate the validate date range according to the start and end dates\n  const calculateValidatedDateRange = (\n    startDayjs: Dayjs,\n    endDayjs: Dayjs\n  ): [Dayjs, Dayjs][] => {\n    const firstDay = startDayjs.startOf('week')\n    const lastDay = endDayjs.endOf('week')\n    const firstMonth = firstDay.get('month')\n    const lastMonth = lastDay.get('month')\n\n    // Current mouth\n    if (firstMonth === lastMonth) {\n      return [[firstDay, lastDay]]\n    }\n    // Two adjacent months\n    else if ((firstMonth + 1) % 12 === lastMonth) {\n      return adjacentMonth(firstDay, lastDay)\n    }\n    // Three consecutive months (compatible: 2021-01-30 to 2021-02-28)\n    else if (\n      firstMonth + 2 === lastMonth ||\n      (firstMonth + 1) % 11 === lastMonth\n    ) {\n      return threeConsecutiveMonth(firstDay, lastDay)\n    }\n    // Other cases\n    else {\n      debugWarn(\n        componentName,\n        'start time and end time interval must not exceed two months'\n      )\n      return []\n    }\n  }\n\n  const pickDay = (day: Dayjs) => {\n    realSelectedDay.value = day\n  }\n\n  const selectDate = (type: CalendarDateType) => {\n    const dateMap: Record<CalendarDateType, Dayjs> = {\n      'prev-month': prevMonthDayjs.value,\n      'next-month': nextMonthDayjs.value,\n      'prev-year': prevYearDayjs.value,\n      'next-year': nextYearDayjs.value,\n      today: now,\n    }\n\n    const day = dateMap[type]\n\n    if (!day.isSame(date.value, 'day')) {\n      pickDay(day)\n    }\n  }\n\n  return {\n    calculateValidatedDateRange,\n    date,\n    realSelectedDay,\n    pickDay,\n    selectDate,\n    validatedRange,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAKA,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AACtC,EAAE,MAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjD,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACjD,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;AACzE,EAAE,MAAM,iBAAiB,GAAG,UAAU,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC;AAC9F,EAAE,OAAO;AACT,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;AAC9B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;AAC5C,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK;AAC9C,EAAE,MAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjD,EAAE,MAAM,mBAAmB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrE,EAAE,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,mBAAmB,CAAC;AAC/I,EAAE,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAChE,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACjD,EAAE,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,iBAAiB,CAAC;AACxI,EAAE,OAAO;AACT,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC;AAC9B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,kBAAkB,CAAC;AAC7D,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;AAC5C,GAAG,CAAC;AACJ,CAAC,CAAC;AACU,MAAC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,aAAa,KAAK;AAC3D,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE,CAAC;AAC/B,EAAE,MAAM,WAAW,GAAG,GAAG,EAAE,CAAC;AAC5B,EAAE,MAAM,GAAG,GAAG,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC;AACnC,IAAI,GAAG,GAAG;AACV,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU;AAC3B,QAAQ,OAAO,WAAW,CAAC,KAAK,CAAC;AACjC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC;AACxB,KAAK;AACL,IAAI,GAAG,CAAC,GAAG,EAAE;AACb,MAAM,IAAI,CAAC,GAAG;AACd,QAAQ,OAAO;AACf,MAAM,WAAW,CAAC,KAAK,GAAG,GAAG,CAAC;AAC9B,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;AAClC,MAAM,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAChC,MAAM,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;AACvC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtH,MAAM,OAAO,EAAE,CAAC;AAChB,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9E,IAAI,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG,aAAa,CAAC;AACjD,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACtC,MAAM,SAAS,CAAC,aAAa,EAAE,4CAA4C,CAAC,CAAC;AAC7E,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;AAC9C,MAAM,OAAO,2BAA2B,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC/D,KAAK,MAAM;AACX,MAAM,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,QAAQ,CAAC,KAAK,EAAE,EAAE;AACnE,QAAQ,SAAS,CAAC,aAAa,EAAE,6DAA6D,CAAC,CAAC;AAChG,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP,MAAM,OAAO,2BAA2B,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC/D,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM;AAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AAC3B,MAAM,OAAO,eAAe,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACvG,KAAK,MAAM;AACX,MAAM,OAAO,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjF,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/E,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,EAAE,MAAM,2BAA2B,GAAG,CAAC,UAAU,EAAE,QAAQ,KAAK;AAChE,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAChD,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC7C,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,IAAI,UAAU,KAAK,SAAS,EAAE;AAClC,MAAM,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AACnC,KAAK,MAAM,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,EAAE,KAAK,SAAS,EAAE;AACpD,MAAM,OAAO,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC9C,KAAK,MAAM,IAAI,UAAU,GAAG,CAAC,KAAK,SAAS,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,EAAE,KAAK,SAAS,EAAE;AACpF,MAAM,OAAO,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACtD,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,aAAa,EAAE,6DAA6D,CAAC,CAAC;AAC9F,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK;AAC3B,IAAI,eAAe,CAAC,KAAK,GAAG,GAAG,CAAC;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,IAAI,KAAK;AAC/B,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,YAAY,EAAE,cAAc,CAAC,KAAK;AACxC,MAAM,YAAY,EAAE,cAAc,CAAC,KAAK;AACxC,MAAM,WAAW,EAAE,aAAa,CAAC,KAAK;AACtC,MAAM,WAAW,EAAE,aAAa,CAAC,KAAK;AACtC,MAAM,KAAK,EAAE,GAAG;AAChB,KAAK,CAAC;AACN,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;AACxC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AACnB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,2BAA2B;AAC/B,IAAI,IAAI;AACR,IAAI,eAAe;AACnB,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}