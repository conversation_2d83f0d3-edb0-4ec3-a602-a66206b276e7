@font-face {
  font-family: 'icomoon';
  src:  url('/icomoon.eot');
  src:  url('/icomoon.eot?#iefix') format('embedded-opentype'),
    url('/icomoon.woff') format('woff'),
    url('/icomoon.ttf') format('truetype'),
    url('/icomoon.svg#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}
.iconfont {
  font-family: 'icomoon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* e900: 首页, ea1c: 我的工具, e93a: 工具市场 */
.icon-home:before { content: "\e900"; }
.icon-tools:before { content: "\ea1c"; }
.icon-market:before { content: "\e93a"; } 