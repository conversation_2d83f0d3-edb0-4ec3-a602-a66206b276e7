import{d as lt,r as d,M as ce,u as ot,N as Ve,L as me,o as nt,a as rt,c as x,b as l,p as xe,e as t,y as c,w as a,f as u,D as C,U as w,E as p,i as Me,l as m,af as Y,ag as pe,z as j,R as $,A as ve,I as Pe,J as it,B as dt,v as Se,t as Te,ah as ut,q as fe,T as G,k as _,_ as ct}from"./index-LgsLWi3x.js";const mt={class:"settings"},pt={class:"page-header"},vt={class:"page-title"},ft={key:0,class:"loading-container"},gt={class:"loading-content"},_t={class:"loading-icon"},bt={key:1,class:"content-wrapper"},yt={class:"tab-content"},wt={class:"tab-header"},kt={class:"tab-title"},ht={class:"collapse-title"},Vt={class:"card-header"},xt={class:"card-title"},Mt={class:"card-actions"},Pt={class:"task-stats"},St={class:"stat-item"},Tt={class:"stat-value running"},Ct={class:"stat-item"},It={class:"stat-value pending"},Nt={class:"task-list-container"},Ut={key:0,class:"no-tasks"},Rt={key:1},Dt={class:"tab-content"},Ot={class:"tab-header"},Lt={class:"tab-title"},zt={class:"collapse-title"},qt={class:"card-header"},Bt={class:"card-title"},Jt={class:"action-buttons"},At={class:"tab-content"},Ft={class:"tab-header"},Et={class:"tab-title"},Yt={class:"collapse-title"},jt={class:"card-header"},$t={class:"card-title"},Gt={class:"port-control"},Ht={class:"port-status"},Kt={class:"action-buttons"},Qt={class:"tab-content"},Wt={class:"tab-header"},Xt={class:"tab-title"},Zt={class:"collapse-title"},ea={class:"card-header"},ta={class:"card-title"},aa={class:"action-buttons"},sa={class:"tab-content"},la={class:"tab-header"},oa={class:"tab-title"},na={class:"collapse-title"},ra={class:"card-header"},ia={class:"card-title"},da={key:2,class:"restart-mask"},ua={class:"restart-mask-content"},ca={class:"restart-title"},ma=lt({__name:"SettingsView",setup(pa){const H=d("monitor"),f=d("task-monitor"),r=ce({limits:1,server_host:"127.0.0.1",server_port:9997,mode:"offline"}),i=ce({visibleItems:{home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},itemModes:{home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},itemProgress:{home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:75,settings:75,about:75}}),Ce=[{key:"home",label:"首页",description:"系统首页和仪表板"},{key:"tools",label:"我的工具",description:"个人工具管理"},{key:"market",label:"工具市场",description:"浏览和申请工具"},{key:"quality",label:"数据质检",description:"数据质量检查平台"},{key:"coordinate",label:"坐标转换",description:"坐标系转换工具"},{key:"cadtogis",label:"CAD转GIS",description:"CAD文件转换工具"},{key:"requirement",label:"需求提交",description:"功能需求申请"},{key:"layerPreview",label:"图层预览",description:"地理数据预览"},{key:"userManagement",label:"用户管理",description:"用户账户管理（管理员）"},{key:"settings",label:"设置",description:"系统设置（管理员）"},{key:"about",label:"关于",description:"系统信息"}],k=d(null),J=d(null),S=d([]),I=d(!1),K=d(!1),N=d(!1),Ie=d(),Ne=d(),A=d(!1),U=d("."),Q=d(!1),h=d(!1),V=d(!1),Ue=ot(),M=d(!1),F=d(!0),W=d(!0),X=d(!0),Z=d(!0),ee=d([]),te=d({running_count:0,pending_count:0}),ae=d(!1);let T=null;const b=ce({tableName:"task_records_all",dateRange:null}),se=d(!1),Re=[{text:"最近一周",value:()=>{const s=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*7),[e,s]}},{text:"最近一个月",value:()=>{const s=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*30),[e,s]}},{text:"最近三个月",value:()=>{const s=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*90),[e,s]}}];let R=null,le=null;Ve(()=>r.server_port,()=>{le&&clearTimeout(le),le=setTimeout(()=>{h.value=!1,V.value=!1},300)},{flush:"post"}),Ve(()=>H.value,s=>{const e={monitor:"task-monitor",data:"data-export",config:"backend-config",ui:"nav-control",user:"register-control"};f.value=e[s]||"task-monitor"});const De=async()=>{W.value=!0;try{const s=await w.post("/api/settings/get",{},{timeout:1e4});s.data.success?(Object.assign(r,s.data.data),k.value=JSON.parse(JSON.stringify(s.data.data)),r.mode==="online"&&ge().catch(console.error)):p.error(s.data.message||"获取设置失败")}catch(s){console.error("获取设置失败:",s),p.error("获取设置失败")}finally{W.value=!1,ne()}},ge=async()=>{try{const s=await w.post("/api/settings/ips",{},{timeout:5e3});s.data.success&&Array.isArray(s.data.data)?(S.value=s.data.data,r.mode==="online"&&!S.value.includes(r.server_host)&&(r.server_host=S.value[0]||"127.0.0.1")):S.value=["127.0.0.1"]}catch(s){console.error("获取IP列表失败:",s),S.value=["127.0.0.1"]}},Oe=async s=>{s==="offline"?r.server_host="127.0.0.1":setTimeout(()=>{ge().catch(console.error)},0)},_e=me(()=>k.value&&r.server_port!==k.value.server_port),be=me(()=>k.value?r.limits!==k.value.limits||r.server_host!==k.value.server_host||r.server_port!==k.value.server_port||r.mode!==k.value.mode:!1),Le=me(()=>be.value?_e.value?h.value&&V.value&&!I.value:!I.value:!1),ze=async()=>{if(!be.value){p.info("未检测到任何更改，无需保存");return}if(_e.value&&(!h.value||!V.value)){p.warning("端口更改后必须检测端口占用，并确保端口可用后才能保存！");return}G.confirm("确定要保存当前设置吗？保存后需重启服务器才能生效。","确认保存",{confirmButtonText:"保存",cancelButtonText:"取消",type:"warning"}).then(async()=>{I.value=!0;try{const s=await w.post("/api/settings/set",{...r});s.data.success?(p.success("设置已保存，将在重启服务器后生效"),k.value=JSON.parse(JSON.stringify(r))):p.error(s.data.message||"保存失败")}catch{p.error("保存失败")}finally{I.value=!1}})},qe=async()=>{G.confirm("确认要重启服务器吗？","确认重启",{confirmButtonText:"重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{N.value=!0;try{const s=await w.post("/api/settings/restart",{});s.data.success?(p.success("服务器重启命令已发送"),A.value=!0,ye(),we()):s.data.needForce?G.confirm(s.data.message||"当前有任务正在进行，是否强制重启？","警告",{confirmButtonText:"强制重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{N.value=!0;try{const e=await w.post("/api/settings/restart",{force:!0});e.data.success?(p.success("服务器重启命令已发送"),A.value=!0,ye(),we()):p.error(e.data.message||"重启失败")}finally{N.value=!1}}):p.error(s.data.message||"重启失败")}catch{p.error("重启失败")}finally{N.value=!1}})};function ye(){U.value=".",R&&clearInterval(R),R=setInterval(()=>{U.value=U.value.length<3?U.value+".":"."},500)}function we(){const s=window.location.protocol;let e=r.server_host;e==="0.0.0.0"&&(e=window.location.hostname||"127.0.0.1");const n=r.server_port;setTimeout(()=>{A.value=!1,Ue.logout(),window.location.href=`${s}//${e}:${n}/`},5e3)}const Be=async()=>{Q.value=!0;try{const s=await w.post("/api/settings/check_port",{port:r.server_port});await new Promise(e=>setTimeout(e,200)),h.value=!0,s.data.success?V.value=!0:V.value=!1}catch{await new Promise(e=>setTimeout(e,200)),h.value=!0,V.value=!1}finally{Q.value=!1}},Je=async()=>{X.value=!0;try{const s=await w.post("/api/settings/get-nav-settings",{},{timeout:8e3});if(s.data.success){const e={...s.data.data.visibleItems,userManagement:!0,settings:!0};Object.assign(i.visibleItems,e),s.data.data.itemModes&&Object.assign(i.itemModes,s.data.data.itemModes),s.data.data.itemProgress&&Object.assign(i.itemProgress,s.data.data.itemProgress),J.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:i.itemModes,itemProgress:i.itemProgress}))}else{const e={...i.visibleItems,userManagement:!0,settings:!0};J.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:i.itemModes,itemProgress:i.itemProgress}))}}catch(s){console.error("获取导航栏设置失败:",s);const e={...i.visibleItems,userManagement:!0,settings:!0};J.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:i.itemModes,itemProgress:i.itemProgress}))}finally{X.value=!1,ne()}},Ae=async()=>{K.value=!0;try{const s={visibleItems:{...i.visibleItems,userManagement:!0,settings:!0},itemModes:{...i.itemModes,userManagement:"production",settings:"production"},itemProgress:{...i.itemProgress,userManagement:100,settings:100}},e=await w.post("/api/settings/set-nav-settings",s);e.data.success?(p.success("导航栏设置已保存"),i.visibleItems=s.visibleItems,i.itemModes=s.itemModes,i.itemProgress=s.itemProgress,J.value=JSON.parse(JSON.stringify(s)),window.dispatchEvent(new CustomEvent("navSettingsChanged",{detail:s}))):p.error(e.data.message||"保存导航栏设置失败")}catch{p.error("保存导航栏设置失败")}finally{K.value=!1}},Fe=()=>{G.confirm("确定要重置导航栏设置为默认值吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{i.visibleItems={home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},i.itemModes={home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},i.itemProgress={home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:100,settings:100,about:75},p.success("已重置为默认设置")})},Ee=async()=>{Z.value=!0;try{const s=await Me.post("/api/get_allow_register",{},{timeout:5e3});s.data&&typeof s.data.allow_register=="boolean"?M.value=s.data.allow_register:s.data&&typeof s.data.allow_register=="string"?M.value=s.data.allow_register==="true":M.value=!1}catch(s){console.error("获取注册开关状态失败:",s),M.value=!1}finally{Z.value=!1,ne()}},Ye=async s=>{try{await Me.post("/api/set_allow_register",{allow_register:s})}catch{M.value=!s}},oe=async()=>{ae.value=!0;try{const s=await w.post("/api/task/current/list",{});s.data.success?(ee.value=s.data.data.tasks||[],te.value={running_count:s.data.data.running_count||0,pending_count:s.data.data.pending_count||0}):p.error(s.data.message||"获取任务列表失败")}catch(s){console.error("获取任务列表失败:",s),p.error("获取任务列表失败")}finally{ae.value=!1}},je=s=>{if(!s)return"";try{return new Date(s).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return s}},$e=()=>{T&&clearInterval(T),oe(),T=setInterval(()=>{oe()},5e3)},Ge=()=>{T&&(clearInterval(T),T=null)},He=async()=>{var s,e;if(!b.tableName){p.warning("请选择要导出的数据表");return}se.value=!0;try{const n={table_name:b.tableName,date_range:b.dateRange},re=await w.post("/api/task/export-excel",n,{responseType:"blob"}),ie=new Blob([re.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),g=window.URL.createObjectURL(ie),P=document.createElement("a");P.href=g;const v=new Date().toISOString().slice(0,19).replace(/:/g,"-");P.download=`${b.tableName}_${v}.xlsx`,document.body.appendChild(P),P.click(),document.body.removeChild(P),window.URL.revokeObjectURL(g),p.success("导出成功")}catch(n){console.error("导出失败:",n),(e=(s=n.response)==null?void 0:s.data)!=null&&e.message?p.error(n.response.data.message):p.error("导出失败")}finally{se.value=!1}},Ke=()=>{b.tableName="task_records_all",b.dateRange=null},ne=()=>{F.value=W.value||X.value||Z.value},Qe=async()=>{F.value=!0;const s=[De().catch(console.error),Je().catch(console.error),Ee().catch(console.error)];try{await Promise.allSettled(s)}catch(e){console.error("加载数据时发生错误:",e)}setTimeout(()=>{F.value=!1},100)};return nt(()=>{Qe(),$e()}),rt(()=>{Ge(),R&&clearInterval(R)}),(s,e)=>{const n=u("el-icon"),re=u("Loading"),ie=u("el-skeleton"),g=u("el-button"),P=u("el-empty"),v=u("el-table-column"),We=u("el-tag"),ke=u("el-table"),D=u("el-card"),O=u("el-collapse-item"),L=u("el-collapse"),z=u("el-tab-pane"),q=u("el-option"),de=u("el-select"),y=u("el-form-item"),Xe=u("el-date-picker"),E=u("el-form"),ue=u("el-input-number"),he=u("el-radio"),Ze=u("el-radio-group"),et=u("el-input"),tt=u("el-checkbox"),at=u("el-switch"),st=u("el-tabs");return _(),x("div",mt,[l("div",pt,[l("h2",vt,[t(n,{class:"title-icon"},{default:a(()=>[t(m(Y))]),_:1}),e[14]||(e[14]=c(" 系统设置 "))]),e[15]||(e[15]=l("p",{class:"page-description"},"管理系统配置、监控任务状态、控制功能模块",-1))]),F.value?(_(),x("div",ft,[l("div",gt,[l("div",_t,[t(n,{class:"is-loading"},{default:a(()=>[t(re)]),_:1})]),e[16]||(e[16]=l("div",{class:"loading-text"},"正在加载设置...",-1)),t(ie,{rows:6,animated:"",class:"loading-skeleton"})])])):(_(),x("div",bt,[t(st,{modelValue:H.value,"onUpdate:modelValue":e[13]||(e[13]=o=>H.value=o),type:"border-card",class:"settings-tabs"},{default:a(()=>[t(z,{label:"系统监控",name:"monitor"},{default:a(()=>[l("div",yt,[l("div",wt,[l("h3",kt,[t(n,null,{default:a(()=>[t(m(pe))]),_:1}),e[17]||(e[17]=c(" 系统监控 "))]),e[18]||(e[18]=l("p",{class:"tab-description"},"监控任务状态、系统运行情况",-1))]),t(L,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=o=>f.value=o),accordion:"",class:"settings-collapse"},{default:a(()=>[t(O,{title:"任务监控",name:"task-monitor"},{title:a(()=>[l("div",ht,[t(n,{class:"collapse-icon"},{default:a(()=>[t(m(pe))]),_:1}),e[19]||(e[19]=l("span",null,"任务监控",-1))])]),default:a(()=>[t(D,{class:"settings-card task-monitor-card",shadow:"never"},{header:a(()=>[l("div",Vt,[l("span",xt,[t(n,{class:"card-icon"},{default:a(()=>[t(m(pe))]),_:1}),e[20]||(e[20]=c(" 任务监控 "))]),l("div",Mt,[t(g,{type:"primary",size:"small",onClick:oe,loading:ae.value},{default:a(()=>[t(n,null,{default:a(()=>[t(m(j))]),_:1}),e[21]||(e[21]=c(" 刷新 "))]),_:1},8,["loading"])])])]),default:a(()=>[l("div",Pt,[l("div",St,[e[22]||(e[22]=l("span",{class:"stat-label"},"运行中",-1)),l("span",Tt,C(te.value.running_count),1)]),l("div",Ct,[e[23]||(e[23]=l("span",{class:"stat-label"},"等待中",-1)),l("span",It,C(te.value.pending_count),1)])]),l("div",Nt,[ee.value.length===0?(_(),x("div",Ut,[t(P,{description:"暂无任务"})])):(_(),x("div",Rt,[t(ke,{data:ee.value,border:"",size:"small",style:{width:"100%"}},{default:a(()=>[t(v,{prop:"task_id",label:"任务ID",width:"180"}),t(v,{label:"状态",width:"80"},{default:a(o=>[t(We,{type:o.row.status==="running"?"success":"warning",size:"small"},{default:a(()=>[c(C(o.row.status==="running"?"运行中":"等待中"),1)]),_:2},1032,["type"])]),_:1}),t(v,{prop:"tool_name",label:"工具名称","min-width":"120"}),t(v,{prop:"submitter",label:"提交者",width:"100"}),t(v,{prop:"project",label:"项目",width:"120"}),t(v,{prop:"file_name",label:"文件名","min-width":"150","show-overflow-tooltip":""}),t(v,{prop:"up_nums",label:"文件数",width:"80",align:"center"}),t(v,{label:"提交时间",width:"160"},{default:a(o=>[c(C(je(o.row.submit_time)),1)]),_:1})]),_:1},8,["data"])]))])]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),_:1}),t(z,{label:"数据管理",name:"data"},{default:a(()=>[l("div",Dt,[l("div",Ot,[l("h3",Lt,[t(n,null,{default:a(()=>[t(m($))]),_:1}),e[24]||(e[24]=c(" 数据管理 "))]),e[25]||(e[25]=l("p",{class:"tab-description"},"导出运行记录、管理数据表",-1))]),t(L,{modelValue:f.value,"onUpdate:modelValue":e[3]||(e[3]=o=>f.value=o),accordion:"",class:"settings-collapse"},{default:a(()=>[t(O,{title:"数据导出",name:"data-export"},{title:a(()=>[l("div",zt,[t(n,{class:"collapse-icon"},{default:a(()=>[t(m($))]),_:1}),e[26]||(e[26]=l("span",null,"数据导出",-1))])]),default:a(()=>[t(D,{class:"settings-card data-export-card",shadow:"never"},{header:a(()=>[l("div",qt,[l("span",Bt,[t(n,{class:"card-icon"},{default:a(()=>[t(m($))]),_:1}),e[27]||(e[27]=c(" 运行记录导出 "))])])]),default:a(()=>[t(E,{"label-width":"120px",class:"export-form"},{default:a(()=>[t(y,{label:"选择数据表"},{default:a(()=>[t(de,{modelValue:b.tableName,"onUpdate:modelValue":e[1]||(e[1]=o=>b.tableName=o),placeholder:"请选择要导出的数据表"},{default:a(()=>[t(q,{label:"任务记录总表",value:"task_records_all"}),t(q,{label:"当前任务记录",value:"task_records"})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"日期范围"},{default:a(()=>[t(Xe,{modelValue:b.dateRange,"onUpdate:modelValue":e[2]||(e[2]=o=>b.dateRange=o),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",shortcuts:Re},null,8,["modelValue"])]),_:1}),t(y,{class:"export-actions"},{default:a(()=>[l("div",Jt,[t(g,{type:"primary",onClick:He,loading:se.value,size:"large"},{default:a(()=>[t(n,null,{default:a(()=>[t(m($))]),_:1}),e[28]||(e[28]=c(" 导出Excel "))]),_:1},8,["loading"]),t(g,{onClick:Ke,size:"large"},{default:a(()=>[t(n,null,{default:a(()=>[t(m(j))]),_:1}),e[29]||(e[29]=c(" 重置 "))]),_:1})])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),_:1}),t(z,{label:"系统配置",name:"config"},{default:a(()=>[l("div",At,[l("div",Ft,[l("h3",Et,[t(n,null,{default:a(()=>[t(m(Y))]),_:1}),e[30]||(e[30]=c(" 系统配置 "))]),e[31]||(e[31]=l("p",{class:"tab-description"},"配置服务器参数、运行模式",-1))]),t(L,{modelValue:f.value,"onUpdate:modelValue":e[9]||(e[9]=o=>f.value=o),accordion:"",class:"settings-collapse"},{default:a(()=>[t(O,{title:"后端配置",name:"backend-config"},{title:a(()=>[l("div",Yt,[t(n,{class:"collapse-icon"},{default:a(()=>[t(m(Te))]),_:1}),e[32]||(e[32]=l("span",null,"后端配置",-1))])]),default:a(()=>[t(D,{class:"settings-card",shadow:"never"},{header:a(()=>[l("div",jt,[l("span",$t,[t(n,{class:"card-icon"},{default:a(()=>[t(m(Te))]),_:1}),e[33]||(e[33]=c(" 后端配置 "))])])]),default:a(()=>[t(E,{"label-width":"120px",model:r,ref_key:"formRef",ref:Ie,class:"settings-form"},{default:a(()=>[t(y,{label:"最大并发任务数"},{default:a(()=>[t(ue,{modelValue:r.limits,"onUpdate:modelValue":e[4]||(e[4]=o=>r.limits=o),min:1,max:8},null,8,["modelValue"])]),_:1}),t(y,{label:"运行模式"},{default:a(()=>[t(Ze,{modelValue:r.mode,"onUpdate:modelValue":e[5]||(e[5]=o=>r.mode=o),onChange:Oe},{default:a(()=>[t(he,{label:"offline"},{default:a(()=>e[34]||(e[34]=[c("离线（本地127.0.0.1）")])),_:1}),t(he,{label:"online"},{default:a(()=>e[35]||(e[35]=[c("在线（局域网/公网）")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"IP"},{default:a(()=>[r.mode==="online"?(_(),ve(de,{key:0,modelValue:r.server_host,"onUpdate:modelValue":e[6]||(e[6]=o=>r.server_host=o),filterable:"",placeholder:"请选择IP","filter-method":null,remote:!1},{default:a(()=>[(_(!0),x(Pe,null,it(S.value,o=>(_(),ve(q,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):(_(),ve(et,{key:1,modelValue:r.server_host,"onUpdate:modelValue":e[7]||(e[7]=o=>r.server_host=o),disabled:""},null,8,["modelValue"]))]),_:1}),t(y,{label:"端口"},{default:a(()=>[l("div",Gt,[t(ue,{modelValue:r.server_port,"onUpdate:modelValue":e[8]||(e[8]=o=>r.server_port=o),min:1024,max:65535,class:"port-input"},null,8,["modelValue"]),t(g,{size:"small",class:"port-check-btn",onClick:Be,loading:Q.value},{default:a(()=>e[36]||(e[36]=[c("检测端口占用")])),_:1},8,["loading"])]),l("div",Ht,[l("div",{class:dt({"status-warning":!h.value,"status-error":h.value&&!V.value,"status-success":h.value&&V.value})},C(h.value?V.value?"端口可用，可以保存设置。":"端口未通过占用检测，无法保存！":'端口更改后必须点击"检测端口占用"并通过检测后才能保存。'),3)])]),_:1}),t(y,{class:"form-actions"},{default:a(()=>[l("div",Kt,[t(g,{type:"primary",onClick:ze,loading:I.value,disabled:!Le.value,size:"large",class:"save-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(m(Se))]),_:1}),e[37]||(e[37]=c(" 保存设置 "))]),_:1},8,["loading","disabled"]),t(g,{type:"danger",onClick:qe,loading:N.value,size:"large",class:"restart-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(m(j))]),_:1}),e[38]||(e[38]=c(" 重启服务器 "))]),_:1},8,["loading"])])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),_:1}),t(z,{label:"界面控制",name:"ui"},{default:a(()=>[l("div",Qt,[l("div",Wt,[l("h3",Xt,[t(n,null,{default:a(()=>[t(m(ut))]),_:1}),e[39]||(e[39]=c(" 界面控制 "))]),e[40]||(e[40]=l("p",{class:"tab-description"},"控制导航栏显示、功能模块可见性",-1))]),t(L,{modelValue:f.value,"onUpdate:modelValue":e[10]||(e[10]=o=>f.value=o),accordion:"",class:"settings-collapse"},{default:a(()=>[t(O,{title:"导航栏控制",name:"nav-control"},{title:a(()=>[l("div",Zt,[t(n,{class:"collapse-icon"},{default:a(()=>[t(m(Y))]),_:1}),e[41]||(e[41]=l("span",null,"导航栏控制",-1))])]),default:a(()=>[t(D,{class:"settings-card nav-control-card",shadow:"never"},{header:a(()=>[l("div",ea,[l("span",ta,[t(n,{class:"card-icon"},{default:a(()=>[t(m(Y))]),_:1}),e[42]||(e[42]=c(" 导航栏显示控制 "))])])]),default:a(()=>[t(E,{"label-width":"120px",model:i,ref_key:"navFormRef",ref:Ne,class:"settings-form nav-form"},{default:a(()=>[t(ke,{data:Ce,border:"",size:"small",class:"nav-items-table",style:{width:"100%"}},{default:a(()=>[t(v,{label:"显示",width:"60"},{default:a(o=>[t(tt,{modelValue:i.visibleItems[o.row.key],"onUpdate:modelValue":B=>i.visibleItems[o.row.key]=B,disabled:o.row.key==="userManagement"||o.row.key==="settings"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),t(v,{prop:"label",label:"菜单项","min-width":"80"}),t(v,{label:"模式",width:"100"},{default:a(o=>[t(de,{modelValue:i.itemModes[o.row.key],"onUpdate:modelValue":B=>i.itemModes[o.row.key]=B,size:"small",disabled:o.row.key==="userManagement"||o.row.key==="settings",style:{width:"80px"}},{default:a(()=>[t(q,{label:"生产",value:"production"}),t(q,{label:"开发",value:"development"})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),t(v,{label:"开发进度",width:"120"},{default:a(o=>[i.itemModes[o.row.key]==="development"?(_(),x(Pe,{key:0},[t(ue,{modelValue:i.itemProgress[o.row.key],"onUpdate:modelValue":B=>i.itemProgress[o.row.key]=B,min:0,max:100,step:1,size:"small",style:{width:"80px","vertical-align":"middle"},disabled:o.row.key==="userManagement"||o.row.key==="settings"},null,8,["modelValue","onUpdate:modelValue","disabled"]),e[43]||(e[43]=l("span",{class:"progress-value"},"%",-1))],64)):xe("",!0)]),_:1})]),_:1}),t(y,{class:"nav-actions"},{default:a(()=>[l("div",aa,[t(g,{type:"primary",onClick:Ae,loading:K.value,size:"large",class:"save-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(m(Se))]),_:1}),e[44]||(e[44]=c(" 保存导航设置 "))]),_:1},8,["loading"]),t(g,{onClick:Fe,size:"large",class:"reset-btn"},{default:a(()=>[t(n,null,{default:a(()=>[t(m(j))]),_:1}),e[45]||(e[45]=c(" 重置为默认 "))]),_:1})])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),_:1}),t(z,{label:"用户管理",name:"user"},{default:a(()=>[l("div",sa,[l("div",la,[l("h3",oa,[t(n,null,{default:a(()=>[t(m(fe))]),_:1}),e[46]||(e[46]=c(" 用户管理 "))]),e[47]||(e[47]=l("p",{class:"tab-description"},"控制用户注册、权限管理",-1))]),t(L,{modelValue:f.value,"onUpdate:modelValue":e[12]||(e[12]=o=>f.value=o),accordion:"",class:"settings-collapse"},{default:a(()=>[t(O,{title:"注册控制",name:"register-control"},{title:a(()=>[l("div",na,[t(n,{class:"collapse-icon"},{default:a(()=>[t(m(fe))]),_:1}),e[48]||(e[48]=l("span",null,"注册控制",-1))])]),default:a(()=>[t(D,{class:"settings-card register-control-card",shadow:"never"},{header:a(()=>[l("div",ra,[l("span",ia,[t(n,{class:"card-icon"},{default:a(()=>[t(m(fe))]),_:1}),e[49]||(e[49]=c(" 注册功能控制 "))])])]),default:a(()=>[t(E,{"label-width":"120px",class:"settings-form"},{default:a(()=>[t(y,{label:"是否开放注册"},{default:a(()=>[t(at,{modelValue:M.value,"onUpdate:modelValue":e[11]||(e[11]=o=>M.value=o),"active-text":"开放","inactive-text":"关闭",onChange:Ye},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"])])),A.value?(_(),x("div",da,[l("div",ua,[l("div",ca,"服务器重启中"+C(U.value),1),e[50]||(e[50]=l("div",{class:"restart-desc"},"请勿关闭页面，重启完成后将自动跳转...",-1))])])):xe("",!0)])}}}),fa=ct(ma,[["__scopeId","data-v-0e3a2f1f"]]);export{fa as default};
