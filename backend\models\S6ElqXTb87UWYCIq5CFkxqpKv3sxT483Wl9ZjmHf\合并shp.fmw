#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\工具\新建文件夹\合并shp.fmw
#          --dir "$(FME_MF_DIR)2023"
#          --PARAMETER ""
#          --dir_2 "$(DEF_VAL)"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAA7CAIAAADacUsCAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAbs0lEQVR4nO2daXwb5bnon3cWbdZiOd4UL1q8KSZxEpNAKAnZU0gLhMKFlttSKJxbltN7b+85PfRc2v7oaQ+3pU0XSoGyppRSIFwKBUKx4+AkkBCSOHGc2PKmzbZGtmTJkiyNpNnOh3Fkx5ZlOSVO7Mz/02g0yzOj0fM+6ztIEASQkJCQmA9gF1sACQkJiWyRFJaEhMS8QVJYEhIS8wZJYUlISMwbJIUlISExb5AUloSExLxBUlgSEhLzBklhSUhIzBskhSUhITFvkBSWhITEvIG42AJISMwPPmg+EtVUicu8ABiaxb5KGTYaZ5Ag9LYf//e7b7gg8l0eSApLQiIrgqqK8OnTlKnGcHYN5XwX4EaDCcDZOWF9J+WsMZjO2VerQAHPKRJDuvK61tPty5fWzp3cCwtJYUlIZImtpfmlYNU9VHdzECL6u280OEmf7+9gMlPN78KGGoOps2VXl+Fu0jcAVPNLdpNZ71xce3c1OGuUlnhJbgQHxpVMXOyrmN8gabYGCYls2HWwP86TWW9+jp2Vq8L7KS8CyNEV3L++6ILId3kgKSwJiWzp7OlFCHX4IEejQwCAAGb692BI4AUkAJQrQgBQXWGZAzkXMJLCkpDIBM/zLpfL7/cXFxcfOHSY43gVLPLXrTZmpa8gR4ZFkzwAHHrnDz955OE5EHhhs6BiWFE6kaOUX2wpJBYOvb29iUTCbDYXFxfzPJ+n14cjkZZ9+0ClAYC+xiYfjOY/8PC6LA6F0GzSihLTsEAsrOMdjo/aBmIciXPxZWXqLatr0azSzgAgACAAAKVCcSEklJh3OBwOnU6Xl5c39avbvvlPAMCwDEnMENXCMYzjeQB484/PXQghLzcWgsIaCoy81NjV09UWjYTKKpcWLTbxSVq9qHhWB8EQ0NEITsgJmUwKi0oIgmCz2ZYsWTJpfevpjj6uwOvpL0nE3ZXW8pmOk3IJW95//q4779DpdBiGqdXqCyP1wmchuITH211y7SKEsLwCA4YQIBSLDUTaQrChxgBAOWFCUUwn1Qzi+kl5nFh4WIbYeHgY05fyPI9hUg/AZY3b7TYajVPXJ5MJHnjvgLu35RAGdcdfT9Y/YnU//Q7M5BhWVFUeP3HiqlWr/H4/SZIpD5HjuKqqKplMdgEuYgGyECysDz9p7QirP/7gNW1eQduRpkVFpV/Yurm/xxd0ugAcQdOGawF8JjPb3GW4u5pqbg46AcABpmv1pmpobiY2fLvOBHQ4QArxGB1TF5rvX1889worNBp9/5MzkYSglaPrr6nVa6VB+GLS3t5eW5u+vLO5MzzsHwIAjufxmZ4TEhcYDgHAjjVmHMenbiAIQmtra0VFhUajme4guw+7lTkaAJARKMnO4g+rJDGa4TmO9Q047/vy6ux3vGRZCAorHKWferfF7XbS0dESizVHpZMRvEqjF3ghm0iWwPMIw+SkjGFZQqb45MM3Xvzpd+ZA7InQ8cRv3zw0QrNMgjaUVYb8nuqyRQjNQmnyvCCO2Qihr29dfqEEvWzo6OiY6g+K7DkVSDJJ734n2pbGJXT1gLFy/GPKJbTmBM1m83Sn6+rqYhhGpVIZDAa5XM7zfCwWA4Cm5uamA4evWHq9WEmfq8JHYhzl7ARTjWHcRRhboM5xJgAACtSk0+3ChCTIc7WBlnu/8bVZ34hLjIXgEmpzlF9ZY94rx4I0jyXC66qKTzuLjMVAYKi3SzBWTn6GJoEh4AUAgBuW5gLAvevmWlsBwN7P2gmtIdR3lAfBN9hnrllx+uCninVrxG4PasqDOMmfFZ/gvGiYowMYqfrTYd83rimYO+kXHBlG8aMtJ1lYfOLTA0k+agI4+HQTWWGmvFB/l8X99Av+io0AFrDbYNuOSf6kY4A62nLi9lu/kvaw1dXVAEDTNEVRiUSCYRiCILRaLWCETpNztsK+q/pBa9euF4KmDfrmd6m7lxJHHeA0wwZH+9vvUv97afB9gC+ZAcYfDJ5jMYFRqLRROv653JmLzpxaWHQ8oVR8/mUHfr/f5/MFg8H8/Py2zi4v5UX5SzCna6gS9X8s5Ec/slVs3AZAEdvXbEqze0phRboP3H7LTZ+7eNmwu+l4zwhxfP97+gKDvaOlxGzVaU15a7TUrubguFd7fZ0JADpbdr0UPNefpZo7YUONLjgoIzEmEc0rMn3zWilvcP4Eg8FEIlFcnD5v8/6pQGw0rNbmzqoOy/npXx+4757ZShKPxx/5z8ertnxb/ChaWNnvXqAm3V4fl6RV2vwHN5fO9uyXIHOksFraHU2nBuIgw7j48jL1xlXW8y5LUSmVEz/29/cLglBWVsbzPAAcPXassrLqMw8CAAJDLD/z1aUU1oZK5YXQp9lw5FTP3s7QyU8+lCtzchcV5xUYtGq1Or8ki107KScYTDUAwDIJOuwn5Or8PL1kYf0jdHV1VVVVTfeI3vu/Hv7afd8FgKySMzwHGA4AW5bNLm09kSPHWwDg0f+38//86JcIISTwwozhAoEFRAgCaBkPAOTkaJZaq85bgEuHuVBYXn9w175u35CX41hzzXKWZQU2rtbPvuxgNIyTikllB2mDoz/85R/Wbt7O8wKWRQxLnCrkvd1//N1jP5iVSJ8vf2k46gqTpFzJ8ywEevP02lJjBQgCzKzZEQAIAv/Mzkf/57e/BQCbrlt74eVdyHR2dtbU1KT96mjLSS9abDtzsrZuFY51ORsAtllT3t/Z4IPN1WMVoxApC6vv6DuVZqNCJtPr9dNFxzLzwekRW1uLsaIm4m+ikzcBjAc6pgY9dEo8RHMAMHCyYZFWuWrlilgsVlxcrNPpzuPUlw5zEcNqsbkio6OFJWZSJn//z79bbKo219RFQiE4OyMHlSZGM5loyF+taRuI5LP4uHoKBoNT6/pefOU16+pNfV6/0WhhecHVY4NKgLMP0FREC2vNpptf/svrV1+5sqa6ejoZfv/8S+JCluH8FASGszwHALft2FGUr5+6Acdxm5aXn+60B6KR0eG+gSEPo7+PJ2HQhRUZeUj3RE7l2//+KzLSue6aq7MXbL7zzL4+QiYHADmOEtzshl4MUDweI2Ty00f2PfG9O7PfESGUo9a6G/+GfdHqhib/003U1s1MYxNZQbY1sNt+uQkabC1eG1SeE8YKRyKLDQYEkCEbOCNypUomc0APSfU/SxWXgt1BeTcb7gK3HYyV1vTS4mRhQYFWq9VoNJFIpLe3d+XKlfO37H6Ogu6kTB4Nj+RocgtLLEVlFQjC4PQBQEvz2RiN0wwmmBgvnAKiOWWCwfEJ95qiqLSDFUKAYYTrw7fZrTeDvYmymw1g/2ujw1CxmbHYobHNX1FTf25YFOE4wyQV55a5i+ZnkmH6+vpOh7RDAxxsuNEAEBzA9CV89pe/SE26+vpkBP7GifBSZefGtWtSX0WjUYfDIZPJdDrd8toqgiD2NHo5XgCwuRoO7PeVrWfM7sYXbBUbt9mnDcNdPgiCMDAwIJfLj7WciNJ03/EBYsutBgACQ329wqQshCHTkYAO+2TAjgSi1lUbJ67nOC7D/3l1/YonX/hTn9sFAMlXHTKS1OcP9TfszS8ALpSsuhocu/cCgF4Jjt1POgBIGckkGQD4+Y//Ufv9hqW5D+/e6zoBCrk8rkzqQ58e9RXmF+x17AYAaN69d+LGKqUyRtOTzpubm1tYWNjV1TWd/Xge7D/e0e+PKgi0aXXNHNTizIVLGBqNPrunbTTBMUm61LxkNBIk+KRCnSsIQlaanhcAQ3K5nGVZklR88PpTu5/8sfhNW1vbsmXLpu5xzz//K8OyHM/hWJrKl0mwHEPgpFKheO43P5u43uPxeDwegiCSySRSKA+f7B0WQlQzQ5g8I4RZ3eMQA97ZoJVxXk8/AAJStUQ1eMOWca3T2tq6fHmaKoSv3vsQQZIYQvxMPxDLsjiOIYTlanKe/MV/ZiXQPCQej3d2diqVSoIgdDrdkWPHojTdOiQvrclp2dWlrKA8XLne6ZqYhcissGIhnwLnY7GYalH5g5sWp9b39PQYjUaSzH4mmXnGpCjKT37xa3EBx3COn0VEXyFXUDRRcsV1oeEhQEiJC/d8cWlxOgfic2SOgu5dTk9ji2skzqtIuLq60M/pCJL0OLpYS41o5kx1/sdFRCAIwCTja83yPP347bDb7RqNpqBgcnTZ1us848MBoeChAdbcS9mThm1W6AGA9F6h6BJGQwEiaNu2aQNCCMfxUCgUjUZNJhMAJJNJh9PZEc3zhsd+TpUMiyVnYWHlq0lXn1shI3mZZgnp3rJ+LMbk8/kAYOolfPzZiQEmV65Q5mpUtvZ2qLQa092ZSQSH+ovQ8OaN67MXbB5x8uTJ5cuXTxrhntk/KC4oSCzOiL/IeBYiMziGYqNhQqZMJujvbh+7s4IgtLW11dXVfa6yX1rYbDardcx/3PniW+G+fFG5axV4p407a6V2Uk4AJ6RrFwE6OEjgPI6wKIsn6Kiz+zRCYKxeZtLC12+4sOWpc+QSVpsWV5vGBrHnX36VNNT5fZRu0A+8cLCxyQ9Of8VGa6ODrChhLNY0DREAIb+3g+O8/X3LrqgNh8NarbawsDA3N3fqlocOf0oU1/mGBkbAP9jYBjU3rwE4aLfBNEcWiUZD5Xr93n37li9bFggEDAaDqK0AQCaT1VRX1wDsfPIZcc3eo8cQoLPt0pkQe18xDLvqypVxgDtvv91QOB4R93q9aS3EAwcPFtVeN0QNjHjsFMsbKsHVYIeZvFo6NMzmwrGWllX19TPJdQG5ZtvN3/rheTb6quVYaDQOgHCS/Kd1han1Xq+3tLR0qj1+//oijuMB4MP2EQwj+xs7ua111+Snvk+v5cVyBI5lNl5ZIpfLcXws45ZMJk+dOpXW4F1IEATBMIxoQqpzF3X2OYK7mtshsvj+Os/7PDxkBidQzV2ECfqArHcCBUA5HVTzmPXKJpPA0RyQgWFvAsgzR/cXFJcFA0P5hvKIQnuhhb8Ile50PLHn5BBOkmqlksmi7EC0sOjRUDk5fGX9SlHgzOnk91r9GIaTOJZ9WQPLJLcu0SpmmqrB4x1s9cv7XL18JyPbvsQw3ieR/r+hUeCROAcAp/a9evP1WwxFRaIjPDw8TNO00Wicrg/2vVY/jhNKGUZnbcoxCXr78gKCmHkQ+rcf/URcyFEpozE6y+MDgExGJpOMuPz4f/wwtd7v9zMM0+twUINDPhkxeEw7nUdGZUywyCAR8lEcLyjzSr9ULZjKy8T1GRpl4OwQGAoO+z0eb/cpqKgxwADVC4aKEsayY91UhYVAEMDvcS5WRiPBwLIraoeHh9VqNUEQGaoZFgzxeNzj8VgsFgD4l0d/XrXxbnG9VoGH41m5hNHgIEkghYzs7rb7B92CIIyGAtYVX6gtUd/5xQVhYU1EqZAblaFwKMRFOHkWMSaGYWUkUW00VhjrIbt5hRCGMAwhAITA1fM2wI7MzhRCkE0BBJwNw/uHvCOsxgq2g0+/3T3qBPXS2ultQ5HFRQVtp8/oNJqRkRG9Xl9aWpq53xVhCCFAArh7Z5b/7D6ZSrTj8TgAHDtxoqvboawwiG9PKNAQvgib2oYa0yZiizj4Pqmpu/acg+iUxJDPxyXCuFL3o1/8/j++9xAA2Gw2HMfz8vIwhMSBhILm4K5mn2lxgakaAMDp8JFmtruZMBlZAHCCj7x+0pHHEHgBBASTdXTm8amubrmL1qi1utplK5gt2b6TRqXWBodcN22/ASFkNBqzUfQLA4VCQdNjo9TORx9+8ZXXxOW3GvbChPlwMkASBMOyCENXrNpQcOU6NpkQBIFgI19eu/SCSg4Lo5dwEi++8hpRXOfzUTpvP7u1BhqaGo7CstVmqjdZ/4CM2mcxsAAw3jwhWlhDHkdZDo3z3Pp1M0zH9tQbewuKSgEAYSDMZP3ISUgwAAC3rqvOvqH6sZ1PFNVeR8ei6oEDDtbAWKzQ2ERWmKleh2HrZgMA2M+5BBFPd2upTmATtLW6OhAITEyfC4IQiUQKCwuPtp7y+XxUHzdosUJzc96SzfIiDpxAmRzQ7AqajLUmM+VsDoKxwAS+o6CPuibmFnRKwj/YHw6HNVpd2Hn8B9+5RxCESVmn2x78wZY7vnNu81CaF8mkRaPAg+FRQBhBylPVdpFIJBAIpJ07IcVX730wkYhnWXknOkQ6Xe6u3/9qZpkWIqFQyOl08jyv0+naO2wszyHDlbJ+twusSrKTZgQAmxus66YpldAq8TDNAYA+h9DwIz39PpWcWL+qVkbMbH/8gyxAhdXSZusdVSKE6bTa7F3CaDiAfO233Lh9RrXywemROB0bPOiWbV9iYDvOeoIzuIRvPfPoc0/+JstL+PtHH4dkJTK5Qq/Nyd4lDA715wu+jdetBQAMw9LODTA4ONg/FDwaGMtdTLKwACCzctEpCV8gyCdiuCKno+G5p3b+bGBgQK1Wpy1HfP9UAAG493ZNLK1MMSnNIkbNBUFYa8S12vFQSDwe7+joWLlyZYYLf/7lV3NMVyUTceZMlN1SczZBMW2aQnQJR3wDo+6WL6yqLyoqisViGo2GIAiSJEmSxDBsATuGYmKBIAifz1dYWNh2ph0jCEZXGTrxysdwcxXxtwSbUw4lHwOsndIRKTJRYa0xz+m0IgtQYQHAHd96IJGICwKfzYQHLMsRBK7Val9++rczbjxAeU8NKz79uDEepiN9B9nNG+H1ZP0jVmiwudOFS1IKi/ccH/YP3XVntu3yO/77PQCA4wTHsZm3ZBkOJ3CEwGQs/81jP87m4AePnBgc9ADAK6+/CQAKhUJ0GDNAkiTDMADw9TtuE9fcdtOX4NyU00Sa9h/04aWu3s6Q/7jfXlL/gBUamihis4E90LSXqd1iZgCgV0yz7FhXCQoSxRkhmYjnxHr8FLWqfmUoFNJoNBiGVVRUZFYfz7/8qrJ8VSw22tfV6e3eB1s3wusOw+r0ASxIxbC8bn6oo7a6wmwyxWIxmUzGMAzDMCzL8hl9IoQQQig3N7egoGDe6TVBEI4dO7Zy5cpJLvCX77jr+z/9Vw+YyzDgeBj2DdraAovHQohgrhqvdnR0dxAYsDwAwDe2XzV3ogPAglRYL77ymrJ8VTweKy01svyYBSSO52nrxUULK+jzRF3Htm3aYCyfYRbJD06PjIZH1NpcOYESM01OlFJYf/3Dj5/57c4svcLHdj5RtmIrYFhZyYitvUaUOcMliFDOdjTiuGHrluKirDqfD9tH287Y8vKLDCFvW2lVJo8LIEeORxMcALjaPi7WwKoVK8QhWqlUVlamEcjZT50cRMkErdGK1pwtsykqKiyOZQLt+/7bLTeJOazs40pvNbeWmSqy6UaGs81YrccP33fr1iyPPwlBEILB4ODgIADgOG6xWOZLCMzpdObn509M9dhd7lBsZIApwe3dBwG+WFs7uWSnx+aqPMdGvogW1vy4y7OFFwSGYVwfvs1uJalDdgosjP1Ay+vd/tVj9eIG9u1JASCeZWRyWTyLWThuWJrb0toHXIRNsrn4DDeQHxVUCAHAs7/79awugeNZgROcf99HlQvQYKcsM18C8DxBEIn4LF7VyfEsx7EfffCcu+Jb7s4mfxavVEA8Jwg4y7IkSVZVVU2XVzWVGoryE3sajkeGeAQChuEF2CcxOwBAamEiUUFACJEkededd2Qvf4qvbJh1LcJq03lqKwBACOXl5YltYRzH2Ww2pVIp5t0uceLx+KTEdHtn95+7X15dfrsKfOV2+ITqSjC8oXyHu/Hn/pya+i9YAQB63v70UCdVWmMA65pN6QNbc8MCtLAA4PHnXtcvKuA5DksXx5mE6BL6vJ7/++DX50C2LPnVrrc1Wi0SkIBm+IEYliNwHCE4dmjfH37501md5bm39gEAjiFupmAfJgg8QgDQ9tn+J36WleN5WREOhx0OR1lZWdqXVlw6THXh+z0UKzBnhtWi+T9NUfQ5dnHKwnrhF//25h+fvfBSj7MAFVaSYd9t8eIEkaNUsnyHK00//Tl3X3QJY5ERo2z4yvp6nufThqvnkiF/YH93RKlUqVV223tC5vkAUoSGvcsKhNol1iwv4dn/v1ddYMrVL9KpetraOLETwNWzB2A7jM8EMHailEvo6+u6/kpTYWFhpkNfrvT19UWjUYSQyWSSy//RqYp+9uvfnd+OySQjk5EA8P3vnjMbJU3Tg4ODqYroiWz9yte+/9PfAwCOBE5AGELYhJIjdkIgFcdxhQxPJHkAqC/BNeqc8xPy/FiACuvFV14jF6/wevr0Pg+7tcbd0OTvHQoGVm5+xAoNBxoaPMu2rWAsO8rtYy5VqqyhKk8YDQ4fPHDgoYcemm7ytrnhsZ1PFF+xPhIO6ryHD7GG+m1W99N/6Q4U1q4+J1adugQRT3erpZAkQHjzjTcef/zxGf8wh+2jnx45qtMvkvXs6U4WGyxWsNtboN9AlLbt+WzZ/V9lGptSJ1LeeGthggMAz+n9uSps7dVXFWUXKbsM4Xne6XQmk8lUSF4QBLGNLHPxHcdxgiA4HA4BYN8hSpwWWTR5spkWOdXynYzTOBdJsqDWF01sGOju7rZYLFMHsxhN7+9NnDpxpKr6ioh/gDbWZA5opiyswdN71QrilhtvnLMxfgEqLAD4W8sQQRAkgZ8ta8jUiCcqLCYRX2eR6/UXtnUze95pGSRJmUqOny1rmLmXkB4NX1+Xr1KpsjzFTV/75r3fe1wmk2uVRJieIReZsrBGg75br62Q3io0K8Q6OLEfILUGplRBBwKBgoKCltZTap3ur39+D224xQAwfHD/IGcH0z2bN0DLrpeCG+6pdTpg7L0qL9nF2WjJ6+uuHW/5jgx71xb9rTN0VVy59P6N49NATpfSpel4c2+8z9VbWm7xffSMnTUCWBiLvdxucVvs0PjWZzlfvu2u8aExpbA8Zw5sWL20Yg6DdwtTYYUjowzDZDX5HYDYK6NUKlXKS+gVqsOBIABkU5mRmvQiL08/2xy7eBZe4LGZzzImyaK8S0WnL1Q8Hg/C8Hc6xz5m12l/Tss3k6QhHmEBU+cWpCwsQRA6OjoydDiJrbIIYcLkemjKSxmKJ3RayUgiybAA8C//fH/21/W5sDAVloTEAuDZXX8CgANHjsVj0bMt25kQBEEMXy42lCy1VgLA/7j7G6lv29raMmR15wuSwpKQuHR56oVduVXXggB05DDD3jSxEC9DRV5wsC/qOr5t80aNRsNxXCwWC4fDFoslJ2dOA+QXgoVZhyUhsWBgEkk6HiV7a8C0x9UALQBgd1BeM1VsmW5aZCYZz9Fq+vr7q6uqwuFwSUlJ5k7MeYRkYUlIXNJ80hsBABwhbuyv2uHoXmJO+wYcAQSx8YgX1lZd8KmpLgqSwpKQkJg3SMlpCQmJeYOksCQkJOYNksKSkJCYN0gKS0JCYt4gKSwJCYl5g6SwJCQk5g2SwpKQkJg3SApLQkJi3iApLAkJiXmDpLAkJCTmDf8FDxD3HNoiVXMAAAAASUVORK5CYII="
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="5886.3 594.126"
#!   DOC_TOP_LEFT="-265.628 -767.879"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="37c010da-e2d9-464f-9ea1-f857157fdc14"
#!   FME_DOCUMENT_PRIORGUID="9c6dbb15-ef38-4bf7-87c2-e8c72f7d7294"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-04-23T10:14:03"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="-225.002 200.002"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="$(DEF_VAL)"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 提交待合并shp数据压缩包"
#!   DEFAULT_VALUE="$(FME_MF_DIR)2023"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI CHOICE_OR_ATTR PARAMETER 是%否 是否去重叠"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR dir_2 保存路径"
#!   DEFAULT_VALUE="$(DEF_VAL)"
#!   IS_STAND_ALONE="false"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJhY2Nlc3NNb2RlIjoicmVhZCIsImRlZmF1bHRWYWx1ZSI6IiQoRk1FX01GX0RJUikyMDIzIiwiaW5jbHVkZVdlYkJyb3dzZXIiOnRydWUsIml0ZW1zVG9TZWxlY3QiOiJmb2xkZXJzIiwibmFtZSI6ImRpciIsInByb21wdCI6IuaPkOS6pOW+heWQiOW5tnNocOaVsOaNruWOi+e8qeWMhSIsInJlcXVpcmVkIjp0cnVlLCJzZWxlY3RNdWx0aXBsZSI6dHJ1ZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOmZhbHNlLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifSx7ImNob2ljZVNldHRpbmdzIjp7ImNob2ljZVNldCI6InVzZXJEZWZpbmVkIiwiY2hvaWNlcyI6W3siZGlzcGxheSI6IuaYryIsInZhbHVlIjoi5pivIn0seyJkaXNwbGF5Ijoi5ZCmIiwidmFsdWUiOiLlkKYifV0sInNvcnQiOiJuYXR1cmFsU29ydCJ9LCJkZWZhdWx0VmFsdWUiOiIiLCJuYW1lIjoiUEFSQU1FVEVSIiwicHJvbXB0Ijoi5piv5ZCm5Y676YeN5Y+gIiwicmVxdWlyZWQiOnRydWUsInNob3dQcm9tcHQiOnRydWUsInN1cHBvcnRlZFZhbHVlVHlwZXMiOlsiZXhwcmVzc2lvbiIsImdsb2JhbFBhcmFtZXRlciJdLCJ0eXBlIjoiZHJvcGRvd24iLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifSx7ImFjY2Vzc01vZGUiOiJ3cml0ZSIsImRlZmF1bHRWYWx1ZSI6IiQoREVGX1ZBTCkiLCJkZXByZWNhdGVkRmxhZ3MiOlsic2hvd1ppcEJ1dHRvbiJdLCJpdGVtc1RvU2VsZWN0IjoiZm9sZGVycyIsIm5hbWUiOiJkaXJfMiIsInByb21wdCI6IuS/neWtmOi3r+W+hCIsInJlcXVpcmVkIjp0cnVlLCJzZWxlY3RNdWx0aXBsZSI6ZmFsc2UsInNob3dQcm9tcHQiOnRydWUsInN1cHBvcnRlZFZhbHVlVHlwZXMiOlsiZXhwcmVzc2lvbiIsImdsb2JhbFBhcmFtZXRlciJdLCJ0eXBlIjoiZmlsZSIsInZhbGlkYXRlRXhpc3RlbmNlIjpmYWxzZSwidmFsdWVUeXBlIjoic3RyaW5nIn1dfQ=="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)2023"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 提交待合并shp数据压缩包"
#! />
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI CHOICE_OR_ATTR PARAMETER 是%否 是否去重叠"
#! />
#!     <INFO NAME="dir_2" 
#!   DEFAULT_VALUE="$(DEF_VAL)"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR dir_2 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-265.62765627656336 -437.50437504375031"
#!   BOUNDING_RECT="-265.62765627656336 -437.50437504375031 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="306.25306253062513 -312.50312503125008"
#!   BOUNDING_RECT="306.25306253062513 -312.50312503125008 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="865.63365633656349 -465.62965629656287"
#!   BOUNDING_RECT="865.63365633656349 -465.62965629656287 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1465.6396563965636 -372.50312503125008"
#!   BOUNDING_RECT="1465.6396563965636 -372.50312503125008 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="3271.90771907719 -456.87896878968775"
#!   BOUNDING_RECT="3271.90771907719 -456.87896878968775 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST $(PARAMETER) = &lt;u662f&gt;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="11"
#!   TYPE="AreaOnAreaOverlayer"
#!   VERSION="9"
#!   POSITION="3918.789187891879 -173.75293752937523"
#!   BOUNDING_RECT="3918.789187891879 -173.75293752937523 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AREA"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ACCUM_ATTRS_NAME" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="First Wins"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVC_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaOnAreaOverlayer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="13"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="5190.6769067690693 -500.0050000500006"
#!   BOUNDING_RECT="5190.6769067690693 -500.0050000500006 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir_2)"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;utf-8&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Split&lt;space&gt;Dataset&lt;space&gt;into&lt;space&gt;2GB&lt;space&gt;Files,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Spatial&lt;space&gt;Index:,SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit,SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;space&gt;Type&lt;space&gt;Storage,SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Compressed&lt;space&gt;Shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Elevation&lt;space&gt;as&lt;space&gt;Measures,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;Surface&lt;space&gt;and&lt;space&gt;Solid&lt;space&gt;Storage,SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;Output&lt;space&gt;Dimension"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="utf-8"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;u5408&gt;&lt;u5e76&gt;&lt;u540e&gt;:Area,ftp_feature_type_name,&lt;u5408&gt;&lt;u5e76&gt;&lt;u540e&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,Q__overlap&lt;comma&gt;double&lt;comma&gt;Area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="15"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="4637.546375463754 -353.75293752937523"
#!   BOUNDING_RECT="4637.546375463754 -353.75293752937523 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="Area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="4575.6694775053202 -696.87896878968775"
#!   BOUNDING_RECT="4575.6694775053202 -696.87896878968775 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="Area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="21"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="2631.2763127631274 -528.13028130281305"
#!   BOUNDING_RECT="2631.2763127631274 -528.13028130281305 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="24"
#!   TYPE="GeometryFilter"
#!   VERSION="8"
#!   POSITION="2068.7706877068767 -552.50312503125008"
#!   BOUNDING_RECT="2068.7706877068767 -552.50312503125008 430 71"
#!   ORDER="500000000000012"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Area"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <OUTPUT_FEAT NAME="&lt;UNFILTERED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILTER_MODE" PARM_VALUE="SIMPLE"/>
#!     <XFORM_PARM PARM_NAME="FILTER_MULTI" PARM_VALUE="FILTER_TYPES]Area]FME_CONTROLLER_QUERY_FILE]]FME_CONTROLLER_CHOICE]Simple"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryFilter"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="8"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="12"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="11"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 AREA"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="16"
#!   SOURCE_NODE="11"
#!   TARGET_NODE="15"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="17"
#!   SOURCE_NODE="15"
#!   TARGET_NODE="13"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Area"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="20"
#!   SOURCE_NODE="18"
#!   TARGET_NODE="13"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Area"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="23"
#!   SOURCE_NODE="21"
#!   TARGET_NODE="9"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="26"
#!   SOURCE_NODE="24"
#!   TARGET_NODE="21"
#!   SOURCE_PORT_DESC="fo 0 Area"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="6"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="24"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="19"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO dir $(FME_MF_DIR)2023

DEFAULT_MACRO PARAMETER 

DEFAULT_MACRO dir_2 $(DEF_VAL)

# ============================================================================
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir_2$encode)} == {}} { puts_real {Parameter 'dir_2' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)合并shp.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 合并shp
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_2_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Tester_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_2_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_2_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_2_<Rejected> INPUT FEATURE_TYPE FeatureReader_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} GeometryFilterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { GeometryFilter }    INPUT  FEATURE_TYPE FeatureReader_2_<OTHER>    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    FILTER_TYPES { Area }    INSTANCES { INSTANTIATE }    BREAK_AGG { Yes }    OUTPUT { <UNFILTERED> FEATURE_TYPE GeometryFilter_<UNFILTERED>  }    OUTPUT { Area FEATURE_TYPE GeometryFilter_Area  }
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter <UNFILTERED> Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_<UNFILTERED>
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE GeometryFilter_Area    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE Reprojector_REPROJECTED    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode),Tester_2)" = <u662f> ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }    OUTPUT { FAILED FEATURE_TYPE Tester_2_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { AreaOnAreaOverlayer }    INPUT POLYGON FEATURE_TYPE Tester_2_PASSED    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    ACCUMULATE_ATTRIBUTES { ONE }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    CONNECT_Z_MODE { FIRST_WINS }    DEAGGREGATE_INPUT { Yes }    OVERLAP_COUNT_ATTRIBUTE { _overlaps }    MODE COMPLETE    OVERLAY_TYPE AREA_ON_AREA    CLEANING_TOLERANCE { AUTO }    OUTPUT_REMNANTS    SELF_INTERSECTION NO    OUTPUT { POLYGON  FEATURE_TYPE AreaOnAreaOverlayer_AREA         }    OUTPUT { ILLEGAL_GEOM FEATURE_TYPE AreaOnAreaOverlayer_<REJECTED>         }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME AreaOnAreaOverlayer_<Rejected> INPUT FEATURE_TYPE AreaOnAreaOverlayer_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, AreaOnAreaOverlayer<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE AreaOnAreaOverlayer_AREA    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, Area, $(AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_2_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_2_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_2_AreaCalculatorInput }    INPUT  FEATURE_TYPE Tester_2_FAILED    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_2_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, Area, $(AreaCalculator_2_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_2_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_2_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(dir_2$encode),FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>utf-8<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma><comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<u5408><u5e76><u540e>:Area,ftp_feature_type_name,<u5408><u5e76><u540e>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,Q__overlap<comma>double<comma>Area<comma>double,ftp_user_attribute_values,<comma>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,utf-8,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Area FEATURE_TYPE AreaCalculator_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u5408><u5e76><u540e>,FeatureWriter)) @CopyAttributes(ENCODED,Q__overlap,_overlaps)    INPUT Area FEATURE_TYPE AreaCalculator_2_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u5408><u5e76><u540e>,FeatureWriter)) @CopyAttributes(ENCODED,Q__overlap,_overlaps)
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

