import{d as _e,u as ve,r as p,L as P,o as ge,c as be,b as o,e as a,w as l,f as u,E as m,i as R,D as y,y as f,l as v,W as F,z as ye,P as he,Q as we,A as L,p as $,B as Ve,X as Ce,Y as ke,S as xe,T as je,K as De,k as A,_ as ze}from"./index-LgsLWi3x.js";import{g as Te}from"./market-pCDzHrq6.js";import{f as Me}from"./format-CBpsKyOP.js";const Se={class:"market-container"},Ue={class:"stats-section"},Ae={class:"card-header"},Ye={class:"stats-value"},Be={class:"number"},qe={class:"card-header"},Fe={class:"stats-value"},Le={class:"number"},Ne={class:"card-header"},Ie={class:"stats-value"},Pe={class:"number"},Re={class:"search-section"},$e={class:"search-row"},Ee={class:"search-buttons"},He={class:"table-container"},Xe={class:"pagination-container"},Oe={class:"dialog-footer"},Qe=_e({__name:"MarketView",setup(Ke){const h=ve(),Y=p(!1),c=p([]),T=p(""),M=p(""),S=p(""),U=p(0),b=p(1),j=p(10),N=P(()=>{if(!c.value||c.value.length===0)return[];const s=T.value.toLowerCase(),e=M.value,n=S.value;return c.value.filter(d=>{if(!d||!d.fmw_name||!d.project)return!1;const i=d.fmw_name.toLowerCase().includes(s)||d.project.toLowerCase().includes(s),q=!e||d.author===e||d.author_name===e,k=!n||d.tools_class===n;return i&&q&&k})}),E=P(()=>{const s=(b.value-1)*j.value,e=s+j.value;return N.value.slice(s,e)}),w=()=>{b.value=1},H=()=>{T.value="",M.value="",S.value="",b.value=1},X=s=>{b.value=s},O=s=>{j.value=s,b.value=1},Q=async()=>{var s;Y.value=!0;try{console.log("开始获取工具列表...");const e=await Te();if(console.log("获取工具列表响应:",e),!e){console.error("获取工具列表失败：响应为空"),m.error("获取工具列表失败：服务器无响应"),c.value=[];return}if(e.success){if(!Array.isArray(e.data)){console.error("获取工具列表失败：返回数据格式错误",e),m.error("获取工具列表失败：数据格式错误"),c.value=[];return}c.value=e.data,console.log("工具列表数据:",c.value),console.log("工具数量:",c.value.length)}else console.error("获取工具列表失败:",e.message||"未知错误"),m.error(e.message||"获取工具列表失败"),c.value=[]}catch(e){console.error("获取工具列表异常:",e),e.response?(console.error("错误响应:",e.response.data),m.error(((s=e.response.data)==null?void 0:s.message)||"获取工具列表失败")):e.request?(console.error("请求错误:",e.request),m.error("服务器无响应，请检查网络连接")):(console.error("错误信息:",e.message),m.error("获取工具列表失败："+e.message)),c.value=[]}finally{Y.value=!1}},K=s=>{je.alert(s.description||"暂无描述","工具描述",{confirmButtonText:"确定",customClass:"detail-message-box"})},V=p(!1),_=p(),B=p(!1),W=p(null),r=p({fmw_id:"",fmw_name:"",project:"",applicant:"",reason:"",end_date:"",usage_count:1,user_project:""}),G=p({project:[{required:!0,message:"请输入项目名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,max:200,message:"长度在 10 到 200 个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]}),J=s=>s.getTime()<Date.now()-864e5,Z=s=>{var e;W.value=s,r.value={fmw_id:s.fmw_id,fmw_name:s.fmw_name,project:"",applicant:((e=h.user)==null?void 0:e.username)||"",reason:"",end_date:"",usage_count:1,user_project:""},V.value=!0,De(()=>{_.value&&(_.value.resetFields(),_.value.clearValidate())})},ee=async()=>{var s,e;if(_.value)try{await _.value.validate(),B.value=!0;const n=await R.post("/api/tools/apply",{...r.value,project:r.value.user_project,applicant:((s=h.user)==null?void 0:s.username)||""},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`,"X-Username":((e=h.user)==null?void 0:e.username)||""}});n.data.success?(m.success("申请提交成功"),V.value=!1):m.error(n.data.message||"申请提交失败")}catch(n){if(n&&(typeof n=="object"||typeof n=="string")){m.error("请检查表单填写内容");return}n.response?m.error(n.response.data.message||"申请提交失败"):m.error("提交申请失败，请检查网络连接")}finally{B.value=!1}},I=(s,e)=>{if(!s)return"未知";try{const n=new Date(s);return isNaN(n.getTime())?s.toString():Me(n,e)}catch{return s.toString()}},ae=()=>{const s=new Date,e=new Date(s.getFullYear(),s.getMonth(),1);return c.value.filter(n=>new Date(n.created_at)>=e).length},le=async()=>{var s;try{const e=await R.get("/api/tools/count",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"X-Username":((s=h.user)==null?void 0:s.username)||""}});e.data.success?U.value=e.data.data.total_runs||0:U.value=0}catch{U.value=0}},te=()=>{_.value&&(_.value.resetFields(),_.value.clearValidate()),r.value.fmw_id="",r.value.fmw_name="",r.value.project="",r.value.applicant="",r.value.reason="",r.value.end_date="",r.value.usage_count=1,r.value.user_project=""},se=()=>{V.value=!1};return ge(async()=>{await Promise.all([Q(),le()])}),(s,e)=>{const n=u("el-tag"),C=u("el-card"),d=u("el-input"),i=u("el-option"),q=u("el-select"),k=u("el-icon"),x=u("el-button"),g=u("el-table-column"),oe=u("el-tooltip"),ne=u("el-button-group"),re=u("el-table"),ue=u("el-pagination"),D=u("el-form-item"),ie=u("el-date-picker"),de=u("el-input-number"),pe=u("el-form"),ce=u("el-dialog"),me=we("loading");return A(),be("div",Se,[o("div",Ue,[a(C,{class:"stats-card"},{header:l(()=>[o("div",Ae,[e[13]||(e[13]=o("span",null,"工具总数",-1)),a(n,{type:"info",effect:"plain"},{default:l(()=>e[12]||(e[12]=[f("总计")])),_:1})])]),default:l(()=>[o("div",Ye,[o("span",Be,y(c.value.length),1),e[14]||(e[14]=o("span",{class:"unit"},"个",-1))])]),_:1}),a(C,{class:"stats-card"},{header:l(()=>[o("div",qe,[e[16]||(e[16]=o("span",null,"本月新增",-1)),a(n,{type:"success",effect:"plain"},{default:l(()=>e[15]||(e[15]=[f("增长")])),_:1})])]),default:l(()=>[o("div",Fe,[o("span",Le,y(ae()),1),e[17]||(e[17]=o("span",{class:"unit"},"个",-1))])]),_:1}),a(C,{class:"stats-card"},{header:l(()=>[o("div",Ne,[e[19]||(e[19]=o("span",null,"总运行次数",-1)),a(n,{type:"warning",effect:"plain"},{default:l(()=>e[18]||(e[18]=[f("累计")])),_:1})])]),default:l(()=>[o("div",Ie,[o("span",Pe,y(U.value),1),e[20]||(e[20]=o("span",{class:"unit"},"次",-1))])]),_:1})]),a(C,{class:"search-card"},{default:l(()=>[o("div",Re,[o("div",$e,[a(d,{modelValue:T.value,"onUpdate:modelValue":e[0]||(e[0]=t=>T.value=t),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":v(F),onClear:w,onInput:w},null,8,["modelValue","prefix-icon"]),a(d,{modelValue:M.value,"onUpdate:modelValue":e[1]||(e[1]=t=>M.value=t),placeholder:"作者",class:"search-input",clearable:"","prefix-icon":v(F),onClear:w,onInput:w},null,8,["modelValue","prefix-icon"]),a(q,{modelValue:S.value,"onUpdate:modelValue":e[2]||(e[2]=t=>S.value=t),placeholder:"全部类型",class:"search-input",clearable:"",style:{width:"160px"},onChange:w},{default:l(()=>[a(i,{label:"全部类型",value:""}),a(i,{label:"格式转换",value:"格式转换"}),a(i,{label:"坐标系转换",value:"坐标系转换"}),a(i,{label:"数据结构转换",value:"数据结构转换"}),a(i,{label:"数据清洗",value:"数据清洗"}),a(i,{label:"几何处理",value:"几何处理"}),a(i,{label:"属性处理",value:"属性处理"}),a(i,{label:"数据合并",value:"数据合并"}),a(i,{label:"数据融合",value:"数据融合"}),a(i,{label:"数据同步",value:"数据同步"}),a(i,{label:"批处理",value:"批处理"}),a(i,{label:"数据质检",value:"数据质检"}),a(i,{label:"空间分析",value:"空间分析"}),a(i,{label:"统计分析",value:"统计分析"})]),_:1},8,["modelValue"]),o("div",Ee,[a(x,{type:"primary",onClick:w},{default:l(()=>[a(k,null,{default:l(()=>[a(v(F))]),_:1}),e[21]||(e[21]=f(" 搜索 "))]),_:1}),a(x,{onClick:H},{default:l(()=>[a(k,null,{default:l(()=>[a(v(ye))]),_:1}),e[22]||(e[22]=f(" 重置 "))]),_:1})])])])]),_:1}),a(C,{class:"table-card"},{header:l(()=>e[23]||(e[23]=[o("div",{class:"table-header"},[o("span",{class:"title"},"工具列表")],-1)])),default:l(()=>[o("div",He,[he((A(),L(re,{data:E.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:l(()=>[a(g,{type:"index",label:"序号",width:"60",align:"center"}),a(g,{prop:"fmw_name",label:"工具名称","min-width":"150","show-overflow-tooltip":"",align:"left"},{default:l(({row:t})=>{var z;return[o("span",null,y(t.fmw_name),1),t.author===((z=v(h).user)==null?void 0:z.username)?(A(),L(n,{key:0,size:"small",type:"success",effect:"plain",style:{"margin-left":"8px"}},{default:l(()=>e[24]||(e[24]=[f(" 我的工具 ")])),_:1})):$("",!0)]}),_:1}),a(g,{prop:"project",label:"所属项目","min-width":"120","show-overflow-tooltip":"",align:"left"}),a(g,{prop:"tools_class",label:"工具类型","min-width":"110","show-overflow-tooltip":"",align:"left"}),a(g,{prop:"run_times",label:"运行次数",width:"90",sortable:!1,align:"center"},{default:l(({row:t})=>[o("span",{class:Ve({highlight:t.run_times>0})},y(t.run_times||0),3)]),_:1}),a(g,{prop:"author_name",label:"上传者",width:"100",align:"center"},{default:l(({row:t})=>[a(n,{size:"small",effect:"plain"},{default:l(()=>[f(y(t.author_name||t.author),1)]),_:2},1024)]),_:1}),a(g,{prop:"data",label:"上传时间",width:"140",align:"center"},{default:l(({row:t})=>[a(oe,{content:I(t.data,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:l(()=>[o("span",null,y(I(t.data,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),a(g,{label:"操作",width:"160",align:"center",fixed:"right"},{default:l(({row:t})=>[a(ne,null,{default:l(()=>{var z;return[a(x,{type:"info",size:"small",onClick:fe=>K(t)},{default:l(()=>[a(k,null,{default:l(()=>[a(v(Ce))]),_:1}),e[25]||(e[25]=f(" 详情 "))]),_:2},1032,["onClick"]),t.author!==((z=v(h).user)==null?void 0:z.username)?(A(),L(x,{key:0,type:"primary",size:"small",onClick:fe=>Z(t)},{default:l(()=>[a(k,null,{default:l(()=>[a(v(ke))]),_:1}),e[26]||(e[26]=f(" 申请 "))]),_:2},1032,["onClick"])):$("",!0)]}),_:2},1024)]),_:1})]),_:1},8,["data"])),[[me,Y.value]]),o("div",Xe,[a(ue,{"current-page":b.value,"onUpdate:currentPage":e[3]||(e[3]=t=>b.value=t),"page-size":j.value,"onUpdate:pageSize":e[4]||(e[4]=t=>j.value=t),"page-sizes":[10,20,50,100],total:N.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:O,onCurrentChange:X},null,8,["current-page","page-size","total"])])])]),_:1}),a(ce,{modelValue:V.value,"onUpdate:modelValue":e[11]||(e[11]=t=>V.value=t),title:"申请使用工具",width:"500px","close-on-click-modal":!1,onClose:se,onAfterClose:te},{footer:l(()=>[o("span",Oe,[a(x,{onClick:e[10]||(e[10]=t=>V.value=!1)},{default:l(()=>e[27]||(e[27]=[f("取消")])),_:1}),a(x,{type:"primary",onClick:ee,loading:B.value},{default:l(()=>e[28]||(e[28]=[f(" 提交申请 ")])),_:1},8,["loading"])])]),default:l(()=>[a(pe,{ref_key:"applyFormRef",ref:_,model:r.value,rules:G.value,"label-width":"100px",class:"apply-form"},{default:l(()=>[a(D,{label:"工具名称",prop:"fmw_name"},{default:l(()=>[a(d,{modelValue:r.value.fmw_name,"onUpdate:modelValue":e[5]||(e[5]=t=>r.value.fmw_name=t),disabled:""},null,8,["modelValue"])]),_:1}),a(D,{label:"使用项目",prop:"user_project"},{default:l(()=>[a(d,{modelValue:r.value.user_project,"onUpdate:modelValue":e[6]||(e[6]=t=>r.value.user_project=t),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),a(D,{label:"申请原因",prop:"reason"},{default:l(()=>[a(d,{modelValue:r.value.reason,"onUpdate:modelValue":e[7]||(e[7]=t=>r.value.reason=t),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),a(D,{label:"有效时间",prop:"end_date"},{default:l(()=>[a(ie,{modelValue:r.value.end_date,"onUpdate:modelValue":e[8]||(e[8]=t=>r.value.end_date=t),type:"date",placeholder:"请选择有效时间","disabled-date":J,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD",locale:v(xe)},null,8,["modelValue","default-time","locale"])]),_:1}),a(D,{label:"申请次数",prop:"usage_count"},{default:l(()=>[a(de,{modelValue:r.value.usage_count,"onUpdate:modelValue":e[9]||(e[9]=t=>r.value.usage_count=t),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Ze=ze(Qe,[["__scopeId","data-v-84d1ea05"]]);export{Ze as default};
