import{d as oe,r as d,u as ne,o as re,a as ie,c as m,b as a,e as s,w as t,f as v,g as ue,h as M,i as b,E as u,j as de,k as c,l as r,t as ce,m as pe,n as ge,p as N,q as T,s as Z,v as G,x as J,y as k,z as O,A as q,B as me,C as ve,D as X,F as j,G as fe,H as _e,I as we,J as ye,K as he,_ as be}from"./index-LgsLWi3x.js";const ke="/assets/logo-BIVXNlIX.png",Ve={class:"login-content"},Ce={class:"info-panel"},xe={class:"feature-list"},ze={class:"feature-item"},Ue={class:"feature-item"},Ie={class:"feature-item"},Le={class:"form-panel"},Re={class:"form-body"},Se={class:"input-group"},Ae={class:"input-wrapper"},Ne={class:"input-group"},qe={class:"input-wrapper"},Be={key:0,class:"input-group"},De={class:"captcha-wrapper"},Ee={class:"input-wrapper captcha-input"},Fe={class:"captcha-image-wrapper"},Ke=["src"],$e={key:1,class:"captcha-loading"},Me={class:"captcha-tip"},Te={key:1,class:"form-actions"},Ze={key:2,class:"form-actions"},Ge={class:"register-content"},Je={class:"register-welcome"},Oe={class:"input-group"},Xe={class:"input-container"},je={class:"input-group"},He={class:"input-container"},Pe={class:"input-group"},Qe={class:"input-container"},We={class:"input-group"},Ye={class:"input-container"},ea={class:"input-group"},aa={class:"captcha-wrapper"},sa={class:"input-container captcha-input"},ta={class:"captcha-image-wrapper"},la=["src"],oa={key:1,class:"captcha-loading"},na={class:"captcha-tip"},ra={class:"dialog-actions"},ia=oe({__name:"LoginView",setup(ua){const g=d(""),V=d(""),C=d(!1),H=de(),B=ne(),f=d(!1),w=d(""),_=d(""),x=d(""),z=d(!1),R=d(!1),S=d(),i=d({username:"",password:"",real_name:"",department:"",captcha:""}),P={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{pattern:/^[a-zA-Z0-9]+$/,message:"用户名只能包含字母和数字",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:8,message:"密码长度不能小于8位",trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"密码必须包含大小写字母和数字，且长度至少8位",trigger:"blur"}],real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5]+$/,message:"真实姓名必须是中文",trigger:"blur"}],department:[{required:!0,message:"请输入所属部门",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:4,max:4,message:"验证码必须是4位",trigger:"blur"}]},A=d([]),U=async l=>{try{const e=await b.post("/api/captcha/generate",l?{username:l}:{});return e.data.success?e.data.captcha_image:(u.error(e.data.message||"生成验证码失败"),null)}catch{return u.error("生成验证码失败，请检查网络连接"),null}},Q=async l=>{try{const e=await b.post("/api/login/check-captcha",{username:l});e.data.success&&(f.value=e.data.need_captcha,f.value?_.value=await U(l):(w.value="",_.value=""))}catch(e){console.error("检查验证码需求失败:",e)}},D=async()=>{if(!g.value){u.warning("请先输入用户名");return}_.value=await U(g.value)},E=async()=>{x.value=await U()},W=async()=>{try{const l=await b.post("/api/get_departments");l.data&&Array.isArray(l.data.departments)?A.value=l.data.departments:A.value=[]}catch{A.value=[],u.error("获取部门列表失败")}},Y=()=>{z.value=!0,i.value={username:"",password:"",real_name:"",department:"",captcha:""},W(),U().then(l=>{x.value=l}),he(()=>{var l;(l=S.value)==null||l.resetFields()})},ee=async()=>{S.value&&await S.value.validate(async(l,e)=>{if(l){if(!i.value.captcha){u.warning("请输入验证码");return}R.value=!0;try{const o=await b.post("/api/register",i.value);o.data.success?(u.success("注册成功"),z.value=!1,g.value=i.value.username,V.value=i.value.password,x.value=""):u.error(o.data.message||"注册失败")}catch{u.error("注册失败，请检查网络连接")}finally{R.value=!1}}})},I=async()=>{if(!g.value||!V.value){u.warning("请输入用户名和密码");return}if(f.value&&!w.value){u.warning("请输入验证码");return}C.value=!0;try{const l={username:g.value,password:V.value};f.value&&(l.captcha=w.value);const e=await b.post("/api/login",l);if(e.data.success){const{token:o,user:p}=e.data;sessionStorage.setItem("token",o),sessionStorage.setItem("user",JSON.stringify(p)),B.setToken(o),B.setUser(p),f.value=!1,w.value="",_.value="",u.success("登录成功！"),H.push("/")}else e.data.need_captcha?(f.value=!0,_.value=await U(g.value),u.warning(e.data.message)):e.data.message==="账号不存在"?u.error("账号不存在"):e.data.message==="密码错误"?(u.error("密码错误"),await Q(g.value)):u.error(e.data.message||"登录失败")}catch{u.error("登录失败，请检查网络连接")}finally{C.value=!1}},F=l=>{l.key==="Enter"&&I()},y=d(null),K=d(!0),ae=async()=>{try{const l=await b.post("/api/get_allow_register");l.data&&typeof l.data.allow_register=="boolean"?y.value=l.data.allow_register:l.data&&typeof l.data.allow_register=="string"?y.value=l.data.allow_register==="true":y.value=!1}catch{y.value=!1}finally{K.value=!1}};return re(()=>{window.addEventListener("keypress",F),ae()}),ie(()=>{window.removeEventListener("keypress",F)}),(l,e)=>{const o=v("el-icon"),p=v("el-input"),h=v("el-button"),$=v("el-form"),L=v("el-form-item"),se=v("el-option"),te=v("el-select"),le=v("el-dialog");return c(),m("div",{class:"login-container",onKeyup:M(I,["enter"])},[e[29]||(e[29]=a("div",{class:"background-decoration"},[a("div",{class:"decoration-circle circle-1"}),a("div",{class:"decoration-circle circle-2"}),a("div",{class:"decoration-circle circle-3"})],-1)),a("div",Ve,[a("div",Ce,[e[13]||(e[13]=a("div",{class:"brand-section"},[a("img",{src:ke,alt:"Logo",class:"brand-logo"}),a("h1",{class:"brand-title"},"GeoStream Integration"),a("p",{class:"brand-subtitle"},"地理信息流集成平台")],-1)),a("div",xe,[a("div",ze,[s(o,{class:"feature-icon"},{default:t(()=>[s(r(ce))]),_:1}),e[10]||(e[10]=a("span",null,"强大的地理数据处理工具",-1))]),a("div",Ue,[s(o,{class:"feature-icon"},{default:t(()=>[s(r(pe))]),_:1}),e[11]||(e[11]=a("span",null,"智能化数据分析平台",-1))]),a("div",Ie,[s(o,{class:"feature-icon"},{default:t(()=>[s(r(ge))]),_:1}),e[12]||(e[12]=a("span",null,"无缝系统集成解决方案",-1))])])]),a("div",Le,[s($,{class:"login-form",onSubmit:ue(I,["prevent"])},{default:t(()=>[e[20]||(e[20]=a("div",{class:"form-header"},[a("h2",{class:"form-title"},"欢迎回来"),a("p",{class:"form-subtitle"},"请登录您的账户")],-1)),a("div",Re,[a("div",Se,[e[14]||(e[14]=a("label",{class:"input-label"},"账号",-1)),a("div",Ae,[s(o,{class:"input-icon"},{default:t(()=>[s(r(T))]),_:1}),s(p,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=n=>g.value=n),placeholder:"请输入您的账号",class:"custom-input",size:"large"},null,8,["modelValue"])])]),a("div",Ne,[e[15]||(e[15]=a("label",{class:"input-label"},"密码",-1)),a("div",qe,[s(o,{class:"input-icon"},{default:t(()=>[s(r(Z))]),_:1}),s(p,{modelValue:V.value,"onUpdate:modelValue":e[1]||(e[1]=n=>V.value=n),type:"password",placeholder:"请输入您的密码",class:"custom-input",size:"large","show-password":""},null,8,["modelValue"])])]),f.value?(c(),m("div",Be,[e[17]||(e[17]=a("label",{class:"input-label"},"验证码",-1)),a("div",De,[a("div",Ee,[s(o,{class:"input-icon"},{default:t(()=>[s(r(G))]),_:1}),s(p,{modelValue:w.value,"onUpdate:modelValue":e[2]||(e[2]=n=>w.value=n),placeholder:"请输入验证码",class:"custom-input",size:"large",maxlength:"4",onKeyup:M(I,["enter"])},null,8,["modelValue"])]),a("div",Fe,[_.value?(c(),m("img",{key:0,src:_.value,alt:"验证码",class:"captcha-image",onClick:D},null,8,Ke)):(c(),m("div",$e,[s(o,{class:"is-loading"},{default:t(()=>[s(r(J))]),_:1})]))])]),a("div",Me,[s(h,{type:"text",size:"small",onClick:D,class:"refresh-btn"},{default:t(()=>[s(o,null,{default:t(()=>[s(r(O))]),_:1}),e[16]||(e[16]=k(" 刷新验证码 "))]),_:1})])])):N("",!0),K.value?(c(),m("div",Te,e[18]||(e[18]=[a("div",{class:"button-skeleton"},[a("div",{class:"skeleton-button primary full-width"}),a("div",{class:"skeleton-button secondary",style:{opacity:"0.3"}})],-1)]))):(c(),m("div",Ze,[s(h,{type:"primary",loading:C.value,onClick:I,class:me(["login-btn",{"full-width":!y.value}]),size:"large"},{default:t(()=>[C.value?N("",!0):(c(),q(o,{key:0},{default:t(()=>[s(r(ve))]),_:1})),k(" "+X(C.value?"登录中...":"立即登录"),1)]),_:1},8,["loading","class"]),y.value?(c(),q(h,{key:0,onClick:Y,class:"register-btn",size:"large",plain:""},{default:t(()=>[s(o,null,{default:t(()=>[s(r(j))]),_:1}),e[19]||(e[19]=k(" 注册账户 "))]),_:1})):N("",!0)]))])]),_:1})])]),s(le,{modelValue:z.value,"onUpdate:modelValue":e[9]||(e[9]=n=>z.value=n),title:"创建新账户",width:"480px","close-on-click-modal":!1,class:"modern-register-dialog",center:""},{footer:t(()=>[a("div",ra,[s(h,{onClick:e[8]||(e[8]=n=>z.value=!1),size:"large",class:"cancel-button"},{default:t(()=>e[28]||(e[28]=[k(" 取消 ")])),_:1}),s(h,{type:"primary",loading:R.value,onClick:ee,size:"large",class:"register-button"},{default:t(()=>[k(X(R.value?"注册中...":"立即注册"),1)]),_:1},8,["loading"])])]),default:t(()=>[a("div",Ge,[a("div",Je,[s(o,{class:"welcome-icon"},{default:t(()=>[s(r(j))]),_:1}),e[21]||(e[21]=a("p",{class:"welcome-text"},"填写以下信息完成注册",-1))]),s($,{ref_key:"registerFormRef",ref:S,model:i.value,rules:P,class:"register-form","label-width":"0"},{default:t(()=>[s(L,{prop:"username"},{default:t(()=>[a("div",Oe,[e[22]||(e[22]=a("label",{class:"input-label"},"用户名",-1)),a("div",Xe,[s(o,{class:"input-prefix-icon"},{default:t(()=>[s(r(T))]),_:1}),s(p,{modelValue:i.value.username,"onUpdate:modelValue":e[3]||(e[3]=n=>i.value.username=n),placeholder:"仅限字母和数字",size:"large",class:"styled-input"},null,8,["modelValue"])])])]),_:1}),s(L,{prop:"real_name"},{default:t(()=>[a("div",je,[e[23]||(e[23]=a("label",{class:"input-label"},"真实姓名",-1)),a("div",He,[s(o,{class:"input-prefix-icon"},{default:t(()=>[s(r(fe))]),_:1}),s(p,{modelValue:i.value.real_name,"onUpdate:modelValue":e[4]||(e[4]=n=>i.value.real_name=n),placeholder:"请输入中文姓名",size:"large",class:"styled-input"},null,8,["modelValue"])])])]),_:1}),s(L,{prop:"password"},{default:t(()=>[a("div",Pe,[e[24]||(e[24]=a("label",{class:"input-label"},"密码",-1)),a("div",Qe,[s(o,{class:"input-prefix-icon"},{default:t(()=>[s(r(Z))]),_:1}),s(p,{modelValue:i.value.password,"onUpdate:modelValue":e[5]||(e[5]=n=>i.value.password=n),type:"password",placeholder:"至少8位，包含大小写字母和数字","show-password":"",size:"large",class:"styled-input"},null,8,["modelValue"])])])]),_:1}),s(L,{prop:"department"},{default:t(()=>[a("div",We,[e[25]||(e[25]=a("label",{class:"input-label"},"所属部门",-1)),a("div",Ye,[s(o,{class:"input-prefix-icon"},{default:t(()=>[s(r(_e))]),_:1}),s(te,{modelValue:i.value.department,"onUpdate:modelValue":e[6]||(e[6]=n=>i.value.department=n),placeholder:"请选择您的部门",size:"large",class:"styled-select"},{default:t(()=>[(c(!0),m(we,null,ye(A.value,n=>(c(),q(se,{key:n,label:n,value:n},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])]),_:1}),s(L,{prop:"captcha"},{default:t(()=>[a("div",ea,[e[27]||(e[27]=a("label",{class:"input-label"},"验证码",-1)),a("div",aa,[a("div",sa,[s(o,{class:"input-prefix-icon"},{default:t(()=>[s(r(G))]),_:1}),s(p,{modelValue:i.value.captcha,"onUpdate:modelValue":e[7]||(e[7]=n=>i.value.captcha=n),placeholder:"请输入验证码",size:"large",class:"styled-input",maxlength:"4"},null,8,["modelValue"])]),a("div",ta,[x.value?(c(),m("img",{key:0,src:x.value,alt:"验证码",class:"captcha-image",onClick:E},null,8,la)):(c(),m("div",oa,[s(o,{class:"is-loading"},{default:t(()=>[s(r(J))]),_:1})]))])]),a("div",na,[s(h,{type:"text",size:"small",onClick:E,class:"refresh-btn"},{default:t(()=>[s(o,null,{default:t(()=>[s(r(O))]),_:1}),e[26]||(e[26]=k(" 刷新验证码 "))]),_:1})])])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])],32)}}}),ca=be(ia,[["__scopeId","data-v-66839f36"]]);export{ca as default};
