import{d as al,u as ll,r as _,I as qe,K as Ee,a as oa,o as tl,c as _e,e as t,w as o,f as T,E as i,i as L,X as ra,M as ge,Y as He,b as g,l as A,T as Fe,y,U as sa,s as P,x as Oe,A as F,v as Ne,Z as na,$ as ua,a0 as ia,a1 as ol,N as rl,a2 as da,O as sl,V as nl,F as pa,G as ca,a3 as Xe,Q as ye,a4 as ul,k as R,a5 as ne,_ as il}from"./index-Dn7OnccA.js";import{g as dl}from"./market-Dpwtuwfb.js";import{f as pl}from"./format-CBpsKyOP.js";const cl={class:"tools-container"},fl={class:"tab-content tab-applications"},ml={class:"search-section"},vl={class:"search-row"},_l={class:"search-buttons"},gl={class:"table-container"},yl={class:"pagination-container"},hl={class:"tab-content tab-tools"},bl={class:"search-section"},wl={class:"search-row"},Cl={class:"search-buttons"},zl={class:"table-header"},kl={class:"table-container"},$l={class:"pagination-container"},Ul={key:0,class:"message-content"},xl={key:6,class:"color-picker-wrapper"},Vl={class:"color-value"},Tl={key:1,class:"no-params"},Rl={class:"dialog-footer"},Al={class:"dialog-footer"},Dl={class:"dialog-footer"},Sl={class:"pagination-container"},Il={class:"error-log-content"},Pl={class:"dialog-footer"},Ll=5e3,Ml=al({__name:"ToolsView",setup(ql){const U=ll(),O=_(!1),he=_([]),J=_([]),be=_(""),we=_(""),ue=_("applications"),ie=_(1),ee=_(1),de=_(1),Ce=_(10),ze=_(10),ae=_(0);_(0);const N=_(localStorage.getItem("token")),M={PARSE_FMW:"/api/parse_fmw",RUN_TOOL:"/api/run_fme",UPDATE_TOOL:"/api/tools/update",UPLOAD_FILE:"/api/upload",UPLOAD_TOOL:"/api/tools/upload",MY_APPLICATIONS:"/api/tools/my-applications",RUN_RECORDS:"/api/tools/run_records",DELETE_RESULT:"/api/tools/delete_result",DOWNLOAD_RESULT:"/api/tools/download_result",UPDATE_COUNT:"/api/tools/update_usacount",VERIFY_RUN_PERMISSION:"/api/tools/verify_run_permission"},De=()=>({Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}),We=qe(()=>he.value.filter(a=>a.fmw_name.toLowerCase().includes(be.value.toLowerCase())||a.project.toLowerCase().includes(be.value.toLowerCase()))),Qe=qe(()=>J.value.filter(a=>a.fmw_name.toLowerCase().includes(we.value.toLowerCase())||a.project.toLowerCase().includes(we.value.toLowerCase()))),fa=qe(()=>{const a=(de.value-1)*Ce.value,e=a+Ce.value;return We.value.slice(a,e)}),ma=qe(()=>{const a=(ee.value-1)*ze.value,e=a+ze.value;return Qe.value.slice(a,e)}),je=()=>{ie.value=1},Be=()=>{ee.value=1},va=()=>{be.value="",de.value=1},_a=()=>{we.value="",ee.value=1},ga=a=>{ee.value=a},pe=async()=>{O.value=!0;try{const a=await dl();a.success?(he.value=a.data.filter(e=>{var n;return e.author===((n=U.user)==null?void 0:n.username)}).map(e=>({...e,created_at:e.created_at&&!isNaN(new Date(e.created_at).getTime())?e.created_at:new Date().toISOString()})),await ke()):(i.error(a.message||"获取工具列表失败"),he.value=[])}catch{i.error("获取工具列表失败，请检查网络连接"),he.value=[]}finally{O.value=!1}},ke=async()=>{var a;try{if(!((a=U.user)!=null&&a.username)){J.value=[];return}const e=await L.get(M.MY_APPLICATIONS,{headers:{"X-Username":U.user.username},params:{source:"tools"}});e.data.success?J.value=e.data.data.filter(n=>n.status==="已通过").map(n=>({...n,count:parseInt(n.count)||0,usage_count:parseInt(n.usage_count)||0,remaining_count:(parseInt(n.usage_count)||0)-(parseInt(n.count)||0),project:n.user_project||"未指定项目",end_date:n.end_date||null,created_at:n.created_at&&!isNaN(new Date(n.created_at).getTime())?n.created_at:new Date().toISOString()})):(i.error(e.data.message||"获取申请列表失败"),J.value=[])}catch{J.value=[]}},G=(a,e="yyyy-MM-dd HH:mm")=>{if(!a)return"--";try{return pl(new Date(a),e)}catch{return"--"}};Ee(()=>U.user,a=>{a!=null&&a.username?pe():(he.value=[],J.value=[])},{immediate:!0});const le=_(!1),z=_(null),Q=_([]),m=_({}),q=_(null),$e=_({}),Ue=_(!1),Se=_(!1),ya=async a=>{var e,n,d,c,p;try{if(!a||!a.fmw_id){i.error("工具信息不完整");return}Ue.value=!1,Se.value=!0,z.value={...a,isApplyRun:!1,isRun:!0},console.log("handleRun 设置后的状态:",{isApplyRun:Ue.value,isRun:Se.value,currentTool:z.value}),O.value=!0,console.log("开始解析工具参数:",{fmw_id:a.fmw_id,username:U.user.username});const u=await L.post(M.PARSE_FMW,{fmw_id:a.fmw_id},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});console.log("解析参数响应:",u.data),console.log("后端返回的原始参数:",u.data.data.parameters);const b=(u.data.data.def_params||"N")==="Y",j=[],X=u.data.data.parameters,ve=u.data.data.parameters_dict,D={},se={};(!Array.isArray(X)||X.length===0)&&(console.warn("未找到任何参数"),i.warning("该工具没有可配置的参数"));for(const x of X)try{const v=x.name,s=x.info;if(!v||!s){console.warn("参数格式不正确:",x);continue}if(s.access_mode==="write"){console.log(`跳过write类型参数 ${v}`);continue}console.log(`处理参数 ${v}:`,s);const B=s.type==="file"||((e=s.prompt)==null?void 0:e.includes("压缩包"));let K={prop:v,label:s.prompt||v,required:s.required,tip:s.prompt||"",type:"text",order:s.order,component:{type:"el-input",props:{placeholder:""}}};s.visibility&&(K.component.visibility=s.visibility);const S=xe(K);let f=b;if(!S){B&&b&&(se[v]=s.default_value||"",console.log(`参数 ${v} 不显示但保存到hiddenParams:`,s.default_value));continue}s.type==="file"&&console.log(`文件参数 ${v} 的配置:`,{itemsToSelect:s.itemsToSelect,is_folder:s.is_folder,file_types:s.file_types,selectMultiple:s.selectMultiple});let r={prop:v,label:s.prompt||v,required:s.required,tip:s.prompt||"",type:"text",order:s.order,component:{type:"el-input",props:{placeholder:""}}};if(s.required&&(D[v]=[{required:!0,message:`请输入${s.prompt||v}`,trigger:["blur","change"]}]),s.type==="message"||(n=s.prompt)!=null&&n.includes("消息")||(d=s.prompt)!=null&&d.includes("提示"))r.type="message",r.component={type:"div",props:{class:"message-content"},content:s.prompt||v},delete D[v];else if((s.type==="dropdown"||s.type==="listbox")&&s.options&&s.options.length>0){if(r.type="select",r.component={type:"el-select",props:{placeholder:"",multiple:s.type==="listbox",delimiter:s.delimiter||","},options:s.options.map(V=>typeof V=="object"&&V!==null?{label:V.label||V,value:V.value||V.label||V}:{label:V,value:V})},f&&s.default_value)s.type==="listbox"?m.value[v]=s.default_value.split(s.delimiter||","):m.value[v]=s.default_value;else if(s.options.length>0){const V=s.options[0];m.value[v]=typeof V=="object"?V.value:V}}else if(s.type==="color")r.type="color",r.component={type:"el-color-picker",props:{showAlpha:!1,colorFormat:"rgb",showPicker:!0,size:"default",popperClass:"custom-color-picker"}},m.value[v]=f&&s.default_value?s.default_value:"rgb(255, 255, 255)",s.required&&(D[v]=[{required:!0,message:`请选择${s.prompt||v}`,trigger:["change"]}]);else if(s.type==="file"||(c=s.prompt)!=null&&c.includes("压缩包")){r.type="upload";const V=((p=s.file_types)==null?void 0:p.map(E=>E.replace("*","")).filter(E=>s.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(E.toLowerCase())).join(","))||"";r.component={type:"el-upload",props:{action:`${M.UPLOAD_FILE}`,"on-success":E=>Ge(v,E),"on-remove":()=>Ke(v),"before-remove":Ze,"before-upload":E=>{if(s.file_types&&s.file_types.length>0){const Z=E.name.substring(E.name.lastIndexOf(".")).toLowerCase();if(!s.file_types.some(C=>{const l=C.replace("*","").toLowerCase();return(s.is_folder||![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(l))&&Z===l})){const C=s.file_types.filter(l=>s.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(l.toLowerCase())).join("、");return i.error(`只能上传${C}格式的文件`),!1}}return!0},multiple:s.selectMultiple||!1,"show-file-list":!0,accept:V,headers:De()}},s.required&&(D[v]=[{required:!0,message:`请上传${s.prompt||"文件"}`,trigger:["change"]}])}else s.type==="number"?(r.type="number",r.component={type:"el-input-number",props:{min:0,max:999999}},m.value[v]=f&&s.default_value?Number(s.default_value):0):s.type==="datetime"?(r.type="datetime",r.component={type:"el-date-picker",props:{type:"datetime",placeholder:"",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",style:"width: 100%"}},m.value[v]=f&&s.default_value?s.default_value:"",s.required&&(D[v]=[{required:!0,message:`请选择${s.prompt||v}`,trigger:["change"]}])):s.type==="checkbox"||s.type==="boolean"?(r.type="checkbox",r.component={type:"el-checkbox",props:{class:"custom-checkbox"}},m.value[v]=f&&s.default_value?s.default_value:"NO",s.required&&(D[v]=[{required:!0,message:`请选择${s.prompt||v}`,trigger:["change"]}])):(s.type==="text"||!s.type)&&(r.type="text",r.component={type:"el-input",props:{placeholder:""}},m.value[v]=f&&s.default_value?s.default_value:"");s.visibility&&(console.log(`参数 ${v} 的visibility条件:`,s.visibility),r.component.visibility=s.visibility),j.push(r)}catch(v){console.error("处理参数失败:",v),i.error("处理参数失败，请检查网络连接")}Q.value=j.sort((x,v)=>x.order-v.order),console.log("处理后的表单项:",Q.value),$e.value=D,z.value={...a,fmw_path:u.data.data.path,name:u.data.data.name,hiddenParams:se},le.value=!0}catch(u){console.error("运行工具失败:",u);let h="运行工具失败";if(u.response){const b=u.response.data;h=(b==null?void 0:b.error)||(b==null?void 0:b.message)||"服务器错误",console.error("服务器返回错误:",b)}else u.request?(h="网络请求失败，请检查网络连接",console.error("网络请求错误:",u.request)):console.error("请求配置错误:",u.message);i.error(h)}finally{O.value=!1}},Ge=(a,e)=>{e.success?m.value[a]=e.data.path:i.error(e.message||"文件上传失败")},Ke=a=>{m.value[a]=""},Ze=a=>!0,Ie=_(!1),Y=_([]),I=_(null),Je=a=>a.some(e=>{var d;const n=(d=e.status)==null?void 0:d.toLowerCase();return n==="running"||n==="pending"||n==="processing"||n==="运行中"||n==="处理中"||n==="等待中"});Ee(Ie,a=>{a?ha():(ie.value=1,I.value&&(clearInterval(I.value),I.value=null))});const ha=()=>{I.value&&clearInterval(I.value),I.value=setInterval(async()=>{Je(Y.value)?(console.log("定时刷新运行记录..."),await Pe(!1)):(console.log("没有运行中的任务，停止定时器"),I.value&&(clearInterval(I.value),I.value=null))},Ll)};oa(()=>{I.value&&(clearInterval(I.value),I.value=null)});const ba=a=>{const e=(a||"").toLowerCase().replace(/\s/g,"");return["approved","已通过","completed","success","已完成"].some(n=>e.includes(n))?"success":["rejected","已驳回","failed","error","运行失败"].some(n=>e.includes(n))?"danger":["pending","审批中"].some(n=>e.includes(n))?"info":["running","processing","运行中","处理中","等待中"].some(n=>e.includes(n))?"warning":"info"},wa=a=>{switch(a==null?void 0:a.toLowerCase()){case"approved":return"已通过";case"rejected":return"已驳回";case"pending":return"审批中";case"running":case"processing":return"运行中";case"completed":case"success":return"已完成";case"failed":case"error":return"运行失败";case"运行中":return"运行中";case"处理中":return"处理中";case"等待中":return"等待中";case"已完成":return"已完成";case"运行失败":return"运行失败";default:return a||"未知状态"}},Ca=a=>{if(!a)return"-";const e=Math.floor(a/60),n=a%60;return`${e}分${n}秒`},za=async a=>{try{if(!a.tool_id||!a.task_id||!a.file_name){console.error("缺少必要参数:",{tool_id:a.tool_id,task_id:a.task_id,file_name:a.file_name}),i.error("下载失败：缺少必要参数");return}const e=`models/${a.tool_id}/output/${a.task_id}/${a.file_name}`,n=document.createElement("iframe");n.style.display="none",document.body.appendChild(n);const d=document.createElement("a");d.style.display="none",document.body.appendChild(d);const c=`${M.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`;d.href=c,d.download=a.file_name,d.click(),setTimeout(()=>{document.body.removeChild(d),document.body.removeChild(n)},100),i.success("开始下载")}catch(e){console.error("下载运行成果失败:",e),i.error("下载失败，请稍后重试")}},ka=async a=>{try{await ye.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=Y.value.findIndex(n=>n.task_id===a.task_id);e!==-1&&(Y.value.splice(e,1),ae.value--),L.post("/api/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}}).then(n=>{n.data.success?i.success("删除成功"):(Y.value.splice(e,0,a),ae.value++,i.error(n.data.message||"删除失败"))}).catch(n=>{Y.value.splice(e,0,a),ae.value++,i.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&i.error("删除失败，请稍后重试")}},Pe=async(a=!0)=>{var e,n,d,c;if(!((e=z.value)!=null&&e.fmw_id)){console.error("缺少 fmw_id，当前工具信息:",z.value),i.error("获取运行记录失败：缺少必要参数");return}if(!((n=U.user)!=null&&n.username)){console.error("缺少 username，当前用户信息:",U.user),i.error("获取运行记录失败：用户未登录");return}a&&(O.value=!0);try{const p={fmw_id:z.value.fmw_id,username:U.user.username,page:ie.value,page_size:10};console.log("获取运行记录参数:",p);const u=await L.get(`/api/tools/run_records?fmw_id=${p.fmw_id}&username=${p.username}&page=${p.page}&page_size=${p.page_size}`,{headers:{Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});u.data.success?(Y.value=Array.isArray(u.data.data.records)?u.data.data.records:[],ae.value=((d=u.data.data.pagination)==null?void 0:d.total)||0,a||!Je(Y.value)&&I.value&&(console.log("没有运行中的任务，停止定时器"),clearInterval(I.value),I.value=null)):(i.error(u.data.message||"获取运行记录失败"),Y.value=[],ae.value=0)}catch(p){console.error("获取运行记录失败:",p),p.response?(console.error("错误响应:",p.response.data),a&&i.error(((c=p.response.data)==null?void 0:c.message)||"获取运行记录失败")):a&&i.error("获取运行记录失败"),Y.value=[],ae.value=0}finally{a&&(O.value=!1)}},$a=a=>{de.value=a},Ua=a=>{console.log("页码改变:",a),console.log("当前工具信息:",z.value),ie.value=a,Pe()},xa=a=>{ye.confirm("确定要删除该申请吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await L.post("/api/tools/delete-application",{applicationId:a.id},{headers:{"X-Username":U.user.username}});e.data.success?(i.success("删除成功"),await ke()):i.error(e.data.message||"删除失败")}catch{i.error("删除申请失败，请检查网络连接")}}).catch(()=>{})},Va=a=>{ye.confirm("确定要撤回该申请吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await L.post("/api/tools/withdraw-application",{applicationId:a.id},{headers:{"X-Username":U.user.username}});e.data.success?(i.success("撤回成功"),await ke()):i.error(e.data.message||"撤回失败")}catch{i.error("撤回申请失败，请检查网络连接")}}).catch(()=>{})};tl(()=>{pe()}),oa(()=>{});const ce=_(!1),fe=_(!1),w=_({fmw_name:"",project:"",tools_class:"",description:"",file:null,use_default_params:!1}),H=_({fmw_id:"",file:null}),Ta={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],tools_class:[{required:!0,message:"请选择工具类型",trigger:"change"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},te=_(null),oe=_(null),Ra=()=>{le.value=!1},Aa=()=>{var a;m.value={},Q.value=[],q.value&&q.value.resetFields(),(a=q.value)!=null&&a.$el&&q.value.$el.querySelectorAll(".el-upload").forEach(n=>{var c;const d=(c=n.__vueParentComponent)==null?void 0:c.ctx;d&&typeof d.clearFiles=="function"&&d.clearFiles()})},Da=()=>{ce.value=!1},Sa=()=>{re.value&&re.value.resetFields(),w.value={fmw_name:"",project:"",description:"",file:null,file_path:"",use_default_params:!1},te.value&&typeof te.value.clearFiles=="function"&&te.value.clearFiles()},Ia=()=>{fe.value=!1},Pa=()=>{Le.value&&Le.value.resetFields(),H.value={fmw_id:"",file:null,file_path:""},oe.value&&typeof oe.value.clearFiles=="function"&&oe.value.clearFiles()},La=()=>{ce.value=!0},re=_(),Le=_();Ee(()=>m.value,a=>{const e={};Q.value.forEach(n=>{xe(n)&&$e.value[n.prop]&&(e[n.prop]=$e.value[n.prop])}),q.value&&(q.value.clearValidate(),q.value.rules=e)},{deep:!0});const Ma=async()=>{var a;if(!Ve.value){Ve.value=!0;try{if(!z.value){i.error("工具信息不完整");return}const e=Q.value.filter(p=>xe(p));console.log("可见的表单项:",e.map(p=>p.prop));for(const p of e)if(p.required&&!m.value[p.prop]){let u="";p.type==="file"||p.type==="upload"?u=`请上传${p.label}`:p.type==="select"||p.type==="dropdown"||p.type==="listbox"?u=`请选择${p.label}`:u=`请填写${p.label}`,i.error(u);return}const d={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:z.value.fmw_id,fmw_name:z.value.fmw_name,fmw_path:z.value.fmw_path,params:{}};for(const p of e){const u=m.value[p.prop];u!=null&&!p.prop.endsWith("_value")&&(p.type==="color"?d.params[p.prop]=m.value[`${p.prop}_value`]:d.params[p.prop]=u)}if(z.value.hiddenParams&&(Object.assign(d.params,z.value.hiddenParams),console.log("合并的hiddenParams:",z.value.hiddenParams)),console.log("提交的请求数据:",d),Ue.value)try{const p=await L.post(M.UPDATE_COUNT,{id:z.value.id,username:U.user.username});if(!p.data.success){i.error(p.data.message||"更新使用次数失败");return}const u=J.value.find(h=>h.id===z.value.id);u&&(u.count=(parseInt(u.count)||0)+1,u.remaining_count=(parseInt(u.usage_count)||0)-u.count)}catch(p){console.error("更新使用次数失败:",p),i.error("更新使用次数失败，请稍后重试");return}const c=await L.post(M.RUN_TOOL,d,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});c.data.success?(i.success("任务提交成功"),m.value={},Q.value=[],q.value&&q.value.resetFields(),(a=q.value)!=null&&a.$el&&q.value.$el.querySelectorAll(".el-upload").forEach(u=>{var b;const h=(b=u.__vueParentComponent)==null?void 0:b.ctx;h&&typeof h.clearFiles=="function"&&h.clearFiles()}),le.value=!1,await Pe(),await ke()):i.error(c.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),i.error("提交失败，请稍后重试")}finally{Ve.value=!1}}},qa=async()=>{if(!Te.value&&(Te.value=!0,!!re.value))try{if(await re.value.validate(),!w.value.file_path){i.error("请上传工具文件");return}const a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",n={fmw_id:Array.from({length:40},()=>a[Math.floor(Math.random()*a.length)]).join(""),fmw_name:w.value.fmw_name,project:w.value.project,tools_class:w.value.tools_class,description:w.value.description,fmw_path:w.value.file_path,file_path:w.value.file_path,data:new Date().toISOString(),def_params:w.value.use_default_params?"Y":"N"},d=await L.post(`${M.UPLOAD_TOOL}`,n,{headers:{Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});d.data.success?(i.success("工具上传成功"),ce.value=!1,w.value={fmw_name:"",project:"",description:"",file:null,file_path:"",use_default_params:!1},re.value&&re.value.resetFields(),te.value&&typeof te.value.clearFiles=="function"&&te.value.clearFiles(),await pe()):i.error(d.data.message||"上传失败")}catch(a){console.error("上传工具失败:",a),i.error("参数未填写完整")}finally{Te.value=!1}},Ea=async()=>{if(!me.value){if(me.value=!0,!H.value.file_path){i.error("请先上传工具文件"),me.value=!1;return}try{const a={fmw_id:H.value.fmw_id,file_path:H.value.file_path},e=await L.post(`${M.UPDATE_TOOL}`,a,{headers:{Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});e.data.success?(i.success("工具更新成功"),fe.value=!1,H.value={fmw_id:"",file:null,file_path:""},Le.value&&Le.value.resetFields(),oe.value&&typeof oe.value.clearFiles=="function"&&oe.value.clearFiles(),await pe()):i.error(e.data.message||"更新失败")}catch(a){console.error("更新工具失败:",a),i.error("更新失败，请检查网络连接")}finally{me.value=!1}}},ea=async a=>{try{z.value=a,Ie.value=!0,await Pe()}catch(e){console.error("获取运行记录失败:",e),i.error("获取运行记录失败，请检查网络连接")}},Fa=a=>{H.value={fmw_id:a.fmw_id,file:null},fe.value=!0},Oa=async a=>{try{if(await ye.confirm("是否确认下载该工具？","下载确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"}),!a.fmw_path){i.error("下载失败：缺少必要参数");return}const e=`/api/tools/download?fmw_path=${encodeURIComponent(a.fmw_path)}`;window.open(e),i.success("已发起下载")}catch(e){if(e==="cancel")return;i.error("下载失败，请稍后重试")}},Na=async a=>{try{await ye.confirm("确定要删除该工具吗？删除后无法恢复。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await L.post("/api/tools/delete",{fmw_id:a.fmw_id},{headers:{Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});e.data.success?(i.success("删除成功"),await pe()):i.error(e.data.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除工具失败:",e),i.error("删除失败，请检查网络连接"))}},ja=async a=>{try{const e=await L.post("/api/tools/detail",{fmw_id:a.fmw_id},{headers:{Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});if(e.data.success){const n=e.data.data;ye.alert(`
        <div class="tool-detail">
          <p><strong>工具名称：</strong>${n.fmw_name}</p>
          <p><strong>所属项目：</strong>${n.project}</p>
          <p><strong>上传时间：</strong>${G(n.created_at)}</p>
          <p><strong>运行次数：</strong>${n.run_times}</p>
          <p><strong>工具描述：</strong>${n.description||"暂无描述"}</p>
        </div>
        `,"工具详情",{confirmButtonText:"确定",dangerouslyUseHTMLString:!0,customClass:"tool-detail-dialog"})}else i.error(e.data.message||"获取详情失败")}catch(e){console.error("获取工具详情失败:",e),i.error("获取详情失败，请检查网络连接")}},Ba=(a,e)=>{if(!e){m.value[a]="rgb(255, 255, 255)";return}m.value[a]=e;const n=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(n){const[,d,c,p]=n;m.value[`${a}_value`]=`${d},${c},${p}`}else m.value[`${a}_value`]="255,255,255"},xe=a=>{var n;if(!((n=a.component)!=null&&n.visibility))return!0;const e=a.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const d of e.if){const{condition:c,then:p}=d;let u=!1;if(c.allOf)u=c.allOf.every(h=>{if(h.equals){const{parameter:b,value:j}=h.equals;return m.value[b]===j}else if(h.isEnabled){const{parameter:b}=h.isEnabled;return!!m.value[b]}return!1});else if(c.equals){const{parameter:h,value:b}=c.equals;u=m.value[h]===b}else if(c.isEnabled){const{parameter:h}=c.isEnabled;u=!!m.value[h]}if(u)return p==="visibleEnabled"||p==="visibleDisabled"}return!1},Ya=async a=>{var e,n,d,c,p,u,h,b,j,X,ve;try{if(!a||!a.fmw_id){i.error("工具信息不完整");return}console.log("handleApplyRun 接收到的 row:",a);try{const S=await L.post(M.VERIFY_RUN_PERMISSION,{id:a.id,username:U.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});if(!S.data.success){i.error(S.data.message||"验证运行权限失败");return}}catch(S){console.error("验证运行权限失败:",S),S.response?i.error(((e=S.response.data)==null?void 0:e.message)||"验证运行权限失败"):i.error("验证运行权限失败，请稍后重试");return}Ue.value=!0,Se.value=!1,z.value={...a,isApplyRun:!0,isRun:!1},console.log("handleApplyRun 设置后的状态:",{isApplyRun:Ue.value,isRun:Se.value,currentTool:z.value,id:z.value.id,fmw_id:z.value.fmw_id}),O.value=!0;const D=await L.post(M.PARSE_FMW,{fmw_id:a.fmw_id},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${N.value}`,"X-Username":U.user.username}});if(!((n=D.data)!=null&&n.success)||!((c=(d=D.data)==null?void 0:d.data)!=null&&c.parameters)){const S=((u=(p=D.data)==null?void 0:p.data)==null?void 0:u.error)||"解析工具参数失败";i.error(S);return}const x=(D.data.data.def_params||"N")==="Y",v=[],s=D.data.data.parameters,B={},K={};for(const S of s)try{const f=S.name,r=S.info;if(!f||!r||r.access_mode==="write")continue;const V=r.type==="file"||((h=r.prompt)==null?void 0:h.includes("压缩包"));let E={prop:f,label:r.prompt||f,required:r.required,tip:r.prompt||"",type:"text",order:r.order,component:{type:"el-input",props:{placeholder:""}}};r.visibility&&(E.component.visibility=r.visibility);const Z=xe(E);let W=x;if(!Z){V&&x&&(K[f]=r.default_value||"",console.log(`参数 ${f} 不显示但保存到hiddenParams:`,r.default_value));continue}let C={prop:f,label:r.prompt||f,required:r.required,tip:r.prompt||"",type:"text",order:r.order,component:{type:"el-input",props:{placeholder:""}}};if(r.required&&(B[f]=[{required:!0,message:`请输入${r.prompt||f}`,trigger:["blur","change"]}]),r.type==="message"||(b=r.prompt)!=null&&b.includes("消息")||(j=r.prompt)!=null&&j.includes("提示"))C.type="message",C.component={type:"div",props:{class:"message-content"},content:r.prompt||f},delete B[f];else if((r.type==="dropdown"||r.type==="listbox")&&r.options&&r.options.length>0){if(C.type="select",C.component={type:"el-select",props:{placeholder:""},options:r.options.map(l=>typeof l=="object"&&l!==null?{label:l.label||l,value:l.value||l.label||l}:{label:l,value:l})},W&&r.default_value)m.value[f]=r.default_value;else if(r.options.length>0){const l=r.options[0];m.value[f]=typeof l=="object"?l.value:l}}else if(r.type==="color")C.type="color",C.component={type:"el-color-picker",props:{showAlpha:!1,colorFormat:"rgb",showPicker:!0,size:"default",popperClass:"custom-color-picker"}},m.value[f]=W&&r.default_value?r.default_value:"rgb(255, 255, 255)",r.required&&(B[f]=[{required:!0,message:`请选择${r.prompt||f}`,trigger:["change"]}]);else if(r.type==="file"||(X=r.prompt)!=null&&X.includes("压缩包")){C.type="upload";const l=((ve=r.file_types)==null?void 0:ve.map(k=>k.replace("*","")).filter(k=>r.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(k.toLowerCase())).join(","))||"";C.component={type:"el-upload",props:{action:`${M.UPLOAD_FILE}`,"on-success":k=>Ge(f,k),"on-remove":()=>Ke(f),"before-remove":Ze,"before-upload":k=>{if(r.file_types&&r.file_types.length>0){const Re=k.name.substring(k.name.lastIndexOf(".")).toLowerCase();if(!r.file_types.some($=>{const Ae=$.replace("*","").toLowerCase();return(r.is_folder||![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(Ae))&&Re===Ae})){const $=r.file_types.filter(Ae=>r.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(Ae.toLowerCase())).join("、");return i.error(`只能上传${$}格式的文件`),!1}}return!0},multiple:r.selectMultiple||!1,"show-file-list":!0,accept:l,headers:De()}},r.required&&(B[f]=[{required:!0,message:`请上传${r.prompt||"文件"}`,trigger:["change"]}])}else r.type==="number"?(C.type="number",C.component={type:"el-input-number",props:{min:0,max:999999}},m.value[f]=W&&r.default_value?Number(r.default_value):0):r.type==="datetime"?(C.type="datetime",C.component={type:"el-date-picker",props:{type:"datetime",placeholder:"",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",style:"width: 100%"}},m.value[f]=W&&r.default_value?r.default_value:"",r.required&&(B[f]=[{required:!0,message:`请选择${r.prompt||f}`,trigger:["change"]}])):r.type==="checkbox"||r.type==="boolean"?(C.type="checkbox",C.component={type:"el-checkbox",props:{class:"custom-checkbox"}},m.value[f]=W&&r.default_value?r.default_value:"NO",r.required&&(B[f]=[{required:!0,message:`请选择${r.prompt||f}`,trigger:["change"]}])):(r.type==="text"||!r.type)&&(C.type="text",C.component={type:"el-input",props:{placeholder:""}},m.value[f]=W&&r.default_value?r.default_value:"");r.visibility&&(console.log(`参数 ${f} 的visibility条件:`,r.visibility),C.component.visibility=r.visibility),v.push(C)}catch{console.error("处理参数失败，请检查网络连接")}Q.value=v.sort((S,f)=>S.order-f.order),$e.value=B,z.value={...a,fmw_path:D.data.data.path,name:D.data.data.name,hiddenParams:K},le.value=!0}catch{i.error("运行工具失败")}finally{O.value=!1}},Me=_(!1),aa=_(""),Ha=a=>{aa.value=a,Me.value=!0},Xa=a=>{Ce.value=a,de.value=1},Wa=a=>{ze.value=a,ee.value=1},Qa=(a,e)=>{a.success&&a.data&&a.data.path?(H.value.file_path=a.data.path,H.value.file=e):(i.error(a.message||"文件上传失败"),H.value.file_path="")},Ga=a=>{i.error("文件上传失败，请检查网络连接"),H.value.file_path=""};_(0);const Ka=(a,e)=>{a.success&&a.data&&a.data.path?(w.value.file_path=a.data.path,w.value.file=e):(i.error(a.message||"文件上传失败"),w.value.file_path="")},Za=a=>{i.error("文件上传失败，请检查网络连接"),w.value.file_path=""},Ja=a=>{ue.value=a.name,a.name==="applications"?ke():a.name==="tools"&&pe()},la=_(!1);Ee(ue,(a,e)=>{a!==e&&(la.value=!0,setTimeout(()=>{la.value=!1},400))});const ta=ul(),el=a=>{const e=a.row||a;return ta.query.highlightId&&String(e.id)===String(ta.query.highlightId)?"highlight-row":""},Ve=_(!1),Te=_(!1),me=_(!1);return(a,e)=>{var W,C;const n=T("el-input"),d=T("el-icon"),c=T("el-button"),p=T("el-card"),u=T("el-table-column"),h=T("el-tooltip"),b=T("el-button-group"),j=T("el-table"),X=T("el-pagination"),ve=T("el-tab-pane"),D=T("el-tabs"),se=T("el-upload"),x=T("el-option"),v=T("el-select"),s=T("el-input-number"),B=T("el-date-picker"),K=T("el-checkbox"),S=T("el-color-picker"),f=T("el-form-item"),r=T("el-form"),V=T("el-dialog"),E=T("el-tag"),Z=rl("loading");return R(),_e("div",cl,[t(D,{modelValue:ue.value,"onUpdate:modelValue":e[6]||(e[6]=l=>ue.value=l),class:"tools-tabs",onTabClick:Ja},{default:o(()=>[t(ve,{label:"我的申请",name:"applications"},{default:o(()=>[t(ra,{name:"tab-slide",mode:"out-in"},{default:o(()=>[ge(g("div",fl,[t(p,{class:"search-card"},{default:o(()=>[g("div",ml,[g("div",vl,[t(n,{modelValue:we.value,"onUpdate:modelValue":e[0]||(e[0]=l=>we.value=l),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":A(Fe),onClear:Be,onInput:Be},null,8,["modelValue","prefix-icon"]),g("div",_l,[t(c,{type:"primary",onClick:Be},{default:o(()=>[t(d,null,{default:o(()=>[t(A(Fe))]),_:1}),e[22]||(e[22]=y(" 搜索 "))]),_:1}),t(c,{onClick:_a},{default:o(()=>[t(d,null,{default:o(()=>[t(A(sa))]),_:1}),e[23]||(e[23]=y(" 重置 "))]),_:1})])])])]),_:1}),t(p,{class:"table-card"},{header:o(()=>e[24]||(e[24]=[g("div",{class:"table-header"},[g("span",{class:"title"},"我的申请")],-1)])),default:o(()=>[g("div",gl,[ge((R(),P(j,{data:ma.value,style:{width:"100%"},"row-class-name":el},{default:o(()=>[t(u,{type:"index",label:"序号",width:"80",align:"center"}),t(u,{prop:"fmw_name",label:"工具名称","min-width":"200","show-overflow-tooltip":"",align:"left"}),t(u,{prop:"project",label:"所属项目","min-width":"150","show-overflow-tooltip":"",align:"left"}),t(u,{prop:"usage_count",label:"申请运行次数",width:"120",align:"center"},{default:o(({row:l})=>[g("span",{class:Oe({highlight:l.usage_count>0})},F(l.usage_count),3)]),_:1}),t(u,{label:"已运行次数",width:"120",align:"center"},{default:o(({row:l})=>[g("span",{class:Oe({highlight:l.count>0})},F(l.count),3)]),_:1}),t(u,{prop:"end_date",label:"使用截止日期",width:"180",align:"center"},{default:o(({row:l})=>[t(h,{content:G(l.end_date,"yyyy-MM-dd"),placement:"top"},{default:o(()=>[g("span",null,F(G(l.end_date,"yyyy-MM-dd")),1)]),_:2},1032,["content"])]),_:1}),t(u,{prop:"created_at",label:"申请时间",width:"180",align:"center"},{default:o(({row:l})=>[t(h,{content:G(l.created_at,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:o(()=>[g("span",null,F(G(l.created_at,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(u,{label:"操作",width:"300",align:"center",fixed:"right"},{default:o(({row:l})=>[t(b,null,{default:o(()=>[t(c,{type:"primary",size:"small",onClick:k=>Ya(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(na))]),_:1}),e[25]||(e[25]=y(" 运行 "))]),_:2},1032,["onClick"]),t(c,{type:"success",size:"small",onClick:k=>ea(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(ua))]),_:1}),e[26]||(e[26]=y(" 运行成果 "))]),_:2},1032,["onClick"]),l.status==="已通过"?(R(),P(c,{key:0,type:"danger",size:"small",onClick:k=>xa(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(ia))]),_:1}),e[27]||(e[27]=y(" 删除 "))]),_:2},1032,["onClick"])):Ne("",!0),l.status==="审批中"?(R(),P(c,{key:1,type:"warning",size:"small",onClick:k=>Va(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(ol))]),_:1}),e[28]||(e[28]=y(" 撤回 "))]),_:2},1032,["onClick"])):Ne("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Z,O.value]]),g("div",yl,[t(X,{"current-page":ee.value,"onUpdate:currentPage":e[1]||(e[1]=l=>ee.value=l),"page-size":ze.value,"onUpdate:pageSize":e[2]||(e[2]=l=>ze.value=l),"page-sizes":[10,20,50,100],total:Qe.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Wa,onCurrentChange:ga},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[He,ue.value==="applications"]])]),_:1})]),_:1}),t(ve,{label:"我的工具",name:"tools"},{default:o(()=>[t(ra,{name:"tab-slide",mode:"out-in"},{default:o(()=>[ge(g("div",hl,[t(p,{class:"search-card"},{default:o(()=>[g("div",bl,[g("div",wl,[t(n,{modelValue:be.value,"onUpdate:modelValue":e[3]||(e[3]=l=>be.value=l),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":A(Fe),onClear:je,onInput:je},null,8,["modelValue","prefix-icon"]),g("div",Cl,[t(c,{type:"primary",onClick:je},{default:o(()=>[t(d,null,{default:o(()=>[t(A(Fe))]),_:1}),e[29]||(e[29]=y(" 搜索 "))]),_:1}),t(c,{onClick:va},{default:o(()=>[t(d,null,{default:o(()=>[t(A(sa))]),_:1}),e[30]||(e[30]=y(" 重置 "))]),_:1})])])])]),_:1}),t(p,{class:"table-card"},{header:o(()=>[g("div",zl,[e[32]||(e[32]=g("span",{class:"title"},"我的工具",-1)),t(c,{type:"primary",onClick:La},{default:o(()=>[t(d,null,{default:o(()=>[t(A(da))]),_:1}),e[31]||(e[31]=y(" 上传工具 "))]),_:1})])]),default:o(()=>[g("div",kl,[ge((R(),P(j,{data:fa.value,style:{width:"100%"}},{default:o(()=>[t(u,{type:"index",label:"序号",width:"80",align:"center"}),t(u,{prop:"fmw_name",label:"工具名称","min-width":"200","show-overflow-tooltip":"",align:"left"}),t(u,{prop:"project",label:"所属项目","min-width":"150","show-overflow-tooltip":"",align:"left"}),t(u,{prop:"run_times",label:"运行次数",width:"120",align:"center"},{default:o(({row:l})=>[g("span",{class:Oe({highlight:l.run_times>0})},F(l.run_times),3)]),_:1}),t(u,{prop:"created_at",label:"上传时间",width:"180",align:"center"},{default:o(({row:l})=>[t(h,{content:G(l.created_at,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:o(()=>[g("span",null,F(G(l.created_at,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(u,{label:"工具更新",width:"120",align:"center"},{default:o(({row:l})=>[t(c,{type:"primary",size:"small",onClick:k=>Fa(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(da))]),_:1}),e[33]||(e[33]=y(" 更新 "))]),_:2},1032,["onClick"])]),_:1}),t(u,{label:"工具下载",width:"120",align:"center"},{default:o(({row:l})=>[t(c,{type:"success",size:"small",onClick:k=>Oa(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(sl))]),_:1}),e[34]||(e[34]=y(" 下载 "))]),_:2},1032,["onClick"])]),_:1}),t(u,{label:"操作",width:"400",align:"center",fixed:"right"},{default:o(({row:l})=>[t(b,null,{default:o(()=>[t(c,{type:"primary",size:"small",onClick:k=>ya(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(na))]),_:1}),e[35]||(e[35]=y(" 运行 "))]),_:2},1032,["onClick"]),t(c,{type:"success",size:"small",onClick:k=>ea(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(ua))]),_:1}),e[36]||(e[36]=y(" 运行成果 "))]),_:2},1032,["onClick"]),t(c,{type:"danger",size:"small",onClick:k=>Na(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(ia))]),_:1}),e[37]||(e[37]=y(" 删除 "))]),_:2},1032,["onClick"]),t(c,{type:"info",size:"small",onClick:k=>ja(l)},{default:o(()=>[t(d,null,{default:o(()=>[t(A(nl))]),_:1}),e[38]||(e[38]=y(" 详情 "))]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Z,O.value]]),g("div",$l,[t(X,{"current-page":de.value,"onUpdate:currentPage":e[4]||(e[4]=l=>de.value=l),"page-size":Ce.value,"onUpdate:pageSize":e[5]||(e[5]=l=>Ce.value=l),"page-sizes":[10,20,50,100],total:We.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Xa,onCurrentChange:$a},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[He,ue.value==="tools"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(V,{modelValue:le.value,"onUpdate:modelValue":e[8]||(e[8]=l=>le.value=l),title:`运行工具 - ${(W=z.value)==null?void 0:W.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:Ra,onAfterClose:Aa},{footer:o(()=>[g("span",Rl,[t(c,{onClick:e[7]||(e[7]=l=>le.value=!1)},{default:o(()=>e[40]||(e[40]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Ma,loading:Ve.value,disabled:Ve.value},{default:o(()=>e[41]||(e[41]=[y("提交任务")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(r,{ref_key:"formRef",ref:q,model:m.value,rules:$e.value,"label-width":"200px",size:"small",class:"run-form"},{default:o(()=>[Q.value.length>0?(R(!0),_e(pa,{key:0},ca(Q.value,l=>ge((R(),P(f,{key:l.prop,label:l.type==="message"?"":l.label,prop:l.prop,required:l.required,class:Oe({"message-form-item":l.type==="message"})},{default:o(()=>{var k,Re,Ye;return[l.type==="message"?(R(),_e("div",Ul,F(l.component.content),1)):l.type==="upload"?(R(),P(se,ne({key:1,ref_for:!0},l.component.props,{class:["upload-area",{"is-error":((Ye=(Re=(k=q.value)==null?void 0:k.fields)==null?void 0:Re.find($=>$.prop===l.prop))==null?void 0:Ye.validateState)==="error"}],drag:""}),{default:o(()=>[t(d,{class:"el-icon--upload"},{default:o(()=>[t(A(Xe))]),_:1}),e[39]||(e[39]=g("div",{class:"el-upload__text"},[y(" 拖拽文件到此处"),g("br"),y("或"),g("em",null,"点击上传")],-1))]),_:2},1040,["class"])):l.type==="select"?(R(),P(v,ne({key:2,modelValue:m.value[l.prop],"onUpdate:modelValue":$=>m.value[l.prop]=$,ref_for:!0},l.component.props,{"collapse-tags":l.component.props.multiple,"collapse-tags-tooltip":!0,style:{width:"100%"}}),{default:o(()=>[(R(!0),_e(pa,null,ca(l.component.options,$=>(R(),P(x,{key:$.value,label:$.label,value:$.value,title:$.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue","collapse-tags"])):l.type==="number"?(R(),P(s,ne({key:3,modelValue:m.value[l.prop],"onUpdate:modelValue":$=>m.value[l.prop]=$,ref_for:!0},l.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="datetime"?(R(),P(B,ne({key:4,modelValue:m.value[l.prop],"onUpdate:modelValue":$=>m.value[l.prop]=$,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"])):l.type==="checkbox"?(R(),P(K,ne({key:5,modelValue:m.value[l.prop],"onUpdate:modelValue":$=>m.value[l.prop]=$,ref_for:!0},l.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="color"?(R(),_e("div",xl,[t(S,ne({modelValue:m.value[l.prop],"onUpdate:modelValue":$=>m.value[l.prop]=$,ref_for:!0},l.component.props,{onChange:$=>Ba(l.prop,$),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),g("span",Vl,F(m.value[`${l.prop}_value`]||"255,255,255"),1)])):(R(),P(n,ne({key:7,modelValue:m.value[l.prop],"onUpdate:modelValue":$=>m.value[l.prop]=$,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[He,xe(l)]])),128)):(R(),_e("div",Tl," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),t(V,{modelValue:ce.value,"onUpdate:modelValue":e[15]||(e[15]=l=>ce.value=l),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:Da,onAfterClose:Sa},{footer:o(()=>[g("span",Al,[t(c,{onClick:e[14]||(e[14]=l=>ce.value=!1)},{default:o(()=>e[45]||(e[45]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:qa,loading:Te.value,disabled:Te.value},{default:o(()=>e[46]||(e[46]=[y("确定")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(r,{ref_key:"uploadFormRef",ref:re,model:w.value,rules:Ta,"label-width":"100px",size:"small"},{default:o(()=>[t(f,{label:"工具名称",prop:"fmw_name"},{default:o(()=>[t(n,{modelValue:w.value.fmw_name,"onUpdate:modelValue":e[9]||(e[9]=l=>w.value.fmw_name=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"所属项目",prop:"project"},{default:o(()=>[t(n,{modelValue:w.value.project,"onUpdate:modelValue":e[10]||(e[10]=l=>w.value.project=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"工具类型",prop:"tools_class"},{default:o(()=>[t(v,{modelValue:w.value.tools_class,"onUpdate:modelValue":e[11]||(e[11]=l=>w.value.tools_class=l),placeholder:"请选择工具类型",style:{width:"100%"}},{default:o(()=>[t(x,{label:"格式转换",value:"格式转换"}),t(x,{label:"坐标系转换",value:"坐标系转换"}),t(x,{label:"数据结构转换",value:"数据结构转换"}),t(x,{label:"数据清洗",value:"数据清洗"}),t(x,{label:"几何处理",value:"几何处理"}),t(x,{label:"属性处理",value:"属性处理"}),t(x,{label:"数据合并",value:"数据合并"}),t(x,{label:"数据融合",value:"数据融合"}),t(x,{label:"数据获取",value:"数据同步"}),t(x,{label:"批处理",value:"批处理"}),t(x,{label:"数据质检",value:"数据质检"}),t(x,{label:"空间分析",value:"空间分析"}),t(x,{label:"统计分析",value:"统计分析"})]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"工具描述",prop:"description"},{default:o(()=>[t(n,{modelValue:w.value.description,"onUpdate:modelValue":e[12]||(e[12]=l=>w.value.description=l),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"工具文件",prop:"file"},{default:o(()=>[t(se,{ref_key:"uploadRef",ref:te,class:"upload-demo",action:M.UPLOAD_FILE,headers:De(),"on-success":Ka,"on-error":Za,limit:1,accept:".fmw",drag:"","show-file-list":!0,"auto-upload":!0},{tip:o(()=>e[42]||(e[42]=[g("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:o(()=>[t(d,{class:"el-icon--upload"},{default:o(()=>[t(A(Xe))]),_:1}),e[43]||(e[43]=g("div",{class:"el-upload__text"},[y(" 将文件拖到此处，或"),g("em",null,"点击上传")],-1))]),_:1},8,["action","headers"])]),_:1}),t(f,{label:"使用默认参数",prop:"use_default_params"},{default:o(()=>[t(K,{modelValue:w.value.use_default_params,"onUpdate:modelValue":e[13]||(e[13]=l=>w.value.use_default_params=l)},{default:o(()=>e[44]||(e[44]=[y(" 勾选此项代表运行工具时自动读取默认参数 ")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(V,{modelValue:fe.value,"onUpdate:modelValue":e[17]||(e[17]=l=>fe.value=l),title:"更新工具",width:"500px","close-on-click-modal":!1,onClose:Ia,onAfterClose:Pa},{footer:o(()=>[g("span",Dl,[t(c,{onClick:e[16]||(e[16]=l=>fe.value=!1)},{default:o(()=>e[49]||(e[49]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Ea,loading:me.value,disabled:me.value},{default:o(()=>e[50]||(e[50]=[y("确认更新")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(se,{ref_key:"updateUploadRef",ref:oe,class:"upload-demo",action:M.UPLOAD_FILE,headers:De(),"on-success":Qa,"on-error":Ga,limit:1,accept:".fmw",drag:"","show-file-list":!0,"auto-upload":!0},{tip:o(()=>e[47]||(e[47]=[g("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:o(()=>[t(d,{class:"el-icon--upload"},{default:o(()=>[t(A(Xe))]),_:1}),e[48]||(e[48]=g("div",{class:"el-upload__text"},[y(" 将文件拖到此处，或"),g("em",null,"点击上传")],-1))]),_:1},8,["action","headers"])]),_:1},8,["modelValue"]),t(V,{modelValue:Ie.value,"onUpdate:modelValue":e[19]||(e[19]=l=>Ie.value=l),title:`运行成果 - ${(C=z.value)==null?void 0:C.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:o(()=>[ge((R(),P(j,{data:Y.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:o(()=>[t(u,{type:"index",label:"序号","min-width":"80",align:"center"}),t(u,{prop:"submit_time",label:"提交时间","min-width":"180",align:"center"},{default:o(({row:l})=>[y(F(G(l.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),t(u,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:o(({row:l})=>[t(E,{type:ba(l.status)},{default:o(()=>[y(F(wa(l.status)),1)]),_:2},1032,["type"])]),_:1}),t(u,{prop:"time_consuming",label:"运行耗时","min-width":"100",align:"center"},{default:o(({row:l})=>[y(F(Ca(l.time_consuming)),1)]),_:1}),t(u,{prop:"file_size",label:"文件大小","min-width":"100",align:"center"}),t(u,{label:"操作","min-width":"200",align:"center"},{default:o(({row:l})=>[t(b,null,{default:o(()=>[l.status==="failed"?(R(),P(c,{key:0,type:"warning",size:"small",onClick:k=>Ha(l.error_message)},{default:o(()=>e[51]||(e[51]=[y(" 日志 ")])),_:2},1032,["onClick"])):Ne("",!0),l.file_name&&l.file_size!=="0.0MB"?(R(),P(c,{key:1,type:"primary",size:"small",onClick:k=>za(l)},{default:o(()=>e[52]||(e[52]=[y(" 下载 ")])),_:2},1032,["onClick"])):Ne("",!0),t(c,{type:"danger",size:"small",onClick:k=>ka(l)},{default:o(()=>e[53]||(e[53]=[y(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Z,O.value]]),g("div",Sl,[t(X,{"current-page":ie.value,"onUpdate:currentPage":e[18]||(e[18]=l=>ie.value=l),"page-size":10,total:ae.value,layout:"total, prev, pager, next, jumper",onCurrentChange:Ua},null,8,["current-page","total"])])]),_:1},8,["modelValue","title"]),t(V,{modelValue:Me.value,"onUpdate:modelValue":e[21]||(e[21]=l=>Me.value=l),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:o(()=>[g("span",Pl,[t(c,{onClick:e[20]||(e[20]=l=>Me.value=!1)},{default:o(()=>e[54]||(e[54]=[y("关闭")])),_:1})])]),default:o(()=>[g("div",Il,[g("pre",null,F(aa.value),1)])]),_:1},8,["modelValue"])])}}}),Nl=il(Ml,[["__scopeId","data-v-8a9cf59d"]]);export{Nl as default};
