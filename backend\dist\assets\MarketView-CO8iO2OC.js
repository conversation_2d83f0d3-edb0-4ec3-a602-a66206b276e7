import{i as P,d as ve,u as ge,r as p,M as $,o as ye,c as be,b as o,e as a,w as l,f as u,E as m,F as b,A as f,m as v,V as I,B as he,P as we,Q as Ve,C as N,s as E,n as Ce,W as xe,X as ke,S as je,T as De,j as Me,l as B,_ as Te}from"./index-B7WNRWO3.js";import{f as ze}from"./format-CBpsKyOP.js";async function Se(){try{const h=await P.get("/api/tools/list");return console.log("API响应:",h),h.data}catch(h){throw console.error("API调用失败:",h),h}}const Ae={class:"market-container"},Ue={class:"stats-section"},Be={class:"card-header"},Ye={class:"stats-value"},qe={class:"number"},Fe={class:"card-header"},Ie={class:"stats-value"},Ne={class:"number"},Pe={class:"card-header"},Le={class:"stats-value"},Re={class:"number"},$e={class:"search-section"},Ee={class:"search-row"},He={class:"search-buttons"},Xe={class:"table-container"},Oe={class:"pagination-container"},Qe={class:"dialog-footer"},We=ve({__name:"MarketView",setup(h){const w=ge(),Y=p(!1),c=p([]),z=p(""),S=p(""),A=p(""),U=p(0),y=p(1),D=p(10),L=$(()=>{if(!c.value||c.value.length===0)return[];const s=z.value.toLowerCase(),e=S.value,n=A.value;return c.value.filter(d=>{if(!d||!d.fmw_name||!d.project)return!1;const i=d.fmw_name.toLowerCase().includes(s)||d.project.toLowerCase().includes(s),F=!e||d.author===e||d.author_name===e,k=!n||d.tools_class===n;return i&&F&&k})}),H=$(()=>{const s=(y.value-1)*D.value,e=s+D.value;return L.value.slice(s,e)}),V=()=>{y.value=1},X=()=>{z.value="",S.value="",A.value="",y.value=1},O=s=>{y.value=s},Q=s=>{D.value=s,y.value=1},W=async()=>{var s;Y.value=!0;try{console.log("开始获取工具列表...");const e=await Se();if(console.log("获取工具列表响应:",e),!e){console.error("获取工具列表失败：响应为空"),m.error("获取工具列表失败：服务器无响应"),c.value=[];return}if(e.success){if(!Array.isArray(e.data)){console.error("获取工具列表失败：返回数据格式错误",e),m.error("获取工具列表失败：数据格式错误"),c.value=[];return}c.value=e.data,console.log("工具列表数据:",c.value),console.log("工具数量:",c.value.length)}else console.error("获取工具列表失败:",e.message||"未知错误"),m.error(e.message||"获取工具列表失败"),c.value=[]}catch(e){console.error("获取工具列表异常:",e),e.response?(console.error("错误响应:",e.response.data),m.error(((s=e.response.data)==null?void 0:s.message)||"获取工具列表失败")):e.request?(console.error("请求错误:",e.request),m.error("服务器无响应，请检查网络连接")):(console.error("错误信息:",e.message),m.error("获取工具列表失败："+e.message)),c.value=[]}finally{Y.value=!1}},G=s=>{De.alert(s.description||"暂无描述","工具描述",{confirmButtonText:"确定",customClass:"detail-message-box"})},C=p(!1),_=p(),q=p(!1),J=p(null),r=p({fmw_id:"",fmw_name:"",project:"",applicant:"",reason:"",end_date:"",usage_count:1,user_project:""}),K=p({project:[{required:!0,message:"请输入项目名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,max:200,message:"长度在 10 到 200 个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]}),Z=s=>s.getTime()<Date.now()-864e5,ee=s=>{var e;J.value=s,r.value={fmw_id:s.fmw_id,fmw_name:s.fmw_name,project:"",applicant:((e=w.user)==null?void 0:e.username)||"",reason:"",end_date:"",usage_count:1,user_project:""},C.value=!0,Me(()=>{_.value&&(_.value.resetFields(),_.value.clearValidate())})},ae=async()=>{var s,e;if(_.value)try{await _.value.validate(),q.value=!0;const n=await P.post("/api/tools/apply",{...r.value,project:r.value.user_project,applicant:((s=w.user)==null?void 0:s.username)||""},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`,"X-Username":((e=w.user)==null?void 0:e.username)||""}});n.data.success?(m.success("申请提交成功"),C.value=!1):m.error(n.data.message||"申请提交失败")}catch(n){if(n&&(typeof n=="object"||typeof n=="string")){m.error("请检查表单填写内容");return}n.response?m.error(n.response.data.message||"申请提交失败"):m.error("提交申请失败，请检查网络连接")}finally{q.value=!1}},R=(s,e)=>{if(!s)return"未知";try{const n=new Date(s);return isNaN(n.getTime())?s.toString():ze(n,e)}catch{return s.toString()}},le=()=>{const s=new Date,e=new Date(s.getFullYear(),s.getMonth(),1);return c.value.filter(n=>new Date(n.created_at)>=e).length},te=async()=>{var s;try{const e=await P.get("/api/tools/count",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`,"X-Username":((s=w.user)==null?void 0:s.username)||""}});e.data.success?U.value=e.data.data.total_runs||0:U.value=0}catch{U.value=0}},se=()=>{_.value&&(_.value.resetFields(),_.value.clearValidate()),r.value.fmw_id="",r.value.fmw_name="",r.value.project="",r.value.applicant="",r.value.reason="",r.value.end_date="",r.value.usage_count=1,r.value.user_project=""},oe=()=>{C.value=!1};return ye(async()=>{await Promise.all([W(),te()])}),(s,e)=>{const n=u("el-tag"),x=u("el-card"),d=u("el-input"),i=u("el-option"),F=u("el-select"),k=u("el-icon"),j=u("el-button"),g=u("el-table-column"),ne=u("el-tooltip"),re=u("el-button-group"),ue=u("el-table"),ie=u("el-pagination"),M=u("el-form-item"),de=u("el-date-picker"),pe=u("el-input-number"),ce=u("el-form"),me=u("el-dialog"),fe=Ve("loading");return B(),be("div",Ae,[o("div",Ue,[a(x,{class:"stats-card"},{header:l(()=>[o("div",Be,[e[13]||(e[13]=o("span",null,"工具总数",-1)),a(n,{type:"info",effect:"plain"},{default:l(()=>e[12]||(e[12]=[f("总计")])),_:1})])]),default:l(()=>[o("div",Ye,[o("span",qe,b(c.value.length),1),e[14]||(e[14]=o("span",{class:"unit"},"个",-1))])]),_:1}),a(x,{class:"stats-card"},{header:l(()=>[o("div",Fe,[e[16]||(e[16]=o("span",null,"本月新增",-1)),a(n,{type:"success",effect:"plain"},{default:l(()=>e[15]||(e[15]=[f("增长")])),_:1})])]),default:l(()=>[o("div",Ie,[o("span",Ne,b(le()),1),e[17]||(e[17]=o("span",{class:"unit"},"个",-1))])]),_:1}),a(x,{class:"stats-card"},{header:l(()=>[o("div",Pe,[e[19]||(e[19]=o("span",null,"总运行次数",-1)),a(n,{type:"warning",effect:"plain"},{default:l(()=>e[18]||(e[18]=[f("累计")])),_:1})])]),default:l(()=>[o("div",Le,[o("span",Re,b(U.value),1),e[20]||(e[20]=o("span",{class:"unit"},"次",-1))])]),_:1})]),a(x,{class:"search-card"},{default:l(()=>[o("div",$e,[o("div",Ee,[a(d,{modelValue:z.value,"onUpdate:modelValue":e[0]||(e[0]=t=>z.value=t),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":v(I),onClear:V,onInput:V},null,8,["modelValue","prefix-icon"]),a(d,{modelValue:S.value,"onUpdate:modelValue":e[1]||(e[1]=t=>S.value=t),placeholder:"作者",class:"search-input",clearable:"","prefix-icon":v(I),onClear:V,onInput:V},null,8,["modelValue","prefix-icon"]),a(F,{modelValue:A.value,"onUpdate:modelValue":e[2]||(e[2]=t=>A.value=t),placeholder:"全部类型",class:"search-input",clearable:"",style:{width:"160px"},onChange:V},{default:l(()=>[a(i,{label:"全部类型",value:""}),a(i,{label:"格式转换",value:"格式转换"}),a(i,{label:"坐标系转换",value:"坐标系转换"}),a(i,{label:"数据结构转换",value:"数据结构转换"}),a(i,{label:"数据清洗",value:"数据清洗"}),a(i,{label:"几何处理",value:"几何处理"}),a(i,{label:"属性处理",value:"属性处理"}),a(i,{label:"数据合并",value:"数据合并"}),a(i,{label:"数据融合",value:"数据融合"}),a(i,{label:"数据同步",value:"数据同步"}),a(i,{label:"批处理",value:"批处理"}),a(i,{label:"数据质检",value:"数据质检"}),a(i,{label:"空间分析",value:"空间分析"}),a(i,{label:"统计分析",value:"统计分析"})]),_:1},8,["modelValue"]),o("div",He,[a(j,{type:"primary",onClick:V},{default:l(()=>[a(k,null,{default:l(()=>[a(v(I))]),_:1}),e[21]||(e[21]=f(" 搜索 "))]),_:1}),a(j,{onClick:X},{default:l(()=>[a(k,null,{default:l(()=>[a(v(he))]),_:1}),e[22]||(e[22]=f(" 重置 "))]),_:1})])])])]),_:1}),a(x,{class:"table-card"},{header:l(()=>e[23]||(e[23]=[o("div",{class:"table-header"},[o("span",{class:"title"},"工具列表")],-1)])),default:l(()=>[o("div",Xe,[we((B(),N(ue,{data:H.value,style:{width:"100%"},"empty-text":"暂无数据"},{default:l(()=>[a(g,{type:"index",label:"序号",width:"60",align:"center"}),a(g,{prop:"fmw_name",label:"工具名称","min-width":"150","show-overflow-tooltip":"",align:"left"},{default:l(({row:t})=>{var T;return[o("span",null,b(t.fmw_name),1),t.author===((T=v(w).user)==null?void 0:T.username)?(B(),N(n,{key:0,size:"small",type:"success",effect:"plain",style:{"margin-left":"8px"}},{default:l(()=>e[24]||(e[24]=[f(" 我的工具 ")])),_:1})):E("",!0)]}),_:1}),a(g,{prop:"project",label:"所属项目","min-width":"120","show-overflow-tooltip":"",align:"left"}),a(g,{prop:"tools_class",label:"工具类型","min-width":"110","show-overflow-tooltip":"",align:"left"}),a(g,{prop:"run_times",label:"运行次数",width:"90",sortable:!1,align:"center"},{default:l(({row:t})=>[o("span",{class:Ce({highlight:t.run_times>0})},b(t.run_times||0),3)]),_:1}),a(g,{prop:"author_name",label:"上传者",width:"100",align:"center"},{default:l(({row:t})=>[a(n,{size:"small",effect:"plain"},{default:l(()=>[f(b(t.author_name||t.author),1)]),_:2},1024)]),_:1}),a(g,{prop:"data",label:"上传时间",width:"140",align:"center"},{default:l(({row:t})=>[a(ne,{content:R(t.data,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:l(()=>[o("span",null,b(R(t.data,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),a(g,{label:"操作",width:"160",align:"center",fixed:"right"},{default:l(({row:t})=>[a(re,null,{default:l(()=>{var T;return[a(j,{type:"info",size:"small",onClick:_e=>G(t)},{default:l(()=>[a(k,null,{default:l(()=>[a(v(xe))]),_:1}),e[25]||(e[25]=f(" 详情 "))]),_:2},1032,["onClick"]),t.author!==((T=v(w).user)==null?void 0:T.username)?(B(),N(j,{key:0,type:"primary",size:"small",onClick:_e=>ee(t)},{default:l(()=>[a(k,null,{default:l(()=>[a(v(ke))]),_:1}),e[26]||(e[26]=f(" 申请 "))]),_:2},1032,["onClick"])):E("",!0)]}),_:2},1024)]),_:1})]),_:1},8,["data"])),[[fe,Y.value]]),o("div",Oe,[a(ie,{"current-page":y.value,"onUpdate:currentPage":e[3]||(e[3]=t=>y.value=t),"page-size":D.value,"onUpdate:pageSize":e[4]||(e[4]=t=>D.value=t),"page-sizes":[10,20,50,100],total:L.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Q,onCurrentChange:O},null,8,["current-page","page-size","total"])])])]),_:1}),a(me,{modelValue:C.value,"onUpdate:modelValue":e[11]||(e[11]=t=>C.value=t),title:"申请使用工具",width:"500px","close-on-click-modal":!1,onClose:oe,onAfterClose:se},{footer:l(()=>[o("span",Qe,[a(j,{onClick:e[10]||(e[10]=t=>C.value=!1)},{default:l(()=>e[27]||(e[27]=[f("取消")])),_:1}),a(j,{type:"primary",onClick:ae,loading:q.value},{default:l(()=>e[28]||(e[28]=[f(" 提交申请 ")])),_:1},8,["loading"])])]),default:l(()=>[a(ce,{ref_key:"applyFormRef",ref:_,model:r.value,rules:K.value,"label-width":"100px",class:"apply-form"},{default:l(()=>[a(M,{label:"工具名称",prop:"fmw_name"},{default:l(()=>[a(d,{modelValue:r.value.fmw_name,"onUpdate:modelValue":e[5]||(e[5]=t=>r.value.fmw_name=t),disabled:""},null,8,["modelValue"])]),_:1}),a(M,{label:"使用项目",prop:"user_project"},{default:l(()=>[a(d,{modelValue:r.value.user_project,"onUpdate:modelValue":e[6]||(e[6]=t=>r.value.user_project=t),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),a(M,{label:"申请原因",prop:"reason"},{default:l(()=>[a(d,{modelValue:r.value.reason,"onUpdate:modelValue":e[7]||(e[7]=t=>r.value.reason=t),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),a(M,{label:"有效时间",prop:"end_date"},{default:l(()=>[a(de,{modelValue:r.value.end_date,"onUpdate:modelValue":e[8]||(e[8]=t=>r.value.end_date=t),type:"date",placeholder:"请选择有效时间","disabled-date":Z,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD",locale:v(je)},null,8,["modelValue","default-time","locale"])]),_:1}),a(M,{label:"申请次数",prop:"usage_count"},{default:l(()=>[a(pe,{modelValue:r.value.usage_count,"onUpdate:modelValue":e[9]||(e[9]=t=>r.value.usage_count=t),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Ke=Te(We,[["__scopeId","data-v-84d1ea05"]]);export{Ke as default};
