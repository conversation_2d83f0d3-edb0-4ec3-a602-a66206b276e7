import{ag as <PERSON>,ah as Yo,d as <PERSON>o,r as <PERSON>,o as $o,a as Qo,c as vn,b as Kt,H as ts,I as es,l as gn,n as is,F as ns,_ as os}from"./index-CdoJMuEX.js";var Qe={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */(function(yn,G){(function(l,Zt){Zt(G)})(Yo,function(l){var Zt="1.9.4";function b(t){var e,i,n,o;for(i=1,n=arguments.length;i<n;i++){o=arguments[i];for(e in o)t[e]=o[e]}return t}var xt=Object.create||function(){function t(){}return function(e){return t.prototype=e,new t}}();function T(t,e){var i=Array.prototype.slice;if(t.bind)return t.bind.apply(t,i.call(arguments,1));var n=i.call(arguments,2);return function(){return t.apply(e,n.length?n.concat(i.call(arguments)):arguments)}}var U=0;function g(t){return"_leaflet_id"in t||(t._leaflet_id=++U),t._leaflet_id}function Et(t,e,i){var n,o,s,r;return r=function(){n=!1,o&&(s.apply(i,o),o=!1)},s=function(){n?o=arguments:(t.apply(i,arguments),setTimeout(r,e),n=!0)},s}function lt(t,e,i){var n=e[1],o=e[0],s=n-o;return t===n&&i?t:((t-o)%s+s)%s+o}function Z(){return!1}function Q(t,e){if(e===!1)return t;var i=Math.pow(10,e===void 0?6:e);return Math.round(t*i)/i}function me(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function mt(t){return me(t).split(/\s+/)}function S(t,e){Object.prototype.hasOwnProperty.call(t,"options")||(t.options=t.options?xt(t.options):{});for(var i in e)t.options[i]=e[i];return t.options}function ti(t,e,i){var n=[];for(var o in t)n.push(encodeURIComponent(i?o.toUpperCase():o)+"="+encodeURIComponent(t[o]));return(!e||e.indexOf("?")===-1?"?":"&")+n.join("&")}var wn=/\{ *([\w_ -]+) *\}/g;function ei(t,e){return t.replace(wn,function(i,n){var o=e[n];if(o===void 0)throw new Error("No value provided for variable "+i);return typeof o=="function"&&(o=o(e)),o})}var X=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"};function pe(t,e){for(var i=0;i<t.length;i++)if(t[i]===e)return i;return-1}var Yt="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function ve(t){return window["webkit"+t]||window["moz"+t]||window["ms"+t]}var ii=0;function ni(t){var e=+new Date,i=Math.max(0,16-(e-ii));return ii=e+i,window.setTimeout(t,i)}var ge=window.requestAnimationFrame||ve("RequestAnimationFrame")||ni,oi=window.cancelAnimationFrame||ve("CancelAnimationFrame")||ve("CancelRequestAnimationFrame")||function(t){window.clearTimeout(t)};function R(t,e,i){if(i&&ge===ni)t.call(e);else return ge.call(window,T(t,e))}function q(t){t&&oi.call(window,t)}var xn={__proto__:null,extend:b,create:xt,bind:T,get lastId(){return U},stamp:g,throttle:Et,wrapNum:lt,falseFn:Z,formatNum:Q,trim:me,splitWords:mt,setOptions:S,getParamString:ti,template:ei,isArray:X,indexOf:pe,emptyImageUrl:Yt,requestFn:ge,cancelFn:oi,requestAnimFrame:R,cancelAnimFrame:q};function ot(){}ot.extend=function(t){var e=function(){S(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},i=e.__super__=this.prototype,n=xt(i);n.constructor=e,e.prototype=n;for(var o in this)Object.prototype.hasOwnProperty.call(this,o)&&o!=="prototype"&&o!=="__super__"&&(e[o]=this[o]);return t.statics&&b(e,t.statics),t.includes&&(Pn(t.includes),b.apply(null,[n].concat(t.includes))),b(n,t),delete n.statics,delete n.includes,n.options&&(n.options=i.options?xt(i.options):{},b(n.options,t.options)),n._initHooks=[],n.callInitHooks=function(){if(!this._initHooksCalled){i.callInitHooks&&i.callInitHooks.call(this),this._initHooksCalled=!0;for(var s=0,r=n._initHooks.length;s<r;s++)n._initHooks[s].call(this)}},e},ot.include=function(t){var e=this.prototype.options;return b(this.prototype,t),t.options&&(this.prototype.options=e,this.mergeOptions(t.options)),this},ot.mergeOptions=function(t){return b(this.prototype.options,t),this},ot.addInitHook=function(t){var e=Array.prototype.slice.call(arguments,1),i=typeof t=="function"?t:function(){this[t].apply(this,e)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(i),this};function Pn(t){if(!(typeof L>"u"||!L||!L.Mixin)){t=X(t)?t:[t];for(var e=0;e<t.length;e++)t[e]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var V={on:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e);else{t=mt(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i)}return this},off:function(t,e,i){if(!arguments.length)delete this._events;else if(typeof t=="object")for(var n in t)this._off(n,t[n],e);else{t=mt(t);for(var o=arguments.length===1,s=0,r=t.length;s<r;s++)o?this._off(t[s]):this._off(t[s],e,i)}return this},_on:function(t,e,i,n){if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}if(this._listens(t,e,i)===!1){i===this&&(i=void 0);var o={fn:e,ctx:i};n&&(o.once=!0),this._events=this._events||{},this._events[t]=this._events[t]||[],this._events[t].push(o)}},_off:function(t,e,i){var n,o,s;if(this._events&&(n=this._events[t],!!n)){if(arguments.length===1){if(this._firingCount)for(o=0,s=n.length;o<s;o++)n[o].fn=Z;delete this._events[t];return}if(typeof e!="function"){console.warn("wrong listener type: "+typeof e);return}var r=this._listens(t,e,i);if(r!==!1){var a=n[r];this._firingCount&&(a.fn=Z,this._events[t]=n=n.slice()),n.splice(r,1)}}},fire:function(t,e,i){if(!this.listens(t,i))return this;var n=b({},e,{type:t,target:this,sourceTarget:e&&e.sourceTarget||this});if(this._events){var o=this._events[t];if(o){this._firingCount=this._firingCount+1||1;for(var s=0,r=o.length;s<r;s++){var a=o[s],h=a.fn;a.once&&this.off(t,h,a.ctx),h.call(a.ctx||this,n)}this._firingCount--}}return i&&this._propagateEvent(n),this},listens:function(t,e,i,n){typeof t!="string"&&console.warn('"string" type argument expected');var o=e;typeof e!="function"&&(n=!!e,o=void 0,i=void 0);var s=this._events&&this._events[t];if(s&&s.length&&this._listens(t,o,i)!==!1)return!0;if(n){for(var r in this._eventParents)if(this._eventParents[r].listens(t,e,i,n))return!0}return!1},_listens:function(t,e,i){if(!this._events)return!1;var n=this._events[t]||[];if(!e)return!!n.length;i===this&&(i=void 0);for(var o=0,s=n.length;o<s;o++)if(n[o].fn===e&&n[o].ctx===i)return o;return!1},once:function(t,e,i){if(typeof t=="object")for(var n in t)this._on(n,t[n],e,!0);else{t=mt(t);for(var o=0,s=t.length;o<s;o++)this._on(t[o],e,i,!0)}return this},addEventParent:function(t){return this._eventParents=this._eventParents||{},this._eventParents[g(t)]=t,this},removeEventParent:function(t){return this._eventParents&&delete this._eventParents[g(t)],this},_propagateEvent:function(t){for(var e in this._eventParents)this._eventParents[e].fire(t.type,b({layer:t.target,propagatedFrom:t.target},t),!0)}};V.addEventListener=V.on,V.removeEventListener=V.clearAllEventListeners=V.off,V.addOneTimeEventListener=V.once,V.fireEvent=V.fire,V.hasEventListeners=V.listens;var Ot=ot.extend(V);function m(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}var si=Math.trunc||function(t){return t>0?Math.floor(t):Math.ceil(t)};m.prototype={clone:function(){return new m(this.x,this.y)},add:function(t){return this.clone()._add(_(t))},_add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.clone()._subtract(_(t))},_subtract:function(t){return this.x-=t.x,this.y-=t.y,this},divideBy:function(t){return this.clone()._divideBy(t)},_divideBy:function(t){return this.x/=t,this.y/=t,this},multiplyBy:function(t){return this.clone()._multiplyBy(t)},_multiplyBy:function(t){return this.x*=t,this.y*=t,this},scaleBy:function(t){return new m(this.x*t.x,this.y*t.y)},unscaleBy:function(t){return new m(this.x/t.x,this.y/t.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=si(this.x),this.y=si(this.y),this},distanceTo:function(t){t=_(t);var e=t.x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},equals:function(t){return t=_(t),t.x===this.x&&t.y===this.y},contains:function(t){return t=_(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)},toString:function(){return"Point("+Q(this.x)+", "+Q(this.y)+")"}};function _(t,e,i){return t instanceof m?t:X(t)?new m(t[0],t[1]):t==null?t:typeof t=="object"&&"x"in t&&"y"in t?new m(t.x,t.y):new m(t,e,i)}function z(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}z.prototype={extend:function(t){var e,i;if(!t)return this;if(t instanceof m||typeof t[0]=="number"||"x"in t)e=i=_(t);else if(t=F(t),e=t.min,i=t.max,!e||!i)return this;return!this.min&&!this.max?(this.min=e.clone(),this.max=i.clone()):(this.min.x=Math.min(e.x,this.min.x),this.max.x=Math.max(i.x,this.max.x),this.min.y=Math.min(e.y,this.min.y),this.max.y=Math.max(i.y,this.max.y)),this},getCenter:function(t){return _((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)},getBottomLeft:function(){return _(this.min.x,this.max.y)},getTopRight:function(){return _(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(t){var e,i;return typeof t[0]=="number"||t instanceof m?t=_(t):t=F(t),t instanceof z?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y},intersects:function(t){t=F(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>=e.x&&n.x<=i.x,r=o.y>=e.y&&n.y<=i.y;return s&&r},overlaps:function(t){t=F(t);var e=this.min,i=this.max,n=t.min,o=t.max,s=o.x>e.x&&n.x<i.x,r=o.y>e.y&&n.y<i.y;return s&&r},isValid:function(){return!!(this.min&&this.max)},pad:function(t){var e=this.min,i=this.max,n=Math.abs(e.x-i.x)*t,o=Math.abs(e.y-i.y)*t;return F(_(e.x-n,e.y-o),_(i.x+n,i.y+o))},equals:function(t){return t?(t=F(t),this.min.equals(t.getTopLeft())&&this.max.equals(t.getBottomRight())):!1}};function F(t,e){return!t||t instanceof z?t:new z(t,e)}function W(t,e){if(t)for(var i=e?[t,e]:t,n=0,o=i.length;n<o;n++)this.extend(i[n])}W.prototype={extend:function(t){var e=this._southWest,i=this._northEast,n,o;if(t instanceof M)n=t,o=t;else if(t instanceof W){if(n=t._southWest,o=t._northEast,!n||!o)return this}else return t?this.extend(w(t)||O(t)):this;return!e&&!i?(this._southWest=new M(n.lat,n.lng),this._northEast=new M(o.lat,o.lng)):(e.lat=Math.min(n.lat,e.lat),e.lng=Math.min(n.lng,e.lng),i.lat=Math.max(o.lat,i.lat),i.lng=Math.max(o.lng,i.lng)),this},pad:function(t){var e=this._southWest,i=this._northEast,n=Math.abs(e.lat-i.lat)*t,o=Math.abs(e.lng-i.lng)*t;return new W(new M(e.lat-n,e.lng-o),new M(i.lat+n,i.lng+o))},getCenter:function(){return new M((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new M(this.getNorth(),this.getWest())},getSouthEast:function(){return new M(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(t){typeof t[0]=="number"||t instanceof M||"lat"in t?t=w(t):t=O(t);var e=this._southWest,i=this._northEast,n,o;return t instanceof W?(n=t.getSouthWest(),o=t.getNorthEast()):n=o=t,n.lat>=e.lat&&o.lat<=i.lat&&n.lng>=e.lng&&o.lng<=i.lng},intersects:function(t){t=O(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>=e.lat&&n.lat<=i.lat,r=o.lng>=e.lng&&n.lng<=i.lng;return s&&r},overlaps:function(t){t=O(t);var e=this._southWest,i=this._northEast,n=t.getSouthWest(),o=t.getNorthEast(),s=o.lat>e.lat&&n.lat<i.lat,r=o.lng>e.lng&&n.lng<i.lng;return s&&r},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(t,e){return t?(t=O(t),this._southWest.equals(t.getSouthWest(),e)&&this._northEast.equals(t.getNorthEast(),e)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function O(t,e){return t instanceof W?t:new W(t,e)}function M(t,e,i){if(isNaN(t)||isNaN(e))throw new Error("Invalid LatLng object: ("+t+", "+e+")");this.lat=+t,this.lng=+e,i!==void 0&&(this.alt=+i)}M.prototype={equals:function(t,e){if(!t)return!1;t=w(t);var i=Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng));return i<=(e===void 0?1e-9:e)},toString:function(t){return"LatLng("+Q(this.lat,t)+", "+Q(this.lng,t)+")"},distanceTo:function(t){return ct.distance(this,w(t))},wrap:function(){return ct.wrapLatLng(this)},toBounds:function(t){var e=180*t/40075017,i=e/Math.cos(Math.PI/180*this.lat);return O([this.lat-e,this.lng-i],[this.lat+e,this.lng+i])},clone:function(){return new M(this.lat,this.lng,this.alt)}};function w(t,e,i){return t instanceof M?t:X(t)&&typeof t[0]!="object"?t.length===3?new M(t[0],t[1],t[2]):t.length===2?new M(t[0],t[1]):null:t==null?t:typeof t=="object"&&"lat"in t?new M(t.lat,"lng"in t?t.lng:t.lon,t.alt):e===void 0?null:new M(t,e,i)}var st={latLngToPoint:function(t,e){var i=this.projection.project(t),n=this.scale(e);return this.transformation._transform(i,n)},pointToLatLng:function(t,e){var i=this.scale(e),n=this.transformation.untransform(t,i);return this.projection.unproject(n)},project:function(t){return this.projection.project(t)},unproject:function(t){return this.projection.unproject(t)},scale:function(t){return 256*Math.pow(2,t)},zoom:function(t){return Math.log(t/256)/Math.LN2},getProjectedBounds:function(t){if(this.infinite)return null;var e=this.projection.bounds,i=this.scale(t),n=this.transformation.transform(e.min,i),o=this.transformation.transform(e.max,i);return new z(n,o)},infinite:!1,wrapLatLng:function(t){var e=this.wrapLng?lt(t.lng,this.wrapLng,!0):t.lng,i=this.wrapLat?lt(t.lat,this.wrapLat,!0):t.lat,n=t.alt;return new M(i,e,n)},wrapLatLngBounds:function(t){var e=t.getCenter(),i=this.wrapLatLng(e),n=e.lat-i.lat,o=e.lng-i.lng;if(n===0&&o===0)return t;var s=t.getSouthWest(),r=t.getNorthEast(),a=new M(s.lat-n,s.lng-o),h=new M(r.lat-n,r.lng-o);return new W(a,h)}},ct=b({},st,{wrapLng:[-180,180],R:6371e3,distance:function(t,e){var i=Math.PI/180,n=t.lat*i,o=e.lat*i,s=Math.sin((e.lat-t.lat)*i/2),r=Math.sin((e.lng-t.lng)*i/2),a=s*s+Math.cos(n)*Math.cos(o)*r*r,h=2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a));return this.R*h}}),ri=6378137,ye={R:ri,MAX_LATITUDE:85.0511287798,project:function(t){var e=Math.PI/180,i=this.MAX_LATITUDE,n=Math.max(Math.min(i,t.lat),-i),o=Math.sin(n*e);return new m(this.R*t.lng*e,this.R*Math.log((1+o)/(1-o))/2)},unproject:function(t){var e=180/Math.PI;return new M((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:function(){var t=ri*Math.PI;return new z([-t,-t],[t,t])}()};function we(t,e,i,n){if(X(t)){this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3];return}this._a=t,this._b=e,this._c=i,this._d=n}we.prototype={transform:function(t,e){return this._transform(t.clone(),e)},_transform:function(t,e){return e=e||1,t.x=e*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t},untransform:function(t,e){return e=e||1,new m((t.x/e-this._b)/this._a,(t.y/e-this._d)/this._c)}};function It(t,e,i,n){return new we(t,e,i,n)}var xe=b({},ct,{code:"EPSG:3857",projection:ye,transformation:function(){var t=.5/(Math.PI*ye.R);return It(t,.5,-t,.5)}()}),Ln=b({},xe,{code:"EPSG:900913"});function ai(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function hi(t,e){var i="",n,o,s,r,a,h;for(n=0,s=t.length;n<s;n++){for(a=t[n],o=0,r=a.length;o<r;o++)h=a[o],i+=(o?"L":"M")+h.x+" "+h.y;i+=e?f.svg?"z":"x":""}return i||"M0 0"}var Pe=document.documentElement.style,Xt="ActiveXObject"in window,Tn=Xt&&!document.addEventListener,ui="msLaunchUri"in navigator&&!("documentMode"in document),Le=tt("webkit"),li=tt("android"),ci=tt("android 2")||tt("android 3"),bn=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),Mn=li&&tt("Google")&&bn<537&&!("AudioNode"in window),Te=!!window.opera,fi=!ui&&tt("chrome"),di=tt("gecko")&&!Le&&!Te&&!Xt,Cn=!fi&&tt("safari"),_i=tt("phantom"),mi="OTransition"in Pe,Sn=navigator.platform.indexOf("Win")===0,pi=Xt&&"transition"in Pe,be="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!ci,vi="MozPerspective"in Pe,zn=!window.L_DISABLE_3D&&(pi||be||vi)&&!mi&&!_i,Bt=typeof orientation<"u"||tt("mobile"),kn=Bt&&Le,Zn=Bt&&be,gi=!window.PointerEvent&&window.MSPointerEvent,yi=!!(window.PointerEvent||gi),wi="ontouchstart"in window||!!window.TouchEvent,En=!window.L_NO_TOUCH&&(wi||yi),On=Bt&&Te,In=Bt&&di,Bn=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,An=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("testPassiveEventSupport",Z,e),window.removeEventListener("testPassiveEventSupport",Z,e)}catch{}return t}(),Nn=function(){return!!document.createElement("canvas").getContext}(),Me=!!(document.createElementNS&&ai("svg").createSVGRect),Dn=!!Me&&function(){var t=document.createElement("div");return t.innerHTML="<svg/>",(t.firstChild&&t.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),Rn=!Me&&function(){try{var t=document.createElement("div");t.innerHTML='<v:shape adj="1"/>';var e=t.firstChild;return e.style.behavior="url(#default#VML)",e&&typeof e.adj=="object"}catch{return!1}}(),Fn=navigator.platform.indexOf("Mac")===0,Wn=navigator.platform.indexOf("Linux")===0;function tt(t){return navigator.userAgent.toLowerCase().indexOf(t)>=0}var f={ie:Xt,ielt9:Tn,edge:ui,webkit:Le,android:li,android23:ci,androidStock:Mn,opera:Te,chrome:fi,gecko:di,safari:Cn,phantom:_i,opera12:mi,win:Sn,ie3d:pi,webkit3d:be,gecko3d:vi,any3d:zn,mobile:Bt,mobileWebkit:kn,mobileWebkit3d:Zn,msPointer:gi,pointer:yi,touch:En,touchNative:wi,mobileOpera:On,mobileGecko:In,retina:Bn,passiveEvents:An,canvas:Nn,svg:Me,vml:Rn,inlineSvg:Dn,mac:Fn,linux:Wn},xi=f.msPointer?"MSPointerDown":"pointerdown",Pi=f.msPointer?"MSPointerMove":"pointermove",Li=f.msPointer?"MSPointerUp":"pointerup",Ti=f.msPointer?"MSPointerCancel":"pointercancel",Ce={touchstart:xi,touchmove:Pi,touchend:Li,touchcancel:Ti},bi={touchstart:jn,touchmove:Jt,touchend:Jt,touchcancel:Jt},Pt={},Mi=!1;function Hn(t,e,i){return e==="touchstart"&&qn(),bi[e]?(i=bi[e].bind(this,i),t.addEventListener(Ce[e],i,!1),i):(console.warn("wrong event specified:",e),Z)}function Un(t,e,i){if(!Ce[e]){console.warn("wrong event specified:",e);return}t.removeEventListener(Ce[e],i,!1)}function Vn(t){Pt[t.pointerId]=t}function Gn(t){Pt[t.pointerId]&&(Pt[t.pointerId]=t)}function Ci(t){delete Pt[t.pointerId]}function qn(){Mi||(document.addEventListener(xi,Vn,!0),document.addEventListener(Pi,Gn,!0),document.addEventListener(Li,Ci,!0),document.addEventListener(Ti,Ci,!0),Mi=!0)}function Jt(t,e){if(e.pointerType!==(e.MSPOINTER_TYPE_MOUSE||"mouse")){e.touches=[];for(var i in Pt)e.touches.push(Pt[i]);e.changedTouches=[e],t(e)}}function jn(t,e){e.MSPOINTER_TYPE_TOUCH&&e.pointerType===e.MSPOINTER_TYPE_TOUCH&&N(e),Jt(t,e)}function Kn(t){var e={},i,n;for(n in t)i=t[n],e[n]=i&&i.bind?i.bind(t):i;return t=e,e.type="dblclick",e.detail=2,e.isTrusted=!1,e._simulated=!0,e}var Yn=200;function Xn(t,e){t.addEventListener("dblclick",e);var i=0,n;function o(s){if(s.detail!==1){n=s.detail;return}if(!(s.pointerType==="mouse"||s.sourceCapabilities&&!s.sourceCapabilities.firesTouchEvents)){var r=Ei(s);if(!(r.some(function(h){return h instanceof HTMLLabelElement&&h.attributes.for})&&!r.some(function(h){return h instanceof HTMLInputElement||h instanceof HTMLSelectElement}))){var a=Date.now();a-i<=Yn?(n++,n===2&&e(Kn(s))):n=1,i=a}}}return t.addEventListener("click",o),{dblclick:e,simDblclick:o}}function Jn(t,e){t.removeEventListener("dblclick",e.dblclick),t.removeEventListener("click",e.simDblclick)}var Se=te(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),At=te(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Si=At==="webkitTransition"||At==="OTransition"?At+"End":"transitionend";function zi(t){return typeof t=="string"?document.getElementById(t):t}function Nt(t,e){var i=t.style[e]||t.currentStyle&&t.currentStyle[e];if((!i||i==="auto")&&document.defaultView){var n=document.defaultView.getComputedStyle(t,null);i=n?n[e]:null}return i==="auto"?null:i}function P(t,e,i){var n=document.createElement(t);return n.className=e||"",i&&i.appendChild(n),n}function k(t){var e=t.parentNode;e&&e.removeChild(t)}function $t(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function Lt(t){var e=t.parentNode;e&&e.lastChild!==t&&e.appendChild(t)}function Tt(t){var e=t.parentNode;e&&e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function ze(t,e){if(t.classList!==void 0)return t.classList.contains(e);var i=Qt(t);return i.length>0&&new RegExp("(^|\\s)"+e+"(\\s|$)").test(i)}function v(t,e){if(t.classList!==void 0)for(var i=mt(e),n=0,o=i.length;n<o;n++)t.classList.add(i[n]);else if(!ze(t,e)){var s=Qt(t);ke(t,(s?s+" ":"")+e)}}function E(t,e){t.classList!==void 0?t.classList.remove(e):ke(t,me((" "+Qt(t)+" ").replace(" "+e+" "," ")))}function ke(t,e){t.className.baseVal===void 0?t.className=e:t.className.baseVal=e}function Qt(t){return t.correspondingElement&&(t=t.correspondingElement),t.className.baseVal===void 0?t.className:t.className.baseVal}function j(t,e){"opacity"in t.style?t.style.opacity=e:"filter"in t.style&&$n(t,e)}function $n(t,e){var i=!1,n="DXImageTransform.Microsoft.Alpha";try{i=t.filters.item(n)}catch{if(e===1)return}e=Math.round(e*100),i?(i.Enabled=e!==100,i.Opacity=e):t.style.filter+=" progid:"+n+"(opacity="+e+")"}function te(t){for(var e=document.documentElement.style,i=0;i<t.length;i++)if(t[i]in e)return t[i];return!1}function pt(t,e,i){var n=e||new m(0,0);t.style[Se]=(f.ie3d?"translate("+n.x+"px,"+n.y+"px)":"translate3d("+n.x+"px,"+n.y+"px,0)")+(i?" scale("+i+")":"")}function I(t,e){t._leaflet_pos=e,f.any3d?pt(t,e):(t.style.left=e.x+"px",t.style.top=e.y+"px")}function vt(t){return t._leaflet_pos||new m(0,0)}var Dt,Rt,Ze;if("onselectstart"in document)Dt=function(){p(window,"selectstart",N)},Rt=function(){C(window,"selectstart",N)};else{var Ft=te(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);Dt=function(){if(Ft){var t=document.documentElement.style;Ze=t[Ft],t[Ft]="none"}},Rt=function(){Ft&&(document.documentElement.style[Ft]=Ze,Ze=void 0)}}function Ee(){p(window,"dragstart",N)}function Oe(){C(window,"dragstart",N)}var ee,Ie;function Be(t){for(;t.tabIndex===-1;)t=t.parentNode;t.style&&(ie(),ee=t,Ie=t.style.outlineStyle,t.style.outlineStyle="none",p(window,"keydown",ie))}function ie(){ee&&(ee.style.outlineStyle=Ie,ee=void 0,Ie=void 0,C(window,"keydown",ie))}function ki(t){do t=t.parentNode;while((!t.offsetWidth||!t.offsetHeight)&&t!==document.body);return t}function Ae(t){var e=t.getBoundingClientRect();return{x:e.width/t.offsetWidth||1,y:e.height/t.offsetHeight||1,boundingClientRect:e}}var Qn={__proto__:null,TRANSFORM:Se,TRANSITION:At,TRANSITION_END:Si,get:zi,getStyle:Nt,create:P,remove:k,empty:$t,toFront:Lt,toBack:Tt,hasClass:ze,addClass:v,removeClass:E,setClass:ke,getClass:Qt,setOpacity:j,testProp:te,setTransform:pt,setPosition:I,getPosition:vt,get disableTextSelection(){return Dt},get enableTextSelection(){return Rt},disableImageDrag:Ee,enableImageDrag:Oe,preventOutline:Be,restoreOutline:ie,getSizedParentNode:ki,getScale:Ae};function p(t,e,i,n){if(e&&typeof e=="object")for(var o in e)De(t,o,e[o],i);else{e=mt(e);for(var s=0,r=e.length;s<r;s++)De(t,e[s],i,n)}return this}var et="_leaflet_events";function C(t,e,i,n){if(arguments.length===1)Zi(t),delete t[et];else if(e&&typeof e=="object")for(var o in e)Re(t,o,e[o],i);else if(e=mt(e),arguments.length===2)Zi(t,function(a){return pe(e,a)!==-1});else for(var s=0,r=e.length;s<r;s++)Re(t,e[s],i,n);return this}function Zi(t,e){for(var i in t[et]){var n=i.split(/\d/)[0];(!e||e(n))&&Re(t,n,null,null,i)}}var Ne={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function De(t,e,i,n){var o=e+g(i)+(n?"_"+g(n):"");if(t[et]&&t[et][o])return this;var s=function(a){return i.call(n||t,a||window.event)},r=s;!f.touchNative&&f.pointer&&e.indexOf("touch")===0?s=Hn(t,e,s):f.touch&&e==="dblclick"?s=Xn(t,s):"addEventListener"in t?e==="touchstart"||e==="touchmove"||e==="wheel"||e==="mousewheel"?t.addEventListener(Ne[e]||e,s,f.passiveEvents?{passive:!1}:!1):e==="mouseenter"||e==="mouseleave"?(s=function(a){a=a||window.event,We(t,a)&&r(a)},t.addEventListener(Ne[e],s,!1)):t.addEventListener(e,r,!1):t.attachEvent("on"+e,s),t[et]=t[et]||{},t[et][o]=s}function Re(t,e,i,n,o){o=o||e+g(i)+(n?"_"+g(n):"");var s=t[et]&&t[et][o];if(!s)return this;!f.touchNative&&f.pointer&&e.indexOf("touch")===0?Un(t,e,s):f.touch&&e==="dblclick"?Jn(t,s):"removeEventListener"in t?t.removeEventListener(Ne[e]||e,s,!1):t.detachEvent("on"+e,s),t[et][o]=null}function gt(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function Fe(t){return De(t,"wheel",gt),this}function Wt(t){return p(t,"mousedown touchstart dblclick contextmenu",gt),t._leaflet_disable_click=!0,this}function N(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function yt(t){return N(t),gt(t),this}function Ei(t){if(t.composedPath)return t.composedPath();for(var e=[],i=t.target;i;)e.push(i),i=i.parentNode;return e}function Oi(t,e){if(!e)return new m(t.clientX,t.clientY);var i=Ae(e),n=i.boundingClientRect;return new m((t.clientX-n.left)/i.x-e.clientLeft,(t.clientY-n.top)/i.y-e.clientTop)}var to=f.linux&&f.chrome?window.devicePixelRatio:f.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Ii(t){return f.edge?t.wheelDeltaY/2:t.deltaY&&t.deltaMode===0?-t.deltaY/to:t.deltaY&&t.deltaMode===1?-t.deltaY*20:t.deltaY&&t.deltaMode===2?-t.deltaY*60:t.deltaX||t.deltaZ?0:t.wheelDelta?(t.wheelDeltaY||t.wheelDelta)/2:t.detail&&Math.abs(t.detail)<32765?-t.detail*20:t.detail?t.detail/-32765*60:0}function We(t,e){var i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch{return!1}return i!==t}var eo={__proto__:null,on:p,off:C,stopPropagation:gt,disableScrollPropagation:Fe,disableClickPropagation:Wt,preventDefault:N,stop:yt,getPropagationPath:Ei,getMousePosition:Oi,getWheelDelta:Ii,isExternalTarget:We,addListener:p,removeListener:C},Bi=Ot.extend({run:function(t,e,i,n){this.stop(),this._el=t,this._inProgress=!0,this._duration=i||.25,this._easeOutPower=1/Math.max(n||.5,.2),this._startPos=vt(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=R(this._animate,this),this._step()},_step:function(t){var e=+new Date-this._startTime,i=this._duration*1e3;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame:function(t,e){var i=this._startPos.add(this._offset.multiplyBy(t));e&&i._round(),I(this._el,i),this.fire("step")},_complete:function(){q(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(t){return 1-Math.pow(1-t,this._easeOutPower)}}),x=Ot.extend({options:{crs:xe,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(t,e){e=S(this,e),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._onResize=T(this._onResize,this),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),e.zoom!==void 0&&(this._zoom=this._limitZoom(e.zoom)),e.center&&e.zoom!==void 0&&this.setView(w(e.center),e.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=At&&f.any3d&&!f.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),p(this._proxy,Si,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(t,e,i){if(e=e===void 0?this._zoom:this._limitZoom(e),t=this._limitCenter(w(t),e,this.options.maxBounds),i=i||{},this._stop(),this._loaded&&!i.reset&&i!==!0){i.animate!==void 0&&(i.zoom=b({animate:i.animate},i.zoom),i.pan=b({animate:i.animate,duration:i.duration},i.pan));var n=this._zoom!==e?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,e,i.zoom):this._tryAnimatedPan(t,i.pan);if(n)return clearTimeout(this._sizeTimer),this}return this._resetView(t,e,i.pan&&i.pan.noMoveStart),this},setZoom:function(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn:function(t,e){return t=t||(f.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+t,e)},zoomOut:function(t,e){return t=t||(f.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-t,e)},setZoomAround:function(t,e,i){var n=this.getZoomScale(e),o=this.getSize().divideBy(2),s=t instanceof m?t:this.latLngToContainerPoint(t),r=s.subtract(o).multiplyBy(1-1/n),a=this.containerPointToLatLng(o.add(r));return this.setView(a,e,{zoom:i})},_getBoundsCenterZoom:function(t,e){e=e||{},t=t.getBounds?t.getBounds():O(t);var i=_(e.paddingTopLeft||e.padding||[0,0]),n=_(e.paddingBottomRight||e.padding||[0,0]),o=this.getBoundsZoom(t,!1,i.add(n));if(o=typeof e.maxZoom=="number"?Math.min(e.maxZoom,o):o,o===1/0)return{center:t.getCenter(),zoom:o};var s=n.subtract(i).divideBy(2),r=this.project(t.getSouthWest(),o),a=this.project(t.getNorthEast(),o),h=this.unproject(r.add(a).divideBy(2).add(s),o);return{center:h,zoom:o}},fitBounds:function(t,e){if(t=O(t),!t.isValid())throw new Error("Bounds are not valid.");var i=this._getBoundsCenterZoom(t,e);return this.setView(i.center,i.zoom,e)},fitWorld:function(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo:function(t,e){return this.setView(t,this._zoom,{pan:e})},panBy:function(t,e){if(t=_(t).round(),e=e||{},!t.x&&!t.y)return this.fire("moveend");if(e.animate!==!0&&!this.getSize().contains(t))return this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new Bi,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),e.animate!==!1){v(this._mapPane,"leaflet-pan-anim");var i=this._getMapPanePos().subtract(t).round();this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)}else this._rawPanBy(t),this.fire("move").fire("moveend");return this},flyTo:function(t,e,i){if(i=i||{},i.animate===!1||!f.any3d)return this.setView(t,e,i);this._stop();var n=this.project(this.getCenter()),o=this.project(t),s=this.getSize(),r=this._zoom;t=w(t),e=e===void 0?r:e;var a=Math.max(s.x,s.y),h=a*this.getZoomScale(r,e),u=o.distanceTo(n)||1,c=1.42,d=c*c;function y(B){var _e=B?-1:1,Vo=B?h:a,Go=h*h-a*a+_e*d*d*u*u,qo=2*Vo*d*u,$e=Go/qo,pn=Math.sqrt($e*$e+1)-$e,jo=pn<1e-9?-18:Math.log(pn);return jo}function D(B){return(Math.exp(B)-Math.exp(-B))/2}function A(B){return(Math.exp(B)+Math.exp(-B))/2}function Y(B){return D(B)/A(B)}var H=y(0);function kt(B){return a*(A(H)/A(H+c*B))}function Fo(B){return a*(A(H)*Y(H+c*B)-D(H))/d}function Wo(B){return 1-Math.pow(1-B,1.5)}var Ho=Date.now(),_n=(y(1)-H)/c,Uo=i.duration?1e3*i.duration:1e3*_n*.8;function mn(){var B=(Date.now()-Ho)/Uo,_e=Wo(B)*_n;B<=1?(this._flyToFrame=R(mn,this),this._move(this.unproject(n.add(o.subtract(n).multiplyBy(Fo(_e)/u)),r),this.getScaleZoom(a/kt(_e),r),{flyTo:!0})):this._move(t,e)._moveEnd(!0)}return this._moveStart(!0,i.noMoveStart),mn.call(this),this},flyToBounds:function(t,e){var i=this._getBoundsCenterZoom(t,e);return this.flyTo(i.center,i.zoom,e)},setMaxBounds:function(t){return t=O(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid()?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(t){var e=this.options.minZoom;return this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom:function(t){var e=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds:function(t,e){this._enforcingBounds=!0;var i=this.getCenter(),n=this._limitCenter(i,this._zoom,O(t));return i.equals(n)||this.panTo(n,e),this._enforcingBounds=!1,this},panInside:function(t,e){e=e||{};var i=_(e.paddingTopLeft||e.padding||[0,0]),n=_(e.paddingBottomRight||e.padding||[0,0]),o=this.project(this.getCenter()),s=this.project(t),r=this.getPixelBounds(),a=F([r.min.add(i),r.max.subtract(n)]),h=a.getSize();if(!a.contains(s)){this._enforcingBounds=!0;var u=s.subtract(a.getCenter()),c=a.extend(s).getSize().subtract(h);o.x+=u.x<0?-c.x:c.x,o.y+=u.y<0?-c.y:c.y,this.panTo(this.unproject(o),e),this._enforcingBounds=!1}return this},invalidateSize:function(t){if(!this._loaded)return this;t=b({animate:!1,pan:!0},t===!0?{animate:!0}:t);var e=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var i=this.getSize(),n=e.divideBy(2).round(),o=i.divideBy(2).round(),s=n.subtract(o);return!s.x&&!s.y?this:(t.animate&&t.pan?this.panBy(s):(t.pan&&this._rawPanBy(s),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(T(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:e,newSize:i}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(t){if(t=this._locateOptions=b({timeout:1e4,watch:!1},t),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var e=T(this._handleGeolocationResponse,this),i=T(this._handleGeolocationError,this);return t.watch?this._locationWatchId=navigator.geolocation.watchPosition(e,i,t):navigator.geolocation.getCurrentPosition(e,i,t),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(t){if(this._container._leaflet_id){var e=t.code,i=t.message||(e===1?"permission denied":e===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:"Geolocation error: "+i+"."})}},_handleGeolocationResponse:function(t){if(this._container._leaflet_id){var e=t.coords.latitude,i=t.coords.longitude,n=new M(e,i),o=n.toBounds(t.coords.accuracy*2),s=this._locateOptions;if(s.setView){var r=this.getBoundsZoom(o);this.setView(n,s.maxZoom?Math.min(r,s.maxZoom):r)}var a={latlng:n,bounds:o,timestamp:t.timestamp};for(var h in t.coords)typeof t.coords[h]=="number"&&(a[h]=t.coords[h]);this.fire("locationfound",a)}},addHandler:function(t,e){if(!e)return this;var i=this[t]=new e(this);return this._handlers.push(i),this.options[t]&&i.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),k(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(q(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var t;for(t in this._layers)this._layers[t].remove();for(t in this._panes)k(this._panes[t]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(t,e){var i="leaflet-pane"+(t?" leaflet-"+t.replace("Pane","")+"-pane":""),n=P("div",i,e||this._mapPane);return t&&(this._panes[t]=n),n},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var t=this.getPixelBounds(),e=this.unproject(t.getBottomLeft()),i=this.unproject(t.getTopRight());return new W(e,i)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(t,e,i){t=O(t),i=_(i||[0,0]);var n=this.getZoom()||0,o=this.getMinZoom(),s=this.getMaxZoom(),r=t.getNorthWest(),a=t.getSouthEast(),h=this.getSize().subtract(i),u=F(this.project(a,n),this.project(r,n)).getSize(),c=f.any3d?this.options.zoomSnap:1,d=h.x/u.x,y=h.y/u.y,D=e?Math.max(d,y):Math.min(d,y);return n=this.getScaleZoom(D,n),c&&(n=Math.round(n/(c/100))*(c/100),n=e?Math.ceil(n/c)*c:Math.floor(n/c)*c),Math.max(o,Math.min(s,n))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new m(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(t,e){var i=this._getTopLeftPoint(t,e);return new z(i,i.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(t){return this.options.crs.getProjectedBounds(t===void 0?this.getZoom():t)},getPane:function(t){return typeof t=="string"?this._panes[t]:t},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(t,e){var i=this.options.crs;return e=e===void 0?this._zoom:e,i.scale(t)/i.scale(e)},getScaleZoom:function(t,e){var i=this.options.crs;e=e===void 0?this._zoom:e;var n=i.zoom(t*i.scale(e));return isNaN(n)?1/0:n},project:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.latLngToPoint(w(t),e)},unproject:function(t,e){return e=e===void 0?this._zoom:e,this.options.crs.pointToLatLng(_(t),e)},layerPointToLatLng:function(t){var e=_(t).add(this.getPixelOrigin());return this.unproject(e)},latLngToLayerPoint:function(t){var e=this.project(w(t))._round();return e._subtract(this.getPixelOrigin())},wrapLatLng:function(t){return this.options.crs.wrapLatLng(w(t))},wrapLatLngBounds:function(t){return this.options.crs.wrapLatLngBounds(O(t))},distance:function(t,e){return this.options.crs.distance(w(t),w(e))},containerPointToLayerPoint:function(t){return _(t).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(t){return _(t).add(this._getMapPanePos())},containerPointToLatLng:function(t){var e=this.containerPointToLayerPoint(_(t));return this.layerPointToLatLng(e)},latLngToContainerPoint:function(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(w(t)))},mouseEventToContainerPoint:function(t){return Oi(t,this._container)},mouseEventToLayerPoint:function(t){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(t))},mouseEventToLatLng:function(t){return this.layerPointToLatLng(this.mouseEventToLayerPoint(t))},_initContainer:function(t){var e=this._container=zi(t);if(e){if(e._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");p(e,"scroll",this._onScroll,this),this._containerId=g(e)},_initLayout:function(){var t=this._container;this._fadeAnimated=this.options.fadeAnimation&&f.any3d,v(t,"leaflet-container"+(f.touch?" leaflet-touch":"")+(f.retina?" leaflet-retina":"")+(f.ielt9?" leaflet-oldie":"")+(f.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var e=Nt(t,"position");e!=="absolute"&&e!=="relative"&&e!=="fixed"&&e!=="sticky"&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),I(this._mapPane,new m(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(v(t.markerPane,"leaflet-zoom-hide"),v(t.shadowPane,"leaflet-zoom-hide"))},_resetView:function(t,e,i){I(this._mapPane,new m(0,0));var n=!this._loaded;this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset");var o=this._zoom!==e;this._moveStart(o,i)._move(t,e)._moveEnd(o),this.fire("viewreset"),n&&this.fire("load")},_moveStart:function(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move:function(t,e,i,n){e===void 0&&(e=this._zoom);var o=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),n?i&&i.pinch&&this.fire("zoom",i):((o||i&&i.pinch)&&this.fire("zoom",i),this.fire("move",i)),this},_moveEnd:function(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return q(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(t){I(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(t){this._targets={},this._targets[g(this._container)]=this;var e=t?C:p;e(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&e(window,"resize",this._onResize,this),f.any3d&&this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){q(this._resizeRequest),this._resizeRequest=R(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(t,e){for(var i=[],n,o=e==="mouseout"||e==="mouseover",s=t.target||t.srcElement,r=!1;s;){if(n=this._targets[g(s)],n&&(e==="click"||e==="preclick")&&this._draggableMoved(n)){r=!0;break}if(n&&n.listens(e,!0)&&(o&&!We(s,t)||(i.push(n),o))||s===this._container)break;s=s.parentNode}return!i.length&&!r&&!o&&this.listens(e,!0)&&(i=[this]),i},_isClickDisabled:function(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click)return!0;t=t.parentNode}},_handleDOMEvent:function(t){var e=t.target||t.srcElement;if(!(!this._loaded||e._leaflet_disable_events||t.type==="click"&&this._isClickDisabled(e))){var i=t.type;i==="mousedown"&&Be(e),this._fireDOMEvent(t,i)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(t,e,i){if(t.type==="click"){var n=b({},t);n.type="preclick",this._fireDOMEvent(n,n.type,i)}var o=this._findEventTargets(t,e);if(i){for(var s=[],r=0;r<i.length;r++)i[r].listens(e,!0)&&s.push(i[r]);o=s.concat(o)}if(o.length){e==="contextmenu"&&N(t);var a=o[0],h={originalEvent:t};if(t.type!=="keypress"&&t.type!=="keydown"&&t.type!=="keyup"){var u=a.getLatLng&&(!a._radius||a._radius<=10);h.containerPoint=u?this.latLngToContainerPoint(a.getLatLng()):this.mouseEventToContainerPoint(t),h.layerPoint=this.containerPointToLayerPoint(h.containerPoint),h.latlng=u?a.getLatLng():this.layerPointToLatLng(h.layerPoint)}for(r=0;r<o.length;r++)if(o[r].fire(e,h,!0),h.originalEvent._stopped||o[r].options.bubblingMouseEvents===!1&&pe(this._mouseEvents,e)!==-1)return}},_draggableMoved:function(t){return t=t.dragging&&t.dragging.enabled()?t:this,t.dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var t=0,e=this._handlers.length;t<e;t++)this._handlers[t].disable()},whenReady:function(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos:function(){return vt(this._mapPane)||new m(0,0)},_moved:function(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint:function(t,e){var i=t&&e!==void 0?this._getNewPixelOrigin(t,e):this.getPixelOrigin();return i.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return this.project(t,e)._subtract(n)},_latLngBoundsToNewLayerBounds:function(t,e,i){var n=this._getNewPixelOrigin(i,e);return F([this.project(t.getSouthWest(),e)._subtract(n),this.project(t.getNorthWest(),e)._subtract(n),this.project(t.getSouthEast(),e)._subtract(n),this.project(t.getNorthEast(),e)._subtract(n)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter:function(t,e,i){if(!i)return t;var n=this.project(t,e),o=this.getSize().divideBy(2),s=new z(n.subtract(o),n.add(o)),r=this._getBoundsOffset(s,i,e);return Math.abs(r.x)<=1&&Math.abs(r.y)<=1?t:this.unproject(n.add(r),e)},_limitOffset:function(t,e){if(!e)return t;var i=this.getPixelBounds(),n=new z(i.min.add(t),i.max.add(t));return t.add(this._getBoundsOffset(n,e))},_getBoundsOffset:function(t,e,i){var n=F(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i)),o=n.min.subtract(t.min),s=n.max.subtract(t.max),r=this._rebound(o.x,-s.x),a=this._rebound(o.y,-s.y);return new m(r,a)},_rebound:function(t,e){return t+e>0?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom:function(t){var e=this.getMinZoom(),i=this.getMaxZoom(),n=f.any3d?this.options.zoomSnap:1;return n&&(t=Math.round(t/n)*n),Math.max(e,Math.min(i,t))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){E(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(t,e){var i=this._getCenterOffset(t)._trunc();return(e&&e.animate)!==!0&&!this.getSize().contains(i)?!1:(this.panBy(i,e),!0)},_createAnimProxy:function(){var t=this._proxy=P("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(t),this.on("zoomanim",function(e){var i=Se,n=this._proxy.style[i];pt(this._proxy,this.project(e.center,e.zoom),this.getZoomScale(e.zoom,1)),n===this._proxy.style[i]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){k(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var t=this.getCenter(),e=this.getZoom();pt(this._proxy,this.project(t,e),this.getZoomScale(e,1))},_catchTransitionEnd:function(t){this._animatingZoom&&t.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(t,e,i){if(this._animatingZoom)return!0;if(i=i||{},!this._zoomAnimated||i.animate===!1||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var n=this.getZoomScale(e),o=this._getCenterOffset(t)._divideBy(1-1/n);return i.animate!==!0&&!this.getSize().contains(o)?!1:(R(function(){this._moveStart(!0,i.noMoveStart||!1)._animateZoom(t,e,!0)},this),!0)},_animateZoom:function(t,e,i,n){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,v(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:n}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(T(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&E(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function io(t,e){return new x(t,e)}var J=ot.extend({options:{position:"topright"},initialize:function(t){S(this,t)},getPosition:function(){return this.options.position},setPosition:function(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer:function(){return this._container},addTo:function(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),n=t._controlCorners[i];return v(e,"leaflet-control"),i.indexOf("bottom")!==-1?n.insertBefore(e,n.firstChild):n.appendChild(e),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(k(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(t){this._map&&t&&t.screenX>0&&t.screenY>0&&this._map.getContainer().focus()}}),Ht=function(t){return new J(t)};x.include({addControl:function(t){return t.addTo(this),this},removeControl:function(t){return t.remove(),this},_initControlPos:function(){var t=this._controlCorners={},e="leaflet-",i=this._controlContainer=P("div",e+"control-container",this._container);function n(o,s){var r=e+o+" "+e+s;t[o+s]=P("div",r,i)}n("top","left"),n("top","right"),n("bottom","left"),n("bottom","right")},_clearControlPos:function(){for(var t in this._controlCorners)k(this._controlCorners[t]);k(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var Ai=J.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(t,e,i,n){return i<n?-1:n<i?1:0}},initialize:function(t,e,i){S(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var n in t)this._addLayer(t[n],n);for(n in e)this._addLayer(e[n],n,!0)},onAdd:function(t){this._initLayout(),this._update(),this._map=t,t.on("zoomend",this._checkDisabledLayers,this);for(var e=0;e<this._layers.length;e++)this._layers[e].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(t){return J.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t=0;t<this._layers.length;t++)this._layers[t].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay:function(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer:function(t){t.off("add remove",this._onLayerChange,this);var e=this._getLayer(g(t));return e&&this._layers.splice(this._layers.indexOf(e),1),this._map?this._update():this},expand:function(){v(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(v(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):E(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return E(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var t="leaflet-control-layers",e=this._container=P("div",t),i=this.options.collapsed;e.setAttribute("aria-haspopup",!0),Wt(e),Fe(e);var n=this._section=P("section",t+"-list");i&&(this._map.on("click",this.collapse,this),p(e,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var o=this._layersLink=P("a",t+"-toggle",e);o.href="#",o.title="Layers",o.setAttribute("role","button"),p(o,{keydown:function(s){s.keyCode===13&&this._expandSafely()},click:function(s){N(s),this._expandSafely()}},this),i||this.expand(),this._baseLayersList=P("div",t+"-base",n),this._separator=P("div",t+"-separator",n),this._overlaysList=P("div",t+"-overlays",n),e.appendChild(n)},_getLayer:function(t){for(var e=0;e<this._layers.length;e++)if(this._layers[e]&&g(this._layers[e].layer)===t)return this._layers[e]},_addLayer:function(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort(T(function(n,o){return this.options.sortFunction(n.layer,o.layer,n.name,o.name)},this)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;$t(this._baseLayersList),$t(this._overlaysList),this._layerControlInputs=[];var t,e,i,n,o=0;for(i=0;i<this._layers.length;i++)n=this._layers[i],this._addItem(n),e=e||n.overlay,t=t||!n.overlay,o+=n.overlay?0:1;return this.options.hideSingleBase&&(t=t&&o>1,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=e&&t?"":"none",this},_onLayerChange:function(t){this._handlingClick||this._update();var e=this._getLayer(g(t.target)),i=e.overlay?t.type==="add"?"overlayadd":"overlayremove":t.type==="add"?"baselayerchange":null;i&&this._map.fire(i,e)},_createRadioElement:function(t,e){var i='<input type="radio" class="leaflet-control-layers-selector" name="'+t+'"'+(e?' checked="checked"':"")+"/>",n=document.createElement("div");return n.innerHTML=i,n.firstChild},_addItem:function(t){var e=document.createElement("label"),i=this._map.hasLayer(t.layer),n;t.overlay?(n=document.createElement("input"),n.type="checkbox",n.className="leaflet-control-layers-selector",n.defaultChecked=i):n=this._createRadioElement("leaflet-base-layers_"+g(this),i),this._layerControlInputs.push(n),n.layerId=g(t.layer),p(n,"click",this._onInputClick,this);var o=document.createElement("span");o.innerHTML=" "+t.name;var s=document.createElement("span");e.appendChild(s),s.appendChild(n),s.appendChild(o);var r=t.overlay?this._overlaysList:this._baseLayersList;return r.appendChild(e),this._checkDisabledLayers(),e},_onInputClick:function(){if(!this._preventClick){var t=this._layerControlInputs,e,i,n=[],o=[];this._handlingClick=!0;for(var s=t.length-1;s>=0;s--)e=t[s],i=this._getLayer(e.layerId).layer,e.checked?n.push(i):e.checked||o.push(i);for(s=0;s<o.length;s++)this._map.hasLayer(o[s])&&this._map.removeLayer(o[s]);for(s=0;s<n.length;s++)this._map.hasLayer(n[s])||this._map.addLayer(n[s]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var t=this._layerControlInputs,e,i,n=this._map.getZoom(),o=t.length-1;o>=0;o--)e=t[o],i=this._getLayer(e.layerId).layer,e.disabled=i.options.minZoom!==void 0&&n<i.options.minZoom||i.options.maxZoom!==void 0&&n>i.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var t=this._section;this._preventClick=!0,p(t,"click",N),this.expand();var e=this;setTimeout(function(){C(t,"click",N),e._preventClick=!1})}}),no=function(t,e,i){return new Ai(t,e,i)},He=J.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(t){var e="leaflet-control-zoom",i=P("div",e+" leaflet-bar"),n=this.options;return this._zoomInButton=this._createButton(n.zoomInText,n.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(n.zoomOutText,n.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove:function(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut:function(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton:function(t,e,i,n,o){var s=P("a",i,n);return s.innerHTML=t,s.href="#",s.title=e,s.setAttribute("role","button"),s.setAttribute("aria-label",e),Wt(s),p(s,"click",yt),p(s,"click",o,this),p(s,"click",this._refocusOnMap,this),s},_updateDisabled:function(){var t=this._map,e="leaflet-disabled";E(this._zoomInButton,e),E(this._zoomOutButton,e),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||t._zoom===t.getMinZoom())&&(v(this._zoomOutButton,e),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||t._zoom===t.getMaxZoom())&&(v(this._zoomInButton,e),this._zoomInButton.setAttribute("aria-disabled","true"))}});x.mergeOptions({zoomControl:!0}),x.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new He,this.addControl(this.zoomControl))});var oo=function(t){return new He(t)},Ni=J.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(t){var e="leaflet-control-scale",i=P("div",e),n=this.options;return this._addScales(n,e+"-line",i),t.on(n.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),i},onRemove:function(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(t,e,i){t.metric&&(this._mScale=P("div",e,i)),t.imperial&&(this._iScale=P("div",e,i))},_update:function(){var t=this._map,e=t.getSize().y/2,i=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(i)},_updateScales:function(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric:function(t){var e=this._getRoundNum(t),i=e<1e3?e+" m":e/1e3+" km";this._updateScale(this._mScale,i,e/t)},_updateImperial:function(t){var e=t*3.2808399,i,n,o;e>5280?(i=e/5280,n=this._getRoundNum(i),this._updateScale(this._iScale,n+" mi",n/i)):(o=this._getRoundNum(e),this._updateScale(this._iScale,o+" ft",o/e))},_updateScale:function(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum:function(t){var e=Math.pow(10,(Math.floor(t)+"").length-1),i=t/e;return i=i>=10?10:i>=5?5:i>=3?3:i>=2?2:1,e*i}}),so=function(t){return new Ni(t)},ro='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Ue=J.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(f.inlineSvg?ro+" ":"")+"Leaflet</a>"},initialize:function(t){S(this,t),this._attributions={}},onAdd:function(t){t.attributionControl=this,this._container=P("div","leaflet-control-attribution"),Wt(this._container);for(var e in t._layers)t._layers[e].getAttribution&&this.addAttribution(t._layers[e].getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove:function(t){t.off("layeradd",this._addAttribution,this)},_addAttribution:function(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",function(){this.removeAttribution(t.layer.getAttribution())},this))},setPrefix:function(t){return this.options.prefix=t,this._update(),this},addAttribution:function(t){return t?(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update(),this):this},removeAttribution:function(t){return t?(this._attributions[t]&&(this._attributions[t]--,this._update()),this):this},_update:function(){if(this._map){var t=[];for(var e in this._attributions)this._attributions[e]&&t.push(e);var i=[];this.options.prefix&&i.push(this.options.prefix),t.length&&i.push(t.join(", ")),this._container.innerHTML=i.join(' <span aria-hidden="true">|</span> ')}}});x.mergeOptions({attributionControl:!0}),x.addInitHook(function(){this.options.attributionControl&&new Ue().addTo(this)});var ao=function(t){return new Ue(t)};J.Layers=Ai,J.Zoom=He,J.Scale=Ni,J.Attribution=Ue,Ht.layers=no,Ht.zoom=oo,Ht.scale=so,Ht.attribution=ao;var it=ot.extend({initialize:function(t){this._map=t},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});it.addTo=function(t,e){return t.addHandler(e,this),this};var ho={Events:V},Di=f.touch?"touchstart mousedown":"mousedown",ft=Ot.extend({options:{clickTolerance:3},initialize:function(t,e,i,n){S(this,n),this._element=t,this._dragStartTarget=e||t,this._preventOutline=i},enable:function(){this._enabled||(p(this._dragStartTarget,Di,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(ft._dragging===this&&this.finishDrag(!0),C(this._dragStartTarget,Di,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(t){if(this._enabled&&(this._moved=!1,!ze(this._element,"leaflet-zoom-anim"))){if(t.touches&&t.touches.length!==1){ft._dragging===this&&this.finishDrag();return}if(!(ft._dragging||t.shiftKey||t.which!==1&&t.button!==1&&!t.touches)&&(ft._dragging=this,this._preventOutline&&Be(this._element),Ee(),Dt(),!this._moving)){this.fire("down");var e=t.touches?t.touches[0]:t,i=ki(this._element);this._startPoint=new m(e.clientX,e.clientY),this._startPos=vt(this._element),this._parentScale=Ae(i);var n=t.type==="mousedown";p(document,n?"mousemove":"touchmove",this._onMove,this),p(document,n?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(t){if(this._enabled){if(t.touches&&t.touches.length>1){this._moved=!0;return}var e=t.touches&&t.touches.length===1?t.touches[0]:t,i=new m(e.clientX,e.clientY)._subtract(this._startPoint);!i.x&&!i.y||Math.abs(i.x)+Math.abs(i.y)<this.options.clickTolerance||(i.x/=this._parentScale.x,i.y/=this._parentScale.y,N(t),this._moved||(this.fire("dragstart"),this._moved=!0,v(document.body,"leaflet-dragging"),this._lastTarget=t.target||t.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),v(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(i),this._moving=!0,this._lastEvent=t,this._updatePosition())}},_updatePosition:function(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),I(this._element,this._newPos),this.fire("drag",t)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(t){E(document.body,"leaflet-dragging"),this._lastTarget&&(E(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),C(document,"mousemove touchmove",this._onMove,this),C(document,"mouseup touchend touchcancel",this._onUp,this),Oe(),Rt();var e=this._moved&&this._moving;this._moving=!1,ft._dragging=!1,e&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}});function Ri(t,e,i){var n,o=[1,4,2,8],s,r,a,h,u,c,d,y;for(s=0,c=t.length;s<c;s++)t[s]._code=wt(t[s],e);for(a=0;a<4;a++){for(d=o[a],n=[],s=0,c=t.length,r=c-1;s<c;r=s++)h=t[s],u=t[r],h._code&d?u._code&d||(y=ne(u,h,d,e,i),y._code=wt(y,e),n.push(y)):(u._code&d&&(y=ne(u,h,d,e,i),y._code=wt(y,e),n.push(y)),n.push(h));t=n}return t}function Fi(t,e){var i,n,o,s,r,a,h,u,c;if(!t||t.length===0)throw new Error("latlngs not passed");K(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var d=w([0,0]),y=O(t),D=y.getNorthWest().distanceTo(y.getSouthWest())*y.getNorthEast().distanceTo(y.getNorthWest());D<1700&&(d=Ve(t));var A=t.length,Y=[];for(i=0;i<A;i++){var H=w(t[i]);Y.push(e.project(w([H.lat-d.lat,H.lng-d.lng])))}for(a=h=u=0,i=0,n=A-1;i<A;n=i++)o=Y[i],s=Y[n],r=o.y*s.x-s.y*o.x,h+=(o.x+s.x)*r,u+=(o.y+s.y)*r,a+=r*3;a===0?c=Y[0]:c=[h/a,u/a];var kt=e.unproject(_(c));return w([kt.lat+d.lat,kt.lng+d.lng])}function Ve(t){for(var e=0,i=0,n=0,o=0;o<t.length;o++){var s=w(t[o]);e+=s.lat,i+=s.lng,n++}return w([e/n,i/n])}var uo={__proto__:null,clipPolygon:Ri,polygonCenter:Fi,centroid:Ve};function Wi(t,e){if(!e||!t.length)return t.slice();var i=e*e;return t=fo(t,i),t=co(t,i),t}function Hi(t,e,i){return Math.sqrt(Ut(t,e,i,!0))}function lo(t,e,i){return Ut(t,e,i)}function co(t,e){var i=t.length,n=typeof Uint8Array<"u"?Uint8Array:Array,o=new n(i);o[0]=o[i-1]=1,Ge(t,o,e,0,i-1);var s,r=[];for(s=0;s<i;s++)o[s]&&r.push(t[s]);return r}function Ge(t,e,i,n,o){var s=0,r,a,h;for(a=n+1;a<=o-1;a++)h=Ut(t[a],t[n],t[o],!0),h>s&&(r=a,s=h);s>i&&(e[r]=1,Ge(t,e,i,n,r),Ge(t,e,i,r,o))}function fo(t,e){for(var i=[t[0]],n=1,o=0,s=t.length;n<s;n++)_o(t[n],t[o])>e&&(i.push(t[n]),o=n);return o<s-1&&i.push(t[s-1]),i}var Ui;function Vi(t,e,i,n,o){var s=n?Ui:wt(t,i),r=wt(e,i),a,h,u;for(Ui=r;;){if(!(s|r))return[t,e];if(s&r)return!1;a=s||r,h=ne(t,e,a,i,o),u=wt(h,i),a===s?(t=h,s=u):(e=h,r=u)}}function ne(t,e,i,n,o){var s=e.x-t.x,r=e.y-t.y,a=n.min,h=n.max,u,c;return i&8?(u=t.x+s*(h.y-t.y)/r,c=h.y):i&4?(u=t.x+s*(a.y-t.y)/r,c=a.y):i&2?(u=h.x,c=t.y+r*(h.x-t.x)/s):i&1&&(u=a.x,c=t.y+r*(a.x-t.x)/s),new m(u,c,o)}function wt(t,e){var i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function _o(t,e){var i=e.x-t.x,n=e.y-t.y;return i*i+n*n}function Ut(t,e,i,n){var o=e.x,s=e.y,r=i.x-o,a=i.y-s,h=r*r+a*a,u;return h>0&&(u=((t.x-o)*r+(t.y-s)*a)/h,u>1?(o=i.x,s=i.y):u>0&&(o+=r*u,s+=a*u)),r=t.x-o,a=t.y-s,n?r*r+a*a:new m(o,s)}function K(t){return!X(t[0])||typeof t[0][0]!="object"&&typeof t[0][0]<"u"}function Gi(t){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),K(t)}function qi(t,e){var i,n,o,s,r,a,h,u;if(!t||t.length===0)throw new Error("latlngs not passed");K(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);var c=w([0,0]),d=O(t),y=d.getNorthWest().distanceTo(d.getSouthWest())*d.getNorthEast().distanceTo(d.getNorthWest());y<1700&&(c=Ve(t));var D=t.length,A=[];for(i=0;i<D;i++){var Y=w(t[i]);A.push(e.project(w([Y.lat-c.lat,Y.lng-c.lng])))}for(i=0,n=0;i<D-1;i++)n+=A[i].distanceTo(A[i+1])/2;if(n===0)u=A[0];else for(i=0,s=0;i<D-1;i++)if(r=A[i],a=A[i+1],o=r.distanceTo(a),s+=o,s>n){h=(s-n)/o,u=[a.x-h*(a.x-r.x),a.y-h*(a.y-r.y)];break}var H=e.unproject(_(u));return w([H.lat+c.lat,H.lng+c.lng])}var mo={__proto__:null,simplify:Wi,pointToSegmentDistance:Hi,closestPointOnSegment:lo,clipSegment:Vi,_getEdgeIntersection:ne,_getBitCode:wt,_sqClosestPointOnSegment:Ut,isFlat:K,_flat:Gi,polylineCenter:qi},qe={project:function(t){return new m(t.lng,t.lat)},unproject:function(t){return new M(t.y,t.x)},bounds:new z([-180,-90],[180,90])},je={R:6378137,R_MINOR:6356752314245179e-9,bounds:new z([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(t){var e=Math.PI/180,i=this.R,n=t.lat*e,o=this.R_MINOR/i,s=Math.sqrt(1-o*o),r=s*Math.sin(n),a=Math.tan(Math.PI/4-n/2)/Math.pow((1-r)/(1+r),s/2);return n=-i*Math.log(Math.max(a,1e-10)),new m(t.lng*e*i,n)},unproject:function(t){for(var e=180/Math.PI,i=this.R,n=this.R_MINOR/i,o=Math.sqrt(1-n*n),s=Math.exp(-t.y/i),r=Math.PI/2-2*Math.atan(s),a=0,h=.1,u;a<15&&Math.abs(h)>1e-7;a++)u=o*Math.sin(r),u=Math.pow((1-u)/(1+u),o/2),h=Math.PI/2-2*Math.atan(s*u)-r,r+=h;return new M(r*e,t.x*e/i)}},po={__proto__:null,LonLat:qe,Mercator:je,SphericalMercator:ye},vo=b({},ct,{code:"EPSG:3395",projection:je,transformation:function(){var t=.5/(Math.PI*je.R);return It(t,.5,-t,.5)}()}),ji=b({},ct,{code:"EPSG:4326",projection:qe,transformation:It(1/180,1,-1/180,.5)}),go=b({},st,{projection:qe,transformation:It(1,0,-1,0),scale:function(t){return Math.pow(2,t)},zoom:function(t){return Math.log(t)/Math.LN2},distance:function(t,e){var i=e.lng-t.lng,n=e.lat-t.lat;return Math.sqrt(i*i+n*n)},infinite:!0});st.Earth=ct,st.EPSG3395=vo,st.EPSG3857=xe,st.EPSG900913=Ln,st.EPSG4326=ji,st.Simple=go;var $=Ot.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(t){return t.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(t){return t&&t.removeLayer(this),this},getPane:function(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget:function(t){return this._map._targets[g(t)]=this,this},removeInteractiveTarget:function(t){return delete this._map._targets[g(t)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(t){var e=t.target;if(e.hasLayer(this)){if(this._map=e,this._zoomAnimated=e._zoomAnimated,this.getEvents){var i=this.getEvents();e.on(i,this),this.once("remove",function(){e.off(i,this)},this)}this.onAdd(e),this.fire("add"),e.fire("layeradd",{layer:this})}}});x.include({addLayer:function(t){if(!t._layerAdd)throw new Error("The provided object is not a Layer.");var e=g(t);return this._layers[e]?this:(this._layers[e]=t,t._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t),this)},removeLayer:function(t){var e=g(t);return this._layers[e]?(this._loaded&&t.onRemove(this),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null,this):this},hasLayer:function(t){return g(t)in this._layers},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},_addLayers:function(t){t=t?X(t)?t:[t]:[];for(var e=0,i=t.length;e<i;e++)this.addLayer(t[e])},_addZoomLimit:function(t){(!isNaN(t.options.maxZoom)||!isNaN(t.options.minZoom))&&(this._zoomBoundLayers[g(t)]=t,this._updateZoomLevels())},_removeZoomLimit:function(t){var e=g(t);this._zoomBoundLayers[e]&&(delete this._zoomBoundLayers[e],this._updateZoomLevels())},_updateZoomLevels:function(){var t=1/0,e=-1/0,i=this._getZoomSpan();for(var n in this._zoomBoundLayers){var o=this._zoomBoundLayers[n].options;t=o.minZoom===void 0?t:Math.min(t,o.minZoom),e=o.maxZoom===void 0?e:Math.max(e,o.maxZoom)}this._layersMaxZoom=e===-1/0?void 0:e,this._layersMinZoom=t===1/0?void 0:t,i!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var bt=$.extend({initialize:function(t,e){S(this,e),this._layers={};var i,n;if(t)for(i=0,n=t.length;i<n;i++)this.addLayer(t[i])},addLayer:function(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer:function(t){var e=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[e]&&this._map.removeLayer(this._layers[e]),delete this._layers[e],this},hasLayer:function(t){var e=typeof t=="number"?t:this.getLayerId(t);return e in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(t){var e=Array.prototype.slice.call(arguments,1),i,n;for(i in this._layers)n=this._layers[i],n[t]&&n[t].apply(n,e);return this},onAdd:function(t){this.eachLayer(t.addLayer,t)},onRemove:function(t){this.eachLayer(t.removeLayer,t)},eachLayer:function(t,e){for(var i in this._layers)t.call(e,this._layers[i]);return this},getLayer:function(t){return this._layers[t]},getLayers:function(){var t=[];return this.eachLayer(t.push,t),t},setZIndex:function(t){return this.invoke("setZIndex",t)},getLayerId:function(t){return g(t)}}),yo=function(t,e){return new bt(t,e)},rt=bt.extend({addLayer:function(t){return this.hasLayer(t)?this:(t.addEventParent(this),bt.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer:function(t){return this.hasLayer(t)?(t in this._layers&&(t=this._layers[t]),t.removeEventParent(this),bt.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle:function(t){return this.invoke("setStyle",t)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var t=new W;for(var e in this._layers){var i=this._layers[e];t.extend(i.getBounds?i.getBounds():i.getLatLng())}return t}}),wo=function(t,e){return new rt(t,e)},Mt=ot.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(t){S(this,t)},createIcon:function(t){return this._createIcon("icon",t)},createShadow:function(t){return this._createIcon("shadow",t)},_createIcon:function(t,e){var i=this._getIconUrl(t);if(!i){if(t==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var n=this._createImg(i,e&&e.tagName==="IMG"?e:null);return this._setIconStyles(n,t),(this.options.crossOrigin||this.options.crossOrigin==="")&&(n.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),n},_setIconStyles:function(t,e){var i=this.options,n=i[e+"Size"];typeof n=="number"&&(n=[n,n]);var o=_(n),s=_(e==="shadow"&&i.shadowAnchor||i.iconAnchor||o&&o.divideBy(2,!0));t.className="leaflet-marker-"+e+" "+(i.className||""),s&&(t.style.marginLeft=-s.x+"px",t.style.marginTop=-s.y+"px"),o&&(t.style.width=o.x+"px",t.style.height=o.y+"px")},_createImg:function(t,e){return e=e||document.createElement("img"),e.src=t,e},_getIconUrl:function(t){return f.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}});function xo(t){return new Mt(t)}var Vt=Mt.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(t){return typeof Vt.imagePath!="string"&&(Vt.imagePath=this._detectIconPath()),(this.options.imagePath||Vt.imagePath)+Mt.prototype._getIconUrl.call(this,t)},_stripUrl:function(t){var e=function(i,n,o){var s=n.exec(i);return s&&s[o]};return t=e(t,/^url\((['"])?(.+)\1\)$/,2),t&&e(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var t=P("div","leaflet-default-icon-path",document.body),e=Nt(t,"background-image")||Nt(t,"backgroundImage");if(document.body.removeChild(t),e=this._stripUrl(e),e)return e;var i=document.querySelector('link[href$="leaflet.css"]');return i?i.href.substring(0,i.href.length-11-1):""}}),Ki=it.extend({initialize:function(t){this._marker=t},addHooks:function(){var t=this._marker._icon;this._draggable||(this._draggable=new ft(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),v(t,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&E(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(t){var e=this._marker,i=e._map,n=this._marker.options.autoPanSpeed,o=this._marker.options.autoPanPadding,s=vt(e._icon),r=i.getPixelBounds(),a=i.getPixelOrigin(),h=F(r.min._subtract(a).add(o),r.max._subtract(a).subtract(o));if(!h.contains(s)){var u=_((Math.max(h.max.x,s.x)-h.max.x)/(r.max.x-h.max.x)-(Math.min(h.min.x,s.x)-h.min.x)/(r.min.x-h.min.x),(Math.max(h.max.y,s.y)-h.max.y)/(r.max.y-h.max.y)-(Math.min(h.min.y,s.y)-h.min.y)/(r.min.y-h.min.y)).multiplyBy(n);i.panBy(u,{animate:!1}),this._draggable._newPos._add(u),this._draggable._startPos._add(u),I(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=R(this._adjustPan.bind(this,t))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(t){this._marker.options.autoPan&&(q(this._panRequest),this._panRequest=R(this._adjustPan.bind(this,t)))},_onDrag:function(t){var e=this._marker,i=e._shadow,n=vt(e._icon),o=e._map.layerPointToLatLng(n);i&&I(i,n),e._latlng=o,t.latlng=o,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd:function(t){q(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),oe=$.extend({options:{icon:new Vt,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(t,e){S(this,e),this._latlng=w(t)},onAdd:function(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(t){var e=this._latlng;return this._latlng=w(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset:function(t){return this.options.zIndexOffset=t,this.update()},getIcon:function(){return this.options.icon},setIcon:function(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var t=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(t)}return this},_initIcon:function(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon),n=!1;i!==this._icon&&(this._icon&&this._removeIcon(),n=!0,t.title&&(i.title=t.title),i.tagName==="IMG"&&(i.alt=t.alt||"")),v(i,e),t.keyboard&&(i.tabIndex="0",i.setAttribute("role","button")),this._icon=i,t.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&p(i,"focus",this._panOnFocus,this);var o=t.icon.createShadow(this._shadow),s=!1;o!==this._shadow&&(this._removeShadow(),s=!0),o&&(v(o,e),o.alt=""),this._shadow=o,t.opacity<1&&this._updateOpacity(),n&&this.getPane().appendChild(this._icon),this._initInteraction(),o&&s&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&C(this._icon,"focus",this._panOnFocus,this),k(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&k(this._shadow),this._shadow=null},_setPos:function(t){this._icon&&I(this._icon,t),this._shadow&&I(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(e)},_initInteraction:function(){if(this.options.interactive&&(v(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),Ki)){var t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new Ki(this),t&&this.dragging.enable()}},setOpacity:function(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var t=this.options.opacity;this._icon&&j(this._icon,t),this._shadow&&j(this._shadow,t)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var t=this._map;if(t){var e=this.options.icon.options,i=e.iconSize?_(e.iconSize):_(0,0),n=e.iconAnchor?_(e.iconAnchor):_(0,0);t.panInside(this._latlng,{paddingTopLeft:n,paddingBottomRight:i.subtract(n)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function Po(t,e){return new oe(t,e)}var dt=$.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(t){this._renderer=t.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(t){return S(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&t&&Object.prototype.hasOwnProperty.call(t,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),se=dt.extend({options:{fill:!0,radius:10},initialize:function(t,e){S(this,e),this._latlng=w(t),this._radius=this.options.radius},setLatLng:function(t){var e=this._latlng;return this._latlng=w(t),this.redraw(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(t){return this.options.radius=this._radius=t,this.redraw()},getRadius:function(){return this._radius},setStyle:function(t){var e=t&&t.radius||this._radius;return dt.prototype.setStyle.call(this,t),this.setRadius(e),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var t=this._radius,e=this._radiusY||t,i=this._clickTolerance(),n=[t+i,e+i];this._pxBounds=new z(this._point.subtract(n),this._point.add(n))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function Lo(t,e){return new se(t,e)}var Ke=se.extend({initialize:function(t,e,i){if(typeof e=="number"&&(e=b({},i,{radius:e})),S(this,e),this._latlng=w(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(t){return this._mRadius=t,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var t=[this._radius,this._radiusY||this._radius];return new W(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:dt.prototype.setStyle,_project:function(){var t=this._latlng.lng,e=this._latlng.lat,i=this._map,n=i.options.crs;if(n.distance===ct.distance){var o=Math.PI/180,s=this._mRadius/ct.R/o,r=i.project([e+s,t]),a=i.project([e-s,t]),h=r.add(a).divideBy(2),u=i.unproject(h).lat,c=Math.acos((Math.cos(s*o)-Math.sin(e*o)*Math.sin(u*o))/(Math.cos(e*o)*Math.cos(u*o)))/o;(isNaN(c)||c===0)&&(c=s/Math.cos(Math.PI/180*e)),this._point=h.subtract(i.getPixelOrigin()),this._radius=isNaN(c)?0:h.x-i.project([u,t-c]).x,this._radiusY=h.y-r.y}else{var d=n.unproject(n.project(this._latlng).subtract([this._mRadius,0]));this._point=i.latLngToLayerPoint(this._latlng),this._radius=this._point.x-i.latLngToLayerPoint(d).x}this._updateBounds()}});function To(t,e,i){return new Ke(t,e,i)}var at=dt.extend({options:{smoothFactor:1,noClip:!1},initialize:function(t,e){S(this,e),this._setLatLngs(t)},getLatLngs:function(){return this._latlngs},setLatLngs:function(t){return this._setLatLngs(t),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(t){for(var e=1/0,i=null,n=Ut,o,s,r=0,a=this._parts.length;r<a;r++)for(var h=this._parts[r],u=1,c=h.length;u<c;u++){o=h[u-1],s=h[u];var d=n(t,o,s,!0);d<e&&(e=d,i=n(t,o,s))}return i&&(i.distance=Math.sqrt(e)),i},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return qi(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(t,e){return e=e||this._defaultShape(),t=w(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs:function(t){this._bounds=new W,this._latlngs=this._convertLatLngs(t)},_defaultShape:function(){return K(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(t){for(var e=[],i=K(t),n=0,o=t.length;n<o;n++)i?(e[n]=w(t[n]),this._bounds.extend(e[n])):e[n]=this._convertLatLngs(t[n]);return e},_project:function(){var t=new z;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds:function(){var t=this._clickTolerance(),e=new m(t,t);this._rawPxBounds&&(this._pxBounds=new z([this._rawPxBounds.min.subtract(e),this._rawPxBounds.max.add(e)]))},_projectLatlngs:function(t,e,i){var n=t[0]instanceof M,o=t.length,s,r;if(n){for(r=[],s=0;s<o;s++)r[s]=this._map.latLngToLayerPoint(t[s]),i.extend(r[s]);e.push(r)}else for(s=0;s<o;s++)this._projectLatlngs(t[s],e,i)},_clipPoints:function(){var t=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}var e=this._parts,i,n,o,s,r,a,h;for(i=0,o=0,s=this._rings.length;i<s;i++)for(h=this._rings[i],n=0,r=h.length;n<r-1;n++)a=Vi(h[n],h[n+1],t,n,!0),a&&(e[o]=e[o]||[],e[o].push(a[0]),(a[1]!==h[n+1]||n===r-2)&&(e[o].push(a[1]),o++))}},_simplifyPoints:function(){for(var t=this._parts,e=this.options.smoothFactor,i=0,n=t.length;i<n;i++)t[i]=Wi(t[i],e)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(t,e){var i,n,o,s,r,a,h=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(i=0,s=this._parts.length;i<s;i++)for(a=this._parts[i],n=0,r=a.length,o=r-1;n<r;o=n++)if(!(!e&&n===0)&&Hi(t,a[o],a[n])<=h)return!0;return!1}});function bo(t,e){return new at(t,e)}at._flat=Gi;var Ct=at.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Fi(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(t){var e=at.prototype._convertLatLngs.call(this,t),i=e.length;return i>=2&&e[0]instanceof M&&e[0].equals(e[i-1])&&e.pop(),e},_setLatLngs:function(t){at.prototype._setLatLngs.call(this,t),K(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return K(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var t=this._renderer._bounds,e=this.options.weight,i=new m(e,e);if(t=new z(t.min.subtract(i),t.max.add(i)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(t))){if(this.options.noClip){this._parts=this._rings;return}for(var n=0,o=this._rings.length,s;n<o;n++)s=Ri(this._rings[n],t,!0),s.length&&this._parts.push(s)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(t){var e=!1,i,n,o,s,r,a,h,u;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(s=0,h=this._parts.length;s<h;s++)for(i=this._parts[s],r=0,u=i.length,a=u-1;r<u;a=r++)n=i[r],o=i[a],n.y>t.y!=o.y>t.y&&t.x<(o.x-n.x)*(t.y-n.y)/(o.y-n.y)+n.x&&(e=!e);return e||at.prototype._containsPoint.call(this,t,!0)}});function Mo(t,e){return new Ct(t,e)}var ht=rt.extend({initialize:function(t,e){S(this,e),this._layers={},t&&this.addData(t)},addData:function(t){var e=X(t)?t:t.features,i,n,o;if(e){for(i=0,n=e.length;i<n;i++)o=e[i],(o.geometries||o.geometry||o.features||o.coordinates)&&this.addData(o);return this}var s=this.options;if(s.filter&&!s.filter(t))return this;var r=re(t,s);return r?(r.feature=ue(t),r.defaultOptions=r.options,this.resetStyle(r),s.onEachFeature&&s.onEachFeature(t,r),this.addLayer(r)):this},resetStyle:function(t){return t===void 0?this.eachLayer(this.resetStyle,this):(t.options=b({},t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle:function(t){return this.eachLayer(function(e){this._setLayerStyle(e,t)},this)},_setLayerStyle:function(t,e){t.setStyle&&(typeof e=="function"&&(e=e(t.feature)),t.setStyle(e))}});function re(t,e){var i=t.type==="Feature"?t.geometry:t,n=i?i.coordinates:null,o=[],s=e&&e.pointToLayer,r=e&&e.coordsToLatLng||Ye,a,h,u,c;if(!n&&!i)return null;switch(i.type){case"Point":return a=r(n),Yi(s,t,a,e);case"MultiPoint":for(u=0,c=n.length;u<c;u++)a=r(n[u]),o.push(Yi(s,t,a,e));return new rt(o);case"LineString":case"MultiLineString":return h=ae(n,i.type==="LineString"?0:1,r),new at(h,e);case"Polygon":case"MultiPolygon":return h=ae(n,i.type==="Polygon"?1:2,r),new Ct(h,e);case"GeometryCollection":for(u=0,c=i.geometries.length;u<c;u++){var d=re({geometry:i.geometries[u],type:"Feature",properties:t.properties},e);d&&o.push(d)}return new rt(o);case"FeatureCollection":for(u=0,c=i.features.length;u<c;u++){var y=re(i.features[u],e);y&&o.push(y)}return new rt(o);default:throw new Error("Invalid GeoJSON object.")}}function Yi(t,e,i,n){return t?t(e,i):new oe(i,n&&n.markersInheritOptions&&n)}function Ye(t){return new M(t[1],t[0],t[2])}function ae(t,e,i){for(var n=[],o=0,s=t.length,r;o<s;o++)r=e?ae(t[o],e-1,i):(i||Ye)(t[o]),n.push(r);return n}function Xe(t,e){return t=w(t),t.alt!==void 0?[Q(t.lng,e),Q(t.lat,e),Q(t.alt,e)]:[Q(t.lng,e),Q(t.lat,e)]}function he(t,e,i,n){for(var o=[],s=0,r=t.length;s<r;s++)o.push(e?he(t[s],K(t[s])?0:e-1,i,n):Xe(t[s],n));return!e&&i&&o.length>0&&o.push(o[0].slice()),o}function St(t,e){return t.feature?b({},t.feature,{geometry:e}):ue(e)}function ue(t){return t.type==="Feature"||t.type==="FeatureCollection"?t:{type:"Feature",properties:{},geometry:t}}var Je={toGeoJSON:function(t){return St(this,{type:"Point",coordinates:Xe(this.getLatLng(),t)})}};oe.include(Je),Ke.include(Je),se.include(Je),at.include({toGeoJSON:function(t){var e=!K(this._latlngs),i=he(this._latlngs,e?1:0,!1,t);return St(this,{type:(e?"Multi":"")+"LineString",coordinates:i})}}),Ct.include({toGeoJSON:function(t){var e=!K(this._latlngs),i=e&&!K(this._latlngs[0]),n=he(this._latlngs,i?2:e?1:0,!0,t);return e||(n=[n]),St(this,{type:(i?"Multi":"")+"Polygon",coordinates:n})}}),bt.include({toMultiPoint:function(t){var e=[];return this.eachLayer(function(i){e.push(i.toGeoJSON(t).geometry.coordinates)}),St(this,{type:"MultiPoint",coordinates:e})},toGeoJSON:function(t){var e=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(e==="MultiPoint")return this.toMultiPoint(t);var i=e==="GeometryCollection",n=[];return this.eachLayer(function(o){if(o.toGeoJSON){var s=o.toGeoJSON(t);if(i)n.push(s.geometry);else{var r=ue(s);r.type==="FeatureCollection"?n.push.apply(n,r.features):n.push(r)}}}),i?St(this,{geometries:n,type:"GeometryCollection"}):{type:"FeatureCollection",features:n}}});function Xi(t,e){return new ht(t,e)}var Co=Xi,le=$.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(t,e,i){this._url=t,this._bounds=O(e),S(this,i)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(v(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){k(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle:function(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront:function(){return this._map&&Lt(this._image),this},bringToBack:function(){return this._map&&Tt(this._image),this},setUrl:function(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds:function(t){return this._bounds=O(t),this._map&&this._reset(),this},getEvents:function(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var t=this._url.tagName==="IMG",e=this._image=t?this._url:P("img");if(v(e,"leaflet-image-layer"),this._zoomAnimated&&v(e,"leaflet-zoom-animated"),this.options.className&&v(e,this.options.className),e.onselectstart=Z,e.onmousemove=Z,e.onload=T(this.fire,this,"load"),e.onerror=T(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(e.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),t){this._url=e.src;return}e.src=this._url,e.alt=this.options.alt},_animateZoom:function(t){var e=this._map.getZoomScale(t.zoom),i=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;pt(this._image,i,e)},_reset:function(){var t=this._image,e=new z(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();I(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity:function(){j(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter:function(){return this._bounds.getCenter()}}),So=function(t,e,i){return new le(t,e,i)},Ji=le.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var t=this._url.tagName==="VIDEO",e=this._image=t?this._url:P("video");if(v(e,"leaflet-image-layer"),this._zoomAnimated&&v(e,"leaflet-zoom-animated"),this.options.className&&v(e,this.options.className),e.onselectstart=Z,e.onmousemove=Z,e.onloadeddata=T(this.fire,this,"load"),t){for(var i=e.getElementsByTagName("source"),n=[],o=0;o<i.length;o++)n.push(i[o].src);this._url=i.length>0?n:[e.src];return}X(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(e.style,"objectFit")&&(e.style.objectFit="fill"),e.autoplay=!!this.options.autoplay,e.loop=!!this.options.loop,e.muted=!!this.options.muted,e.playsInline=!!this.options.playsInline;for(var s=0;s<this._url.length;s++){var r=P("source");r.src=this._url[s],e.appendChild(r)}}});function zo(t,e,i){return new Ji(t,e,i)}var $i=le.extend({_initImage:function(){var t=this._image=this._url;v(t,"leaflet-image-layer"),this._zoomAnimated&&v(t,"leaflet-zoom-animated"),this.options.className&&v(t,this.options.className),t.onselectstart=Z,t.onmousemove=Z}});function ko(t,e,i){return new $i(t,e,i)}var nt=$.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(t,e){t&&(t instanceof M||X(t))?(this._latlng=w(t),S(this,e)):(S(this,t),this._source=e),this.options.content&&(this._content=this.options.content)},openOn:function(t){return t=arguments.length?t:this._source._map,t.hasLayer(this)||t.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd:function(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&j(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&j(this._container,1),this.bringToFront(),this.options.interactive&&(v(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(t){t._fadeAnimated?(j(this._container,0),this._removeTimeout=setTimeout(T(k,void 0,this._container),200)):k(this._container),this.options.interactive&&(E(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(t){return this._latlng=w(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(t){return this._content=t,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Lt(this._container),this},bringToBack:function(){return this._map&&Tt(this._container),this},_prepareOpen:function(t){var e=this._source;if(!e._map)return!1;if(e instanceof rt){e=null;var i=this._source._layers;for(var n in i)if(i[n]._map){e=i[n];break}if(!e)return!1;this._source=e}if(!t)if(e.getCenter)t=e.getCenter();else if(e.getLatLng)t=e.getLatLng();else if(e.getBounds)t=e.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(t),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var t=this._contentNode,e=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof e=="string")t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var t=this._map.latLngToLayerPoint(this._latlng),e=_(this.options.offset),i=this._getAnchor();this._zoomAnimated?I(this._container,t.add(i)):e=e.add(t).add(i);var n=this._containerBottom=-e.y,o=this._containerLeft=-Math.round(this._containerWidth/2)+e.x;this._container.style.bottom=n+"px",this._container.style.left=o+"px"}},_getAnchor:function(){return[0,0]}});x.include({_initOverlay:function(t,e,i,n){var o=e;return o instanceof t||(o=new t(n).setContent(e)),i&&o.setLatLng(i),o}}),$.include({_initOverlay:function(t,e,i,n){var o=i;return o instanceof t?(S(o,n),o._source=this):(o=e&&!n?e:new t(n,this),o.setContent(i)),o}});var ce=nt.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(t){return t=arguments.length?t:this._source._map,!t.hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,nt.prototype.openOn.call(this,t)},onAdd:function(t){nt.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof dt||this._source.on("preclick",gt))},onRemove:function(t){nt.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof dt||this._source.off("preclick",gt))},getEvents:function(){var t=nt.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout:function(){var t="leaflet-popup",e=this._container=P("div",t+" "+(this.options.className||"")+" leaflet-zoom-animated"),i=this._wrapper=P("div",t+"-content-wrapper",e);if(this._contentNode=P("div",t+"-content",i),Wt(e),Fe(this._contentNode),p(e,"contextmenu",gt),this._tipContainer=P("div",t+"-tip-container",e),this._tip=P("div",t+"-tip",this._tipContainer),this.options.closeButton){var n=this._closeButton=P("a",t+"-close-button",e);n.setAttribute("role","button"),n.setAttribute("aria-label","Close popup"),n.href="#close",n.innerHTML='<span aria-hidden="true">&#215;</span>',p(n,"click",function(o){N(o),this.close()},this)}},_updateLayout:function(){var t=this._contentNode,e=t.style;e.width="",e.whiteSpace="nowrap";var i=t.offsetWidth;i=Math.min(i,this.options.maxWidth),i=Math.max(i,this.options.minWidth),e.width=i+1+"px",e.whiteSpace="",e.height="";var n=t.offsetHeight,o=this.options.maxHeight,s="leaflet-popup-scrolled";o&&n>o?(e.height=o+"px",v(t,s)):E(t,s),this._containerWidth=this._container.offsetWidth},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),i=this._getAnchor();I(this._container,e.add(i))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var t=this._map,e=parseInt(Nt(this._container,"marginBottom"),10)||0,i=this._container.offsetHeight+e,n=this._containerWidth,o=new m(this._containerLeft,-i-this._containerBottom);o._add(vt(this._container));var s=t.layerPointToContainerPoint(o),r=_(this.options.autoPanPadding),a=_(this.options.autoPanPaddingTopLeft||r),h=_(this.options.autoPanPaddingBottomRight||r),u=t.getSize(),c=0,d=0;s.x+n+h.x>u.x&&(c=s.x+n-u.x+h.x),s.x-c-a.x<0&&(c=s.x-a.x),s.y+i+h.y>u.y&&(d=s.y+i-u.y+h.y),s.y-d-a.y<0&&(d=s.y-a.y),(c||d)&&(this.options.keepInView&&(this._autopanning=!0),t.fire("autopanstart").panBy([c,d]))}},_getAnchor:function(){return _(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Zo=function(t,e){return new ce(t,e)};x.mergeOptions({closePopupOnClick:!0}),x.include({openPopup:function(t,e,i){return this._initOverlay(ce,t,e,i).openOn(this),this},closePopup:function(t){return t=arguments.length?t:this._popup,t&&t.close(),this}}),$.include({bindPopup:function(t,e){return this._popup=this._initOverlay(ce,this._popup,t,e),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(t){return this._popup&&(this instanceof rt||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(t){return this._popup&&this._popup.setContent(t),this},getPopup:function(){return this._popup},_openPopup:function(t){if(!(!this._popup||!this._map)){yt(t);var e=t.layer||t.target;if(this._popup._source===e&&!(e instanceof dt)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng);return}this._popup._source=e,this.openPopup(t.latlng)}},_movePopup:function(t){this._popup.setLatLng(t.latlng)},_onKeyPress:function(t){t.originalEvent.keyCode===13&&this._openPopup(t)}});var fe=nt.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(t){nt.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(t){nt.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var t=nt.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout:function(){var t="leaflet-tooltip",e=t+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=P("div",e),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+g(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(t){var e,i,n=this._map,o=this._container,s=n.latLngToContainerPoint(n.getCenter()),r=n.layerPointToContainerPoint(t),a=this.options.direction,h=o.offsetWidth,u=o.offsetHeight,c=_(this.options.offset),d=this._getAnchor();a==="top"?(e=h/2,i=u):a==="bottom"?(e=h/2,i=0):a==="center"?(e=h/2,i=u/2):a==="right"?(e=0,i=u/2):a==="left"?(e=h,i=u/2):r.x<s.x?(a="right",e=0,i=u/2):(a="left",e=h+(c.x+d.x)*2,i=u/2),t=t.subtract(_(e,i,!0)).add(c).add(d),E(o,"leaflet-tooltip-right"),E(o,"leaflet-tooltip-left"),E(o,"leaflet-tooltip-top"),E(o,"leaflet-tooltip-bottom"),v(o,"leaflet-tooltip-"+a),I(o,t)},_updatePosition:function(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity:function(t){this.options.opacity=t,this._container&&j(this._container,t)},_animateZoom:function(t){var e=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(e)},_getAnchor:function(){return _(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),Eo=function(t,e){return new fe(t,e)};x.include({openTooltip:function(t,e,i){return this._initOverlay(fe,t,e,i).openOn(this),this},closeTooltip:function(t){return t.close(),this}}),$.include({bindTooltip:function(t,e){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(fe,this._tooltip,t,e),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(t){if(!(!t&&this._tooltipHandlersAdded)){var e=t?"off":"on",i={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?i.add=this._openTooltip:(i.mouseover=this._openTooltip,i.mouseout=this.closeTooltip,i.click=this._openTooltip,this._map?this._addFocusListeners():i.add=this._addFocusListeners),this._tooltip.options.sticky&&(i.mousemove=this._moveTooltip),this[e](i),this._tooltipHandlersAdded=!t}},openTooltip:function(t){return this._tooltip&&(this instanceof rt||(this._tooltip._source=this),this._tooltip._prepareOpen(t)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&(p(e,"focus",function(){this._tooltip._source=t,this.openTooltip()},this),p(e,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(t){var e=typeof t.getElement=="function"&&t.getElement();e&&e.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(t){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var e=this;this._map.once("moveend",function(){e._openOnceFlag=!1,e._openTooltip(t)});return}this._tooltip._source=t.layer||t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)}},_moveTooltip:function(t){var e=t.latlng,i,n;this._tooltip.options.sticky&&t.originalEvent&&(i=this._map.mouseEventToContainerPoint(t.originalEvent),n=this._map.containerPointToLayerPoint(i),e=this._map.layerPointToLatLng(n)),this._tooltip.setLatLng(e)}});var Qi=Mt.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(t){var e=t&&t.tagName==="DIV"?t:document.createElement("div"),i=this.options;if(i.html instanceof Element?($t(e),e.appendChild(i.html)):e.innerHTML=i.html!==!1?i.html:"",i.bgPos){var n=_(i.bgPos);e.style.backgroundPosition=-n.x+"px "+-n.y+"px"}return this._setIconStyles(e,"icon"),e},createShadow:function(){return null}});function Oo(t){return new Qi(t)}Mt.Default=Vt;var Gt=$.extend({options:{tileSize:256,opacity:1,updateWhenIdle:f.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(t){S(this,t)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(t){t._addZoomLimit(this)},onRemove:function(t){this._removeAllTiles(),k(this._container),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Lt(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Tt(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex:function(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var t=this._clampZoom(this._map.getZoom());t!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()}return this},getEvents:function(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=Et(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile:function(){return document.createElement("div")},getTileSize:function(){var t=this.options.tileSize;return t instanceof m?t:new m(t,t)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(t){for(var e=this.getPane().children,i=-t(-1/0,1/0),n=0,o=e.length,s;n<o;n++)s=e[n].style.zIndex,e[n]!==this._container&&s&&(i=t(i,+s));isFinite(i)&&(this.options.zIndex=i+t(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!f.ielt9){j(this._container,this.options.opacity);var t=+new Date,e=!1,i=!1;for(var n in this._tiles){var o=this._tiles[n];if(!(!o.current||!o.loaded)){var s=Math.min(1,(t-o.loaded)/200);j(o.el,s),s<1?e=!0:(o.active?i=!0:this._onOpaqueTile(o),o.active=!0)}}i&&!this._noPrune&&this._pruneTiles(),e&&(q(this._fadeFrame),this._fadeFrame=R(this._updateOpacity,this))}},_onOpaqueTile:Z,_initContainer:function(){this._container||(this._container=P("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var t=this._tileZoom,e=this.options.maxZoom;if(t!==void 0){for(var i in this._levels)i=Number(i),this._levels[i].el.children.length||i===t?(this._levels[i].el.style.zIndex=e-Math.abs(t-i),this._onUpdateLevel(i)):(k(this._levels[i].el),this._removeTilesAtZoom(i),this._onRemoveLevel(i),delete this._levels[i]);var n=this._levels[t],o=this._map;return n||(n=this._levels[t]={},n.el=P("div","leaflet-tile-container leaflet-zoom-animated",this._container),n.el.style.zIndex=e,n.origin=o.project(o.unproject(o.getPixelOrigin()),t).round(),n.zoom=t,this._setZoomTransform(n,o.getCenter(),o.getZoom()),Z(n.el.offsetWidth),this._onCreateLevel(n)),this._level=n,n}},_onUpdateLevel:Z,_onRemoveLevel:Z,_onCreateLevel:Z,_pruneTiles:function(){if(this._map){var t,e,i=this._map.getZoom();if(i>this.options.maxZoom||i<this.options.minZoom){this._removeAllTiles();return}for(t in this._tiles)e=this._tiles[t],e.retain=e.current;for(t in this._tiles)if(e=this._tiles[t],e.current&&!e.active){var n=e.coords;this._retainParent(n.x,n.y,n.z,n.z-5)||this._retainChildren(n.x,n.y,n.z,n.z+2)}for(t in this._tiles)this._tiles[t].retain||this._removeTile(t)}},_removeTilesAtZoom:function(t){for(var e in this._tiles)this._tiles[e].coords.z===t&&this._removeTile(e)},_removeAllTiles:function(){for(var t in this._tiles)this._removeTile(t)},_invalidateAll:function(){for(var t in this._levels)k(this._levels[t].el),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(t,e,i,n){var o=Math.floor(t/2),s=Math.floor(e/2),r=i-1,a=new m(+o,+s);a.z=+r;var h=this._tileCoordsToKey(a),u=this._tiles[h];return u&&u.active?(u.retain=!0,!0):(u&&u.loaded&&(u.retain=!0),r>n?this._retainParent(o,s,r,n):!1)},_retainChildren:function(t,e,i,n){for(var o=2*t;o<2*t+2;o++)for(var s=2*e;s<2*e+2;s++){var r=new m(o,s);r.z=i+1;var a=this._tileCoordsToKey(r),h=this._tiles[a];if(h&&h.active){h.retain=!0;continue}else h&&h.loaded&&(h.retain=!0);i+1<n&&this._retainChildren(o,s,i+1,n)}},_resetView:function(t){var e=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),e,e)},_animateZoom:function(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom:function(t){var e=this.options;return e.minNativeZoom!==void 0&&t<e.minNativeZoom?e.minNativeZoom:e.maxNativeZoom!==void 0&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView:function(t,e,i,n){var o=Math.round(e);this.options.maxZoom!==void 0&&o>this.options.maxZoom||this.options.minZoom!==void 0&&o<this.options.minZoom?o=void 0:o=this._clampZoom(o);var s=this.options.updateWhenZooming&&o!==this._tileZoom;(!n||s)&&(this._tileZoom=o,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),o!==void 0&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms:function(t,e){for(var i in this._levels)this._setZoomTransform(this._levels[i],t,e)},_setZoomTransform:function(t,e,i){var n=this._map.getZoomScale(i,t.zoom),o=t.origin.multiplyBy(n).subtract(this._map._getNewPixelOrigin(e,i)).round();f.any3d?pt(t.el,o,n):I(t.el,o)},_resetGrid:function(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),n=this._tileZoom,o=this._map.getPixelWorldBounds(this._tileZoom);o&&(this._globalTileRange=this._pxBoundsToTileRange(o)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],n).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],n).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],n).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],n).y/i.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),n=e.getZoomScale(i,this._tileZoom),o=e.project(t,this._tileZoom).floor(),s=e.getSize().divideBy(n*2);return new z(o.subtract(s),o.add(s))},_update:function(t){var e=this._map;if(e){var i=this._clampZoom(e.getZoom());if(t===void 0&&(t=e.getCenter()),this._tileZoom!==void 0){var n=this._getTiledPixelBounds(t),o=this._pxBoundsToTileRange(n),s=o.getCenter(),r=[],a=this.options.keepBuffer,h=new z(o.getBottomLeft().subtract([a,-a]),o.getTopRight().add([a,-a]));if(!(isFinite(o.min.x)&&isFinite(o.min.y)&&isFinite(o.max.x)&&isFinite(o.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var u in this._tiles){var c=this._tiles[u].coords;(c.z!==this._tileZoom||!h.contains(new m(c.x,c.y)))&&(this._tiles[u].current=!1)}if(Math.abs(i-this._tileZoom)>1){this._setView(t,i);return}for(var d=o.min.y;d<=o.max.y;d++)for(var y=o.min.x;y<=o.max.x;y++){var D=new m(y,d);if(D.z=this._tileZoom,!!this._isValidTile(D)){var A=this._tiles[this._tileCoordsToKey(D)];A?A.current=!0:r.push(D)}}if(r.sort(function(H,kt){return H.distanceTo(s)-kt.distanceTo(s)}),r.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var Y=document.createDocumentFragment();for(y=0;y<r.length;y++)this._addTile(r[y],Y);this._level.el.appendChild(Y)}}}},_isValidTile:function(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}if(!this.options.bounds)return!0;var n=this._tileCoordsToBounds(t);return O(this.options.bounds).overlaps(n)},_keyToBounds:function(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe:function(t){var e=this._map,i=this.getTileSize(),n=t.scaleBy(i),o=n.add(i),s=e.unproject(n,t.z),r=e.unproject(o,t.z);return[s,r]},_tileCoordsToBounds:function(t){var e=this._tileCoordsToNwSe(t),i=new W(e[0],e[1]);return this.options.noWrap||(i=this._map.wrapLatLngBounds(i)),i},_tileCoordsToKey:function(t){return t.x+":"+t.y+":"+t.z},_keyToTileCoords:function(t){var e=t.split(":"),i=new m(+e[0],+e[1]);return i.z=+e[2],i},_removeTile:function(t){var e=this._tiles[t];e&&(k(e.el),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile:function(t){v(t,"leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=Z,t.onmousemove=Z,f.ielt9&&this.options.opacity<1&&j(t,this.options.opacity)},_addTile:function(t,e){var i=this._getTilePos(t),n=this._tileCoordsToKey(t),o=this.createTile(this._wrapCoords(t),T(this._tileReady,this,t));this._initTile(o),this.createTile.length<2&&R(T(this._tileReady,this,t,null,o)),I(o,i),this._tiles[n]={el:o,coords:t,current:!0},e.appendChild(o),this.fire("tileloadstart",{tile:o,coords:t})},_tileReady:function(t,e,i){e&&this.fire("tileerror",{error:e,tile:i,coords:t});var n=this._tileCoordsToKey(t);i=this._tiles[n],i&&(i.loaded=+new Date,this._map._fadeAnimated?(j(i.el,0),q(this._fadeFrame),this._fadeFrame=R(this._updateOpacity,this)):(i.active=!0,this._pruneTiles()),e||(v(i.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),f.ielt9||!this._map._fadeAnimated?R(this._pruneTiles,this):setTimeout(T(this._pruneTiles,this),250)))},_getTilePos:function(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(t){var e=new m(this._wrapX?lt(t.x,this._wrapX):t.x,this._wrapY?lt(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange:function(t){var e=this.getTileSize();return new z(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var t in this._tiles)if(!this._tiles[t].loaded)return!1;return!0}});function Io(t){return new Gt(t)}var zt=Gt.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(t,e){this._url=t,e=S(this,e),e.detectRetina&&f.retina&&e.maxZoom>0?(e.tileSize=Math.floor(e.tileSize/2),e.zoomReverse?(e.zoomOffset--,e.minZoom=Math.min(e.maxZoom,e.minZoom+1)):(e.zoomOffset++,e.maxZoom=Math.max(e.minZoom,e.maxZoom-1)),e.minZoom=Math.max(0,e.minZoom)):e.zoomReverse?e.minZoom=Math.min(e.maxZoom,e.minZoom):e.maxZoom=Math.max(e.minZoom,e.maxZoom),typeof e.subdomains=="string"&&(e.subdomains=e.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(t,e){return this._url===t&&e===void 0&&(e=!0),this._url=t,e||this.redraw(),this},createTile:function(t,e){var i=document.createElement("img");return p(i,"load",T(this._tileOnLoad,this,e,i)),p(i,"error",T(this._tileOnError,this,e,i)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(i.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(i.referrerPolicy=this.options.referrerPolicy),i.alt="",i.src=this.getTileUrl(t),i},getTileUrl:function(t){var e={r:f.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var i=this._globalTileRange.max.y-t.y;this.options.tms&&(e.y=i),e["-y"]=i}return ei(this._url,b(e,this.options))},_tileOnLoad:function(t,e){f.ielt9?setTimeout(T(t,this,null,e),0):t(null,e)},_tileOnError:function(t,e,i){var n=this.options.errorTileUrl;n&&e.getAttribute("src")!==n&&(e.src=n),t(i,e)},_onTileRemove:function(t){t.tile.onload=null},_getZoomForUrl:function(){var t=this._tileZoom,e=this.options.maxZoom,i=this.options.zoomReverse,n=this.options.zoomOffset;return i&&(t=e-t),t+n},_getSubdomain:function(t){var e=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[e]},_abortLoading:function(){var t,e;for(t in this._tiles)if(this._tiles[t].coords.z!==this._tileZoom&&(e=this._tiles[t].el,e.onload=Z,e.onerror=Z,!e.complete)){e.src=Yt;var i=this._tiles[t].coords;k(e),delete this._tiles[t],this.fire("tileabort",{tile:e,coords:i})}},_removeTile:function(t){var e=this._tiles[t];if(e)return e.el.setAttribute("src",Yt),Gt.prototype._removeTile.call(this,t)},_tileReady:function(t,e,i){if(!(!this._map||i&&i.getAttribute("src")===Yt))return Gt.prototype._tileReady.call(this,t,e,i)}});function tn(t,e){return new zt(t,e)}var en=zt.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(t,e){this._url=t;var i=b({},this.defaultWmsParams);for(var n in e)n in this.options||(i[n]=e[n]);e=S(this,e);var o=e.detectRetina&&f.retina?2:1,s=this.getTileSize();i.width=s.x*o,i.height=s.y*o,this.wmsParams=i},onAdd:function(t){this._crs=this.options.crs||t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[e]=this._crs.code,zt.prototype.onAdd.call(this,t)},getTileUrl:function(t){var e=this._tileCoordsToNwSe(t),i=this._crs,n=F(i.project(e[0]),i.project(e[1])),o=n.min,s=n.max,r=(this._wmsVersion>=1.3&&this._crs===ji?[o.y,o.x,s.y,s.x]:[o.x,o.y,s.x,s.y]).join(","),a=zt.prototype.getTileUrl.call(this,t);return a+ti(this.wmsParams,a,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+r},setParams:function(t,e){return b(this.wmsParams,t),e||this.redraw(),this}});function Bo(t,e){return new en(t,e)}zt.WMS=en,tn.wms=Bo;var ut=$.extend({options:{padding:.1},initialize:function(t){S(this,t),g(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),v(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),t},_onAnimZoom:function(t){this._updateTransform(t.center,t.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(t,e){var i=this._map.getZoomScale(e,this._zoom),n=this._map.getSize().multiplyBy(.5+this.options.padding),o=this._map.project(this._center,e),s=n.multiplyBy(-i).add(o).subtract(this._map._getNewPixelOrigin(t,e));f.any3d?pt(this._container,s,i):I(this._container,s)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var t in this._layers)this._layers[t]._reset()},_onZoomEnd:function(){for(var t in this._layers)this._layers[t]._project()},_updatePaths:function(){for(var t in this._layers)this._layers[t]._update()},_update:function(){var t=this.options.padding,e=this._map.getSize(),i=this._map.containerPointToLayerPoint(e.multiplyBy(-t)).round();this._bounds=new z(i,i.add(e.multiplyBy(1+t*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),nn=ut.extend({options:{tolerance:0},getEvents:function(){var t=ut.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){ut.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var t=this._container=document.createElement("canvas");p(t,"mousemove",this._onMouseMove,this),p(t,"click dblclick mousedown mouseup contextmenu",this._onClick,this),p(t,"mouseout",this._handleMouseOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer:function(){q(this._redrawRequest),delete this._ctx,k(this._container),C(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var t;this._redrawBounds=null;for(var e in this._layers)t=this._layers[e],t._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){ut.prototype._update.call(this);var t=this._bounds,e=this._container,i=t.getSize(),n=f.retina?2:1;I(e,t.min),e.width=n*i.x,e.height=n*i.y,e.style.width=i.x+"px",e.style.height=i.y+"px",f.retina&&this._ctx.scale(2,2),this._ctx.translate(-t.min.x,-t.min.y),this.fire("update")}},_reset:function(){ut.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(t){this._updateDashArray(t),this._layers[g(t)]=t;var e=t._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=e),this._drawLast=e,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(t){this._requestRedraw(t)},_removePath:function(t){var e=t._order,i=e.next,n=e.prev;i?i.prev=n:this._drawLast=n,n?n.next=i:this._drawFirst=i,delete t._order,delete this._layers[g(t)],this._requestRedraw(t)},_updatePath:function(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle:function(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray:function(t){if(typeof t.options.dashArray=="string"){var e=t.options.dashArray.split(/[, ]+/),i=[],n,o;for(o=0;o<e.length;o++){if(n=Number(e[o]),isNaN(n))return;i.push(n)}t.options._dashArray=i}else t.options._dashArray=t.options.dashArray},_requestRedraw:function(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest=this._redrawRequest||R(this._redraw,this))},_extendRedrawBounds:function(t){if(t._pxBounds){var e=(t.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new z,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var t=this._redrawBounds;if(t){var e=t.getSize();this._ctx.clearRect(t.min.x,t.min.y,e.x,e.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var t,e=this._redrawBounds;if(this._ctx.save(),e){var i=e.getSize();this._ctx.beginPath(),this._ctx.rect(e.min.x,e.min.y,i.x,i.y),this._ctx.clip()}this._drawing=!0;for(var n=this._drawFirst;n;n=n.next)t=n.layer,(!e||t._pxBounds&&t._pxBounds.intersects(e))&&t._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(t,e){if(this._drawing){var i,n,o,s,r=t._parts,a=r.length,h=this._ctx;if(a){for(h.beginPath(),i=0;i<a;i++){for(n=0,o=r[i].length;n<o;n++)s=r[i][n],h[n?"lineTo":"moveTo"](s.x,s.y);e&&h.closePath()}this._fillStroke(h,t)}}},_updateCircle:function(t){if(!(!this._drawing||t._empty())){var e=t._point,i=this._ctx,n=Math.max(Math.round(t._radius),1),o=(Math.max(Math.round(t._radiusY),1)||n)/n;o!==1&&(i.save(),i.scale(1,o)),i.beginPath(),i.arc(e.x,e.y/o,n,0,Math.PI*2,!1),o!==1&&i.restore(),this._fillStroke(i,t)}},_fillStroke:function(t,e){var i=e.options;i.fill&&(t.globalAlpha=i.fillOpacity,t.fillStyle=i.fillColor||i.color,t.fill(i.fillRule||"evenodd")),i.stroke&&i.weight!==0&&(t.setLineDash&&t.setLineDash(e.options&&e.options._dashArray||[]),t.globalAlpha=i.opacity,t.lineWidth=i.weight,t.strokeStyle=i.color,t.lineCap=i.lineCap,t.lineJoin=i.lineJoin,t.stroke())},_onClick:function(t){for(var e=this._map.mouseEventToLayerPoint(t),i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(!(t.type==="click"||t.type==="preclick")||!this._map._draggableMoved(i))&&(n=i);this._fireEvent(n?[n]:!1,t)},_onMouseMove:function(t){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var e=this._map.mouseEventToLayerPoint(t);this._handleMouseHover(t,e)}},_handleMouseOut:function(t){var e=this._hoveredLayer;e&&(E(this._container,"leaflet-interactive"),this._fireEvent([e],t,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(t,e){if(!this._mouseHoverThrottled){for(var i,n,o=this._drawFirst;o;o=o.next)i=o.layer,i.options.interactive&&i._containsPoint(e)&&(n=i);n!==this._hoveredLayer&&(this._handleMouseOut(t),n&&(v(this._container,"leaflet-interactive"),this._fireEvent([n],t,"mouseover"),this._hoveredLayer=n)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,t),this._mouseHoverThrottled=!0,setTimeout(T(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(i)i.prev=n;else return;n?n.next=i:i&&(this._drawFirst=i),e.prev=this._drawLast,this._drawLast.next=e,e.next=null,this._drawLast=e,this._requestRedraw(t)}},_bringToBack:function(t){var e=t._order;if(e){var i=e.next,n=e.prev;if(n)n.next=i;else return;i?i.prev=n:n&&(this._drawLast=n),e.prev=null,e.next=this._drawFirst,this._drawFirst.prev=e,this._drawFirst=e,this._requestRedraw(t)}}});function on(t){return f.canvas?new nn(t):null}var qt=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<lvml:"+t+' class="lvml">')}}catch{}return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),Ao={_initContainer:function(){this._container=P("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(ut.prototype._update.call(this),this.fire("update"))},_initPath:function(t){var e=t._container=qt("shape");v(e,"leaflet-vml-shape "+(this.options.className||"")),e.coordsize="1 1",t._path=qt("path"),e.appendChild(t._path),this._updateStyle(t),this._layers[g(t)]=t},_addPath:function(t){var e=t._container;this._container.appendChild(e),t.options.interactive&&t.addInteractiveTarget(e)},_removePath:function(t){var e=t._container;k(e),t.removeInteractiveTarget(e),delete this._layers[g(t)]},_updateStyle:function(t){var e=t._stroke,i=t._fill,n=t.options,o=t._container;o.stroked=!!n.stroke,o.filled=!!n.fill,n.stroke?(e||(e=t._stroke=qt("stroke")),o.appendChild(e),e.weight=n.weight+"px",e.color=n.color,e.opacity=n.opacity,n.dashArray?e.dashStyle=X(n.dashArray)?n.dashArray.join(" "):n.dashArray.replace(/( *, *)/g," "):e.dashStyle="",e.endcap=n.lineCap.replace("butt","flat"),e.joinstyle=n.lineJoin):e&&(o.removeChild(e),t._stroke=null),n.fill?(i||(i=t._fill=qt("fill")),o.appendChild(i),i.color=n.fillColor||n.color,i.opacity=n.fillOpacity):i&&(o.removeChild(i),t._fill=null)},_updateCircle:function(t){var e=t._point.round(),i=Math.round(t._radius),n=Math.round(t._radiusY||i);this._setPath(t,t._empty()?"M0 0":"AL "+e.x+","+e.y+" "+i+","+n+" 0,"+65535*360)},_setPath:function(t,e){t._path.v=e},_bringToFront:function(t){Lt(t._container)},_bringToBack:function(t){Tt(t._container)}},de=f.vml?qt:ai,jt=ut.extend({_initContainer:function(){this._container=de("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=de("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){k(this._container),C(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){ut.prototype._update.call(this);var t=this._bounds,e=t.getSize(),i=this._container;(!this._svgSize||!this._svgSize.equals(e))&&(this._svgSize=e,i.setAttribute("width",e.x),i.setAttribute("height",e.y)),I(i,t.min),i.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update")}},_initPath:function(t){var e=t._path=de("path");t.options.className&&v(e,t.options.className),t.options.interactive&&v(e,"leaflet-interactive"),this._updateStyle(t),this._layers[g(t)]=t},_addPath:function(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath:function(t){k(t._path),t.removeInteractiveTarget(t._path),delete this._layers[g(t)]},_updatePath:function(t){t._project(),t._update()},_updateStyle:function(t){var e=t._path,i=t.options;e&&(i.stroke?(e.setAttribute("stroke",i.color),e.setAttribute("stroke-opacity",i.opacity),e.setAttribute("stroke-width",i.weight),e.setAttribute("stroke-linecap",i.lineCap),e.setAttribute("stroke-linejoin",i.lineJoin),i.dashArray?e.setAttribute("stroke-dasharray",i.dashArray):e.removeAttribute("stroke-dasharray"),i.dashOffset?e.setAttribute("stroke-dashoffset",i.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),i.fill?(e.setAttribute("fill",i.fillColor||i.color),e.setAttribute("fill-opacity",i.fillOpacity),e.setAttribute("fill-rule",i.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly:function(t,e){this._setPath(t,hi(t._parts,e))},_updateCircle:function(t){var e=t._point,i=Math.max(Math.round(t._radius),1),n=Math.max(Math.round(t._radiusY),1)||i,o="a"+i+","+n+" 0 1,0 ",s=t._empty()?"M0 0":"M"+(e.x-i)+","+e.y+o+i*2+",0 "+o+-i*2+",0 ";this._setPath(t,s)},_setPath:function(t,e){t._path.setAttribute("d",e)},_bringToFront:function(t){Lt(t._path)},_bringToBack:function(t){Tt(t._path)}});f.vml&&jt.include(Ao);function sn(t){return f.svg||f.vml?new jt(t):null}x.include({getRenderer:function(t){var e=t.options.renderer||this._getPaneRenderer(t.options.pane)||this.options.renderer||this._renderer;return e||(e=this._renderer=this._createRenderer()),this.hasLayer(e)||this.addLayer(e),e},_getPaneRenderer:function(t){if(t==="overlayPane"||t===void 0)return!1;var e=this._paneRenderers[t];return e===void 0&&(e=this._createRenderer({pane:t}),this._paneRenderers[t]=e),e},_createRenderer:function(t){return this.options.preferCanvas&&on(t)||sn(t)}});var rn=Ct.extend({initialize:function(t,e){Ct.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds:function(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs:function(t){return t=O(t),[t.getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});function No(t,e){return new rn(t,e)}jt.create=de,jt.pointsToPath=hi,ht.geometryToLayer=re,ht.coordsToLatLng=Ye,ht.coordsToLatLngs=ae,ht.latLngToCoords=Xe,ht.latLngsToCoords=he,ht.getFeature=St,ht.asFeature=ue,x.mergeOptions({boxZoom:!0});var an=it.extend({initialize:function(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks:function(){p(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){C(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){k(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(t){if(!t.shiftKey||t.which!==1&&t.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),Dt(),Ee(),this._startPoint=this._map.mouseEventToContainerPoint(t),p(document,{contextmenu:yt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(t){this._moved||(this._moved=!0,this._box=P("div","leaflet-zoom-box",this._container),v(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(t);var e=new z(this._point,this._startPoint),i=e.getSize();I(this._box,e.min),this._box.style.width=i.x+"px",this._box.style.height=i.y+"px"},_finish:function(){this._moved&&(k(this._box),E(this._container,"leaflet-crosshair")),Rt(),Oe(),C(document,{contextmenu:yt,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(t){if(!(t.which!==1&&t.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(T(this._resetState,this),0);var e=new W(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(e).fire("boxzoomend",{boxZoomBounds:e})}},_onKeyDown:function(t){t.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});x.addInitHook("addHandler","boxZoom",an),x.mergeOptions({doubleClickZoom:!0});var hn=it.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(t){var e=this._map,i=e.getZoom(),n=e.options.zoomDelta,o=t.originalEvent.shiftKey?i-n:i+n;e.options.doubleClickZoom==="center"?e.setZoom(o):e.setZoomAround(t.containerPoint,o)}});x.addInitHook("addHandler","doubleClickZoom",hn),x.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var un=it.extend({addHooks:function(){if(!this._draggable){var t=this._map;this._draggable=new ft(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))}v(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){E(this._map._container,"leaflet-grab"),E(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var t=this._map;if(t._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var e=O(this._map.options.maxBounds);this._offsetLimit=F(this._map.latLngToContainerPoint(e.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(e.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;t.fire("movestart").fire("dragstart"),t.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(t){if(this._map.options.inertia){var e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(i),this._times.push(e),this._prunePositions(e)}this._map.fire("move",t).fire("drag",t)},_prunePositions:function(t){for(;this._positions.length>1&&t-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit;t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t)}},_onPreDragWrap:function(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,n=this._draggable._newPos.x,o=(n-e+i)%t+e-i,s=(n+e+i)%t-e-i,r=Math.abs(o+i)<Math.abs(s+i)?o:s;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=r},_onDragEnd:function(t){var e=this._map,i=e.options,n=!i.inertia||t.noInertia||this._times.length<2;if(e.fire("dragend",t),n)e.fire("moveend");else{this._prunePositions(+new Date);var o=this._lastPos.subtract(this._positions[0]),s=(this._lastTime-this._times[0])/1e3,r=i.easeLinearity,a=o.multiplyBy(r/s),h=a.distanceTo([0,0]),u=Math.min(i.inertiaMaxSpeed,h),c=a.multiplyBy(u/h),d=u/(i.inertiaDeceleration*r),y=c.multiplyBy(-d/2).round();!y.x&&!y.y?e.fire("moveend"):(y=e._limitOffset(y,e.options.maxBounds),R(function(){e.panBy(y,{duration:d,easeLinearity:r,noMoveStart:!0,animate:!0})}))}}});x.addInitHook("addHandler","dragging",un),x.mergeOptions({keyboard:!0,keyboardPanDelta:80});var ln=it.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks:function(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),p(t,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),C(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var t=document.body,e=document.documentElement,i=t.scrollTop||e.scrollTop,n=t.scrollLeft||e.scrollLeft;this._map._container.focus(),window.scrollTo(n,i)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(t){var e=this._panKeys={},i=this.keyCodes,n,o;for(n=0,o=i.left.length;n<o;n++)e[i.left[n]]=[-1*t,0];for(n=0,o=i.right.length;n<o;n++)e[i.right[n]]=[t,0];for(n=0,o=i.down.length;n<o;n++)e[i.down[n]]=[0,t];for(n=0,o=i.up.length;n<o;n++)e[i.up[n]]=[0,-1*t]},_setZoomDelta:function(t){var e=this._zoomKeys={},i=this.keyCodes,n,o;for(n=0,o=i.zoomIn.length;n<o;n++)e[i.zoomIn[n]]=t;for(n=0,o=i.zoomOut.length;n<o;n++)e[i.zoomOut[n]]=-t},_addHooks:function(){p(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){C(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(t){if(!(t.altKey||t.ctrlKey||t.metaKey)){var e=t.keyCode,i=this._map,n;if(e in this._panKeys){if(!i._panAnim||!i._panAnim._inProgress)if(n=this._panKeys[e],t.shiftKey&&(n=_(n).multiplyBy(3)),i.options.maxBounds&&(n=i._limitOffset(_(n),i.options.maxBounds)),i.options.worldCopyJump){var o=i.wrapLatLng(i.unproject(i.project(i.getCenter()).add(n)));i.panTo(o)}else i.panBy(n)}else if(e in this._zoomKeys)i.setZoom(i.getZoom()+(t.shiftKey?3:1)*this._zoomKeys[e]);else if(e===27&&i._popup&&i._popup.options.closeOnEscapeKey)i.closePopup();else return;yt(t)}}});x.addInitHook("addHandler","keyboard",ln),x.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var cn=it.extend({addHooks:function(){p(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){C(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(t){var e=Ii(t),i=this._map.options.wheelDebounceTime;this._delta+=e,this._lastMousePos=this._map.mouseEventToContainerPoint(t),this._startTime||(this._startTime=+new Date);var n=Math.max(i-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(T(this._performZoom,this),n),yt(t)},_performZoom:function(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap||0;t._stop();var n=this._delta/(this._map.options.wheelPxPerZoomLevel*4),o=4*Math.log(2/(1+Math.exp(-Math.abs(n))))/Math.LN2,s=i?Math.ceil(o/i)*i:o,r=t._limitZoom(e+(this._delta>0?s:-s))-e;this._delta=0,this._startTime=null,r&&(t.options.scrollWheelZoom==="center"?t.setZoom(e+r):t.setZoomAround(this._lastMousePos,e+r))}});x.addInitHook("addHandler","scrollWheelZoom",cn);var Do=600;x.mergeOptions({tapHold:f.touchNative&&f.safari&&f.mobile,tapTolerance:15});var fn=it.extend({addHooks:function(){p(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){C(this._map._container,"touchstart",this._onDown,this)},_onDown:function(t){if(clearTimeout(this._holdTimeout),t.touches.length===1){var e=t.touches[0];this._startPos=this._newPos=new m(e.clientX,e.clientY),this._holdTimeout=setTimeout(T(function(){this._cancel(),this._isTapValid()&&(p(document,"touchend",N),p(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",e))},this),Do),p(document,"touchend touchcancel contextmenu",this._cancel,this),p(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function t(){C(document,"touchend",N),C(document,"touchend touchcancel",t)},_cancel:function(){clearTimeout(this._holdTimeout),C(document,"touchend touchcancel contextmenu",this._cancel,this),C(document,"touchmove",this._onMove,this)},_onMove:function(t){var e=t.touches[0];this._newPos=new m(e.clientX,e.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(t,e){var i=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY});i._simulated=!0,e.target.dispatchEvent(i)}});x.addInitHook("addHandler","tapHold",fn),x.mergeOptions({touchZoom:f.touch,bounceAtZoomLimits:!0});var dn=it.extend({addHooks:function(){v(this._map._container,"leaflet-touch-zoom"),p(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){E(this._map._container,"leaflet-touch-zoom"),C(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(t){var e=this._map;if(!(!t.touches||t.touches.length!==2||e._animatingZoom||this._zooming)){var i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]);this._centerPoint=e.getSize()._divideBy(2),this._startLatLng=e.containerPointToLatLng(this._centerPoint),e.options.touchZoom!=="center"&&(this._pinchStartLatLng=e.containerPointToLatLng(i.add(n)._divideBy(2))),this._startDist=i.distanceTo(n),this._startZoom=e.getZoom(),this._moved=!1,this._zooming=!0,e._stop(),p(document,"touchmove",this._onTouchMove,this),p(document,"touchend touchcancel",this._onTouchEnd,this),N(t)}},_onTouchMove:function(t){if(!(!t.touches||t.touches.length!==2||!this._zooming)){var e=this._map,i=e.mouseEventToContainerPoint(t.touches[0]),n=e.mouseEventToContainerPoint(t.touches[1]),o=i.distanceTo(n)/this._startDist;if(this._zoom=e.getScaleZoom(o,this._startZoom),!e.options.bounceAtZoomLimits&&(this._zoom<e.getMinZoom()&&o<1||this._zoom>e.getMaxZoom()&&o>1)&&(this._zoom=e._limitZoom(this._zoom)),e.options.touchZoom==="center"){if(this._center=this._startLatLng,o===1)return}else{var s=i._add(n)._divideBy(2)._subtract(this._centerPoint);if(o===1&&s.x===0&&s.y===0)return;this._center=e.unproject(e.project(this._pinchStartLatLng,this._zoom).subtract(s),this._zoom)}this._moved||(e._moveStart(!0,!1),this._moved=!0),q(this._animRequest);var r=T(e._move,e,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=R(r,this,!0),N(t)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,q(this._animRequest),C(document,"touchmove",this._onTouchMove,this),C(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});x.addInitHook("addHandler","touchZoom",dn),x.BoxZoom=an,x.DoubleClickZoom=hn,x.Drag=un,x.Keyboard=ln,x.ScrollWheelZoom=cn,x.TapHold=fn,x.TouchZoom=dn,l.Bounds=z,l.Browser=f,l.CRS=st,l.Canvas=nn,l.Circle=Ke,l.CircleMarker=se,l.Class=ot,l.Control=J,l.DivIcon=Qi,l.DivOverlay=nt,l.DomEvent=eo,l.DomUtil=Qn,l.Draggable=ft,l.Evented=Ot,l.FeatureGroup=rt,l.GeoJSON=ht,l.GridLayer=Gt,l.Handler=it,l.Icon=Mt,l.ImageOverlay=le,l.LatLng=M,l.LatLngBounds=W,l.Layer=$,l.LayerGroup=bt,l.LineUtil=mo,l.Map=x,l.Marker=oe,l.Mixin=ho,l.Path=dt,l.Point=m,l.PolyUtil=uo,l.Polygon=Ct,l.Polyline=at,l.Popup=ce,l.PosAnimation=Bi,l.Projection=po,l.Rectangle=rn,l.Renderer=ut,l.SVG=jt,l.SVGOverlay=$i,l.TileLayer=zt,l.Tooltip=fe,l.Transformation=we,l.Util=xn,l.VideoOverlay=Ji,l.bind=T,l.bounds=F,l.canvas=on,l.circle=To,l.circleMarker=Lo,l.control=Ht,l.divIcon=Oo,l.extend=b,l.featureGroup=wo,l.geoJSON=Xi,l.geoJson=Co,l.gridLayer=Io,l.icon=xo,l.imageOverlay=So,l.latLng=w,l.latLngBounds=O,l.layerGroup=yo,l.map=io,l.marker=Po,l.point=_,l.polygon=Mo,l.polyline=bo,l.popup=Zo,l.rectangle=No,l.setOptions=S,l.stamp=g,l.svg=sn,l.svgOverlay=ko,l.tileLayer=tn,l.tooltip=Eo,l.transformation=It,l.version=Zt,l.videoOverlay=zo;var Ro=window.L;l.noConflict=function(){return window.L=Ro,this},window.L=l})})(Qe,Qe.exports);var ss=Qe.exports;const _t=Ko(ss),rs={class:"layer-preview-container"},as={class:"layer-control-panel"},hs={class:"layer-options"},us=["onClick"],ls=Xo({__name:"LayerPreviewView",setup(yn){let G=null,l=null;const Zt=[{id:"online",name:"在线地图"},{id:"offline-image",name:"离线影像"},{id:"offline-map",name:"离线地图"}],b=Jo("online"),xt=T=>{if(G){switch(l&&G.removeLayer(l),T){case"online":l=_t.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"© OpenStreetMap contributors",maxZoom:19,tileSize:256,zoomOffset:0,subdomains:"abc",updateWhenIdle:!0,updateWhenZooming:!1,keepBuffer:2});break;case"offline-image":l=_t.tileLayer("/tiles/offline-image/{z}/{x}/{y}.png",{attribution:"离线影像",maxZoom:19,tileSize:256,zoomOffset:0,updateWhenIdle:!0,updateWhenZooming:!1,keepBuffer:2,crossOrigin:!0,errorTileUrl:"/tiles/error.png"});break;case"offline-map":l=_t.tileLayer("/tiles/offline-map/{z}/{x}/{y}.png",{attribution:"离线地图",maxZoom:19,tileSize:256,zoomOffset:0,updateWhenIdle:!0,updateWhenZooming:!1,keepBuffer:2,crossOrigin:!0,errorTileUrl:"/tiles/error.png"});break}l&&(l.addTo(G),b.value=T)}};return $o(()=>{G=_t.map("map",{zoomControl:!0,attributionControl:!0,minZoom:1,maxZoom:19,zoomSnap:1,zoomDelta:1,wheelDebounceTime:40,wheelPxPerZoomLevel:60,preferCanvas:!0,renderer:_t.canvas({padding:.5,tolerance:0})}).setView([31.2989,120.5853],13),l=_t.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"© OpenStreetMap contributors",maxZoom:19,tileSize:256,zoomOffset:0,subdomains:"abc",updateWhenIdle:!0,updateWhenZooming:!1,keepBuffer:2}).addTo(G);const T=_t.control({position:"bottomright"});T.onAdd=function(){const U=_t.DomUtil.create("div","info");return U.innerHTML="经纬度: 未选择",U},T.addTo(G),G.on("mousemove",U=>{const g=U.latlng.lat.toFixed(6),Et=U.latlng.lng.toFixed(6),lt=document.querySelector(".info");lt&&(lt.innerHTML=`经纬度: ${g}, ${Et}`)})}),Qo(()=>{G&&(G.remove(),G=null)}),(T,U)=>(gn(),vn("div",rs,[U[1]||(U[1]=Kt("div",{id:"map",class:"map-container"},null,-1)),Kt("div",as,[U[0]||(U[0]=Kt("div",{class:"layer-title"},"图层选择",-1)),Kt("div",hs,[(gn(),vn(ts,null,es(Zt,g=>Kt("div",{key:g.id,class:is(["layer-option",{active:b.value===g.id}]),onClick:Et=>xt(g.id)},ns(g.name),11,us)),64))])])]))}}),fs=os(ls,[["__scopeId","data-v-f456bfb8"]]);export{fs as default};
