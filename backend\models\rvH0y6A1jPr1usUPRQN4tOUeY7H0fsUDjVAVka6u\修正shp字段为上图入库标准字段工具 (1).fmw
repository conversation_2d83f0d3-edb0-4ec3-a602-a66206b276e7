#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" "C:\Users\<USER>\Desktop\修正shp字段为上图入库标准字段工具 (3).fmw"
#          --待更改shp压缩包 ""
#          --项目名称 ""
#          --保存路径 "D:\NewBeeGisIntegrationV1.2.2\Fmw\8b067cb8-7345-4b2d-98a8-4e397fbe9291\OutFile\431cbf41-6f2a-4beb-be82-c8617ac6ab9c\上图入库格式转换"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="9395.71 571.005"
#!   DOC_TOP_LEFT="-2606.28 -783.507"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="fe8aa1e7-679a-40ed-86e4-49e41898f702"
#!   FME_DOCUMENT_PRIORGUID="a5ae5651-79b7-4d06-b3cb-1424b7a0f273,8f2afec3-50c5-4ede-bd31-db5501828b37,35cbafbe-6280-4f81-bdb4-37153d39ecde,1e9dbc04-4d64-4131-8485-d6f802b25d0d,e99fd53d-6421-4dee-ac5c-154d903d8ade,9589fe5c-622a-45b0-ac4f-40599e6ce8dd,d725a11c-1553-4568-8a85-937f9fdaa38a,a80e0c8a-bad0-41f1-8928-840703a4b0cb,ef8ff058-055d-45e1-b1e5-4a6c01d7d3f8,f2627929-4bab-41bf-b91b-36ebdb2bc40a,cd603995-799f-4391-a434-bb98b9cb1ad0,d7b45d3a-e9cb-4f13-b84e-039b07eb4dce"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-02-06T10:57:57"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="2450.02 940.634"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/上图入库格式转换"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_SRC_OR_ATTR 待更改shp压缩包 待更改shp压缩包"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL STRING_OR_ATTR 项目名称 项目名称"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR 保存路径 保存路径"
#!   DEFAULT_VALUE="D:\NewBeeGisIntegrationV1.2.2\Fmw\8b067cb8-7345-4b2d-98a8-4e397fbe9291\OutFile\431cbf41-6f2a-4beb-be82-c8617ac6ab9c\上图入库格式转换"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJhY2Nlc3NNb2RlIjoicmVhZCIsImFsbG93VVJMIjpmYWxzZSwiZGVmYXVsdFZhbHVlIjoiIiwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoi5b6F5pu05pS5c2hw5Y6L57yp5YyFIiwicHJvbXB0Ijoi5b6F5pu05pS5c2hw5Y6L57yp5YyFIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjpmYWxzZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOnRydWUsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiZGVmYXVsdFZhbHVlIjoiIiwibmFtZSI6IumhueebruWQjeensCIsInByb21wdCI6IumhueebruWQjeensCIsInJlcXVpcmVkIjpmYWxzZSwic2hvd0VkaXRCdXR0b24iOmZhbHNlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6InRleHQiLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifSx7ImFjY2Vzc01vZGUiOiJ3cml0ZSIsImFsbG93VVJMIjpmYWxzZSwiZGVmYXVsdFZhbHVlIjoiRDpcXE5ld0JlZUdpc0ludGVncmF0aW9uVjEuMi4yXFxGbXdcXDhiMDY3Y2I4LTczNDUtNGIyZC05OGE4LTRlMzk3ZmJlOTI5MVxcT3V0RmlsZVxcNDMxY2JmNDEtNmYyYS00YmViLWJlODItYzg2MTdhYzZhYjljXFzkuIrlm77lhaXlupPmoLzlvI/ovazmjaIiLCJpdGVtc1RvU2VsZWN0IjoiZm9sZGVycyIsIm5hbWUiOiLkv53lrZjot6/lvoQiLCJwcm9tcHQiOiLkv53lrZjot6/lvoQiLCJyZXF1aXJlZCI6dHJ1ZSwic2VsZWN0TXVsdGlwbGUiOmZhbHNlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6dHJ1ZSwidmFsdWVUeXBlIjoic3RyaW5nIn1dfQ=="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="待更改shp压缩包" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_SRC_OR_ATTR 待更改shp压缩包 待更改shp压缩包"
#! />
#!     <INFO NAME="项目名称" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL STRING_OR_ATTR 项目名称 项目名称"
#! />
#!     <INFO NAME="保存路径" 
#!   DEFAULT_VALUE="D:\NewBeeGisIntegrationV1.2.2\Fmw\8b067cb8-7345-4b2d-98a8-4e397fbe9291\OutFile\431cbf41-6f2a-4beb-be82-c8617ac6ab9c\上图入库格式转换"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR 保存路径 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-2606.2760627606285 -518.75518755187545"
#!   BOUNDING_RECT="-2606.2760627606285 -518.75518755187545 430 71"
#!   ORDER="500000000000078.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-737.50737507375072 -596.88096880968828"
#!   BOUNDING_RECT="-737.50737507375072 -596.88096880968828 430 71"
#!   ORDER="500000000000079.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="2553.1505315053146 -678.13178131781308"
#!   BOUNDING_RECT="2553.1505315053146 -678.13178131781308 454 71"
#!   ORDER="500000000000080.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="地块编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块面积" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图幅号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块用途" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地类编码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u5730&gt;&lt;u5757&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u5730&gt;&lt;u5757&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO  varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u5730&gt;&lt;u5757&gt;&lt;u9762&gt;&lt;u79ef&gt; SET_TO  varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u56fe&gt;&lt;u5e45&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u5730&gt;&lt;u5757&gt;&lt;u7528&gt;&lt;u9014&gt; SET_TO  varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u5730&gt;&lt;u7c7b&gt;&lt;u7f16&gt;&lt;u7801&gt; SET_TO  varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u9879&gt;&lt;u76ee&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO  varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u9879&gt;&lt;u76ee&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO $(项目名称) varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="3196.9069690696888 -678.13178131781308"
#!   BOUNDING_RECT="3196.9069690696888 -678.13178131781308 454 71"
#!   ORDER="500000000000081.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="地块编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块面积" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图幅号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块用途" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地类编码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="Shape_Area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="LengthCalculator"
#!   VERSION="2"
#!   POSITION="5156.3015630156297 -212.50212502125009"
#!   BOUNDING_RECT="5156.3015630156297 -212.50212502125009 454 71"
#!   ORDER="500000000000082.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="地块编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块面积" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图幅号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块用途" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地类编码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DIM" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="LEN_ATTR" PARM_VALUE="Shape_Leng"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="LengthCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="11"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="6359.4385943859425 -680.63240632406291"
#!   BOUNDING_RECT="6359.4385943859425 -680.63240632406291 430 71"
#!   ORDER="500000000000083.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(保存路径)"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;gbk&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;space&gt;Type&lt;space&gt;Storage,SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Spatial&lt;space&gt;Index:,SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;Output&lt;space&gt;Dimension,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Elevation&lt;space&gt;as&lt;space&gt;Measures,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;Surface&lt;space&gt;and&lt;space&gt;Solid&lt;space&gt;Storage,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Compressed&lt;space&gt;Shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Split&lt;space&gt;Dataset&lt;space&gt;into&lt;space&gt;2GB&lt;space&gt;Files"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="gbk"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;:Output,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;comma&gt;double&lt;comma&gt;&lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5e45&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;2&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9014&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;15&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u7801&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;4&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;80&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;Shape_Area&lt;comma&gt;double&lt;comma&gt;Shape_Leng&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO;&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;_&lt;u8fc7&gt;&lt;u6ee4&gt;&lt;u56fe&gt;&lt;u6591&gt;:Failed,ftp_feature_type_name_exp,&lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt;_&lt;u8fc7&gt;&lt;u6ee4&gt;&lt;u56fe&gt;&lt;u6591&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u56fe&lt;gt&gt;&lt;lt&gt;u5e45&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5757&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9014&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u7801&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_unix&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_windo&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_rootn&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_filen&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_exten&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_direc&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_dir00&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_type&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_geomet&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;Q__overlap&lt;comma&gt;double&lt;comma&gt;Shape_Area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="16"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-1418.7641876418766 -600.00600006000082"
#!   BOUNDING_RECT="-1418.7641876418766 -600.00600006000082 454 71"
#!   ORDER="500000000000085.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="19"
#!   TYPE="AreaOnAreaOverlayer"
#!   VERSION="9"
#!   POSITION="1256.2625626256256 -586.88146881468765"
#!   BOUNDING_RECT="1256.2625626256256 -586.88146881468765 506.00106825772946 71"
#!   ORDER="500000000000086.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AREA"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ACCUM_ATTRS_NAME" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="First Wins"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="path_windows"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVC_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaOnAreaOverlayer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="22"
#!   TYPE="Dissolver"
#!   VERSION="17"
#!   POSITION="1987.5198751987525 -646.88146881468765"
#!   BOUNDING_RECT="1987.5198751987525 -646.88146881468765 454 71"
#!   ORDER="500000000000087.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AREA"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_remnant_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ACCUM_INPUT_ATTRS" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="AV" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="First Wins"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="DIS_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="path_windows"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUM" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WEIGHT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Dissolver"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="21"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-2000.020000200002 -446.87946879468814"
#!   BOUNDING_RECT="-2000.020000200002 -446.87946879468814 456.00106825772946 71"
#!   ORDER="500000000000088.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(待更改shp压缩包)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;&lt;u5141&gt;&lt;u8bb8&gt;&lt;u8def&gt;&lt;u5f84&gt;&lt;u7c7b&gt;&lt;u578b&gt;:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;&lt;u8def&gt;&lt;u5f84&gt;&lt;u8fc7&gt;&lt;u6ee4&gt;:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;&lt;u9012&gt;&lt;u5f52&gt;&lt;u5230&gt;&lt;u5b50&gt;&lt;u6587&gt;&lt;u4ef6&gt;&lt;u5939&gt;&lt;uff1a&gt;,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;&lt;u63d0&gt;&lt;u53d6&gt;&lt;space&gt;&lt;u6587&gt;&lt;u4ef6&gt;&lt;space&gt;&lt;u5c5e&gt;&lt;u6027&gt;:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="26"
#!   TYPE="GeometryFilter"
#!   VERSION="8"
#!   POSITION="18.750187501875359 -646.88146881468811"
#!   BOUNDING_RECT="18.750187501875359 -646.88146881468811 430 71"
#!   ORDER="500000000000089.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Area"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;UNFILTERED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILTER_MODE" PARM_VALUE="SIMPLE"/>
#!     <XFORM_PARM PARM_NAME="FILTER_MULTI" PARM_VALUE="FILTER_TYPES]Area]FME_CONTROLLER_QUERY_FILE]]FME_CONTROLLER_CHOICE]Simple"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryFilter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="29"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="643.75643756437557 -646.88146881468811"
#!   BOUNDING_RECT="643.75643756437557 -646.88146881468811 430 71"
#!   ORDER="500000000000091.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="28"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="4150.0415004150045 -712.50712507125058"
#!   BOUNDING_RECT="4150.0415004150045 -712.50712507125058 430 71"
#!   ORDER="500000000000092.25"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="地块编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块面积" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图幅号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地块用途" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="地类编码" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="地块编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="地块名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="地块面积" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="图幅号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="地块用途" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="地类编码" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="项目名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;Shape_Area&lt;closeparen&gt; &gt; 10"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="14"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="21"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="8"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="32"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="28"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="12"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="11"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="18"
#!   SOURCE_NODE="16"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="23"
#!   SOURCE_NODE="19"
#!   TARGET_NODE="22"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="24"
#!   SOURCE_NODE="22"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="30"
#!   SOURCE_NODE="26"
#!   TARGET_NODE="29"
#!   SOURCE_PORT_DESC="fo 0 Area"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="33"
#!   SOURCE_NODE="28"
#!   TARGET_NODE="9"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="31"
#!   SOURCE_NODE="29"
#!   TARGET_NODE="19"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 AREA"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="27"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="26"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="21"
#!   TARGET_NODE="16"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="34"
#!   SOURCE_NODE="28"
#!   TARGET_NODE="11"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 1 Failed"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO 待更改shp压缩包 

DEFAULT_MACRO 项目名称 

DEFAULT_MACRO 保存路径 D:\NewBeeGisIntegrationV1.2.2\Fmw\8b067cb8-7345-4b2d-98a8-4e397fbe9291\OutFile\431cbf41-6f2a-4beb-be82-c8617ac6ab9c\上图入库格式转换

# ============================================================================
INCLUDE [ if {{$(待更改shp压缩包$encode)} == {}} { puts_real {Parameter '待更改shp压缩包' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(保存路径$encode)} == {}} { puts_real {Parameter '保存路径' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)修正shp字段为上图入库标准字段工具 (3).log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 修正shp字段为上图入库标准字段工具 (3)
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 14"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_3_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,$(待更改shp压缩包$encode),FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_3_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_3_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_3_<Rejected> INPUT FEATURE_TYPE FeatureReader_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_3_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Tester_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} GeometryFilterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { GeometryFilter }    INPUT  FEATURE_TYPE FeatureReader_<OTHER>    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    FILTER_TYPES { Area }    INSTANCES { INSTANTIATE }    BREAK_AGG { Yes }    OUTPUT { <UNFILTERED> FEATURE_TYPE GeometryFilter_<UNFILTERED>  }    OUTPUT { Area FEATURE_TYPE GeometryFilter_Area  }
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter <UNFILTERED> Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_<UNFILTERED>
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE GeometryFilter_Area    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { AreaOnAreaOverlayer }    INPUT POLYGON FEATURE_TYPE Reprojector_REPROJECTED    GROUP_BY { path_windows }    FLUSH_WHEN_GROUPS_CHANGE { No }    ACCUMULATE_ATTRIBUTES { ONE }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    CONNECT_Z_MODE { FIRST_WINS }    DEAGGREGATE_INPUT { Yes }    OVERLAP_COUNT_ATTRIBUTE { _overlaps }    MODE COMPLETE    OVERLAY_TYPE AREA_ON_AREA    CLEANING_TOLERANCE { AUTO }    OUTPUT_REMNANTS    SELF_INTERSECTION NO    OUTPUT { POLYGON  FEATURE_TYPE AreaOnAreaOverlayer_AREA         }    OUTPUT { ILLEGAL_GEOM FEATURE_TYPE AreaOnAreaOverlayer_<REJECTED>         }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME AreaOnAreaOverlayer_<Rejected> INPUT FEATURE_TYPE AreaOnAreaOverlayer_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, AreaOnAreaOverlayer<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} PolygonDissolveFactory    FACTORY_NAME { Dissolver }    INPUT  FEATURE_TYPE AreaOnAreaOverlayer_AREA    GROUP_BY { path_windows }    FLUSH_WHEN_GROUPS_CHANGE { No }    DEAGGREGATE_INPUT { Yes }    MODE COMPLETE    CONNECT_Z_MODE { FIRST_WINS }    CLEANING_TOLERANCE { AUTO }    ACCUM_INPUT_ATTRS { One }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    OUTPUT_REMNANTS    OUTPUT { POLYGON FEATURE_TYPE Dissolver_AREA         }    OUTPUT { REJECTED FEATURE_TYPE Dissolver_<REJECTED>         }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Dissolver_<Rejected> INPUT FEATURE_TYPE Dissolver_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Dissolver<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Dissolver_AREA    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u5730><u5757><u7f16><u53f7>" "SET_TO" "" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u5730><u5757><u540d><u79f0>" "SET_TO" "" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u5730><u5757><u9762><u79ef>" "SET_TO" "" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u56fe><u5e45><u53f7>" "SET_TO" "" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u5730><u5757><u7528><u9014>" "SET_TO" "" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u5730><u7c7b><u7f16><u7801>" "SET_TO" "" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u9879><u76ee><u7f16><u53f7>" "SET_TO" "" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u9879><u76ee><u540d><u79f0>" "SET_TO" "$(项目名称$encode)" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, Shape_Area, $(AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE AreaCalculator_OUTPUT    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>Shape_Area<closeparen>,Tester_2)" > 10 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }    OUTPUT { FAILED FEATURE_TYPE Tester_2_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { LengthCalculator_LengthCalculatorInput }    INPUT  FEATURE_TYPE Tester_2_PASSED    OUTPUT { FEATURE_TYPE ___TOLENGTHCALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { LengthCalculator_LengthCalculator }    INPUT FEATURE_TYPE ___TOLENGTHCALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, Shape_Leng, @Length(REJECTABLE,2,"1")) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { LengthCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE LengthCalculator_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(保存路径$encode),FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>gbk<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma><comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<at>Value<openparen>fme_feature_type<closeparen>:Output,ftp_feature_type_name_exp,<at>Value<openparen>fme_feature_type<closeparen>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u5730<gt><lt>u5757<gt><lt>u7f16<gt><lt>u53f7<gt><comma>varchar<lt>openparen<gt>10<lt>closeparen<gt><comma><lt>u5730<gt><lt>u5757<gt><lt>u540d<gt><lt>u79f0<gt><comma>varchar<lt>openparen<gt>10<lt>closeparen<gt><comma><lt>u5730<gt><lt>u5757<gt><lt>u9762<gt><lt>u79ef<gt><comma>double<comma><lt>u56fe<gt><lt>u5e45<gt><lt>u53f7<gt><comma>varchar<lt>openparen<gt>2<lt>closeparen<gt><comma><lt>u5730<gt><lt>u5757<gt><lt>u7528<gt><lt>u9014<gt><comma>varchar<lt>openparen<gt>15<lt>closeparen<gt><comma><lt>u5730<gt><lt>u7c7b<gt><lt>u7f16<gt><lt>u7801<gt><comma>varchar<lt>openparen<gt>4<lt>closeparen<gt><comma><lt>u9879<gt><lt>u76ee<gt><lt>u7f16<gt><lt>u53f7<gt><comma>varchar<lt>openparen<gt>18<lt>closeparen<gt><comma><lt>u9879<gt><lt>u76ee<gt><lt>u540d<gt><lt>u79f0<gt><comma>varchar<lt>openparen<gt>80<lt>closeparen<gt><comma>Shape_Area<comma>double<comma>Shape_Leng<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO;<at>Value<openparen>fme_feature_type<closeparen>_<u8fc7><u6ee4><u56fe><u6591>:Failed,ftp_feature_type_name_exp,<at>Value<openparen>fme_feature_type<closeparen>_<u8fc7><u6ee4><u56fe><u6591>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,<lt>u5730<gt><lt>u5757<gt><lt>u7f16<gt><lt>u53f7<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma><lt>u5730<gt><lt>u5757<gt><lt>u540d<gt><lt>u79f0<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma><lt>u5730<gt><lt>u5757<gt><lt>u9762<gt><lt>u79ef<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma><lt>u56fe<gt><lt>u5e45<gt><lt>u53f7<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma><lt>u5730<gt><lt>u5757<gt><lt>u7528<gt><lt>u9014<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma><lt>u5730<gt><lt>u7c7b<gt><lt>u7f16<gt><lt>u7801<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma><lt>u9879<gt><lt>u76ee<gt><lt>u7f16<gt><lt>u53f7<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma><lt>u9879<gt><lt>u76ee<gt><lt>u540d<gt><lt>u79f0<gt><comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma>path_unix<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>path_windo<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>path_rootn<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>path_filen<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>path_exten<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>path_direc<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>path_dir00<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>path_type<comma>varchar<lt>openparen<gt>10<lt>closeparen<gt><comma>fme_geomet<comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma>Q__overlap<comma>double<comma>Shape_Area<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,gbk,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE LengthCalculator_OUTPUT  @SupplyAttributes(ENCODED,fme_template_feature_type,Output)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_feature_type<closeparen>,FeatureWriter))    INPUT Failed FEATURE_TYPE Tester_2_FAILED  @SupplyAttributes(ENCODED,fme_template_feature_type,Failed)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_feature_type<closeparen>_<u8fc7><u6ee4><u56fe><u6591>,FeatureWriter)) @CopyAttributes(ENCODED,path_windo,path_windows,path_rootn,path_rootname,path_filen,path_filename,path_exten,path_extension,path_direc,path_directory_unix,path_dir00,path_directory_windows,fme_geomet,fme_geometry<opencurly>0<closecurly>,Q__overlap,_overlaps)
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

