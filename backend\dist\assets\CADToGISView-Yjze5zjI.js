import{d as qa,u as Ha,r as i,M as j,O as pe,o as Pe,a as lt,i as C,E as u,ac as Wa,c as N,e as l,w as s,f as m,Y as st,P as I,Z as q,b as n,J as Xa,n as De,A as d,m as b,D as ot,C as k,a7 as Ya,B as Za,F as v,ab as nt,a4 as Ue,a0 as Ja,s as ce,ad as Qa,a1 as rt,Q as Ka,R as Mt,H as ut,I as it,T as Fe,l as g,a6 as Q,j as el,_ as tl}from"./index-B7WNRWO3.js";import{f as al}from"./format-CBpsKyOP.js";const ll={class:"tools-container"},sl={key:"convert"},ol={class:"steps-progress"},nl={class:"start-container"},rl={class:"start-content"},ul={class:"step-content"},il={style:{"margin-bottom":"20px","margin-top":"20px","padding-left":"50px","text-align":"left",display:"flex",gap:"12px","align-items":"center"}},dl={style:{display:"none"}},pl={class:"step-footer"},cl={class:"step-content"},fl={class:"upload-container"},ml={class:"upload-sections"},vl={class:"upload-section"},_l={class:"section-title"},gl={class:"el-upload-list__item custom-upload-item"},yl={class:"el-upload-list__item-name"},wl={class:"custom-status-label"},hl={class:"upload-section"},bl={class:"section-title"},Cl={class:"el-upload-list__item custom-upload-item"},xl={class:"el-upload-list__item-name",style:{"text-align":"left",display:"block"}},kl={class:"custom-status-label"},Tl={class:"file-list-table"},Dl={class:"table-header",style:{"justify-content":"flex-start","flex-direction":"row","align-items":"center"}},Ul={class:"license-tags",style:{display:"flex","align-items":"center","margin-top":"0px"}},Vl={class:"step-footer"},Ll={class:"step-content"},Al={class:"output-settings-container"},Sl={class:"info-confirmation"},El={class:"section-card"},$l={class:"section-card"},Rl={class:"output-types"},zl={class:"section-card"},Ol={class:"geometry-types"},Il={class:"button-group"},Ml={class:"tip-text"},Pl={class:"step-footer"},Fl={key:"history"},jl={class:"table-container"},Gl={style:{display:"flex","justify-content":"center"}},Bl={class:"pagination-container"},Nl={key:0,class:"message-content"},ql={key:6,class:"color-picker-wrapper"},Hl={class:"color-value"},Wl={key:1,class:"no-params"},Xl={class:"dialog-footer"},Yl={class:"dialog-footer"},Zl={class:"dialog-footer"},Jl={style:{display:"flex","justify-content":"center"}},Ql={class:"pagination-container"},Kl={class:"error-log-content"},es={class:"dialog-footer"},ts={class:"error-message"},as={class:"error-message"},ls={class:"error-message"},ss={class:"error-message"},os={class:"error-message"},ns={class:"dialog-footer"},rs=5e3,us=qa({__name:"CADToGISView",setup(is){const h=Ha(),Pt=i(!1),K=i([]),ee=i([]),dt=i(""),pt=i(""),te=i("convert"),fe=i(1),Ft=i(1),jt=i(1),ct=i(10),ft=i(10),Ve=i(10),me=i(0);i(0);const S=i(localStorage.getItem("token")),U={GET_TOOLS_LIST:"/api/cad2gis/tools/list",GET_TOOLS_COUNT:"/api/cad2gis/tools/count",UPLOAD_TOOL:"/api/cad2gis/tools/upload",DELETE_TOOL:"/api/cad2gis/tools/delete",APPLY_TOOL:"/api/cad2gis/tools/apply",APPROVE_TOOL:"/api/cad2gis/tools/approve",REJECT_TOOL:"/api/cad2gis/tools/reject",GET_MY_APPLICATIONS:"/api/tools/my-applications",GET_MY_APPROVALS:"/api/cad2gis/tools/my-approvals",RUN_TOOL:"/api/cad2gis/tools/run",GET_RUN_RECORDS:"/api/cad2gis/tools/run_records",UPLOAD_FILE:"/api/upload",DOWNLOAD_RESULT:"/api/tools/download_result",PARSE_TOOL:"/api/cad2gis/tools/parse",DELETE_RESULT:"/api/cad2gis/tools/delete_result",DELETE_APPLICATION:"/api/tools/delete-application",WITHDRAW_APPLICATION:"/api/tools/withdraw-application",RUN_FME:"/api/cad2gis/run_fme",UPDATE_RUN_TIMES:"/api/cad2gis/tools/update-run-times",TOOL_DETAIL:"/api/tools/detail",UPDATE_USAGE_COUNT:"/api/cad2gis/tools/update_usacount",DELETE_FILE:"/api/cad2gis/delete"},Gt=t=>window.location.pathname.startsWith("/gsi/")?`/gsi${t}`:`${C.defaults.baseURL}${t}`,Bt=t=>window.location.pathname.startsWith("/gsi/")?`${window.location.protocol}//${window.location.host}/gsi${t}`:`${window.location.protocol}//${window.location.host}${t}`,Nt=t=>{t.name==="history"&&ie()},qt=j(()=>K.value.filter(t=>t.fmw_name.toLowerCase().includes(dt.value.toLowerCase())||(t.user_project||"").toLowerCase().includes(dt.value.toLowerCase()))),Ht=j(()=>ee.value.filter(t=>t.fmw_name.toLowerCase().includes(pt.value.toLowerCase())||t.project.toLowerCase().includes(pt.value.toLowerCase())));j(()=>{const t=(jt.value-1)*ct.value,e=t+ct.value;return qt.value.slice(t,e)}),j(()=>{const t=(Ft.value-1)*ft.value,e=t+ft.value;return Ht.value.slice(t,e)});const Le=async()=>{var t;try{if(!((t=h.user)!=null&&t.username)){K.value=[];return}const e=await C.post(U.GET_TOOLS_LIST,{username:h.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});e.data.success?K.value=e.data.data.map(o=>({...o,user_project:o.user_project||o.project||"未指定项目",created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取工具列表失败"),K.value=[])}catch(e){console.error("获取工具列表失败:",e),K.value=[]}},Wt=async()=>{var t;try{if(!((t=h.user)!=null&&t.username)){ee.value=[];return}const e=await C.get("/api/tools/my-applications",{params:{source:"cad2gisView",fmw_id:"cad2gis"},headers:{"X-Username":h.user.username}});e.data.success?ee.value=e.data.data.filter(o=>o.status==="已通过").map(o=>({...o,count:parseInt(o.count)||0,usage_count:parseInt(o.usage_count)||0,remaining_count:(parseInt(o.usage_count)||0)-(parseInt(o.count)||0),user_project:o.user_project||"未指定项目",end_date:o.end_date||null,created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取申请列表失败"),ee.value=[])}catch{ee.value=[]}},Ae=(t,e="yyyy-MM-dd HH:mm")=>{if(!t)return"--";try{return al(new Date(t),e)}catch{return"--"}};pe(()=>h.user,t=>{t!=null&&t.username?Le():(K.value=[],ee.value=[])},{immediate:!0});const ve=i(!1),$=i(null),ae=i([]),_=i({}),E=i(null),je=i({}),Xt=i(!1);i(!1);const Ge=i(!1),le=i([]);pe(Ge,t=>{t||(fe.value=1)});const Yt=t=>({running:"warning",success:"success",failed:"danger"})[t]||"info",Zt=t=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[t]||t,mt=t=>{if(!t)return"-";const e=Math.floor(t/60),o=t%60;return`${e}分${o}秒`},Jt=async t=>{try{await Fe.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=le.value.findIndex(o=>o.task_id===t.task_id);e!==-1&&(le.value.splice(e,1),me.value--),C.post(U.DELETE_RESULT,{task_id:t.task_id},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}}).then(o=>{o.data.success?u.success("删除成功"):(le.value.splice(e,0,t),me.value++,u.error(o.data.message||"删除失败"))}).catch(o=>{le.value.splice(e,0,t),me.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&u.error("删除失败，请稍后重试")}},vt=async()=>{try{if(!$.value)return;const t=await C.post(U.GET_RUN_RECORDS,{fmw_id:$.value.fmw_id,username:h.user.username,page:fe.value,page_size:Ve.value},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});t.data.success?(le.value=t.data.data.records,me.value=t.data.data.pagination.total):u.error(t.data.message||"获取运行记录失败")}catch(t){console.error("获取运行记录失败:",t),u.error("获取运行记录失败，请检查网络连接")}},Qt=t=>{console.log("页码改变:",t),console.log("当前工具信息:",$.value),fe.value=t,vt()};Pe(()=>{Le(),Y.value=Xe(),$.value={fmw_id:"cad2gis",fmw_name:"CAD转GIS",fmw_path:"tools/cad2gis/cad2gis.fmw"}}),lt(()=>{});const _e=i(!1),ge=i(!1),L=i({fmw_name:"",project:"",description:"",file:null}),H=i({fmw_id:"",file:null}),Kt={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},W=i(null),X=i(null),ea=()=>{ve.value=!1},ta=()=>{var t;_.value={},ae.value=[],E.value&&E.value.resetFields(),(t=E.value)!=null&&t.$el&&E.value.$el.querySelectorAll(".el-upload").forEach(o=>{var p;const c=(p=o.__vueParentComponent)==null?void 0:p.ctx;c&&typeof c.clearFiles=="function"&&c.clearFiles()})},aa=()=>{_e.value=!1},la=()=>{G.value&&G.value.resetFields(),L.value={fmw_name:"",project:"",description:"",file:null},W.value&&typeof W.value.clearFiles=="function"&&W.value.clearFiles()},sa=()=>{ge.value=!1},oa=()=>{Se.value&&Se.value.resetFields(),H.value={fmw_id:"",file:null},X.value&&typeof X.value.clearFiles=="function"&&X.value.clearFiles()},G=i(),Se=i();pe(()=>_.value,t=>{const e={};ae.value.forEach(o=>{Be(o)&&je.value[o.prop]&&(e[o.prop]=je.value[o.prop])}),E.value&&(E.value.clearValidate(),E.value.rules=e)},{deep:!0});const se=i(!1),na=()=>{Fe.confirm("确定要提交任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{se.value=!0,Ca().finally(()=>{se.value=!1})})},ra=async()=>{var t;se.value=!0;try{if(!$.value){u.error("工具信息不完整"),se.value=!1;return}const e=ae.value.filter(r=>Be(r));console.log("可见的表单项:",e.map(r=>r.prop));for(const r of e)if(r.required&&!_.value[r.prop]){let y="";r.type==="file"||r.type==="upload"?y=`请上传${r.label}`:r.type==="select"||r.type==="dropdown"||r.type==="listbox"?y=`请选择${r.label}`:y=`请填写${r.label}`,u.error(y);return}const c={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:$.value.fmw_id,fmw_name:$.value.fmw_name,fmw_path:$.value.fmw_path,params:{}};for(const r of e){const y=_.value[r.prop];y!=null&&!r.prop.endsWith("_value")&&(r.type==="color"?c.params[r.prop]=_.value[`${r.prop}_value`]:c.params[r.prop]=y)}if(console.log("提交的请求数据:",c),Xt.value)try{const r=await C.post(U.UPDATE_COUNT,{id:$.value.id,username:h.user.username});if(!r.data.success){u.error(r.data.message||"更新使用次数失败");return}}catch(r){console.error("更新使用次数失败:",r),u.error("更新使用次数失败，请稍后重试");return}const p=await C.post(U.RUN_FME,c,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(p.data.success){try{const r=await C.post(U.UPDATE_RUN_TIMES,{},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});r.data.success||console.error("更新运行次数失败:",r.data.message)}catch(r){console.error("更新运行次数失败:",r)}u.success("任务提交成功"),_.value={},ae.value=[],E.value&&E.value.resetFields(),(t=E.value)!=null&&t.$el&&E.value.$el.querySelectorAll(".el-upload").forEach(y=>{var f;const V=(f=y.__vueParentComponent)==null?void 0:f.ctx;V&&typeof V.clearFiles=="function"&&V.clearFiles()}),ve.value=!1,await Wt(),te.value="history",await ie("cad2gis")}else u.error(p.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),u.error("提交失败，请稍后重试")}finally{se.value=!1}},ua=async()=>{if(G.value)try{if(await G.value.validate(),!L.value.file){u.error("请上传工具文件");return}const t=new FormData;t.append("file",L.value.file);const e=await C.post(`${U.UPLOAD_FILE}`,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const o={fmw_id,fmw_name:L.value.fmw_name,project:L.value.project,description:L.value.description,fmw_path:e.data.data.path,file_path:e.data.data.path,data:new Date().toISOString()},c=await C.post(`${U.UPLOAD_TOOL}`,o,{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});c.data.success?(u.success("工具上传成功"),_e.value=!1,L.value={fmw_name:"",project:"",description:"",file:null},G.value&&G.value.resetFields(),W.value&&typeof W.value.clearFiles=="function"&&W.value.clearFiles(),await Le()):u.error(c.data.message||"上传失败")}catch(t){console.error("上传工具失败:",t),u.error("参数未填写完整")}},ia=t=>t.name.toLowerCase().endsWith(".fmw")?t.size/1024/1024/1024<10?!0:(u.error("文件大小不能超过10GB"),!1):(u.error("只能上传FMW文件"),!1),da=t=>(H.value.file=t.raw,!1),pa=async()=>{if(!H.value.file){u.error("请选择更新文件");return}try{const t=new FormData;t.append("file",H.value.file);const e=await C.post(`${U.UPLOAD_FILE}`,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const o={fmw_id:H.value.fmw_id,file_path:e.data.data.path},c=await C.post(`${U.UPDATE_TOOL}`,o,{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});c.data.success?(u.success("工具更新成功"),ge.value=!1,H.value={fmw_id:"",file:null},Se.value&&Se.value.resetFields(),X.value&&typeof X.value.clearFiles=="function"&&X.value.clearFiles(),await Le()):u.error(c.data.message||"更新失败")}catch(t){console.error("更新工具失败:",t),u.error("更新失败，请检查网络连接")}},ca=(t,e)=>{if(!e){_.value[t]="rgb(255, 255, 255)";return}_.value[t]=e;const o=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(o){const[,c,p,r]=o;_.value[`${t}_value`]=`${c},${p},${r}`}else _.value[`${t}_value`]="255,255,255"},Be=t=>{var o;if(!((o=t.component)!=null&&o.visibility))return!0;const e=t.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const c of e.if){const{condition:p,then:r}=c;let y=!1;if(p.allOf)y=p.allOf.every(V=>{if(V.equals){const{parameter:f,value:F}=V.equals;return _.value[f]===F}else if(V.isEnabled){const{parameter:f}=V.isEnabled;return!!_.value[f]}return!1});else if(p.equals){const{parameter:V,value:f}=p.equals;y=_.value[V]===f}else if(p.isEnabled){const{parameter:V}=p.isEnabled;y=!!_.value[V]}if(y)return r==="visibleEnabled"||r==="visibleDisabled"}return!1},Ee=i(!1),_t=i(""),fa=t=>{Ve.value=t,vt()},oe=i(!1),ye=i(),Ne=i(!1),gt=i([]),T=i({tool_name:"CAD转GIS",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),ma={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},va=t=>t.getTime()<Date.now()-864e5,_a=()=>{oe.value=!0,ga()},ga=async()=>{try{const t=await C.get("/api/admin-users");t.data.success?gt.value=t.data.data:u.error("获取审批人失败")}catch{u.error("获取审批人失败")}},ya=async()=>{ye.value&&await ye.value.validate(async t=>{if(t){Ne.value=!0;try{const e=await C.post(U.APPLY_TOOL,{fmw_id:"cad2gis",fmw_name:"CAD转GIS",applicant:h.user.username,reason:T.value.reason,end_date:T.value.end_date,usage_count:T.value.usage_count,user_project:T.value.user_project,reviewer:T.value.approver},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});e.data.success?(u.success("申请提交成功"),oe.value=!1,yt(),await Re()):u.error(e.data.message||"申请提交失败")}catch{u.error("申请提交失败")}finally{Ne.value=!1}}})},yt=()=>{ye.value&&ye.value.resetFields(),Object.assign(T.value,{tool_name:"CAD转GIS",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})},$e=i([]),qe=i(!1),M=i(null),w=i(0),wt=i(0),wa=()=>{w.value>0&&w.value--},ha=()=>{if(w.value===0)w.value++;else if(w.value===1){if(!M.value){u.warning("请先选择一个可用的许可");return}w.value++}else if(w.value===2){if(D.value.length===0){u.warning("请先上传要转换的文件");return}w.value++}else if(w.value===3&&P.value.length===0){u.warning("请至少选择一种输出几何类型");return}};pe(w,t=>{wt.value=t});const R=i(!1),B=async t=>{if(!R.value){R.value=!0;try{t==="next"?await ha():t==="prev"&&await wa()}catch(e){console.error("步骤切换失败:",e),u.error("步骤切换失败，请重试")}finally{setTimeout(()=>{R.value=!1},250)}}},ht=t=>{t.key==="ArrowLeft"&&w.value>0?(t.preventDefault(),B("prev")):t.key==="ArrowRight"&&w.value<3&&(t.preventDefault(),B("next"))};Pe(()=>{Re(),document.addEventListener("keydown",ht)}),lt(()=>{document.removeEventListener("keydown",ht)});const Re=async()=>{qe.value=!0;try{const t=await C.get("/api/tools/my-applications",{params:{source:"cad2gisView",fmw_id:"cad2gis"},headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(t.data.success){$e.value=t.data.data;const e=$e.value.find(o=>bt(o));e&&(M.value=e.id)}else u.error(t.data.message||"获取许可列表失败")}catch(t){console.error("获取许可列表失败:",t),u.error("获取许可列表失败")}finally{qe.value=!1}};function we(t){return{审批中:{type:"info",text:"审批中"},已通过:{type:"success",text:"已通过"},已驳回:{type:"danger",text:"已驳回"},已过期:{type:"warning",text:"已过期"},已耗尽:{type:"warning",text:"已耗尽"},可用:{type:"success",text:"可用"}}[t]||{type:"default",text:t}}const ne=i(!1),ba=Wa(async()=>{if(!ne.value){ne.value=!0;try{await Re()}finally{ne.value=!1}}},1e3,{leading:!0,trailing:!1}),Ca=async()=>{if(w.value===3)try{const t=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,e={点:"Point",多点:"Multipoint",线:"Line",面:"Area",文本:"Text",多文本:"Multitext",曲线:"Curve",圆弧:"Arc",椭圆:"Ellipse",其他:"Other"},o={task_id:t,fmw_id:"cad2gis",fmw_name:"CAD转GIS",fmw_path:"tools/cad2gis/cad2gis.fmw",params:{dwg_path:`temp/${Y.value}`,output_Choice:P.value.join(" "),save_path:`tools/cad2gis/output/${t}`},up_nums:D.value.length},c=sessionStorage.getItem("user");if(!c){u.error("未登录,请先登录");return}try{const p=JSON.parse(c);if(!p.username){u.error("用户信息不完整,请重新登录");return}const r=await C.post(U.RUN_FME,o,{headers:{"X-Username":p.username}});if(r.data.success){const y=await C.post(U.UPDATE_USAGE_COUNT,{id:M.value,username:p.username,file_count:D.value.length},{headers:{"X-Username":p.username}});y.data.success||console.error("更新使用次数失败:",y.data.message),u.success("任务提交成功"),await new Promise(V=>setTimeout(V,1e3)),await ie("cad2gis"),za(),w.value=0,M.value=null,D.value=[],P.value=[],Y.value=Xe(),He.value=[],We.value=!1,ne.value=!1,await Re(),te.value="history"}else u.error(r.data.message||"任务提交失败")}catch(p){console.error("解析用户信息失败:",p),u.error("用户信息解析失败,请重新登录")}}catch(t){console.error("提交任务失败:",t),u.error("任务提交失败，请重试")}else{if(w.value===2&&!Va.value){u.warning("请先上传DWG文件");return}w.value++}},xa=t=>{if(we(t.status).text!=="已通过")return;const e=new Date(t.end_date),o=new Date;if(o.setHours(0,0,0,0),e<o){u.warning("该许可已过期");return}if(t.count>=t.usage_count){u.warning("该许可使用次数已达上限");return}if(w.value===2&&D.value.length>0&&(t.usage_count||0)-(t.count||0)-D.value.length<0){u.warning(`当前选择的许可剩余次数不足，无法处理 ${D.value.length} 个文件`);return}M.value=t.id},ka=({row:t})=>{if(we(t.status).text!=="已通过")return"disabled-row";const e=new Date(t.end_date),o=new Date;return o.setHours(0,0,0,0),e<o||t.count>=t.usage_count?"disabled-row":"clickable-row"},bt=t=>{if(we(t.status).text!=="已通过")return!1;const e=new Date(t.end_date),o=new Date;return o.setHours(0,0,0,0),!(e<o||t.count>=t.usage_count)},He=i([]),D=i([]),We=i(!1),Y=i(""),Xe=()=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return Array.from({length:40},()=>t.charAt(Math.floor(Math.random()*t.length))).join("")};Pe(()=>{Y.value=Xe()});const Ta=t=>t.name.toLowerCase().endsWith(".dwg")?!0:(u.error("只能上传DWG格式的文件！"),!1),Da=t=>[".zip",".rar",".7z"].some(o=>t.name.toLowerCase().endsWith(o))?!0:(u.error("只能上传ZIP、RAR、7Z格式的压缩包！"),!1),Ct=(t,e)=>{t.success?([".zip",".rar",".7z"].some(c=>e.name.toLowerCase().endsWith(c))&&(e.extracting=!1,e.parsing=!1,e.parsed=!0,e.dwgCount=t.files.length),D.value=[...D.value,...t.files],u.success("文件上传成功")):u.error(t.message||"文件上传失败")},xt=t=>{console.error("Upload error:",t),u.error("文件上传失败")},Ye=async t=>{try{const e=await C.post(U.DELETE_FILE,{folderId:Y.value,fileName:t.name});if(e.success){const o=D.value.findIndex(c=>c.name===t.name);o!==-1&&D.value.splice(o,1),u.success("文件删除成功")}else u.error(e.message||"文件删除失败")}catch(e){console.error("Delete error:",e),u.error("文件删除失败")}},Ua=t=>{Fe.confirm(`确定要移除文件 "${t.name}" 吗？`,"移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Ye({name:t.name})}).catch(()=>{})},kt=t=>{if(!t)return"-";if(typeof t=="string")return t;const e=["B","KB","MB","GB"];let o=0,c=t;for(;c>=1024&&o<e.length-1;)c/=1024,o++;return`${c.toFixed(2)} ${e[o]}`},Va=j(()=>D.value.length>0),re=j(()=>M.value?$e.value.find(t=>t.id===M.value):null),he=j(()=>{var t,e;return(((t=re.value)==null?void 0:t.usage_count)||0)-(((e=re.value)==null?void 0:e.count)||0)-D.value.length}),Ze=j(()=>he.value>=0),P=i([]),Je=["点（Point）","富点（MultiPoint）","线（Line）","面（Area）","文本（Text）","富文本（MultiText）","曲线（Curve）","圆弧（Arc）","椭圆（Ellipse）","其他（Other）"],La=()=>{P.value=[...Je]},Aa=()=>{const t=new Set(P.value);P.value=Je.filter(e=>!t.has(e))},Z=i([]),Qe=i(!1),ue=i(1),be=i(10),Ce=i(0),ze=i(null),Ke=i(!1),Sa=t=>({running:"warning",success:"success",failed:"danger"})[t]||"info",Ea=t=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[t]||t,$a=t=>{be.value=t,ue.value=1},Ra=t=>{ue.value=t},ie=async(t=(e=>(e=$.value)==null?void 0:e.fmw_id)()||"cad2gis")=>{try{Qe.value=!0;const o=await C.post(U.GET_RUN_RECORDS,{fmw_id:t,username:h.user.username,page:ue.value,page_size:be.value},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});o.data.success?(Z.value=o.data.data.records,Ce.value=o.data.data.pagination.total):u.error(o.data.message||"获取历史记录失败")}catch(o){console.error("获取历史记录失败:",o),u.error("获取历史记录失败")}finally{Qe.value=!1}},za=()=>{Ke.value||(Ke.value=!0,ze.value=setInterval(async()=>{Z.value.some(e=>e.status==="running"||e.status==="pending")?(console.log("检测到运行中的任务，正在更新状态..."),await ie()):(console.log("没有运行中的任务，停止轮询"),Tt())},rs))},Tt=()=>{ze.value&&(clearInterval(ze.value),ze.value=null),Ke.value=!1},Oa=async t=>{try{await Fe.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=Z.value.findIndex(o=>o.task_id===t.task_id);e!==-1&&(Z.value.splice(e,1),Ce.value--),C.post(U.DELETE_RESULT,{task_id:t.task_id},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}}).then(o=>{o.data.success?u.success("删除成功"):(Z.value.splice(e,0,t),Ce.value++,u.error(o.data.message||"删除失败"))}).catch(o=>{Z.value.splice(e,0,t),Ce.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&(console.error("删除历史记录失败:",e),u.error("删除历史记录失败"))}},Dt=async t=>{try{const e=`tools/cad2gis/output/${t.task_id}/${t.file_name}`,o=`${U.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`,c=document.createElement("a");c.style.display="none",document.body.appendChild(c),c.href=Bt(o),c.download=t.file_name,c.click(),setTimeout(()=>{document.body.removeChild(c)},100),u.success("开始下载")}catch(e){console.error("下载历史记录失败:",e),u.error("下载失败，请稍后重试")}},Ut=async t=>{try{t.error_message?(_t.value=t.error_message,Ee.value=!0):u.warning("暂无错误信息")}catch(e){console.error("显示日志失败:",e),u.error("显示日志失败")}};pe([ue,be],()=>{ie()}),Pe(()=>{ie()}),lt(()=>{Tt()});const Oe=i(null);pe(w,async()=>{if(await el(),Oe.value){const t=Oe.value.querySelector('.step-content[v-show="true"]');t&&(Oe.value.style.height=`${t.scrollHeight}px`)}});const Ia=j(()=>P.value.length>0);return(t,e)=>{var Ot,It;const o=m("el-step"),c=m("el-steps"),p=m("el-icon"),r=m("el-button"),y=m("el-skeleton-item"),V=m("el-skeleton"),f=m("el-table-column"),F=m("el-tag"),Ma=m("el-radio"),xe=m("el-table"),et=m("ArrowLeft"),ke=m("el-upload"),Ie=m("el-descriptions-item"),Pa=m("el-descriptions"),Fa=m("el-alert"),z=m("el-checkbox"),ja=m("el-checkbox-group"),Vt=m("el-card"),Lt=m("el-tab-pane"),Ga=m("el-tooltip"),At=m("el-button-group"),St=m("el-pagination"),Ba=m("el-tabs"),Et=m("el-option"),$t=m("el-select"),Rt=m("el-input-number"),zt=m("el-date-picker"),Na=m("el-color-picker"),J=m("el-input"),O=m("el-form-item"),tt=m("el-form"),de=m("el-dialog"),at=Ka("loading");return g(),N("div",ll,[l(Ba,{modelValue:te.value,"onUpdate:modelValue":e[10]||(e[10]=a=>te.value=a),class:"cad-tabs",onTabClick:Nt},{default:s(()=>[l(Lt,{label:"转换工具",name:"convert"},{default:s(()=>[l(st,{name:"slide-fade",mode:"out-in"},{default:s(()=>[I(n("div",sl,[l(Vt,{class:"license-card",style:{"margin-top":"0"}},{default:s(()=>[l(c,{active:w.value,"finish-status":"success",simple:""},{default:s(()=>[l(o,{title:"开始"}),l(o,{title:"选择许可"}),l(o,{title:"上传文件"}),l(o,{title:"设置输出类型"})]),_:1},8,["active"]),n("div",ol,[n("div",{class:"progress-bar",style:Xa({width:`${wt.value/4*100}%`})},null,4)]),n("div",{class:De(["step-content-container",{transitioning:R.value}]),ref_key:"stepContentRef",ref:Oe},[l(st,{name:"step-fade",mode:"out-in"},{default:s(()=>[(g(),N("div",{key:w.value},[I(n("div",nl,[n("div",rl,[l(r,{type:"primary",size:"large",class:"start-button",onClick:e[0]||(e[0]=a=>B("next")),disabled:R.value},{default:s(()=>[l(p,null,{default:s(()=>[l(b(ot))]),_:1}),e[34]||(e[34]=d(" 开始转换 "))]),_:1},8,["disabled"])])],512),[[q,w.value===0]]),I(n("div",ul,[n("div",il,[l(r,{type:"primary",onClick:_a},{default:s(()=>[l(p,null,{default:s(()=>[l(b(Ya))]),_:1}),e[35]||(e[35]=d(" 申请许可 "))]),_:1}),l(r,{type:"primary",onClick:b(ba),disabled:ne.value},{default:s(()=>[l(p,{class:De({"refresh-rotate":ne.value})},{default:s(()=>[l(b(Za))]),_:1},8,["class"]),e[36]||(e[36]=d(" 刷新许可 "))]),_:1},8,["onClick","disabled"])]),qe.value?(g(),k(V,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:s(()=>[l(y,{variant:"text",style:{width:"80px","margin-right":"16px"}}),l(y,{variant:"text",style:{width:"150px","margin-right":"16px"}}),l(y,{variant:"text",style:{width:"150px","margin-right":"16px"}}),l(y,{variant:"text",style:{width:"100px","margin-right":"16px"}}),l(y,{variant:"text",style:{width:"100px","margin-right":"16px"}}),l(y,{variant:"text",style:{width:"180px","margin-right":"16px"}}),l(y,{variant:"text",style:{width:"100px","margin-right":"16px"}}),l(y,{variant:"text",style:{width:"80px"}})]),_:1})):(g(),k(xe,{key:1,data:$e.value,style:{width:"100%"},onRowClick:xa,"row-class-name":ka},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"user_project",label:"项目","min-width":"150","show-overflow-tooltip":""}),l(f,{prop:"reason",label:"原因","min-width":"150","show-overflow-tooltip":""}),l(f,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),l(f,{prop:"count",label:"已用次数",width:"100",align:"center"}),l(f,{prop:"end_date",label:"截止时间",width:"180",align:"center"},{default:s(({row:a})=>[d(v(Ae(a.end_date,"yyyy-MM-dd")),1)]),_:1}),l(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:a})=>[l(F,{type:we(a.status).type},{default:s(()=>[d(v(we(a.status).text),1)]),_:2},1032,["type"])]),_:1}),l(f,{label:"选择",width:"80",align:"center"},{default:s(({row:a})=>[l(Ma,{modelValue:M.value,"onUpdate:modelValue":e[1]||(e[1]=A=>M.value=A),label:a.id,disabled:!bt(a),class:"custom-radio"},{default:s(()=>[n("span",dl,v(a.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"])),n("div",pl,[l(r,{onClick:e[2]||(e[2]=a=>B("prev")),disabled:R.value},{default:s(()=>[l(p,null,{default:s(()=>[l(et)]),_:1}),e[37]||(e[37]=d(" 上一步 "))]),_:1},8,["disabled"]),l(r,{type:"primary",onClick:e[3]||(e[3]=a=>B("next")),disabled:!M.value||R.value||he.value<0},{default:s(()=>[e[38]||(e[38]=d(" 下一步 ")),l(p,null,{default:s(()=>[l(b(ot))]),_:1})]),_:1},8,["disabled"])])],512),[[q,w.value===1]]),I(n("div",cl,[n("div",fl,[e[49]||(e[49]=n("div",{style:{"margin-bottom":"20px",display:"flex","justify-content":"space-between","align-items":"center"}},[n("h3",{style:{margin:"0"}},"上传文件")],-1)),n("div",ml,[n("div",vl,[n("div",_l,[l(p,null,{default:s(()=>[l(b(nt))]),_:1}),e[39]||(e[39]=n("span",null,"直接上传DWG文件",-1))]),e[42]||(e[42]=n("div",{class:"section-desc"},"支持直接上传单个或多个DWG文件",-1)),l(ke,{class:"upload-component",drag:"",action:b(C).defaults.baseURL+"/api/cad2gis/upload","auto-upload":!0,"on-success":Ct,"on-error":xt,"on-remove":Ye,"file-list":He.value,"before-upload":Ta,accept:".dwg",multiple:"","show-file-list":!0,data:{folderId:Y.value}},{tip:s(()=>e[40]||(e[40]=[n("div",{class:"el-upload__tip"}," 支持 .dwg 格式文件 ",-1)])),file:s(({file:a})=>[n("div",gl,[n("span",yl,v(a.name),1),n("span",wl,v(a.status==="success"?"上传成功":a.status==="uploading"?`上传中 ${Math.round(a.percentage)}%`:"上传中"),1)])]),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(Ue))]),_:1}),e[41]||(e[41]=n("div",{class:"el-upload__text"},[d(" 将DWG文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])]),n("div",hl,[n("div",bl,[l(p,null,{default:s(()=>[l(b(Ja))]),_:1}),e[43]||(e[43]=n("span",null,"上传压缩包",-1))]),e[46]||(e[46]=n("div",{class:"section-desc"},"支持上传包含DWG文件的ZIP/RAR/7Z压缩包，将自动解压并提取DWG文件",-1)),l(ke,{class:"upload-component",drag:"",action:Gt("/api/cad2gis/upload"),"auto-upload":!0,"on-success":Ct,"on-error":xt,"on-remove":Ye,"file-list":He.value,"before-upload":Da,accept:".zip,.rar,.7z",multiple:"","show-file-list":!0,data:{folderId:Y.value}},{tip:s(()=>e[44]||(e[44]=[n("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包 ",-1)])),file:s(({file:a})=>[n("div",Cl,[n("span",xl,v(a.name),1),n("span",kl,v(a.status==="success"?a.extracting?"解压中":a.parsing?"解析中":a.parsed?`找到${a.dwgCount||0}个DWG文件`:"解压中":a.status==="uploading"?`上传中 ${Math.round(a.percentage)}%`:"上传中"),1)])]),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(Ue))]),_:1}),e[45]||(e[45]=n("div",{class:"el-upload__text"},[d(" 将压缩包拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])])]),n("div",Tl,[n("div",Dl,[e[47]||(e[47]=n("span",{class:"table-title"},"解析后CAD文件",-1)),n("div",Ul,[l(F,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var a;return[d("当前许可次数："+v(((a=re.value)==null?void 0:a.usage_count)||0),1)]}),_:1}),l(F,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var a;return[d("已用次数："+v(((a=re.value)==null?void 0:a.count)||0),1)]}),_:1}),l(F,{type:"warning",style:{"margin-right":"10px"}},{default:s(()=>[d("消耗次数："+v(D.value.length),1)]),_:1}),l(F,{type:Ze.value?"success":"danger",style:{"margin-right":"10px"}},{default:s(()=>[d(" 消耗后剩余次数："+v(he.value)+" ",1),Ze.value?ce("",!0):(g(),k(p,{key:0,style:{"margin-left":"4px"}},{default:s(()=>[l(b(Qa))]),_:1}))]),_:1},8,["type"])])]),I((g(),k(xe,{data:D.value,style:{width:"100%"},border:""},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"name",label:"文件名","min-width":"200","show-overflow-tooltip":""}),l(f,{prop:"size",label:"文件大小",width:"120",align:"center"},{default:s(({row:a})=>[d(v(kt(a.size)),1)]),_:1}),l(f,{label:"操作",width:"120",align:"center"},{default:s(({row:a})=>[l(r,{type:"danger",size:"small",onClick:A=>Ua(a),disabled:We.value},{default:s(()=>[l(p,null,{default:s(()=>[l(b(rt))]),_:1}),e[48]||(e[48]=d(" 移除 "))]),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[at,We.value]])])]),n("div",Vl,[l(r,{onClick:e[4]||(e[4]=a=>B("prev")),disabled:R.value},{default:s(()=>[l(p,null,{default:s(()=>[l(et)]),_:1}),e[50]||(e[50]=d(" 上一步 "))]),_:1},8,["disabled"]),l(r,{type:"primary",onClick:e[5]||(e[5]=a=>B("next")),disabled:D.value.length===0||R.value||he.value<0},{default:s(()=>[e[51]||(e[51]=d(" 下一步 ")),l(p,null,{default:s(()=>[l(b(ot))]),_:1})]),_:1},8,["disabled"])])],512),[[q,w.value===2]]),I(n("div",Ll,[n("div",Al,[n("div",Sl,[n("div",El,[e[52]||(e[52]=n("h3",null,"待转换文件",-1)),l(xe,{data:D.value,style:{width:"100%"},size:"small"},{default:s(()=>[l(f,{prop:"name",label:"文件名"}),l(f,{prop:"size",label:"大小",width:"120"},{default:s(a=>[d(v(kt(a.row.size)),1)]),_:1})]),_:1},8,["data"])]),n("div",$l,[e[53]||(e[53]=n("h3",null,"许可信息",-1)),l(Pa,{column:1,border:""},{default:s(()=>[l(Ie,{label:"当前许可次数"},{default:s(()=>{var a;return[d(v(((a=re.value)==null?void 0:a.usage_count)||0),1)]}),_:1}),l(Ie,{label:"已用次数"},{default:s(()=>{var a;return[d(v(((a=re.value)==null?void 0:a.count)||0),1)]}),_:1}),l(Ie,{label:"消耗次数"},{default:s(()=>[d(v(D.value.length),1)]),_:1}),l(Ie,{label:"消耗后剩余次数"},{default:s(()=>[n("span",{class:De({"text-danger":!Ze.value})},v(he.value),3)]),_:1})]),_:1})])]),n("div",Rl,[n("div",zl,[e[66]||(e[66]=n("h3",null,"选择输出Geometry类型",-1)),n("div",Ol,[n("div",Il,[l(r,{type:"primary",onClick:La},{default:s(()=>e[54]||(e[54]=[d("全选")])),_:1}),l(r,{onClick:Aa},{default:s(()=>e[55]||(e[55]=[d("反选")])),_:1})]),I(n("div",Ml,[l(Fa,{title:"为了避免图形丢失，建议选择全部类型",type:"warning",closable:!1,"show-icon":""})],512),[[q,P.value.length!==Je.length]]),l(ja,{modelValue:P.value,"onUpdate:modelValue":e[6]||(e[6]=a=>P.value=a)},{default:s(()=>[l(z,{label:"点（Point）"},{default:s(()=>e[56]||(e[56]=[d("点（Point）")])),_:1}),l(z,{label:"富点（MultiPoint）"},{default:s(()=>e[57]||(e[57]=[d("富点（MultiPoint）")])),_:1}),l(z,{label:"线（Line）"},{default:s(()=>e[58]||(e[58]=[d("线（Line）")])),_:1}),l(z,{label:"面（Area）"},{default:s(()=>e[59]||(e[59]=[d("面（Area）")])),_:1}),l(z,{label:"文本（Text）"},{default:s(()=>e[60]||(e[60]=[d("文本（Text）")])),_:1}),l(z,{label:"富文本（MultiText）"},{default:s(()=>e[61]||(e[61]=[d("富文本（MultiText）")])),_:1}),l(z,{label:"曲线（Curve）"},{default:s(()=>e[62]||(e[62]=[d("曲线（Curve）")])),_:1}),l(z,{label:"圆弧（Arc）"},{default:s(()=>e[63]||(e[63]=[d("圆弧（Arc）")])),_:1}),l(z,{label:"椭圆（Ellipse）"},{default:s(()=>e[64]||(e[64]=[d("椭圆（Ellipse）")])),_:1}),l(z,{label:"其他（Other）"},{default:s(()=>e[65]||(e[65]=[d("其他（Other）")])),_:1})]),_:1},8,["modelValue"])])])])]),n("div",Pl,[l(r,{onClick:e[7]||(e[7]=a=>B("prev")),disabled:R.value},{default:s(()=>[l(p,null,{default:s(()=>[l(et)]),_:1}),e[67]||(e[67]=d(" 上一步 "))]),_:1},8,["disabled"]),w.value===3?(g(),k(r,{key:0,type:"primary",loading:se.value,onClick:na,disabled:!Ia.value||R.value},{default:s(()=>e[68]||(e[68]=[d("提交任务")])),_:1},8,["loading","disabled"])):ce("",!0)])],512),[[q,w.value===3]])]))]),_:1})],2)]),_:1})],512),[[q,te.value==="convert"]])]),_:1})]),_:1}),l(Lt,{label:"历史记录",name:"history"},{default:s(()=>[l(st,{name:"slide-fade",mode:"out-in"},{default:s(()=>[I(n("div",Fl,[l(Vt,{class:"history-card",style:{"margin-top":"0px"}},{header:s(()=>e[69]||(e[69]=[])),default:s(()=>[n("div",jl,[I((g(),k(xe,{data:Z.value,style:{width:"100%"}},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"submit_time",label:"提交时间",width:"350",align:"center"},{default:s(({row:a})=>[l(Ga,{content:Ae(a.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:s(()=>[n("span",null,v(Ae(a.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),l(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:a})=>[l(F,{type:Sa(a.status)},{default:s(()=>[d(v(Ea(a.status)),1)]),_:2},1032,["type"])]),_:1}),l(f,{prop:"time_consuming",label:"运行耗时",width:"250",align:"center"},{default:s(({row:a})=>[d(v(mt(a.time_consuming)),1)]),_:1}),l(f,{prop:"file_size",label:"文件大小",width:"150",align:"center"},{default:s(({row:a})=>[d(v(a.file_size),1)]),_:1}),l(f,{prop:"up_nums",label:"转换文件数量","min-width":"130",align:"center","show-overflow-tooltip":""}),l(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:a})=>[n("div",Gl,[l(At,null,{default:s(()=>[a.status==="success"&&a.up_nums>0&&a.file_size!=="0.0MB"?(g(),k(r,{key:0,type:"success",size:"small",onClick:A=>Dt(a),disabled:a.status!=="success"},{default:s(()=>[l(p,null,{default:s(()=>[l(b(Mt))]),_:1}),e[70]||(e[70]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):ce("",!0),a.error_message?(g(),k(r,{key:1,type:"info",size:"small",onClick:A=>Ut(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(nt))]),_:1}),e[71]||(e[71]=d(" 日志 "))]),_:2},1032,["onClick"])):ce("",!0),l(r,{type:"danger",size:"small",onClick:A=>Oa(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(rt))]),_:1}),e[72]||(e[72]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[at,Qe.value]]),n("div",Bl,[l(St,{"current-page":ue.value,"onUpdate:currentPage":e[8]||(e[8]=a=>ue.value=a),"page-size":be.value,"onUpdate:pageSize":e[9]||(e[9]=a=>be.value=a),"page-sizes":[10,20,50,100],total:Ce.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$a,onCurrentChange:Ra},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[q,te.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(de,{modelValue:ve.value,"onUpdate:modelValue":e[12]||(e[12]=a=>ve.value=a),title:`运行工具 - ${(Ot=$.value)==null?void 0:Ot.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:ea,onAfterClose:ta},{footer:s(()=>[n("span",Xl,[l(r,{onClick:e[11]||(e[11]=a=>ve.value=!1)},{default:s(()=>e[74]||(e[74]=[d("取消")])),_:1}),l(r,{type:"primary",onClick:ra},{default:s(()=>e[75]||(e[75]=[d("提交任务")])),_:1})])]),default:s(()=>[l(tt,{ref_key:"formRef",ref:E,model:_.value,rules:je.value,"label-width":"200px",size:"small",class:"run-form"},{default:s(()=>[ae.value.length>0?(g(!0),N(ut,{key:0},it(ae.value,a=>I((g(),k(O,{key:a.prop,label:a.type==="message"?"":a.label,prop:a.prop,required:a.required,class:De({"message-form-item":a.type==="message"})},{default:s(()=>{var A,Te,Me;return[a.type==="message"?(g(),N("div",Nl,v(a.component.content),1)):a.type==="upload"?(g(),k(ke,Q({key:1,ref_for:!0},a.component.props,{class:["upload-area",{"is-error":((Me=(Te=(A=E.value)==null?void 0:A.fields)==null?void 0:Te.find(x=>x.prop===a.prop))==null?void 0:Me.validateState)==="error"}],drag:""}),{default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(Ue))]),_:1}),e[73]||(e[73]=n("div",{class:"el-upload__text"},[d(" 拖拽文件到此处"),n("br"),d("或"),n("em",null,"点击上传")],-1))]),_:2},1040,["class"])):a.type==="select"?(g(),k($t,Q({key:2,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{style:{width:"100%"}}),{default:s(()=>[(g(!0),N(ut,null,it(a.component.options,x=>(g(),k(Et,{key:x.value,label:x.label,value:x.value,title:x.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue"])):a.type==="number"?(g(),k(Rt,Q({key:3,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):a.type==="datetime"?(g(),k(zt,Q({key:4,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props),null,16,["modelValue","onUpdate:modelValue"])):a.type==="checkbox"?(g(),k(z,Q({key:5,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):a.type==="color"?(g(),N("div",ql,[l(Na,Q({modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{onChange:x=>ca(a.prop,x),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),n("span",Hl,v(_.value[`${a.prop}_value`]||"255,255,255"),1)])):(g(),k(J,Q({key:7,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[q,Be(a)]])),128)):(g(),N("div",Wl," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),l(de,{modelValue:_e.value,"onUpdate:modelValue":e[17]||(e[17]=a=>_e.value=a),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:aa,onAfterClose:la},{footer:s(()=>[n("span",Yl,[l(r,{onClick:e[16]||(e[16]=a=>_e.value=!1)},{default:s(()=>e[78]||(e[78]=[d("取消")])),_:1}),l(r,{type:"primary",onClick:ua},{default:s(()=>e[79]||(e[79]=[d("确定")])),_:1})])]),default:s(()=>[l(tt,{ref_key:"uploadFormRef",ref:G,model:L.value,rules:Kt,"label-width":"100px",size:"small"},{default:s(()=>[l(O,{label:"工具名称",prop:"fmw_name"},{default:s(()=>[l(J,{modelValue:L.value.fmw_name,"onUpdate:modelValue":e[13]||(e[13]=a=>L.value.fmw_name=a),placeholder:""},null,8,["modelValue"])]),_:1}),l(O,{label:"所属项目",prop:"project"},{default:s(()=>[l(J,{modelValue:L.value.project,"onUpdate:modelValue":e[14]||(e[14]=a=>L.value.project=a),placeholder:""},null,8,["modelValue"])]),_:1}),l(O,{label:"工具描述",prop:"description"},{default:s(()=>[l(J,{modelValue:L.value.description,"onUpdate:modelValue":e[15]||(e[15]=a=>L.value.description=a),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),l(O,{label:"工具文件",prop:"file"},{default:s(()=>{var a,A,Te;return[l(ke,{ref_key:"uploadRef",ref:W,class:De(["upload-demo",{"is-error":((Te=(A=(a=G.value)==null?void 0:a.fields)==null?void 0:A.find(Me=>Me.prop==="file"))==null?void 0:Te.validateState)==="error"}]),drag:"","auto-upload":!1,"on-change":t.handleFileChange,"before-upload":ia,limit:1,accept:".fmw","file-list":[]},{tip:s(()=>e[76]||(e[76]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(Ue))]),_:1}),e[77]||(e[77]=n("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["class","on-change"])]}),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(de,{modelValue:ge.value,"onUpdate:modelValue":e[19]||(e[19]=a=>ge.value=a),title:"更新工具",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:sa,onAfterClose:oa},{footer:s(()=>[n("span",Zl,[l(r,{onClick:e[18]||(e[18]=a=>ge.value=!1)},{default:s(()=>e[82]||(e[82]=[d("取消")])),_:1}),l(r,{type:"primary",onClick:pa,disabled:!H.value.file},{default:s(()=>e[83]||(e[83]=[d("确认更新")])),_:1},8,["disabled"])])]),default:s(()=>[l(ke,{ref_key:"updateUploadRef",ref:X,class:"upload-demo","auto-upload":!1,"on-change":da,limit:1,accept:".fmw",drag:""},{tip:s(()=>e[80]||(e[80]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(Ue))]),_:1}),e[81]||(e[81]=n("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},512)]),_:1},8,["modelValue"]),l(de,{modelValue:Ge.value,"onUpdate:modelValue":e[22]||(e[22]=a=>Ge.value=a),title:`运行成果 - ${(It=$.value)==null?void 0:It.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:s(()=>[I((g(),k(xe,{data:le.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:s(({row:a})=>[d(v(Ae(a.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),l(f,{prop:"status",label:"运行状态",width:"100",align:"center"},{default:s(({row:a})=>[l(F,{type:Yt(a.status)},{default:s(()=>[d(v(Zt(a.status)),1)]),_:2},1032,["type"])]),_:1}),l(f,{prop:"time_consuming",label:"运行耗时",width:"100",align:"center"},{default:s(({row:a})=>[d(v(mt(a.time_consuming)),1)]),_:1}),l(f,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),l(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:a})=>[n("div",Jl,[l(At,null,{default:s(()=>[a.status==="success"&&a.up_nums>0&&a.file_size!=="0.0MB"?(g(),k(r,{key:0,type:"success",size:"small",onClick:A=>Dt(a),disabled:a.status!=="success"},{default:s(()=>[l(p,null,{default:s(()=>[l(b(Mt))]),_:1}),e[84]||(e[84]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):ce("",!0),a.error_message?(g(),k(r,{key:1,type:"info",size:"small",onClick:A=>Ut(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(nt))]),_:1}),e[85]||(e[85]=d(" 日志 "))]),_:2},1032,["onClick"])):ce("",!0),l(r,{type:"danger",size:"small",onClick:A=>Jt(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(rt))]),_:1}),e[86]||(e[86]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[at,Pt.value]]),n("div",Ql,[l(St,{"current-page":fe.value,"onUpdate:currentPage":e[20]||(e[20]=a=>fe.value=a),"page-size":Ve.value,"onUpdate:pageSize":e[21]||(e[21]=a=>Ve.value=a),"page-sizes":[10,20,50,100],total:me.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:fa,onCurrentChange:Qt},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"]),l(de,{modelValue:Ee.value,"onUpdate:modelValue":e[24]||(e[24]=a=>Ee.value=a),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:s(()=>[n("span",es,[l(r,{onClick:e[23]||(e[23]=a=>Ee.value=!1)},{default:s(()=>e[87]||(e[87]=[d("关闭")])),_:1})])]),default:s(()=>[n("div",Kl,[n("pre",null,v(_t.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"]),l(de,{modelValue:oe.value,"onUpdate:modelValue":e[32]||(e[32]=a=>oe.value=a),title:"申请许可-CAD转GIS",width:"500px","close-on-click-modal":!1,onClose:e[33]||(e[33]=a=>oe.value=!1),onAfterClose:yt},{footer:s(()=>[n("span",ns,[l(r,{onClick:e[31]||(e[31]=a=>oe.value=!1)},{default:s(()=>e[88]||(e[88]=[d("取消")])),_:1}),l(r,{type:"primary",loading:Ne.value,onClick:ya},{default:s(()=>e[89]||(e[89]=[d("提交")])),_:1},8,["loading"])])]),default:s(()=>[l(tt,{ref_key:"licenseFormRef",ref:ye,model:T.value,rules:ma,"label-width":"100px",class:"apply-form",size:"small"},{default:s(()=>[l(O,{label:"工具名称",prop:"tool_name"},{default:s(()=>[l(J,{modelValue:T.value.tool_name,"onUpdate:modelValue":e[25]||(e[25]=a=>T.value.tool_name=a),value:"CAD转GIS",disabled:""},null,8,["modelValue"])]),_:1}),l(O,{label:"使用项目",prop:"user_project"},{error:s(({error:a})=>[n("span",ts,v(a),1)]),default:s(()=>[l(J,{modelValue:T.value.user_project,"onUpdate:modelValue":e[26]||(e[26]=a=>T.value.user_project=a),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),l(O,{label:"申请原因",prop:"reason"},{error:s(({error:a})=>[n("span",as,v(a),1)]),default:s(()=>[l(J,{modelValue:T.value.reason,"onUpdate:modelValue":e[27]||(e[27]=a=>T.value.reason=a),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),l(O,{label:"有效期",prop:"end_date"},{error:s(({error:a})=>[n("span",ls,v(a),1)]),default:s(()=>[l(zt,{modelValue:T.value.end_date,"onUpdate:modelValue":e[28]||(e[28]=a=>T.value.end_date=a),type:"date",placeholder:"请选择有效期","disabled-date":va,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),l(O,{label:"申请次数",prop:"usage_count"},{error:s(({error:a})=>[n("span",ss,v(a),1)]),default:s(()=>[l(Rt,{modelValue:T.value.usage_count,"onUpdate:modelValue":e[29]||(e[29]=a=>T.value.usage_count=a),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(O,{label:"审批人",prop:"approver"},{error:s(({error:a})=>[n("span",os,v(a),1)]),default:s(()=>[l($t,{modelValue:T.value.approver,"onUpdate:modelValue":e[30]||(e[30]=a=>T.value.approver=a),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:s(()=>[(g(!0),N(ut,null,it(gt.value,a=>(g(),k(Et,{key:a.username,label:a.real_name,value:a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),fs=tl(us,[["__scopeId","data-v-e509b539"]]);export{fs as default};
