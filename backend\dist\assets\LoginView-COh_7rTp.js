import{d as j,r as u,u as H,o as J,a as P,c as C,b as s,e as a,w as t,f as c,g as Q,h as W,i as U,E as d,j as Y,k as p,l as i,t as ee,m as se,n as ae,p as E,q as F,s as L,v as I,x as le,y as h,z as te,A as N,B as q,C as oe,D as re,F as ne,G as ie,H as ue,_ as de}from"./index-Dn7OnccA.js";const ce="/assets/logo-BIVXNlIX.png",pe={class:"login-content"},me={class:"info-panel"},fe={class:"feature-list"},ge={class:"feature-item"},ve={class:"feature-item"},_e={class:"feature-item"},we={class:"form-panel"},ye={class:"form-body"},be={class:"input-group"},Ve={class:"input-wrapper"},ke={class:"input-group"},ze={class:"input-wrapper"},xe={key:0,class:"form-actions"},Ce={key:1,class:"form-actions"},Ue={class:"register-content"},he={class:"register-welcome"},Le={class:"input-group"},Re={class:"input-container"},Se={class:"input-group"},Ae={class:"input-container"},Be={class:"input-group"},Ee={class:"input-container"},Fe={class:"input-group"},Ie={class:"input-container"},Ne={class:"dialog-actions"},qe=j({__name:"LoginView",setup(De){const f=u(""),g=u(""),v=u(!1),D=Y(),R=H(),_=u(!1),y=u(!1),b=u(),n=u({username:"",password:"",real_name:"",department:""}),$={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{pattern:/^[a-zA-Z0-9]+$/,message:"用户名只能包含字母和数字",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:8,message:"密码长度不能小于8位",trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"密码必须包含大小写字母和数字，且长度至少8位",trigger:"blur"}],real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5]+$/,message:"真实姓名必须是中文",trigger:"blur"}],department:[{required:!0,message:"请输入所属部门",trigger:"blur"}]},V=u([]),K=async()=>{try{const l=await U.post("/api/get_departments");l.data&&Array.isArray(l.data.departments)?V.value=l.data.departments:V.value=[]}catch{V.value=[],d.error("获取部门列表失败")}},M=()=>{_.value=!0,n.value={username:"",password:"",real_name:"",department:""},K(),ue(()=>{var l;(l=b.value)==null||l.resetFields()})},T=async()=>{b.value&&await b.value.validate(async(l,e)=>{if(l){y.value=!0;try{const o=await U.post("/api/register",n.value);o.data.success?(d.success("注册成功"),_.value=!1,f.value=n.value.username,g.value=n.value.password):d.error(o.data.message||"注册失败")}catch{d.error("注册失败，请检查网络连接")}finally{y.value=!1}}})},k=async()=>{if(!f.value||!g.value){d.warning("请输入用户名和密码");return}v.value=!0;try{const l=await U.post("/api/login",{username:f.value,password:g.value});if(l.data.success){const{token:e,user:o}=l.data;sessionStorage.setItem("token",e),sessionStorage.setItem("user",JSON.stringify(o)),R.setToken(e),R.setUser(o),d.success("登录成功！"),D.push("/")}else l.data.message==="账号不存在"?d.error("账号不存在"):l.data.message==="密码错误"?d.error("密码错误"):d.error(l.data.message||"登录失败")}catch{d.error("登录失败，请检查网络连接")}finally{v.value=!1}},S=l=>{l.key==="Enter"&&k()},m=u(null),A=u(!0),Z=async()=>{try{const l=await U.post("/api/get_allow_register");l.data&&typeof l.data.allow_register=="boolean"?m.value=l.data.allow_register:l.data&&typeof l.data.allow_register=="string"?m.value=l.data.allow_register==="true":m.value=!1}catch{m.value=!1}finally{A.value=!1}};return J(()=>{window.addEventListener("keypress",S),Z()}),P(()=>{window.removeEventListener("keypress",S)}),(l,e)=>{const o=c("el-icon"),w=c("el-input"),z=c("el-button"),B=c("el-form"),x=c("el-form-item"),G=c("el-option"),O=c("el-select"),X=c("el-dialog");return p(),C("div",{class:"login-container",onKeyup:W(k,["enter"])},[e[23]||(e[23]=s("div",{class:"background-decoration"},[s("div",{class:"decoration-circle circle-1"}),s("div",{class:"decoration-circle circle-2"}),s("div",{class:"decoration-circle circle-3"})],-1)),s("div",pe,[s("div",me,[e[11]||(e[11]=s("div",{class:"brand-section"},[s("img",{src:ce,alt:"Logo",class:"brand-logo"}),s("h1",{class:"brand-title"},"GeoStream Integration"),s("p",{class:"brand-subtitle"},"地理信息流集成平台")],-1)),s("div",fe,[s("div",ge,[a(o,{class:"feature-icon"},{default:t(()=>[a(i(ee))]),_:1}),e[8]||(e[8]=s("span",null,"强大的地理数据处理工具",-1))]),s("div",ve,[a(o,{class:"feature-icon"},{default:t(()=>[a(i(se))]),_:1}),e[9]||(e[9]=s("span",null,"智能化数据分析平台",-1))]),s("div",_e,[a(o,{class:"feature-icon"},{default:t(()=>[a(i(ae))]),_:1}),e[10]||(e[10]=s("span",null,"无缝系统集成解决方案",-1))])])]),s("div",we,[a(B,{class:"login-form",onSubmit:Q(k,["prevent"])},{default:t(()=>[e[16]||(e[16]=s("div",{class:"form-header"},[s("h2",{class:"form-title"},"欢迎回来"),s("p",{class:"form-subtitle"},"请登录您的账户")],-1)),s("div",ye,[s("div",be,[e[12]||(e[12]=s("label",{class:"input-label"},"账号",-1)),s("div",Ve,[a(o,{class:"input-icon"},{default:t(()=>[a(i(E))]),_:1}),a(w,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=r=>f.value=r),placeholder:"请输入您的账号",class:"custom-input",size:"large"},null,8,["modelValue"])])]),s("div",ke,[e[13]||(e[13]=s("label",{class:"input-label"},"密码",-1)),s("div",ze,[a(o,{class:"input-icon"},{default:t(()=>[a(i(F))]),_:1}),a(w,{modelValue:g.value,"onUpdate:modelValue":e[1]||(e[1]=r=>g.value=r),type:"password",placeholder:"请输入您的密码",class:"custom-input",size:"large","show-password":""},null,8,["modelValue"])])]),A.value?(p(),C("div",xe,e[14]||(e[14]=[s("div",{class:"button-skeleton"},[s("div",{class:"skeleton-button primary full-width"}),s("div",{class:"skeleton-button secondary",style:{opacity:"0.3"}})],-1)]))):(p(),C("div",Ce,[a(z,{type:"primary",loading:v.value,onClick:k,class:le(["login-btn",{"full-width":!m.value}]),size:"large"},{default:t(()=>[v.value?I("",!0):(p(),L(o,{key:0},{default:t(()=>[a(i(te))]),_:1})),h(" "+N(v.value?"登录中...":"立即登录"),1)]),_:1},8,["loading","class"]),m.value?(p(),L(z,{key:0,onClick:M,class:"register-btn",size:"large",plain:""},{default:t(()=>[a(o,null,{default:t(()=>[a(i(q))]),_:1}),e[15]||(e[15]=h(" 注册账户 "))]),_:1})):I("",!0)]))])]),_:1})])]),a(X,{modelValue:_.value,"onUpdate:modelValue":e[7]||(e[7]=r=>_.value=r),title:"创建新账户",width:"480px","close-on-click-modal":!1,class:"modern-register-dialog",center:""},{footer:t(()=>[s("div",Ne,[a(z,{onClick:e[6]||(e[6]=r=>_.value=!1),size:"large",class:"cancel-button"},{default:t(()=>e[22]||(e[22]=[h(" 取消 ")])),_:1}),a(z,{type:"primary",loading:y.value,onClick:T,size:"large",class:"register-button"},{default:t(()=>[h(N(y.value?"注册中...":"立即注册"),1)]),_:1},8,["loading"])])]),default:t(()=>[s("div",Ue,[s("div",he,[a(o,{class:"welcome-icon"},{default:t(()=>[a(i(q))]),_:1}),e[17]||(e[17]=s("p",{class:"welcome-text"},"填写以下信息完成注册",-1))]),a(B,{ref_key:"registerFormRef",ref:b,model:n.value,rules:$,class:"register-form","label-width":"0"},{default:t(()=>[a(x,{prop:"username"},{default:t(()=>[s("div",Le,[e[18]||(e[18]=s("label",{class:"input-label"},"用户名",-1)),s("div",Re,[a(o,{class:"input-prefix-icon"},{default:t(()=>[a(i(E))]),_:1}),a(w,{modelValue:n.value.username,"onUpdate:modelValue":e[2]||(e[2]=r=>n.value.username=r),placeholder:"仅限字母和数字",size:"large",class:"styled-input"},null,8,["modelValue"])])])]),_:1}),a(x,{prop:"real_name"},{default:t(()=>[s("div",Se,[e[19]||(e[19]=s("label",{class:"input-label"},"真实姓名",-1)),s("div",Ae,[a(o,{class:"input-prefix-icon"},{default:t(()=>[a(i(oe))]),_:1}),a(w,{modelValue:n.value.real_name,"onUpdate:modelValue":e[3]||(e[3]=r=>n.value.real_name=r),placeholder:"请输入中文姓名",size:"large",class:"styled-input"},null,8,["modelValue"])])])]),_:1}),a(x,{prop:"password"},{default:t(()=>[s("div",Be,[e[20]||(e[20]=s("label",{class:"input-label"},"密码",-1)),s("div",Ee,[a(o,{class:"input-prefix-icon"},{default:t(()=>[a(i(F))]),_:1}),a(w,{modelValue:n.value.password,"onUpdate:modelValue":e[4]||(e[4]=r=>n.value.password=r),type:"password",placeholder:"至少8位，包含大小写字母和数字","show-password":"",size:"large",class:"styled-input"},null,8,["modelValue"])])])]),_:1}),a(x,{prop:"department"},{default:t(()=>[s("div",Fe,[e[21]||(e[21]=s("label",{class:"input-label"},"所属部门",-1)),s("div",Ie,[a(o,{class:"input-prefix-icon"},{default:t(()=>[a(i(re))]),_:1}),a(O,{modelValue:n.value.department,"onUpdate:modelValue":e[5]||(e[5]=r=>n.value.department=r),placeholder:"请选择您的部门",size:"large",class:"styled-select"},{default:t(()=>[(p(!0),C(ne,null,ie(V.value,r=>(p(),L(G,{key:r,label:r,value:r},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])],32)}}}),Ke=de(qe,[["__scopeId","data-v-4ac37e93"]]);export{Ke as default};
