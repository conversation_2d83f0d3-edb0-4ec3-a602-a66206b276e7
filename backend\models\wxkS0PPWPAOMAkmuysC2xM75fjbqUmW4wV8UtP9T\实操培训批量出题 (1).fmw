#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2025\03\0304\出题\none2none.fmw
#          --NUM "400"
#          --dir "C:\Users\<USER>\Desktop\耕地图斑"
#          --file "C:\Users\<USER>\Desktop\考试工程.xlsx"
#          --PARAMETER_2 "C:\Users\<USER>\Desktop"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAABXCAIAAAClR4QjAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAgAElEQVR4nO2deVxb15n3n3M3rSCBQIAAgQAbDMax44XYJrZjx03spnW2NtMk7XTJTDLTpjNN35ku07dvO9N2Jp0206ZrOk2btsk0adMlaTLOYscxxjhe4h1jAwLtIAm0oF13Oe8fFwQ2MhIOxoGc7x987pXuvee553J/es5znnMOwhgDgUAgLASoa20AgUAg5AsRLAKBsGAggkUgEBYMRLAIBMKCgQgWgUBYMBDBIhAICwYiWAQCYcFABItAICwYiGARCIQFAxEsAoGwYGCutQFzzLFzAycHRtIiNmqpO7aspmmiyFfCyfP2Y/3etIANauqOrddzDH2tLSIQAADQYhpLeORs/yun/aM+D00xtY0rgkMDG9vWJNJS/lfgGJQWsCjwgNDt15dePVPfzZw8b3vxbU9gZBhRVM2SFSHPQPv6tbOqRpZGvIhFgUc0vWul4eqZSnivsag8rBMDAYHnU4kEQhAJB7iCkhO7TyVWNxoBAMDnBmOlfOCg7zBAm8UIE7tui/yVXk0PD3uldIzitJ997vuPP/qNa3Ef15ijvV5JklKJGAAaC/q1xqqjLx/l17ZOq8YMkxXocw9CpaVBSXt9XuBjiNP80m39xPvXzfc9EBYpi0qwkrwYi46NDNl1xcYXfvXdrbs+kUgPB553wd11/uefs1dvXOOCQFWVeKgrVF2+BKD7+a4QOFH1Wl0V+J/votffd8MSoEEQMDAgXOu7uWYkRRyPjvk9Dn1pxUvPPH7TbfcneX/ged/F1XhjUyUADHY//1y4ujp0dmDNp+rAbQEXQCUAAIPElAScxCOWtMoJc8aiahI+88rhc57YhZOHtLoiY2Utxylbl68IJsT8r6BX077AWCoWVmqL+vb+/L3pYf321cPdQ4nzxw9qCvWlphqVWrt8WWMwmVN3Bn1uMFZaAKBQSY+EoqlYSKHRn3n5h//9+HfmwWzCe4FFJViJZOqJPx9MK8tUak3Qa68rTJdZWhEgCosSyhE2RkjEmMaA//Nrj3zzK/8MAGuvXzkvVr/rSPP8T/7YmeRKVJrCkM9RX5g21rYgQBQIUi6XHGERIxoD/vZX/+FbX/0SvIerkXA1WDyChTH2+/0ejycU511DQ2oW8QpjVGw0mSHiYwqMgscBJvNlT9cqqGhqPK58a4sOIXRlZgRDYQCQJImiZtcUwoARIAAo0uuurOjZFYexKIqCIPA8L0yQ2Y7H46E47xn2KRhJUJRExWUmM0R8dIFRBIAZalLNUfGJ8PzmeoVapZqHeyG8d5i/GNbBkxdcI1EVS21va1YpFXN7cZvNFovFysvLm5ubk8lkeXHBud4+bbFxYPcAmJ3WI974YF9gVftmB/iYzStvmNvCJ3miwyvrP0Iw2x8CjYIaHQ0imlaokg9tLptDq0ZGRkZHR2UJln+f5L8syzIMwzCMvKFQKDK7PM8nk0lzueHkmbO6yWocjg8GDQ8vG9095Lvur65eNRIIl2OePKxfvnQwgEsZllGoNLbuwwUpu4rjZnkNBIAB4JFPP3jJF8FgMJVKlZeXTz/nzo9+CgA4jkun0zNfnWVoXhAB4I+/eTIfa0RRHBsbi0ajfr+/rKzs1OnTiTTvkFCkLwJt28oQeF3Te9NmgpHiiUhQFEW2oHxXM1NlqpjFydnw+XyBQAAASkpKSkpK3sml5GpUKBSpVGrmI2mGEgUJAH731M8YmmRvEeaY+RCsc1bnH44MDzv7NYV6TYFeo9WNhUYYloNKS9aecrlr3HjxRZSMFAmM8IKgLjbtakIVZUYAgP7+vX0AADU7GhqmlXugLzLocJSUVug1XM+FXjADOOou15bJNAndvSfaW0xNjY2Xux232x0Oh2ma1uv1Wq2Woiie50+dORseGxukG4Mjb/sPpWnz8Cg2650Oev19TfnJFi0lo0Evz/PaUssHm9A7EayxsTG73W40GsvK5sBTO9g/1m9zlhgrijRsz4W+masx0yQcGji92lJ0XWvrOzeAQMgwH01C+9BoKhGtWbpCqdF+7wv3WRpXtu/YBS4fAHQf6gqBM1y9cY2rCqoAwGKshEzX+FSS8Wg6FacolE7EMZYjI/39L++3LYVaMAFM1ysAgIDfhwBFj533pd3gMBsBPGbwPH2Qqd0omJ3Q0TNaW9/avt005RQpETjTEykxGLJ6JXa7XaVSNTc3T/1QpVLduHEDAGzZde8nv/j9WgvwfCoZj+PW5QiFxUSuCsISIEqhZAtLTDRFJ5OJKlNdrnOy4/V6R0ZGtFpt69wpBUJo1O8FwDFHb/7VKMaDvQMhY0lJRcU7dRUJhAzz4mENOP903O8e6FGoNKUVNRqtftTnNNVkdWEmu8YvQUGLkeCIIIjqYtMdzXRZae42zq133fuZf/kvQFSBmorEcyRqqzmIpwEAuk+99YWPfyDrMel0emBgoKmpKWfRfznhQzTt7bJDe92U13jAk803UbAoxWNRFFaXCRXZGrZZkSQpHA4HAgFRFOVdACgrKysqKsrzCnlyzyce/OjD/wqIKlCjSDzHf0umGmPR8D03Zv8VIRCumPnwsJrrqo/1ODWtbaxCRdOMrfsQDlj37v2NIIkMlTvMgTEgBIU6/XXNjQzAQ3esmvIV7unpucTfyZBOp4GmaZrxHrRHym0+hwPMG42QvTnDchSNJQCoa2jGGF/SS4gxdrvdgUAgH8/lTy/tjmosHqctDanRp187U1tfBsNeG5TVlgvmqfp1Eal4zO4J7tu/f/XKlaFQSK/Xw0R0HACm2iN/SFGUXq83m80sy+Y06Z0QiUaBomiGZRnaO/QKwPYZOltpdrwaaZqeXo2ExcRIcOyN430JHhsL2B0br5ufQucvreHQqV7XSETJoGTIA6Wt6XQqGY/CxAs80VOexQdRMFRKkNLJhO3wn2+/badOp6MoCiE0NDSUSCSam5sv98Ye6IscO3a0xFjus1udvd3QsMwgpMG8fU22920yhnVmn15N15gq1Gp1KpVSqVQIIUmSKisrdbq8Eg5EUXzxuBcQUirz6tSXPSyBT5fytra1a+UP3z2vepc1cvjI0eISo8q535o2CuZ66DjI1Jq9NkfZJjMcqzYarTDRHszEsDxn39Sp6crSksLCwkQiodFoMneEEDIYDAaD4d1zj+9NIrH4t777PQCgEJJmqQPJtKSxrCs0VAb8HrWmsEyVfvCOG6+OmRdxDfKw/vDiywltfSgwohpRm9qdr/7AAUWOQG37EpuDqS0XpgmKLFjx6Bj4T6tZdtXK60ZHR3U6XXl5OcPM5CEe6Is4hrxFRQa9mg0ncgy1yQiWd/Dc9utr5D5HWRmv4B7/8+fPXbe2XfYNc4IlCVGUf8hz361rr6Csq82+c36PP1RUXKJXM+FcwwYyguWznb9xeXlNdTVMq0aM8ejoqM/nE0WxubmZzqMz8Y2Og/KGhCUKzSLBLZPdBgBbN23M/8RFj2d4+Oe/tskjatUKyjYgTen4GvS5AVwAbRa4uEMsnYzhREgCJhgKmSyNvaffioYDNMs2tKy9eQl3XWOWYM7ccm0SRzv6xmCirZcTmkKihAHgxoaC2crHt37yDACwDC1f4RLkuI8MQyFBwgBwx83rly25wpi3zJ9e2p0sXBIKjnBDHLTXmcbdxuwBLJjwsJLxaKj3wIrmxtXXX/9OSr8ayNXIMbSQsxoRCBgAYLml/IO3bpv5sqIonjt3Tq1W19XVXe7JRiKRk90XugcEX6Ul8DZs3MSMxvqmjLUGY6XcrQzgthgrwecGgMl3rFjLDPuDfCKq0OrrkG3nzVtmeevj3PWxBz788KMgZ9YAzOq/UMVRsUQaYwj4PZ/54KqZD37u6Ki8oVFQsdQsZsjQKuhoSkynkpxCec/a3DNkeIaH//3XttI28D/fFWdc/ooNa0AeaTtQevc2OLz3LHDLq6DvFOjjk53dkcCQgkFSOhWIJAd7T8XGgiptQSoe3/KBjzWoA7de/Ybh4sl0n86Bvkj3+Z6SknKTsbjnwu6Zgy8ZD8t+an+VQbF10ya1Wn1l5f7ppd0pXWMsOuax20dtnbCpHV5wlK3K4jzKjAtWIjZ8+tX33bSlvu4dyeWc02WNnDnXU1xSVmkM9lyoletQbsJPT3nPeFjOsweMath6002FBdqZr59KpQYHB+XtjGwJgpBOp3U63Ynucxcu9A7Z47C+yb53ePttbcGECK6uwOiG4p2uY991rvn8vXD4mQAAMBuKy8HvOmbv5Nd8fr2sX8Vaxm23IoqVAMbOv/G1L34+/xtPJBKJRGLQbh8e9g4pmjzDw1OzbbLNWpEdLSt5nAM0TSuLqjdVjLU2L5v6rSiK4XAYIfTa3jdOdp+vWbJNluYNm9hArDebNGe3waBlHHYbwoJEqxN9e/7vP31uZqtCY5FnT8TlbbWCiucnjnw6IcRDQLHB0RE+nRrxusKjXgBYs3nHh9aWW6ry7TK6YhbVbA3TQQAYsGv/r3xpo2Ae8Dx92eDLRWfhWeepT+WO23b8wxe+AgA8z7OsAvYfBT0MD3ph8NQr2Y6naFoSRQD4/rt1rDXGGAN27T/oK5eg0+kzg/cYeDo6+2rHBw8Yhdez1CSFJjySmVAoFNM7XjHGPM8DQP/AYDItttz9NwDQ8jEwaBg2Jky8tJadsv60bYCJF9tYaWlpA4DJzBjEqqVUBKmKZ7AhlUpZrdbMaCpZNxOJhNFoDIXCaUGIx0+CSwAX+KoAXK4AwCiA/1BeeXaSJKhUagohSbgo7dbtdodCIYQQwzA6nU6UJLWS6z60B9Y32buH6yw3hBIArmfOn9pQvNN17Fnnms+vB5cLxpOB4vq7m8J7AbZVjScDATAIKE6FgcqZSAMA+sKChzYX/PYPfwaA3/3xBYQohPJyX0RRoGlGFMXmjR9Yvm5LbCwEgOsKhHlQK1jcHhYA/PCZlwGAQpC1LUNNaYYghDFGAFBXXrBz26Z5s3BBMF6NFBayhbCmViMgDBgBgN/V9/Uv/ONcGfDzF7rKa5YAwLnjh3hRkvKY21vCmEKIYemWlTdgSfK6Bx74wPqsR/p8Pr/f39zcnLVZKvd1PtXlTfJXaLxORQ95/Rhjjc6QGXQVj8c9Hk/DlHxnuaCf7vfKu8UaNhCbRZEGLTM0GklGw+qC4s9sr57hSIxxMpkMBAIKhWLPvjdtDlfpsk3gcHKUYJMkU7af8KzErJ3FFTWxpFBfWdpcX5W/qe+ExexhHeiLFFU3FhUZ9GpHz4Wamdsyk0F3W8/Q8HD++VCLnj2nh4qqlhYVl+jV9p7XpamZZdP7djNNwgK9weVyVVXNzf/xA7s2AMDe8+HKuqbiEuOY741ocuvU5zj9gRYq6bGkCAAjtrO3rFta2LwqmUwqFIqMKkWj0aGhoVQqpdfrW1paLle0fPzHN5TZHE4AsEY1gPMNYjEICxgBhrZWNUVRtebJgQdWq/WSFBm5oFstaQD41MOPfPlbTxjULIswj3MUxlKYlxBg+MXPvvyTxx7NeozL5QqFQnInFUKI4ziEkEKhECWJYSi8v/+oRcHsfUmw7DS153Vr8nW2rpvvkQyL2cM60Bc5cvRIibFc5XjTWr7e6BhvyxjinX217ZsBes6nlz0w/nuSESzP2Tf1anrzhvXvcPzdoqHLGnnr6NGi4hKN60BXWgJbeev93JkfOMpWmQUAsDmY2nKvD1rv3G66OK1Br2HWr77eZMrzBzs3e8+HjxzqNNcpGGt8wIWNd8KZJ94uK73deCd4OmFN+0Wxv4xg+XveZJG4YlkTRVGCILAsK0kSxlir1ZpMplllsb3Z2eWnq2zW80VFYYS2z6yYAKDhqFhaAoBAzx4Vx61ZtTIYDCoUClEUzWZzQUHB5Qra0xM+deJoZXU9Sp+IpnJKMzOWFADAeeJVnZpd0dJMIZROp1mWxRhjjOWknBkyiruskUFrbyrZ5/NWG8uBZVlLw2SgrbfndGa73FSt0RTQDJOMRW6+bs6ebP4sZsG67SMff/Cfv82wnE7F5J/WEAsH7tpgISlCGXZ+6L6HvvRfLMfpVPREWsNlezwzghULB+5cXzvbOXZm5r9ffKusqo7luEIVPZYQZzADpgiW/cLJh+7aMicGHDx8zEdXhoJ+ySqA2QkOOAPQCg6fz+w1Vt/SfmlvSUawTr3yi/v+6sOG4mKEkCqPKXf29IRttoHySiF2dGjAnVOaxwXLfe7g6qVVjUuXAIBSqcyn8g8eOVparreejRxzWGsVS2ypPhMDHgGgfbtpxumYAGDHcn3O6885i1mw3n/PRz/71ccRQhoWxfgctymIwVPOTiGVHj4V+dHX5iz4sjiIRGMAgCWMqBw6nsl7KtBqrp4l+WTEYCwhRM25JclU+oc/e1KeTYxhGAD/iL+05DLLldAUJUoSAPyfh/9utgV95wc/AQCOY9JpYeZSWIblBf7KSnn8f5582f7n27UfOel2LKlDfQO4NnjhQNHOuzbVexyXKuMlLC3G9aY5HgeWk8UsWAf6Im8fP1ZqNFnMyZ7XxZmDL76xs4fOPVvAFFqgpbSoYMuG9aWlpTPPBkEgLHR6rQNFRbpjb9k9UGepo1KCBAAegHwae9fEw1rMQXcAMJSU6Q2lrv2/PQMSPG2FKcEXT4ccfLGCebsJgKW44eGAWIOB1TUvrS8tLc1nNggCYUGztL4OALha2oIQQyMEFADUAZ6aDExRVKaXAWNJwuO9ANdkrOhi9rAA4Kn/eQ4AKIqaeADD3uHyshk7AD9+7z3zYRmB8O7g8See1NauTcajBWp1urI2z0D6iNuq5b0VxpIb1s3rGm6L3MMi6kMgzEyhTq9QabQFBamzL/sG7T6oFsxOk6PaY3ZCR+fopo8YHNbpyVmcUjMyPHrrtpvm2dpF7mERCISc/Oq5P4aDAUnCVJZOFZ/fZyydMv9vOp3mOG7Dhg1rVmSf1umqQgSLQCAsGMiqvAQCYcFABItAICwYiGARCIQFAxEsAoGwYCCCRSAQFgxEsAgEwoKBCBaBQFgwEMEiEAgLBiJYBAJhwUAEi0AgLBiIYBFmghekC4MuXpjFAnkEwtWDjCUkXJa/HDh5whZR643xkH+pAdY1V9NU7lWaM0iShBCSp0yqMc/TqiqExQ0RLEJ2Tp63v9IdOv3WHp5PNa7coCsyqjhg1LmXFJ5KLDyKEFIXFitZ6uMbLjPFL4GQN6RJSMjOBdcIxlhTqC+vakjGIoiihs7ZfRPfyivCX8zg1A997sGhZFyJ0hyk+FQ+K3sSCLlZ5BP4Ea4YlkKAIOgfLi6nj+x7sa55ldHYpIDB7ue7QuAMV29c44JA1Y1NlQAw2P38c+Hqtboq8D/fNb4YsgtQGRcLxRBCajXxrQhzA2kSLmzOWZ2DQ6OlOu261jmedX5oJPSL17qHh9x8KllV34xFQcchxCokLFFoZsfc6fPg0opqAFRcWkZRFM0wzzz2yIu//dXcWkh4D0IEawHzu9ePOlMFaq0+nUqMWt++rkrNMLMIigOAIAjyasC7dt4y/dtem+eNk/YYD1TCv339isN9epMZgkNUUYUEl1k6dDrXZG0VwmKFCNZC5fQF+4sn/B7HBZZTVtY0qjSF4RGnBDRUWuT5bH1uMFbmuAiShGQ0yItYW2T82xuNmRlyBUHo6+sTRTEej+t0ulPnehKJZFxZxfnOHg6Y2lZWeTp+K6+e7WM2r7xhpiIUge6tmzbOxR0T3hVIGL926PRYUtJy6Jb1K2h6XuPgRLAWKi/sP3GwZ9hUszSdSjzxb3+3fN1NqzduAE8Uqqr8hybCTFAFVQBguZxyJcb8DJLEdIouMD64uYLjOPnzSCSi0WgySwdf6O2lOWVftBCmrO2cJ7P1sB5/4kkAQGh2/5k8zzMMI6dQfPbBT82qxPcg3378xwAgSRKWMD0br5ymqKGUtrL5hqBviOWUSjH0yEe2zadmEcFaqLxx+MxbTsHrsipVhRynKDKaYsHh0uqls7qIyCeEWIAXsLak6pPri2TB6t/dD9kWjt1z1gcAFMZSztXo8PhCdrFIaNf6HCbJK7BZrQPBSPT4+aSv0gJu0CkZhaHP5x6X2glvcdDntshnZSTY5x6UnUo+GcOpiERxJSWl999QkncdvOf4VdcRe6cEbRZjNjd86ieZusWSlAwNSxjz6VSh0ezs7/F5BhmWW9Latqwo8f4bV82b8aSXcKGyZd3ykwP7ahtXYUlSawsHu494zu57OxQEkPLJVpFXcjeUGrVKFgC+/+g3pnzZN33h2C//678bl65f2nKdRkEP9veDuc40Zd3sS5kQtMCI/5nfnVm1vLm5edn0o4aHh0OhUDAYNBgMx0+fQQxzuvM43b4DXK6EwpuwrS4W9vqgDlyuPqgyVloAXIFhEAUXAPgPOTLdkVAJAMAnoulknGOSWJpdstjl+Pqjj8kbHMumeT7/E5VKRTKZAgCW4778uc/MiTFzix9coee7+sZ7e6ugDcBtMVYO+tzgd7mm120qEcVSCgFKpZN9Z484+7s1uuJwwFeztDWSmNdREESwFioUQp/50OaXOk4EE5I2FWpeU+1e/vcYYz4RFaos8ipyE3HxLMpCIZAwxMeC2H92x81b4/E4TdMsyw4MDNTdcst0wUMUVaDTI4TcHa/7BMloBk+nE8D5aoejrHajYHZCR89obX3rxQvYsQpVKpSsqq6Wd/v7+6GhAXb32wEAoGZHQ1P5+Kq2CKFgJBa85wYAgEpLkZoJxgUAi7w7scrUjU2rYfzDNpAzv4xt4z4Xq9IylCRRCkRdYQsFYywIAgC8ffz471/crWu5VS56wpjs3sd0SrSsa2gIhBSlLHzsR0888ukH8zRgwCZXDEiiRM2mnSVJ0kT7HdXV5uwKsbS01UDbxZ9Vghw6MFZaptetQqVNpKIIIaUScVxSpdVJggAAALhIy+Zv5zuHCNYChmPoO7eukbd/9tRvFJWr4rGo2ucDCR/rODgKjkBt+5IOB1NbLpjrsq7oG49HqkpK9uzbt2rFCr/fr9Vqa2pqqGwv/De/8oW7P/YAACCKwpIEXZNfuTynxnc9/a6uVwGAF0SaoigK3bJ9+9989L7JQ/sAGvoB9gMAgGmqH7dkyRIAWAfw2I+eAIB9x45LkgQIQa6QhSBJDEWJkrSxba0cw7r/tuwC0d/fb+8DAKjZ0QC7+2FHA+dwxOPxzAE8z4uiWFBQEEmktCoFuFzgAl8VpPyewZSpuC279zEdjLGUiogSReFonku5S5KUSvOv2ZXybrGGCcSE/E4FADBo2aGRcDoRVWmLPl2b4+C/3lD64yefAoBILH7k6HGEEMZS1od+EePPAtWv2rxq463R8ChNsxppbPv6Lfnb+c4hMazFw+6zIQBgacSLuZ+p7GEJAn9zo1alUuU8/js/fKK8ZYuhtCz41l6raZsJBsABYLZ6oN7keM0D72PMYAQwmeumnhX0OmHkwvtv3U5RFMdxHMdJkkTTOaK8X/z6N5dvukup0qajRxLprSbzZArFDLkUflc/Cllv3baV4ziFQqFUKmWPief5VCql1WrtezrsYAMw1exYArv7YMeOy6WuORyOx3/2y4btD8m7GQ9rgkGfG4yVlsvZX6JlnZ5hCvOUotAQ77n3jp1ZDwsGg263GwAKCwsPHD7S39dfVrvFV2kJvA0bNjGB2PQQ3iSX+HpFjUslxwDFchgx8d69X/zcw1lLxBiHQiEAONB1KBga48eooZr35fkoM50tR17+xaabbhmJpIo0zM03rLhcJVwliIe1eLiqGU+jowHROVhcYgTodzkYky19pLb+dqgHM3g6wAUHVRCz2erX3X+RK5eKhw26gqPH3l7W1Oj1etVqdUVFhU6ny1lcPBpNJZMqdx2Y9ns64QwAOBw+n9lrrL7kRcogJCJapbK3t6+uzjI4OKhQKCiKktu5KpWKoqiGHdsmFeqyYgUAYDabv/ON//eLp5+Vd3//+h7AQNOMKObwehiGFQQeUWjHzdsAQKlS3XvXLvmr6c3hhqIivV6fSCQAgKOoeDLdfWgPrG+ydw/XWdpCCQDXM+dPbSjeCX4XGCvB5wb/oa4QxPV3N4X3AmyrGu//dQE0Ai8hhk8K6LK/PZFIxO12C4JQWloaj8cxlgSh3+VQ5PkoM2CMb1rXMnM9XD2Ih0XIC4zxi2+7EUIcg9LCxP+M0zYEtRXVtiFnbUX1RQfLrTOO5d633JC7uTGN/b1jAKBgUYqXyxpw2+sqa7IZBhgBAgAsSVua5l6yX+0O2ay9lTV1EX9HPL0VYNLFm+7uFSrpsaQIAK4zbzZWGVqam0VR5DhOqVS69rlgB8Du/XYAAFNNNv/ud8dG5Y2Qz5ngRSxJueJxrlEPNpiq1RylKzWnUgmFQvXhNQa4WB8zTeBMifID6uwbkwAwxm6nAw05vFBdZnKGg43NKyZDc26nTd6gES42Vvq9no9snW+vairEwyLkBUIIURzLcSqOcve/CrDdZAZoaDEDALSYL+OvYACfz1c+EVnPn81LCy/+YCXMLmFjLonHY6lED9gYn+dZr7ECHA6fb6PxTlmwsrt7IKZHAsFYLIYxTiQSNE03yAKVLV8kg6w1r58LM4YSlUYb9r4RS80skfWFLeMSGRwauGfzcpZlx38tJsOFsp90Uc/v+d5+lVYR6Rs55rCamHqPYDUxEBEuRIzbTUmIpSY7/vTG8fLkJqHeaBYkYK7dnAnEwyLkxdcffaxqxU2JeLw4eMIulAnmeug4yNSavTZH2aaNRgBwAIAVLu4lBAAueDY0OnrX7buuhdVzwB33f/Kfvv59AFCwVIqXAKwue31VNl8PAJQsleQlAFDG3de3Nl1Zia+fCx8+1FG3RE31xQbdHq+xohUmJLIT1rRfJJEZny5woQOJqealSzDGDMMUFhaazWZ0mYy5n/76N/8b+NMmfEufN1hbCzYb1AYvHCjaedemeo/j0iJkMjGsWoNiWUXuoOdVgggWIS/2ds4VtawAAAJ+SURBVHQG2GqVSl2gVswq0105cnrzpvYraBW+Z3n9XDgUDmkLdAVKOpIUs2alZMgIVnh44K7NrSybV5LBmXPn1QWK3jNBD0xe2TPhjGUlI1gbLcpCjXJWdzSHEMEi5MsTf9hTY1kKWASU72COZ3/+2FM//t5VtWpRcrbnPACIopRz1EsmA2v5slk7dPu6/SIAANA0mylIkiRBmMySZVlOdtNYikqkUhKWbm65lpMFEcEi5MW//Nt/mJZvsjQs0yjs/f21U5MMZp62wX38L63LmtrWrp03Uwn5sO9A1yhb5bT1FxToUE39DL6VTMbD8p56xVxZsXXL5nkwcjok6E7IF3lCd3dHp88kQafTZ64WHEfOvGANrGrf7AAfs9kovD49hoUxVqvV18RgwszQNFOoLwHbfp8TQTtAJ3jMTpMD9p/4S/Guvzc4rKZpTxMAvD7vlvb118BcACAeFiF/HvjHL6nUGpqmRDFHDEsURURRFEJ8MvrTx/5jfswjzJZX9u5/6+gxCWNJFOVp0aYwEhgtKZ4yKJOhaUEUAeBrX/z8vFp5MUSwCHnx+xdelkqaC3VFas7W/4YE7ZNZhTOMWAQA3nFox/ab8wwGEwgzQ5qEhLw4fvJ0YQ1ct3qDu6PzDJS1Ahx7+s8DwdKlq8wCgGdixCJ0XtoqVKjUf/nf3Xfu+uA1M52wiCAeFiEvRFF6pTtEUdSUCfxm6m7PsK5CMhiKr76BhPcERLAI+ZJOp2HKsJt8YFk2/4MJhJwQwSIQCAsGkn9MIBAWDESwCATCgoEIFoFAWDAQwSIQCAsGIlgEAmHBQASLQCAsGIhgEQiEBQMRLAKBsGAggkUgEBYM/x9hy/IA6LQIuAAAAABJRU5ErkJggg=="
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="5848.8 955.383"
#!   DOC_TOP_LEFT="-2337.52 -2204.27"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="90639fd1-b580-4e93-8b17-acc71470e358"
#!   FME_DOCUMENT_PRIORGUID="d936b975-c27f-4432-aa88-8eea97a81faf,b03ec714-2fcd-4679-9aea-d4abd2118835,de105467-a170-47c8-a138-45085956ef74,674b7429-f21f-4865-81dd-ace75591f5ee,ca17443e-dad4-40c8-b585-95755ea17561,88ad49e4-e559-439e-a5cf-fe98413d1f2f,531c373c-d22b-4785-b994-0df8b53c04b5,efcaff14-29e8-4c65-bcd3-5023c865f281,7181791b-7be7-4f8c-b333-d2216bde3eb4"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-03-21T17:34:16"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="NO"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="-1703.14 -978.135"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="FeatureReader_2/考试工程.xlsx"
#! />
#! <DATASET
#!   NAME="FeatureReader_3"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_3"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/地块试题"
#! />
#! <DATASET
#!   NAME="FeatureWriter_2"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter_2"
#!   DATASET="FeatureWriter_2/考试工程.xlsx"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI INTEGER NUM 创建份数"
#!   DEFAULT_VALUE="400"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER shp地块（rar压缩包）"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\耕地图斑"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Excel_Files(*.xlsx;*.xlsm;*.xls)|*.xlsx;*.xlsm;*.xls|Microsoft_Excel_2007+_Workbook(*.xlsx)|*.xlsx|Microsoft_Excel_2007+_Macro_Workbook(*.xlsm)|*.xlsm|Microsoft_Excel_2007+_Binary_Workbook_-_Windows_Only(*.xlsb)|*.xlsb|Microsoft_Excel_Pre-2007_Workbook(*.xls)|*.xls|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 考试工程线（excel）"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\考试工程.xlsx"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop"
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="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"
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="NUM" 
#!   DEFAULT_VALUE="400"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI INTEGER NUM 创建份数"
#! />
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\耕地图斑"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER shp地块（rar压缩包）"
#! />
#!     <INFO NAME="file" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop\考试工程.xlsx"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Excel_Files(*.xlsx;*.xlsm;*.xls)|*.xlsx;*.xlsm;*.xls|Microsoft_Excel_2007+_Workbook(*.xlsx)|*.xlsx|Microsoft_Excel_2007+_Macro_Workbook(*.xlsm)|*.xlsm|Microsoft_Excel_2007+_Binary_Workbook_-_Windows_Only(*.xlsb)|*.xlsb|Microsoft_Excel_Pre-2007_Workbook(*.xls)|*.xls|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 考试工程线（excel）"
#! />
#!     <INFO NAME="PARAMETER_2" 
#!   DEFAULT_VALUE="C:\Users\<USER>\Desktop"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_2 保存路径"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="1818.7681876818767 -1496.8899688996889"
#!   BOUNDING_RECT="1818.7681876818767 -1496.8899688996889 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="DCBH" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DKMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Id" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="MJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="QXMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SFYJJBNT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSXMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSXMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZCMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="counter"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Global"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="2431.2743127431281 -1496.8899688996889"
#!   BOUNDING_RECT="2431.2743127431281 -1496.8899688996889 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DCBH" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DKMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Id" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="MJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="QXMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SFYJJBNT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSXMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSXMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZCMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; _count SET_TO &lt;at&gt;Format&lt;openparen&gt;%05d&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;&lt;closeparen&gt; int64  DCBH SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;_FME_NO_OP_&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%5&lt;closeparen&gt; = 0&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;YJDK&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;&apos;BOOL_OP;OR;COMPOSITE_TEST;1 OR 2 OR 3 OR 4;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%1&lt;closeparen&gt; = 0;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%2&lt;closeparen&gt; = 0;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%3&lt;closeparen&gt; = 0;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%4&lt;closeparen&gt; = 0&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;YJDK&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS3___&quot; buffer"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="3081.2808128081288 -1465.6396563965639"
#!   BOUNDING_RECT="3081.2808128081288 -1465.6396563965639 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;&lt;u5730&gt;&lt;u5757&gt;&lt;u8bd5&gt;&lt;u9898&gt;"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;utf-8&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;Output&lt;space&gt;Dimension,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Elevation&lt;space&gt;as&lt;space&gt;Measures,SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Compressed&lt;space&gt;Shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;space&gt;Type&lt;space&gt;Storage,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Spatial&lt;space&gt;Index:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Split&lt;space&gt;Dataset&lt;space&gt;into&lt;space&gt;2GB&lt;space&gt;Files,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;Surface&lt;space&gt;and&lt;space&gt;Solid&lt;space&gt;Storage"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="utf-8"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;u8bd5&gt;&lt;u9898&gt;:Output,ftp_feature_type_name,&lt;u8bd5&gt;&lt;u9898&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,Id&lt;comma&gt;long&lt;comma&gt;DCBH&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;DKMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;QXMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XZMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;XZCMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;DLMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SFYJJBNT&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SSXMDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;SSXMMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;PGSSXMDM&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;PGSSXMMC&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;254&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;MJ&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="11"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="262.50262502625014 -1955.1477156489091"
#!   BOUNDING_RECT="262.50262502625014 -1955.1477156489091 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;u8003&gt;&lt;u8bd5&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7ebf&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="序号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="调查编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="区县" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="乡镇" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="行政村名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程设施类别" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程一级类" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程二级类" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程三级类" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="宽度" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="面积" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="是否实地存在" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="是否高标项目建设" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="所属项目名称" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="竣工时间" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程设施使用状态" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程设施结构状态" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程设施功能状态" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="权属类型" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="是否办理管护手续" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="管护主体类别" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="管护负责人姓名" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="附属设施类别" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="附属设施数量" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="附属设施完好率" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="工程位置（wkt）" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="考试工程线,&quot;序号,工程名称,调查编号,区县,乡镇,行政村名称,工程设施类别,工程一级类,工程二级类,工程三级类,长度,宽度,面积,管径,是否实地存在,是否高标项目建设,所属项目名称,项目编号,工程编号,竣工时间,工程设施使用状态,工程设施结构状态,工程设施功能状态,权属类型,是否办理管护手续,管护主体类别,管护负责人姓名,附属设施类别,附属设施数量,附属设施完好率,工程位置（wkt）&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="考试工程线,&quot;int16,varchar(25),varchar(18),varchar(9),varchar(9),varchar(9),varchar(18),varchar(27),varchar(21),varchar(19),buffer,buffer,buffer,buffer,buffer,varchar(3),varchar(37),int64,buffer,buffer,buffer,buffer,buffer,buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(164)&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%考试工程线"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(file)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="&lt;u8003&gt;&lt;u8bd5&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7ebf&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="考试工程线,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,XLSXR"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXR_READ_RASTER_MODE,&quot;OPTIONAL LOOKUP_CHOICE None%Attribute%Geometry&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Embedded&lt;space&gt;Images&lt;space&gt;As:,XLSXR_FORCE_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_STRIP_SHEETNAME_SPACES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SCAN_MAX_FEATURES,&quot;OPTIONAL RANGE_SLIDER 0%MAX%0&quot;,XLSXR&lt;space&gt;&lt;u626b&gt;&lt;u63cf&gt;&lt;u7684&gt;&lt;u6700&gt;&lt;u5927&gt;&lt;u7684&gt;&lt;u884c&gt;:,XLSXR_READ_BLANK_AS,&quot;OPTIONAL CHOICE Missing%Null&quot;,XLSXR&lt;space&gt;&lt;u8bfb&gt;&lt;u53d6&gt;&lt;u7a7a&gt;&lt;u5355&gt;&lt;u5143&gt;&lt;u4e3a&gt;:,XLSXR_APPLY_FILTERS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Apply&lt;space&gt;Filter&lt;openparen&gt;s&lt;closeparen&gt;:,XLSXR_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP XLSXR_EXPOSE_FORMAT_ATTRS&quot;,XLSXR&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,XLSXR_CASE_SENSITIVE_FEATURE_TYPES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_READ_FORM_CONTROLS,&quot;OPTIONAL CHECKBOX CELL_VALUE%NONE&quot;,XLSXR&lt;space&gt;Read&lt;space&gt;Form&lt;space&gt;Control&lt;space&gt;as&lt;space&gt;Cell&lt;space&gt;Values:,XLSXR_EXPAND_MERGED_CELLS,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;&lt;u6253&gt;&lt;u6563&gt;&lt;u5408&gt;&lt;u5e76&gt;&lt;u7684&gt;&lt;u5355&gt;&lt;u5143&gt;:,XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_ALLOW_DOLLAR_SIGNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_TRIM_ATTR_NAME_WHITESPACE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Whitespace&lt;space&gt;From&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_TABLELIST,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP APPLY_FILTERS%SCAN_MAX_FEATURES%TRIM_ATTR_NAME_WHITESPACE%TRIM_ATTR_NAME_CHARACTERS%READ_BLANK_AS%EXPAND_MERGED_CELLS%READ_RASTER_MODE%READ_FORM_CONTROLS%SCAN_FOR_GEOMETRIC_TYPES&quot;,XLSXR&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,XLSXR_TRIM_ATTR_NAME_CHARACTERS,&quot;OPTIONAL TEXT_EDIT_ENCODED FME_INCLUDEBROWSE%NO&quot;,XLSXR&lt;space&gt;Trim&lt;space&gt;Characters&lt;space&gt;from&lt;space&gt;Attribute&lt;space&gt;Names:,XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SKIP_EMPTY_ROWS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_USE_CUSTOM_SCHEMA,&quot;OPTIONAL RADIO_GROUP 2%Automatic,NO%Manual,YES&quot;,XLSXR&lt;space&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u5b9a&gt;&lt;u4e49&gt;,XLSXR_EXCEL_COL_NAMES,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%使用网络验证%PROMPT_TYPE%NETWORK&quot;,XLSXR&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u7f51&gt;&lt;u7edc&gt;&lt;u9a8c&gt;&lt;u8bc1&gt;,XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS XLSXR%Source&quot;,XLSXR&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,XLSXR_SCHEMA,&quot;OPTIONAL STRING&quot;,XLSXR&lt;space&gt;&lt;u8981&gt;&lt;u586b&gt;&lt;u5145&gt;&lt;u7684&gt;,XLSXR_CREATE_FEATURE_TABLES_FROM_DATA,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_SCAN_FOR_GEOMETRIC_TYPES,&quot;OPTIONAL CHECKBOX Yes%No&quot;,XLSXR&lt;space&gt;Scan&lt;space&gt;For&lt;space&gt;Geometric&lt;space&gt;Types:,XLSXR_SCHEMA_HANDLING_REVISION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;,XLSXR_CONFIGURATION_DATASET,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXR&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_ALLOW_DOLLAR_SIGNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_APPLY_FILTERS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CASE_SENSITIVE_FEATURE_TYPES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CONFIGURATION_DATASET" PARM_VALUE="C:\Users\<USER>\Desktop\考试工程.xlsx"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_CREATE_FEATURE_TABLES_FROM_DATA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXCEL_COL_NAMES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPAND_MERGED_CELLS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_FORCE_DATETIME" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_BLANK_AS" PARM_VALUE="Missing"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_FORM_CONTROLS" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_READ_RASTER_MODE" PARM_VALUE="None"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_FOR_GEOMETRIC_TYPES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCAN_MAX_FEATURES" PARM_VALUE="1000"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA" PARM_VALUE="&lt;u8003&gt;&lt;u8bd5&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7ebf&gt;,0&lt;comma&gt;&lt;u5e8f&gt;&lt;u53f7&gt;&lt;comma&gt;number&lt;comma&gt;1&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;1&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;25&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;2&lt;comma&gt;&lt;u8c03&gt;&lt;u67e5&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;3&lt;comma&gt;&lt;u533a&gt;&lt;u53bf&gt;&lt;comma&gt;char&lt;comma&gt;9&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;4&lt;comma&gt;&lt;u4e61&gt;&lt;u9547&gt;&lt;comma&gt;char&lt;comma&gt;9&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;5&lt;comma&gt;&lt;u884c&gt;&lt;u653f&gt;&lt;u6751&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;9&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;6&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u7c7b&gt;&lt;u522b&gt;&lt;comma&gt;char&lt;comma&gt;18&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;7&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4e00&gt;&lt;u7ea7&gt;&lt;u7c7b&gt;&lt;comma&gt;char&lt;comma&gt;27&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;8&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4e8c&gt;&lt;u7ea7&gt;&lt;u7c7b&gt;&lt;comma&gt;char&lt;comma&gt;21&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;9&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4e09&gt;&lt;u7ea7&gt;&lt;u7c7b&gt;&lt;comma&gt;char&lt;comma&gt;19&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;10&lt;comma&gt;&lt;u957f&gt;&lt;u5ea6&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;11&lt;comma&gt;&lt;u5bbd&gt;&lt;u5ea6&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;12&lt;comma&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;13&lt;comma&gt;&lt;u7ba1&gt;&lt;u5f84&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;14&lt;comma&gt;&lt;u662f&gt;&lt;u5426&gt;&lt;u5b9e&gt;&lt;u5730&gt;&lt;u5b58&gt;&lt;u5728&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;15&lt;comma&gt;&lt;u662f&gt;&lt;u5426&gt;&lt;u9ad8&gt;&lt;u6807&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u5efa&gt;&lt;u8bbe&gt;&lt;comma&gt;char&lt;comma&gt;3&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;16&lt;comma&gt;&lt;u6240&gt;&lt;u5c5e&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u540d&gt;&lt;u79f0&gt;&lt;comma&gt;char&lt;comma&gt;37&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;17&lt;comma&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;comma&gt;number&lt;comma&gt;12&lt;comma&gt;0&lt;comma&gt;&lt;comma&gt;18&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7f16&gt;&lt;u53f7&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;19&lt;comma&gt;&lt;u7ae3&gt;&lt;u5de5&gt;&lt;u65f6&gt;&lt;u95f4&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;20&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u72b6&gt;&lt;u6001&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;21&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u7ed3&gt;&lt;u6784&gt;&lt;u72b6&gt;&lt;u6001&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;22&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u529f&gt;&lt;u80fd&gt;&lt;u72b6&gt;&lt;u6001&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;23&lt;comma&gt;&lt;u6743&gt;&lt;u5c5e&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;24&lt;comma&gt;&lt;u662f&gt;&lt;u5426&gt;&lt;u529e&gt;&lt;u7406&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u624b&gt;&lt;u7eed&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;25&lt;comma&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u4e3b&gt;&lt;u4f53&gt;&lt;u7c7b&gt;&lt;u522b&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;26&lt;comma&gt;&lt;u7ba1&gt;&lt;u62a4&gt;&lt;u8d1f&gt;&lt;u8d23&gt;&lt;u4eba&gt;&lt;u59d3&gt;&lt;u540d&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;27&lt;comma&gt;&lt;u9644&gt;&lt;u5c5e&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u7c7b&gt;&lt;u522b&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;28&lt;comma&gt;&lt;u9644&gt;&lt;u5c5e&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u6570&gt;&lt;u91cf&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;29&lt;comma&gt;&lt;u9644&gt;&lt;u5c5e&gt;&lt;u8bbe&gt;&lt;u65bd&gt;&lt;u5b8c&gt;&lt;u597d&gt;&lt;u7387&gt;&lt;comma&gt;string&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;30&lt;comma&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u4f4d&gt;&lt;u7f6e&gt;&lt;uff08&gt;wkt&lt;uff09&gt;&lt;comma&gt;char&lt;comma&gt;164&lt;comma&gt;&lt;comma&gt;,1&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,NO&lt;comma&gt;NO&lt;comma&gt;1&lt;comma&gt;C:&lt;lt&gt;backslash&lt;gt&gt;Users&lt;lt&gt;backslash&lt;gt&gt;djz&lt;lt&gt;backslash&lt;gt&gt;Desktop&lt;lt&gt;backslash&lt;gt&gt;&lt;lt&gt;u8003&lt;gt&gt;&lt;lt&gt;u8bd5&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;.xlsx&lt;comma&gt;&lt;quote&gt;0&lt;comma&gt;0&lt;comma&gt;30&lt;comma&gt;2000&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;NO&lt;comma&gt;NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SCHEMA_HANDLING_REVISION" PARM_VALUE="2"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_SKIP_EMPTY_ROWS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_STRIP_SHEETNAME_SPACES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TABLELIST" PARM_VALUE="考试工程线"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_CHARACTERS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXR_TRIM_ATTR_NAME_WHITESPACE" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_USE_CUSTOM_SCHEMA" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXR_XLSXR_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="13"
#!   TYPE="Counter"
#!   VERSION="4"
#!   POSITION="1735.0154759909408 -2108.271246884221"
#!   BOUNDING_RECT="1735.0154759909408 -2108.271246884221 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="调查编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="区县" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="乡镇" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="行政村名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程一级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程二级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程三级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="宽度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="面积" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否实地存在" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否高标项目建设" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="所属项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="竣工时间" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施使用状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施结构状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施功能状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="权属类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否办理管护手续" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护主体类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护负责人姓名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施数量" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施完好率" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程位置（wkt）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CNT_ATTR" PARM_VALUE="_count"/>
#!     <XFORM_PARM PARM_NAME="DOMAIN" PARM_VALUE="counter"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GRP_CNT_ATTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_ATTR_NAMES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SCOPE" PARM_VALUE="Global"/>
#!     <XFORM_PARM PARM_NAME="START" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Counter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="14"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="2413.1472573087553 -2133.2714968867208"
#!   BOUNDING_RECT="2413.1472573087553 -2133.2714968867208 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="调查编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="区县" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="乡镇" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="行政村名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程一级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程二级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程三级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="宽度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="面积" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否实地存在" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否高标项目建设" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="所属项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="竣工时间" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施使用状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施结构状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施功能状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="权属类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否办理管护手续" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护主体类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护负责人姓名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施数量" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施完好率" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程位置（wkt）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; _count SET_TO &lt;at&gt;Format&lt;openparen&gt;%05d&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;&lt;closeparen&gt; int64  &lt;u8c03&gt;&lt;u67e5&gt;&lt;u7f16&gt;&lt;u53f7&gt; SET_TO &quot;FME_CONDITIONAL:DEFAULT_VALUE&apos;_FME_NO_OP_&apos;BOOL_OP;OR;COMPOSITE_TEST;1;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%5&lt;closeparen&gt; = 0&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;XMGC&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;&apos;BOOL_OP;OR;COMPOSITE_TEST;1 OR 2 OR 3 OR 4;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%1&lt;closeparen&gt; = 0;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%2&lt;closeparen&gt; = 0;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%3&lt;closeparen&gt; = 0;TEST &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;%4&lt;closeparen&gt; = 0&apos;&lt;at&gt;Value&lt;openparen&gt;_list&lt;opencurly&gt;0&lt;closecurly&gt;&lt;closeparen&gt;XMGC&lt;at&gt;Value&lt;openparen&gt;_count&lt;closeparen&gt;&apos;FME_NUM_CONDITIONS3___&quot; varchar&lt;openparen&gt;18&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="17"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-2337.5233752337517 -1378.1387813878139"
#!   BOUNDING_RECT="-2337.5233752337517 -1378.1387813878139 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="$(NUM)"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="222.50076675937635 -1248.8881686589084"
#!   BOUNDING_RECT="222.50076675937635 -1248.8881686589084 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="DCBH" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="DKMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Id" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="MJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="QXMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SFYJJBNT" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSXMDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SSXMMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XZCMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XZMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="试题,&quot;Id,DCBH,DKMC,QXMC,XZMC,XZCMC,DLMC,SFYJJBNT,SSXMDM,SSXMMC,PGSSXMDM,PGSSXMMC,MJ&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="试题,&quot;int64,varchar(50),varchar(200),varchar(50),varchar(50),varchar(50),varchar(50),varchar(50),varchar(50),varchar(254),varchar(50),varchar(254),&quot;&quot;number(31,15)&quot;&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE="DCBH DKMC DLMC Id MJ PGSSXMDM PGSSXMMC QXMC SFYJJBNT SSXMDM SSXMMC XZCMC XZMC"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%试题"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="试题,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="21"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="3031.2803128031283 -2073.2714968867208"
#!   BOUNDING_RECT="3031.2803128031283 -2073.2714968867208 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;&lt;u8003&gt;&lt;u8bd5&gt;&lt;u5de5&gt;&lt;u7a0b&gt;.xlsx"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_FILE&lt;comma&gt;No&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;comma&gt;TEMPLATE_SHEET&lt;comma&gt;&lt;comma&gt;REMOVE_UNCHANGED_TEMPLATE_SHEET&lt;comma&gt;No&lt;comma&gt;MULTIPLE_TEMPLATE_SHEETS&lt;comma&gt;Yes&lt;comma&gt;INSERT_IGNORE_DB_OP&lt;comma&gt;Yes&lt;comma&gt;DROP_TABLE&lt;comma&gt;No&lt;comma&gt;TRUNCATE_TABLE&lt;comma&gt;No&lt;comma&gt;FIELD_NAMES_OUT&lt;comma&gt;Yes&lt;comma&gt;FIELD_NAMES_FORMATTING&lt;comma&gt;Yes&lt;comma&gt;WRITER_MODE&lt;comma&gt;Insert&lt;comma&gt;RASTER_FORMAT&lt;comma&gt;PNG&lt;comma&gt;PROTECT_SHEET&lt;comma&gt;NO&lt;comma&gt;PROTECT_SHEET_PASSWORD&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_LEVEL&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_PERMISSIONS&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING&lt;comma&gt;yes&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE&lt;comma&gt;CUSTOM_NUMBER_FORMATTING&lt;comma&gt;ENABLE_NATIVE&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXW_PROTECT_SHEET,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP PROTECT_SHEET_PASSWORD%PROTECT_SHEET_LEVEL%PROTECT_SHEET_PERMISSIONS&quot;,XLSXW&lt;space&gt;Protect&lt;space&gt;Sheet,XLSXW_MULTIPLE_TEMPLATE_SHEETS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_OVERWRITE_FILE,&quot;OPTIONAL ACTIVECHOICE Yes%No,TEMPLATEFILE,TEMPLATE_SHEET,REMOVE_UNCHANGED_TEMPLATE_SHEET&quot;,XLSXW&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;File:,XLSXW_FIELD_NAMES_FORMATTING,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:,XLSXW_CUSTOM_NUMBER_FORMATTING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_TRUNCATE_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_INSERT_IGNORE_DB_OP,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_DROP_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_RASTER_FORMAT,&quot;OPTIONAL CHOICE BMP%JPEG%PNG&quot;,XLSXW&lt;space&gt;Raster&lt;space&gt;Format:,XLSXW_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXW&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,XLSXW_FIELD_NAMES_OUT,&quot;OPTIONAL ACTIVECHOICE Yes%No,FIELD_NAMES_FORMATTING,++FIELD_NAMES_FORMATTING+No&quot;,XLSXW&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:,XLSXW_WRITER_MODE,&quot;OPTIONAL CHOICE Insert%Update%Delete&quot;,XLSXW&lt;space&gt;Default&lt;space&gt;Feature&lt;space&gt;Type&lt;space&gt;Writer&lt;space&gt;Mode:"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;u8003&gt;&lt;u8bd5&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7ebf&gt;:Output,ftp_feature_type_name,&lt;u8003&gt;&lt;u8bd5&gt;&lt;u5de5&gt;&lt;u7a0b&gt;&lt;u7ebf&gt;,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u5e8f&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;number&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;25&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u8c03&lt;gt&gt;&lt;lt&gt;u67e5&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u533a&lt;gt&gt;&lt;lt&gt;u53bf&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;9&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u4e61&lt;gt&gt;&lt;lt&gt;u9547&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;9&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u884c&lt;gt&gt;&lt;lt&gt;u653f&lt;gt&gt;&lt;lt&gt;u6751&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;9&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u522b&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;18&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u4e00&lt;gt&gt;&lt;lt&gt;u7ea7&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;27&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u4e8c&lt;gt&gt;&lt;lt&gt;u7ea7&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;21&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u4e09&lt;gt&gt;&lt;lt&gt;u7ea7&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;19&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u957f&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5bbd&lt;gt&gt;&lt;lt&gt;u5ea6&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u5f84&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u662f&lt;gt&gt;&lt;lt&gt;u5426&lt;gt&gt;&lt;lt&gt;u5b9e&lt;gt&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u5b58&lt;gt&gt;&lt;lt&gt;u5728&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u662f&lt;gt&gt;&lt;lt&gt;u5426&lt;gt&gt;&lt;lt&gt;u9ad8&lt;gt&gt;&lt;lt&gt;u6807&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u5efa&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;3&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6240&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;37&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7f16&lt;gt&gt;&lt;lt&gt;u53f7&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ae3&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u65f6&lt;gt&gt;&lt;lt&gt;u95f4&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u4f7f&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u72b6&lt;gt&gt;&lt;lt&gt;u6001&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u7ed3&lt;gt&gt;&lt;lt&gt;u6784&lt;gt&gt;&lt;lt&gt;u72b6&lt;gt&gt;&lt;lt&gt;u6001&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u529f&lt;gt&gt;&lt;lt&gt;u80fd&lt;gt&gt;&lt;lt&gt;u72b6&lt;gt&gt;&lt;lt&gt;u6001&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6743&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u578b&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u662f&lt;gt&gt;&lt;lt&gt;u5426&lt;gt&gt;&lt;lt&gt;u529e&lt;gt&gt;&lt;lt&gt;u7406&lt;gt&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u624b&lt;gt&gt;&lt;lt&gt;u7eed&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u4e3b&lt;gt&gt;&lt;lt&gt;u4f53&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u522b&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u7ba1&lt;gt&gt;&lt;lt&gt;u62a4&lt;gt&gt;&lt;lt&gt;u8d1f&lt;gt&gt;&lt;lt&gt;u8d23&lt;gt&gt;&lt;lt&gt;u4eba&lt;gt&gt;&lt;lt&gt;u59d3&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9644&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u7c7b&lt;gt&gt;&lt;lt&gt;u522b&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9644&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u6570&lt;gt&gt;&lt;lt&gt;u91cf&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u9644&lt;gt&gt;&lt;lt&gt;u5c5e&lt;gt&gt;&lt;lt&gt;u8bbe&lt;gt&gt;&lt;lt&gt;u65bd&lt;gt&gt;&lt;lt&gt;u5b8c&lt;gt&gt;&lt;lt&gt;u597d&lt;gt&gt;&lt;lt&gt;u7387&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u4f4d&lt;gt&gt;&lt;lt&gt;u7f6e&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;wkt&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;164&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_xml&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_json&lt;comma&gt;&lt;quote&gt;auto&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;datetime&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_datetime&lt;comma&gt;&lt;quote&gt;time&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_time&lt;comma&gt;&lt;quote&gt;date&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_date&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;boolean&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;openbracket&gt;&lt;closebracket&gt;*&lt;backslash&gt;&lt;backslash&gt;?:&lt;apos&gt;,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none&lt;comma&gt;fme_no_geom&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point&lt;comma&gt;xlsx_point&lt;comma&gt;fme_point&lt;comma&gt;xlsx_none&lt;comma&gt;fme_line&lt;comma&gt;xlsx_none&lt;comma&gt;fme_polygon&lt;comma&gt;xlsx_none&lt;comma&gt;fme_text&lt;comma&gt;xlsx_none&lt;comma&gt;fme_ellipse&lt;comma&gt;xlsx_none&lt;comma&gt;fme_arc&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_collection&lt;comma&gt;xlsx_none&lt;comma&gt;fme_surface&lt;comma&gt;xlsx_none&lt;comma&gt;fme_solid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_raster&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point_cloud&lt;comma&gt;xlsx_none&lt;comma&gt;fme_voxel_grid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;xlsx_layer_group&lt;space&gt;xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group&lt;space&gt;Sheet&lt;space&gt;Settings&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_truncate_group&lt;space&gt;xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet&lt;space&gt;Drop&lt;solidus&gt;Truncate&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_rowcolumn_group&lt;space&gt;xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Position&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVEDISCLOSUREGROUP&lt;space&gt;xlsx_protect_sheet&lt;space&gt;xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions&lt;space&gt;Protect&lt;space&gt;Sheet&lt;quote&gt;&lt;comma&gt;NO&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_template_group&lt;space&gt;xlsx_template_sheet%xlsx_remove_unchanged_template_sheet&lt;space&gt;Template&lt;space&gt;Options&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_advanced_group&lt;space&gt;xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_drop_sheet&lt;space&gt;Yes%No&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_trunc_sheet&lt;space&gt;Yes%No&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_sheet_order&lt;space&gt;1%MAX&lt;space&gt;Sheet&lt;space&gt;Order&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_freeze_end_row&lt;space&gt;1%MAX&lt;space&gt;Freeze&lt;space&gt;First&lt;space&gt;Row&lt;openparen&gt;s&lt;closeparen&gt;&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_field_names_out&lt;space&gt;Yes%No&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;++xlsx_field_names_formatting+No&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_field_names_formatting&lt;space&gt;Yes%No&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_names_are_positions&lt;space&gt;Yes%No&lt;space&gt;Use&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;As&lt;space&gt;Column&lt;space&gt;Positions:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_start_col&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_start_row&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_offset_col&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_raster_type&lt;space&gt;BMP%JPEG%PNG&lt;space&gt;Raster&lt;space&gt;Format:&lt;quote&gt;&lt;comma&gt;PNG&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;PASSWORD_ENCODED&lt;space&gt;xlsx_protect_sheet_password&lt;space&gt;Password:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;xlsx_protect_sheet_level&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_DEFAULT&lt;comma&gt;xlsx_protect_sheet_permissions%View&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_ALL&lt;comma&gt;xlsx_protect_sheet_permissions%Specific&lt;lt&gt;space&lt;gt&gt;Permissions&lt;space&gt;Protection&lt;space&gt;Level:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;LOOKUP_LISTBOX&lt;space&gt;xlsx_protect_sheet_permissions&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;locked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_LOCKED_CELLS%Select&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_UNLOCKED_CELLS%Format&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_FORMAT_CELLS%Format&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_FORMAT_COLUMNS%Format&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_FORMAT_ROWS%Insert&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_INSERT_COLUMNS%Insert&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_INSERT_ROWS%Add&lt;lt&gt;space&lt;gt&gt;hyperlinks&lt;lt&gt;space&lt;gt&gt;to&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_INSERT_HYPERLINKS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_DELETE_COLUMNS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_DELETE_ROWS%Sort&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;solidus&gt;rows&lt;solidus&gt;columns&lt;comma&gt;PROT_SORT%Use&lt;lt&gt;space&lt;gt&gt;Autofilter&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_AUTOFILTER%Use&lt;lt&gt;space&lt;gt&gt;PivotTable&lt;lt&gt;space&lt;gt&gt;&lt;amp&gt;&lt;lt&gt;space&lt;gt&gt;PivotChart&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_PIVOTTABLES%Edit&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;objects&lt;comma&gt;PROT_OBJECTS%Edit&lt;lt&gt;space&lt;gt&gt;unprotected&lt;lt&gt;space&lt;gt&gt;scenarios&lt;comma&gt;PROT_SCENARIOS&lt;space&gt;Specific&lt;space&gt;Permissions:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_table_writer_mode&lt;space&gt;Insert&lt;comma&gt;+xlsx_row_id_column+&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;%Update&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id%Delete&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id&lt;space&gt;Writer&lt;space&gt;Mode:&lt;quote&gt;&lt;comma&gt;Insert&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ATTR&lt;space&gt;xlsx_row_id_column&lt;space&gt;ALLOW_NEW&lt;space&gt;Row&lt;space&gt;Number&lt;space&gt;Attribute:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT_EDIT&lt;space&gt;xlsx_template_sheet&lt;space&gt;Template&lt;space&gt;Sheet:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_remove_unchanged_template_sheet&lt;space&gt;Yes%No&lt;space&gt;Remove&lt;space&gt;Template&lt;space&gt;Sheet&lt;space&gt;if&lt;space&gt;Unchanged:&lt;quote&gt;&lt;comma&gt;No,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;NO&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;No&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,DEFAULT_READER&lt;comma&gt;XLSXR&lt;comma&gt;ALLOW_DATASET_CONFLICT&lt;comma&gt;YES&lt;comma&gt;MIME_TYPE&lt;comma&gt;&lt;quote&gt;application&lt;solidus&gt;vnd.openxmlformats-officedocument.spreadsheetml.sheet&lt;space&gt;ADD_DISPOSITION&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;DEFAULT_ATTR_TYPE&lt;comma&gt;auto&lt;comma&gt;USER_ATTRIBUTES_COLUMNS&lt;comma&gt;&lt;quote&gt;Width&lt;comma&gt;Cell&lt;space&gt;Width%Precision&lt;comma&gt;Formatting&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Sheet&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Sheet1&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;name&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Microsoft&lt;space&gt;Excel&lt;space&gt;file&lt;quote&gt;,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter_2"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_CUSTOM_NUMBER_FORMATTING" PARM_VALUE="ENABLE_NATIVE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DROP_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_FORMATTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_OUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_INSERT_IGNORE_DB_OP" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_MULTIPLE_TEMPLATE_SHEETS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXW_OVERWRITE_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_PROTECT_SHEET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_RASTER_FORMAT" PARM_VALUE="PNG"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_TRUNCATE_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_WRITER_MODE" PARM_VALUE="Insert"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="23"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="1040.6354063540634 -1488.8881686589084"
#!   BOUNDING_RECT="1040.6354063540634 -1488.8881686589084 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="DCBH" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DKMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DLMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Id" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="MJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="PGSSXMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="QXMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SFYJJBNT" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSXMDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="SSXMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZCMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="DCBH"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="12s"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="26"
#!   TYPE="AttributeSplitter"
#!   VERSION="4"
#!   POSITION="903.76161545062678 -2073.8942479714106"
#!   BOUNDING_RECT="903.76161545062678 -2073.8942479714106 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="序号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="调查编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="区县" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="乡镇" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="行政村名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程一级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程二级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程三级类" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="长度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="宽度" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="面积" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管径" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否实地存在" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否高标项目建设" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="所属项目名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程编号" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="竣工时间" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施使用状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施结构状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程设施功能状态" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="权属类型" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="是否办理管护手续" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护主体类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="管护负责人姓名" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施类别" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施数量" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="附属设施完好率" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="工程位置（wkt）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_list{}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_NAME" PARM_VALUE="&lt;u8c03&gt;&lt;u67e5&gt;&lt;u7f16&gt;&lt;u53f7&gt;"/>
#!     <XFORM_PARM PARM_NAME="DELIMITER" PARM_VALUE="12s"/>
#!     <XFORM_PARM PARM_NAME="DROP_EMPTY_PARTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="_list"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRIM_OPTION" PARM_VALUE="Both"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeSplitter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="29"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-1721.8922189221892 -1248.8881686589084"
#!   BOUNDING_RECT="-1721.8922189221892 -1248.8881686589084 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="32"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-1000.010000100001 -1350.0135001350013"
#!   BOUNDING_RECT="-1000.010000100001 -1350.0135001350013 430 71"
#!   ORDER="500000000000012"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="31"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-306.25306253062524 -2040.6454064540653"
#!   BOUNDING_RECT="-306.25306253062524 -2040.6454064540653 430 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="$(NUM)"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="30"
#!   SOURCE_NODE="17"
#!   TARGET_NODE="29"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="35"
#!   SOURCE_NODE="31"
#!   TARGET_NODE="11"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="8"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="10"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="9"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="13"
#!   TARGET_NODE="14"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="22"
#!   SOURCE_NODE="14"
#!   TARGET_NODE="21"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="23"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="28"
#!   SOURCE_NODE="26"
#!   TARGET_NODE="13"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="34"
#!   SOURCE_NODE="32"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="27"
#!   SOURCE_NODE="11"
#!   TARGET_NODE="26"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;u8003&lt;gt&gt;&lt;lt&gt;u8bd5&lt;gt&gt;&lt;lt&gt;u5de5&lt;gt&gt;&lt;lt&gt;u7a0b&lt;gt&gt;&lt;lt&gt;u7ebf&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="24"
#!   SOURCE_NODE="18"
#!   TARGET_NODE="23"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="33"
#!   SOURCE_NODE="29"
#!   TARGET_NODE="32"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO NUM 400

DEFAULT_MACRO dir C:\Users\<USER>\Desktop\耕地图斑

DEFAULT_MACRO file C:\Users\<USER>\Desktop\考试工程.xlsx

DEFAULT_MACRO PARAMETER_2 C:\Users\<USER>\Desktop

# ============================================================================
INCLUDE [ if {{$(NUM$encode)} == {}} { puts_real {Parameter 'NUM' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(file$encode)} == {}} { puts_real {Parameter 'file' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_2$encode)} == {}} { puts_real {Parameter 'PARAMETER_2' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)none2none.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME none2none
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_2_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_2_XML     NOT_ACTIVATED
MACRO Creator_2_CLASSIC NOT_ACTIVATED
MACRO Creator_2_2D3D    2D_GEOMETRY
MACRO Creator_2_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_2_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2_2D3D 3D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_2: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_2_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
FACTORY_DEF {$(Creator_2_XML)} CreationFactory    FACTORY_NAME { Creator_2_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_2_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_2_CLASSIC_Creator }    $(Creator_2_2D3D) { $(Creator_2_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_2_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_2_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { $(NUM) }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_2_CREATED        fme_feature_type Creator_2         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_2_CREATED Brancher -1 30"   INPUT FEATURE_TYPE Creator_2_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { $(NUM) }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 35"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED <u8003><u8bd5><u5de5><u7a0b><u7ebf>
MACRO FeatureReader_2_DIRECTIVES ADVANCED,,ALLOW_DOLLAR_SIGNS,YES,APPLY_FILTERS,No,CASE_SENSITIVE_FEATURE_TYPES,YES,CONFIGURATION_DATASET,C:<backslash>Users<backslash>djz<backslash>Desktop<backslash><u8003><u8bd5><u5de5><u7a0b>.xlsx,CREATE_FEATURE_TABLES_FROM_DATA,Yes,EXCEL_COL_NAMES,YES,EXPAND_MERGED_CELLS,Yes,EXPOSE_ATTRS_GROUP,,FORCE_DATETIME,NO,NETWORK_AUTHENTICATION,,QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,Yes,READ_BLANK_AS,Missing,READ_FORM_CONTROLS,NONE,READ_RASTER_MODE,None,REPLACE_ONLY_NEWLINES_CARRIAGE_RETURNS,YES,SCAN_FOR_GEOMETRIC_TYPES,Yes,SCAN_MAX_FEATURES,1000,SCHEMA,<lt>u8003<gt><lt>u8bd5<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u7ebf<gt><comma>0<lt>comma<gt><lt>u5e8f<gt><lt>u53f7<gt><lt>comma<gt>number<lt>comma<gt>1<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>1<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>25<lt>comma<gt><lt>comma<gt><lt>comma<gt>2<lt>comma<gt><lt>u8c03<gt><lt>u67e5<gt><lt>u7f16<gt><lt>u53f7<gt><lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>3<lt>comma<gt><lt>u533a<gt><lt>u53bf<gt><lt>comma<gt>char<lt>comma<gt>9<lt>comma<gt><lt>comma<gt><lt>comma<gt>4<lt>comma<gt><lt>u4e61<gt><lt>u9547<gt><lt>comma<gt>char<lt>comma<gt>9<lt>comma<gt><lt>comma<gt><lt>comma<gt>5<lt>comma<gt><lt>u884c<gt><lt>u653f<gt><lt>u6751<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>9<lt>comma<gt><lt>comma<gt><lt>comma<gt>6<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u7c7b<gt><lt>u522b<gt><lt>comma<gt>char<lt>comma<gt>18<lt>comma<gt><lt>comma<gt><lt>comma<gt>7<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u4e00<gt><lt>u7ea7<gt><lt>u7c7b<gt><lt>comma<gt>char<lt>comma<gt>27<lt>comma<gt><lt>comma<gt><lt>comma<gt>8<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u4e8c<gt><lt>u7ea7<gt><lt>u7c7b<gt><lt>comma<gt>char<lt>comma<gt>21<lt>comma<gt><lt>comma<gt><lt>comma<gt>9<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u4e09<gt><lt>u7ea7<gt><lt>u7c7b<gt><lt>comma<gt>char<lt>comma<gt>19<lt>comma<gt><lt>comma<gt><lt>comma<gt>10<lt>comma<gt><lt>u957f<gt><lt>u5ea6<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>11<lt>comma<gt><lt>u5bbd<gt><lt>u5ea6<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>12<lt>comma<gt><lt>u9762<gt><lt>u79ef<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>13<lt>comma<gt><lt>u7ba1<gt><lt>u5f84<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>14<lt>comma<gt><lt>u662f<gt><lt>u5426<gt><lt>u5b9e<gt><lt>u5730<gt><lt>u5b58<gt><lt>u5728<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>15<lt>comma<gt><lt>u662f<gt><lt>u5426<gt><lt>u9ad8<gt><lt>u6807<gt><lt>u9879<gt><lt>u76ee<gt><lt>u5efa<gt><lt>u8bbe<gt><lt>comma<gt>char<lt>comma<gt>3<lt>comma<gt><lt>comma<gt><lt>comma<gt>16<lt>comma<gt><lt>u6240<gt><lt>u5c5e<gt><lt>u9879<gt><lt>u76ee<gt><lt>u540d<gt><lt>u79f0<gt><lt>comma<gt>char<lt>comma<gt>37<lt>comma<gt><lt>comma<gt><lt>comma<gt>17<lt>comma<gt><lt>u9879<gt><lt>u76ee<gt><lt>u7f16<gt><lt>u53f7<gt><lt>comma<gt>number<lt>comma<gt>12<lt>comma<gt>0<lt>comma<gt><lt>comma<gt>18<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>19<lt>comma<gt><lt>u7ae3<gt><lt>u5de5<gt><lt>u65f6<gt><lt>u95f4<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>20<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u4f7f<gt><lt>u7528<gt><lt>u72b6<gt><lt>u6001<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>21<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u7ed3<gt><lt>u6784<gt><lt>u72b6<gt><lt>u6001<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>22<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u529f<gt><lt>u80fd<gt><lt>u72b6<gt><lt>u6001<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>23<lt>comma<gt><lt>u6743<gt><lt>u5c5e<gt><lt>u7c7b<gt><lt>u578b<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>24<lt>comma<gt><lt>u662f<gt><lt>u5426<gt><lt>u529e<gt><lt>u7406<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u624b<gt><lt>u7eed<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>25<lt>comma<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u4e3b<gt><lt>u4f53<gt><lt>u7c7b<gt><lt>u522b<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>26<lt>comma<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u8d1f<gt><lt>u8d23<gt><lt>u4eba<gt><lt>u59d3<gt><lt>u540d<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>27<lt>comma<gt><lt>u9644<gt><lt>u5c5e<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u7c7b<gt><lt>u522b<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>28<lt>comma<gt><lt>u9644<gt><lt>u5c5e<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u6570<gt><lt>u91cf<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>29<lt>comma<gt><lt>u9644<gt><lt>u5c5e<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u5b8c<gt><lt>u597d<gt><lt>u7387<gt><lt>comma<gt>string<lt>comma<gt><lt>comma<gt><lt>comma<gt><lt>comma<gt>30<lt>comma<gt><lt>u5de5<gt><lt>u7a0b<gt><lt>u4f4d<gt><lt>u7f6e<gt><lt>uff08<gt>wkt<lt>uff09<gt><lt>comma<gt>char<lt>comma<gt>164<lt>comma<gt><lt>comma<gt><comma>1<lt>comma<gt><lt>comma<gt><lt>comma<gt><comma>NO<lt>comma<gt>NO<lt>comma<gt>1<lt>comma<gt>C:<lt>lt<gt>backslash<lt>gt<gt>Users<lt>lt<gt>backslash<lt>gt<gt>djz<lt>lt<gt>backslash<lt>gt<gt>Desktop<lt>lt<gt>backslash<lt>gt<gt><lt>lt<gt>u8003<lt>gt<gt><lt>lt<gt>u8bd5<lt>gt<gt><lt>lt<gt>u5de5<lt>gt<gt><lt>lt<gt>u7a0b<lt>gt<gt>.xlsx<lt>comma<gt><lt>quote<gt>0<lt>comma<gt>0<lt>comma<gt>30<lt>comma<gt>2000<lt>quote<gt><lt>comma<gt><lt>comma<gt>NO<lt>comma<gt>NO,SCHEMA_HANDLING_REVISION,2,SKIP_EMPTY_ROWS,YES,STRIP_SHEETNAME_SPACES,YES,TABLELIST,<u8003><u8bd5><u5de5><u7a0b><u7ebf>,TRIM_ATTR_NAME_CHARACTERS,,TRIM_ATTR_NAME_WHITESPACE,Yes,USE_CUSTOM_SCHEMA,NO,XLSXR_EXPOSE_FORMAT_ATTRS,
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(file$encode),FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { XLSXR  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "<u8003><u8bd5><u5de5><u7a0b><u7ebf>" }    READER_DIRECTIVES   { METAFILE,XLSXR }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT <u8003><u8bd5><u5de5><u7a0b><u7ebf> FEATURE_TYPE FeatureReader_2_<u8003><u8bd5><u5de5><u7a0b><u7ebf>
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_2_167d0857_53a7_4725_9a8c_08443d7863482} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {12s}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {<u8c03><u67e5><u7f16><u53f7>}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {<u8c03><u67e5><u7f16><u53f7>}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter_2 }    INPUT  FEATURE_TYPE FeatureReader_2_<u8003><u8bd5><u5de5><u7a0b><u7ebf>    OUTPUT { FEATURE_TYPE AttributeSplitter_2_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter_2 }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "1" }    SCOPE { Global }    DOMAIN { "counter" }    COUNT_ATTR { "_count" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE AttributeSplitter_2_OUTPUT    OUTPUT { OUTPUT FEATURE_TYPE Counter_2_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_2_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Counter_2_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "_count" "SET_TO" "<at>Format<openparen>%05d<comma><at>Value<openparen>_count<closeparen><closeparen>" "int64" }      ATTR_ACTION { "" "<u8c03><u67e5><u7f16><u53f7>" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'_FME_NO_OP_'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%5<closeparen> = 0'<at>Value<openparen>_list<opencurly>0<closecurly><closeparen>XMGC<at>Value<openparen>_count<closeparen>'BOOL_OP;OR;COMPOSITE_TEST;1 OR 2 OR 3 OR 4;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%1<closeparen> = 0;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%2<closeparen> = 0;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%3<closeparen> = 0;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%4<closeparen> = 0'<at>Value<openparen>_list<opencurly>0<closecurly><closeparen>XMGC<at>Value<openparen>_count<closeparen>'FME_NUM_CONDITIONS3___" "varchar<openparen>18<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash><u8003><u8bd5><u5de5><u7a0b>.xlsx,FeatureWriter_2)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter_2 }    WRITER_TYPE { XLSXW }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter_2)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_FILE<comma>No<comma>TEMPLATEFILE<comma><comma>TEMPLATE_SHEET<comma><comma>REMOVE_UNCHANGED_TEMPLATE_SHEET<comma>No<comma>MULTIPLE_TEMPLATE_SHEETS<comma>Yes<comma>INSERT_IGNORE_DB_OP<comma>Yes<comma>DROP_TABLE<comma>No<comma>TRUNCATE_TABLE<comma>No<comma>FIELD_NAMES_OUT<comma>Yes<comma>FIELD_NAMES_FORMATTING<comma>Yes<comma>WRITER_MODE<comma>Insert<comma>RASTER_FORMAT<comma>PNG<comma>PROTECT_SHEET<comma>NO<comma>PROTECT_SHEET_PASSWORD<comma><lt>Unused<gt><comma>PROTECT_SHEET_LEVEL<comma><lt>Unused<gt><comma>PROTECT_SHEET_PERMISSIONS<comma><lt>Unused<gt><comma>STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING<comma>yes<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE<comma>CUSTOM_NUMBER_FORMATTING<comma>ENABLE_NATIVE<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,XLSXW" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,<quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varchar<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varbinary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_char<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binarybuffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_xml<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_json<comma><quote>auto<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>datetime<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_datetime<comma><quote>time<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_time<comma><quote>date<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_date<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int8<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint8<comma><quote>boolean<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<openbracket><closebracket>*<backslash><backslash>?:<apos>,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none<comma>fme_no_geom<comma>xlsx_none<comma>fme_point<comma>xlsx_point<comma>fme_point<comma>xlsx_none<comma>fme_line<comma>xlsx_none<comma>fme_polygon<comma>xlsx_none<comma>fme_text<comma>xlsx_none<comma>fme_ellipse<comma>xlsx_none<comma>fme_arc<comma>xlsx_none<comma>fme_rectangle<comma>xlsx_none<comma>fme_rounded_rectangle<comma>xlsx_none<comma>fme_collection<comma>xlsx_none<comma>fme_surface<comma>xlsx_none<comma>fme_solid<comma>xlsx_none<comma>fme_raster<comma>xlsx_none<comma>fme_point_cloud<comma>xlsx_none<comma>fme_voxel_grid<comma>xlsx_none<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>xlsx_layer_group<space>xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group<space>Sheet<space>Settings<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_truncate_group<space>xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet<space>Drop<solidus>Truncate<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_rowcolumn_group<space>xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row<space>Start<space>Position<quote><comma><comma><quote>GUI<space>ACTIVEDISCLOSUREGROUP<space>xlsx_protect_sheet<space>xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions<space>Protect<space>Sheet<quote><comma>NO<comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_template_group<space>xlsx_template_sheet%xlsx_remove_unchanged_template_sheet<space>Template<space>Options<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_advanced_group<space>xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type<space>Advanced<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_drop_sheet<space>Yes%No<space>Drop<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>CHOICE<space>xlsx_trunc_sheet<space>Yes%No<space>Truncate<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_sheet_order<space>1%MAX<space>Sheet<space>Order<space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_freeze_end_row<space>1%MAX<space>Freeze<space>First<space>Row<openparen>s<closeparen><space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_field_names_out<space>Yes%No<comma>xlsx_field_names_formatting<comma>++xlsx_field_names_formatting+No<space>Output<space>Field<space>Names:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_field_names_formatting<space>Yes%No<space>Format<space>Field<space>Names<space>Row:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_names_are_positions<space>Yes%No<space>Use<space>Attribute<space>Names<space>As<space>Column<space>Positions:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_start_col<space>Named<space>Range<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_start_row<space>Named<space>Range<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_offset_col<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_offset_row<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_raster_type<space>BMP%JPEG%PNG<space>Raster<space>Format:<quote><comma>PNG<comma><quote>GUI<space>OPTIONAL<space>PASSWORD_ENCODED<space>xlsx_protect_sheet_password<space>Password:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>xlsx_protect_sheet_level<space>Select<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_DEFAULT<comma>xlsx_protect_sheet_permissions%View<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_ALL<comma>xlsx_protect_sheet_permissions%Specific<lt>space<gt>Permissions<space>Protection<space>Level:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>OPTIONAL<space>LOOKUP_LISTBOX<space>xlsx_protect_sheet_permissions<space>Select<lt>space<gt>locked<lt>space<gt>cells<comma>PROT_SEL_LOCKED_CELLS%Select<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_SEL_UNLOCKED_CELLS%Format<lt>space<gt>cells<comma>PROT_FORMAT_CELLS%Format<lt>space<gt>columns<comma>PROT_FORMAT_COLUMNS%Format<lt>space<gt>rows<comma>PROT_FORMAT_ROWS%Insert<lt>space<gt>columns<comma>PROT_INSERT_COLUMNS%Insert<lt>space<gt>rows<comma>PROT_INSERT_ROWS%Add<lt>space<gt>hyperlinks<lt>space<gt>to<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_INSERT_HYPERLINKS%Delete<lt>space<gt>unlocked<lt>space<gt>columns<comma>PROT_DELETE_COLUMNS%Delete<lt>space<gt>unlocked<lt>space<gt>rows<comma>PROT_DELETE_ROWS%Sort<lt>space<gt>unlocked<lt>space<gt>cells<solidus>rows<solidus>columns<comma>PROT_SORT%Use<lt>space<gt>Autofilter<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_AUTOFILTER%Use<lt>space<gt>PivotTable<lt>space<gt><amp><lt>space<gt>PivotChart<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_PIVOTTABLES%Edit<lt>space<gt>unlocked<lt>space<gt>objects<comma>PROT_OBJECTS%Edit<lt>space<gt>unprotected<lt>space<gt>scenarios<comma>PROT_SCENARIOS<space>Specific<space>Permissions:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_table_writer_mode<space>Insert<comma>+xlsx_row_id_column+<quote><quote><quote><quote>%Update<comma>+xlsx_row_id_column+xlsx_row_id%Delete<comma>+xlsx_row_id_column+xlsx_row_id<space>Writer<space>Mode:<quote><comma>Insert<comma><quote>GUI<space>OPTIONAL<space>ATTR<space>xlsx_row_id_column<space>ALLOW_NEW<space>Row<space>Number<space>Attribute:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT_EDIT<space>xlsx_template_sheet<space>Template<space>Sheet:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_remove_unchanged_template_sheet<space>Yes%No<space>Remove<space>Template<space>Sheet<space>if<space>Unchanged:<quote><comma>No,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_freeze_end_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_names_are_positions<comma>No<comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_start_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_start_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_raster_type<comma>PNG<comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_protect_sheet<comma><quote><quote><quote>NO<quote><quote><quote><comma>xlsx_protect_sheet_level<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_password<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_permissions<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_template_sheet<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_remove_unchanged_template_sheet<comma><quote><quote><quote>No<quote><quote><quote>,WRITER_FORMAT_PARAMETER,DEFAULT_READER<comma>XLSXR<comma>ALLOW_DATASET_CONFLICT<comma>YES<comma>MIME_TYPE<comma><quote>application<solidus>vnd.openxmlformats-officedocument.spreadsheetml.sheet<space>ADD_DISPOSITION<quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>DEFAULT_ATTR_TYPE<comma>auto<comma>USER_ATTRIBUTES_COLUMNS<comma><quote>Width<comma>Cell<space>Width%Precision<comma>Formatting<quote><comma>FEATURE_TYPE_NAME<comma>Sheet<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Sheet1<comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>name<space>for<space>the<space>Microsoft<space>Excel<space>file<quote>,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<u8003><u8bd5><u5de5><u7a0b><u7ebf>:Output,ftp_feature_type_name,<u8003><u8bd5><u5de5><u7a0b><u7ebf>,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u5e8f<gt><lt>u53f7<gt><comma>number<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>25<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u8c03<gt><lt>u67e5<gt><lt>u7f16<gt><lt>u53f7<gt><comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u533a<gt><lt>u53bf<gt><comma>string<lt>openparen<gt>9<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u4e61<gt><lt>u9547<gt><comma>string<lt>openparen<gt>9<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u884c<gt><lt>u653f<gt><lt>u6751<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>9<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u7c7b<gt><lt>u522b<gt><comma>string<lt>openparen<gt>18<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u4e00<gt><lt>u7ea7<gt><lt>u7c7b<gt><comma>string<lt>openparen<gt>27<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u4e8c<gt><lt>u7ea7<gt><lt>u7c7b<gt><comma>string<lt>openparen<gt>21<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u4e09<gt><lt>u7ea7<gt><lt>u7c7b<gt><comma>string<lt>openparen<gt>19<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u957f<gt><lt>u5ea6<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5bbd<gt><lt>u5ea6<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u9762<gt><lt>u79ef<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u5f84<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u662f<gt><lt>u5426<gt><lt>u5b9e<gt><lt>u5730<gt><lt>u5b58<gt><lt>u5728<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u662f<gt><lt>u5426<gt><lt>u9ad8<gt><lt>u6807<gt><lt>u9879<gt><lt>u76ee<gt><lt>u5efa<gt><lt>u8bbe<gt><comma>string<lt>openparen<gt>3<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u6240<gt><lt>u5c5e<gt><lt>u9879<gt><lt>u76ee<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>37<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u9879<gt><lt>u76ee<gt><lt>u7f16<gt><lt>u53f7<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u7f16<gt><lt>u53f7<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u7ae3<gt><lt>u5de5<gt><lt>u65f6<gt><lt>u95f4<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u4f7f<gt><lt>u7528<gt><lt>u72b6<gt><lt>u6001<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u7ed3<gt><lt>u6784<gt><lt>u72b6<gt><lt>u6001<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u529f<gt><lt>u80fd<gt><lt>u72b6<gt><lt>u6001<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u6743<gt><lt>u5c5e<gt><lt>u7c7b<gt><lt>u578b<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u662f<gt><lt>u5426<gt><lt>u529e<gt><lt>u7406<gt><lt>u7ba1<gt><lt>u62a4<gt><lt>u624b<gt><lt>u7eed<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u62a4<gt><lt>u4e3b<gt><lt>u4f53<gt><lt>u7c7b<gt><lt>u522b<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u7ba1<gt><lt>u62a4<gt><lt>u8d1f<gt><lt>u8d23<gt><lt>u4eba<gt><lt>u59d3<gt><lt>u540d<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u9644<gt><lt>u5c5e<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u7c7b<gt><lt>u522b<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u9644<gt><lt>u5c5e<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u6570<gt><lt>u91cf<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u9644<gt><lt>u5c5e<gt><lt>u8bbe<gt><lt>u65bd<gt><lt>u5b8c<gt><lt>u597d<gt><lt>u7387<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5de5<gt><lt>u7a0b<gt><lt>u4f4d<gt><lt>u7f6e<gt><lt>uff08<gt>wkt<lt>uff09<gt><comma>string<lt>openparen<gt>164<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No" }    WRITER_PARAMS { "COORDINATE_SYSTEM_GRANULARITY,FEATURE,CUSTOM_NUMBER_FORMATTING,ENABLE_NATIVE,DESTINATION_DATASETTYPE_VALIDATION,Yes,DROP_TABLE,No,FIELD_NAMES_FORMATTING,Yes,FIELD_NAMES_OUT,Yes,INSERT_IGNORE_DB_OP,Yes,MULTIPLE_TEMPLATE_SHEETS,Yes,NETWORK_AUTHENTICATION,,OVERWRITE_FILE,No,PROTECT_SHEET,NO,RASTER_FORMAT,PNG,STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,yes,TRUNCATE_TABLE,No,WRITER_MODE,Insert" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE AttributeCreator_2_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u8003><u8bd5><u5de5><u7a0b><u7ebf>,FeatureWriter_2))
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_2_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_3_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE Tester_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_3_<OTHER>           }
# -------------------------------------------------------------------------
# Create a FME_UUID_SAFE from the FME_UUID so we can use it for Tcl identifiers (FMEDESKTOP-11308)
INCLUDE [ FME_CleanseFMEUUID {AttributeSplitter_f32363d3_7d65_4fee_a50a_63309ae7bba92} ]
Tcl2 proc $(FME_UUID_SAFE)_doSplit {} {    set splitDelimiter [FME_DecodeTextOrAttr {12s}];    if { [regexp {^([1-9][0-9]*s)+$} $splitDelimiter] }    {       set splitWidths [split [regsub -all {s$} $splitDelimiter {}] s];       set source [FME_GetAttribute [FME_DecodeText {DCBH}]];       set attrNum 0;       set listName [FME_DecodeText {_list}];       set attrPos 0;       set keepEmptyParts [string equal {No} {No}];       foreach width $splitWidths       {          set endPos [expr $attrPos + $width - 1];          set bit [string range $source $attrPos $endPos];          set part [string trim $bit];          if { $keepEmptyParts || $part != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $part;             incr attrNum;          };          incr attrPos $width;       };    }    else    {       set delimLength [string length $splitDelimiter];       set source [FME_GetAttribute [FME_DecodeText {DCBH}]];       set keepEmptyParts [string equal {No} {No}];       set bits {};       set startIndex 0;       set nextIndex [string first $splitDelimiter $source $startIndex];       while {$nextIndex >= 0} {          lappend bits [string range $source $startIndex [expr $nextIndex-1]];          set startIndex [expr $nextIndex + $delimLength];          set nextIndex [string first $splitDelimiter $source $startIndex];       };       lappend bits [string range $source $startIndex end];       set listName [FME_DecodeText {_list}];       set attrNum 0;       foreach bit $bits       {          set trimmedPart [string trim $bit];          if { $keepEmptyParts || $trimmedPart != \"\" } {             FME_SetAttribute "$listName{$attrNum}" $trimmedPart;             incr attrNum;          };       }    } }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeSplitter }    INPUT  FEATURE_TYPE FeatureReader_3_<OTHER>    OUTPUT { FEATURE_TYPE AttributeSplitter_OUTPUT         @Tcl2($(FME_UUID_SAFE)_doSplit)          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} CounterFactory    FACTORY_NAME { Counter }    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    START { "1" }    SCOPE { Global }    DOMAIN { "counter" }    COUNT_ATTR { "_count" }    GROUP_ID_ATTR { "" }    INPUT  FEATURE_TYPE AttributeSplitter_OUTPUT    OUTPUT { OUTPUT FEATURE_TYPE Counter_OUTPUT        }    OUTPUT { REJECTED FEATURE_TYPE Counter_<REJECTED>        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Counter_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "_count" "SET_TO" "<at>Format<openparen>%05d<comma><at>Value<openparen>_count<closeparen><closeparen>" "int64" }      ATTR_ACTION { "" "DCBH" "SET_TO" "FME_CONDITIONAL:DEFAULT_VALUE'_FME_NO_OP_'BOOL_OP;OR;COMPOSITE_TEST;1;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%5<closeparen> = 0'<at>Value<openparen>_list<opencurly>0<closecurly><closeparen>YJDK<at>Value<openparen>_count<closeparen>'BOOL_OP;OR;COMPOSITE_TEST;1 OR 2 OR 3 OR 4;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%1<closeparen> = 0;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%2<closeparen> = 0;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%3<closeparen> = 0;TEST <at>Evaluate<openparen><at>Value<openparen>_count<closeparen>%4<closeparen> = 0'<at>Value<openparen>_list<opencurly>0<closecurly><closeparen>YJDK<at>Value<openparen>_count<closeparen>'FME_NUM_CONDITIONS3___" "buffer" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash><u5730><u5757><u8bd5><u9898>,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>utf-8<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma><comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<u8bd5><u9898>:Output,ftp_feature_type_name,<u8bd5><u9898>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_polygon,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,Id<comma>long<comma>DCBH<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>DKMC<comma>varchar<lt>openparen<gt>200<lt>closeparen<gt><comma>QXMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XZMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>XZCMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>DLMC<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SFYJJBNT<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SSXMDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>SSXMMC<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>PGSSXMDM<comma>varchar<lt>openparen<gt>50<lt>closeparen<gt><comma>PGSSXMMC<comma>varchar<lt>openparen<gt>254<lt>closeparen<gt><comma>MJ<comma>double,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,utf-8,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE AttributeCreator_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u8bd5><u9898>,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

