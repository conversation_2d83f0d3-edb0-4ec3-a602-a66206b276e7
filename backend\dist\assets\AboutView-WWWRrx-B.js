import{d as _,r as c,o as p,c as m,e,w as l,f as i,b as t,an as v,m as a,ao as g,U as d,ap as k,ab as b,aj as h,t as w,W as x,l as C,_ as S}from"./index-CdoJMuEX.js";const V={class:"about-container"},y={class:"card-header"},G={class:"features-grid"},I={class:"feature-item"},A={class:"feature-link-indicator"},B={class:"feature-item"},N={class:"feature-link-indicator"},D={class:"feature-item"},L={class:"feature-link-indicator"},j={class:"feature-item"},q={class:"feature-link-indicator"},z={class:"copyright-content"},E=_({__name:"AboutView",setup(M){const u=c("");return p(()=>{u.value=new Date().toLocaleString("zh-CN")}),(T,s)=>{const r=i("el-card"),o=i("el-icon"),n=i("router-link"),f=i("el-divider");return C(),m("div",V,[e(r,{class:"intro-card",shadow:"hover"},{header:l(()=>s[0]||(s[0]=[t("div",{class:"card-header"},[t("div",{class:"logo-section"},[t("img",{src:v,alt:"GeoStream Logo",class:"logo"}),t("div",{class:"title-section"},[t("h1",null,"GeoStream Integration"),t("p",{class:"subtitle"},"专业的地理信息数据处理平台")])])],-1)])),default:l(()=>[s[1]||(s[1]=t("div",{class:"intro-content"},[t("p",{class:"description"}," 欢迎使用 GeoStream Integration 平台。这是一个专业的地理信息数据处理平台， 致力于为用户提供高效、便捷的数据处理、坐标转换、格式转换等一站式解决方案。 ")],-1))]),_:1}),e(r,{class:"features-card",shadow:"hover"},{header:l(()=>[t("div",y,[e(o,{class:"header-icon"},{default:l(()=>[e(a(w))]),_:1}),s[2]||(s[2]=t("span",null,"核心功能",-1))])]),default:l(()=>[t("div",G,[e(n,{to:"/market",class:"feature-item-link"},{default:l(()=>[t("div",I,[e(o,{class:"feature-icon"},{default:l(()=>[e(a(g))]),_:1}),s[3]||(s[3]=t("h3",null,"工具市场",-1)),s[4]||(s[4]=t("p",null,"丰富的处理工具生态系统",-1)),t("div",A,[e(o,null,{default:l(()=>[e(a(d))]),_:1})])])]),_:1}),e(n,{to:"/coordinate",class:"feature-item-link"},{default:l(()=>[t("div",B,[e(o,{class:"feature-icon"},{default:l(()=>[e(a(k))]),_:1}),s[5]||(s[5]=t("h3",null,"坐标转换",-1)),s[6]||(s[6]=t("p",null,"支持多种坐标系统间的精确转换",-1)),t("div",N,[e(o,null,{default:l(()=>[e(a(d))]),_:1})])])]),_:1}),e(n,{to:"/cad2gis",class:"feature-item-link"},{default:l(()=>[t("div",D,[e(o,{class:"feature-icon"},{default:l(()=>[e(a(b))]),_:1}),s[7]||(s[7]=t("h3",null,"格式转换",-1)),s[8]||(s[8]=t("p",null,"CAD转GIS等多种数据格式转换",-1)),t("div",L,[e(o,null,{default:l(()=>[e(a(d))]),_:1})])])]),_:1}),e(n,{to:"/quality",class:"feature-item-link"},{default:l(()=>[t("div",j,[e(o,{class:"feature-icon"},{default:l(()=>[e(a(h))]),_:1}),s[9]||(s[9]=t("h3",null,"数据质检",-1)),s[10]||(s[10]=t("p",null,"全面的数据质量检查与验证",-1)),t("div",q,[e(o,null,{default:l(()=>[e(a(d))]),_:1})])])]),_:1})])]),_:1}),e(r,{class:"copyright-card",shadow:"never"},{default:l(()=>[t("div",z,[e(f,null,{default:l(()=>[e(o,null,{default:l(()=>[e(a(x))]),_:1})]),_:1}),s[11]||(s[11]=t("p",{class:"copyright-text"}," © 2025 园测信息科技股份有限公司 技术创新小组 版权所有 ",-1)),s[12]||(s[12]=t("p",{class:"version-info"}," Version 1.0.0 ",-1))])]),_:1})])}}}),W=S(E,[["__scopeId","data-v-bbf24e6b"]]);export{W as default};
