#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\GeoStream_Integration\frontend\backend\models\HGQgWOjWU2Vg4ghMklLqsfZFq8epJba5O1nDu4LU\shp转KML.fmw
#          --PARAMETER "3"
#          --PARAMETER_3 "$(FME_MF_DIR)paramet\b"
#          --PARAMETER_4 "YES"
#          --PARAMETER_5 ""
#          --PARAMETER_6 "默认大概多少公司"
#          --PARAMETER_7 ""
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="3495.89 193.126"
#!   DOC_TOP_LEFT="-1287.51 -33.7498"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="ab653fe4-96f4-4f3e-9598-bc4d812c4035"
#!   FME_DOCUMENT_PRIORGUID="f7e1ad40-6ff8-4074-b09f-14e899da85dc,3ecbb73e-84b7-43c0-a0e3-ca019d4c85cd,c6847e6f-a7ee-4179-b79a-d7472b1bc195,0f1d5983-770b-4e06-a16f-64afe92d6dc2"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2025-07-07T14:14:52"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="-1865.64 1040.64"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="\@Value(path_rootname).kml"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI CHOICE PARAMETER 1%2%3%4 Select a Choice"
#!   DEFAULT_VALUE="3"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI IGNORE NO_EDIT DIRNAME_SRC_OR_ATTR PARAMETER_2 Select a File"
#!   DEFAULT_VALUE="$(FME_MF_DIR)paramet\a"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL DIRNAME_SRC PARAMETER_3 Select a File 2"
#!   DEFAULT_VALUE="$(FME_MF_DIR)paramet\b"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI LITERAL CHECKBOX PARAMETER_4 YES%NO Select Yes/No"
#!   DEFAULT_VALUE="YES"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI LITERAL CHECKBOX PARAMETER_5 YES%NO Select Yes/No 2"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI STRING PARAMETER_6 Enter Text Data"
#!   DEFAULT_VALUE="默认大概多少公司"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_7 Select a File 3"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJjaG9pY2VTZXR0aW5ncyI6eyJjaG9pY2VTZXQiOiJ1c2VyRGVmaW5lZCIsImNob2ljZXMiOlt7ImRpc3BsYXkiOiIxIiwidmFsdWUiOiIxIn0seyJkaXNwbGF5IjoiMiIsInZhbHVlIjoiMiJ9LHsiZGlzcGxheSI6IjMiLCJ2YWx1ZSI6IjMifSx7ImRpc3BsYXkiOiI0IiwidmFsdWUiOiI0In1dLCJzb3J0IjoibmF0dXJhbFNvcnQifSwiZGVmYXVsdFZhbHVlIjoiMyIsIm5hbWUiOiJQQVJBTUVURVIiLCJwcm9tcHQiOiJTZWxlY3QgYSBDaG9pY2UiLCJyZXF1aXJlZCI6dHJ1ZSwic2hvd1Byb21wdCI6dHJ1ZSwidHlwZSI6ImRyb3Bkb3duIiwidmFsdWVUeXBlIjoic3RyaW5nIn0seyJhY2Nlc3NNb2RlIjoicmVhZCIsImFsbG93VVJMIjpmYWxzZSwiZGVmYXVsdFZhbHVlIjoiJChGTUVfTUZfRElSKXBhcmFtZXRcXGEiLCJpdGVtc1RvU2VsZWN0IjoiZm9sZGVycyIsIm5hbWUiOiJQQVJBTUVURVJfMiIsInByb21wdCI6IlNlbGVjdCBhIEZpbGUiLCJyZXF1aXJlZCI6dHJ1ZSwic2VsZWN0TXVsdGlwbGUiOmZhbHNlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6dHJ1ZSwidmFsdWVUeXBlIjoic3RyaW5nIiwidmlzaWJpbGl0eSI6ImhpZGRlbkVuYWJsZWQifSx7ImFjY2Vzc01vZGUiOiJyZWFkIiwiYWxsb3dVUkwiOmZhbHNlLCJkZWZhdWx0VmFsdWUiOiIkKEZNRV9NRl9ESVIpcGFyYW1ldFxcYiIsIml0ZW1zVG9TZWxlY3QiOiJmb2xkZXJzIiwibmFtZSI6IlBBUkFNRVRFUl8zIiwicHJvbXB0IjoiU2VsZWN0IGEgRmlsZSAyIiwicmVxdWlyZWQiOmZhbHNlLCJzZWxlY3RNdWx0aXBsZSI6ZmFsc2UsInNob3dQcm9tcHQiOnRydWUsInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOnRydWUsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiZGVmYXVsdFZhbHVlIjoiWUVTIiwibmFtZSI6IlBBUkFNRVRFUl80IiwicHJvbXB0IjoiU2VsZWN0IFllcy9ObyIsInJlcXVpcmVkIjp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbIm5vbmUiXSwidHlwZSI6ImNoZWNrYm94IiwidmFsdWVUeXBlIjoic3RyaW5nIn0seyJkZWZhdWx0VmFsdWUiOiIiLCJuYW1lIjoiUEFSQU1FVEVSXzUiLCJwcm9tcHQiOiJTZWxlY3QgWWVzL05vIDIiLCJyZXF1aXJlZCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJub25lIl0sInR5cGUiOiJjaGVja2JveCIsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiZGVmYXVsdFZhbHVlIjoi6buY6K6k5aSn5qaC5aSa5bCR5YWs5Y+4IiwibmFtZSI6IlBBUkFNRVRFUl82IiwicHJvbXB0IjoiRW50ZXIgVGV4dCBEYXRhIiwicmVxdWlyZWQiOnRydWUsInNob3dFZGl0QnV0dG9uIjpmYWxzZSwidHlwZSI6InRleHQiLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifSx7ImFjY2Vzc01vZGUiOiJ3cml0ZSIsImFsbG93VVJMIjpmYWxzZSwiZGVmYXVsdFZhbHVlIjoiIiwiaXRlbXNUb1NlbGVjdCI6ImZvbGRlcnMiLCJuYW1lIjoiUEFSQU1FVEVSXzciLCJwcm9tcHQiOiJTZWxlY3QgYSBGaWxlIDMiLCJyZXF1aXJlZCI6dHJ1ZSwic2VsZWN0TXVsdGlwbGUiOmZhbHNlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6dHJ1ZSwidmFsdWVUeXBlIjoic3RyaW5nIn1dfQ=="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE="3"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI CHOICE PARAMETER 1%2%3%4 Select a Choice"
#! />
#!     <INFO NAME="PARAMETER_2" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)paramet\a"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI IGNORE NO_EDIT DIRNAME_SRC_OR_ATTR PARAMETER_2 Select a File"
#! />
#!     <INFO NAME="PARAMETER_3" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)paramet\b"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL DIRNAME_SRC PARAMETER_3 Select a File 2"
#! />
#!     <INFO NAME="PARAMETER_4" 
#!   DEFAULT_VALUE="YES"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI LITERAL CHECKBOX PARAMETER_4 YES%NO Select Yes/No"
#! />
#!     <INFO NAME="PARAMETER_5" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI LITERAL CHECKBOX PARAMETER_5 YES%NO Select Yes/No 2"
#! />
#!     <INFO NAME="PARAMETER_6" 
#!   DEFAULT_VALUE="默认大概多少公司"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI STRING PARAMETER_6 Enter Text Data"
#! />
#!     <INFO NAME="PARAMETER_7" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR PARAMETER_7 Select a File 3"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-1287.5128751287511 159.37659376593768"
#!   BOUNDING_RECT="-1287.5128751287511 159.37659376593768 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-715.51287512875115 159.37659376593768"
#!   BOUNDING_RECT="-715.51287512875115 159.37659376593768 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-147.51287512875115 37.250187501875047"
#!   BOUNDING_RECT="-147.51287512875115 37.250187501875047 454 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="1778.3758437584379 97.250187501875047"
#!   BOUNDING_RECT="1778.3758437584379 97.250187501875047 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_7)&lt;backslash&gt;&lt;at&gt;Value&lt;openparen&gt;path_rootname&lt;closeparen&gt;.kml"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="OGCKML"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,DOCUMENT_NAME&lt;comma&gt;&lt;comma&gt;DOCUMENT_DESC&lt;comma&gt;&lt;comma&gt;DOCUMENT_VISIBILITY&lt;comma&gt;yes&lt;comma&gt;OMIT_DOCUMENT_ELEMENT&lt;comma&gt;no&lt;comma&gt;ATOM_AUTHOR_NAME&lt;comma&gt;&lt;comma&gt;ATOM_AUTHOR_EMAIL&lt;comma&gt;&lt;comma&gt;ATOM_LINK_HREF&lt;comma&gt;&lt;comma&gt;WATERMARK_NAME&lt;comma&gt;&lt;comma&gt;WATERMARK_SNIPPET&lt;comma&gt;&lt;comma&gt;WATERMARK_ICON&lt;comma&gt;&lt;comma&gt;ATTR_IN_DESCRIPTION&lt;comma&gt;yes&lt;comma&gt;HTML_DESCRIPTIONS&lt;comma&gt;yes&lt;comma&gt;KML_WRT_EXTD_PARA&lt;comma&gt;&lt;comma&gt;STYLE_DOC&lt;comma&gt;&lt;comma&gt;SCHEMA_DOC&lt;comma&gt;&lt;comma&gt;KML_WRT_RAST_PARA&lt;comma&gt;&lt;comma&gt;DETECT_RASTERS&lt;comma&gt;yes&lt;comma&gt;EXEC_GO_PIPELINE&lt;comma&gt;no&lt;comma&gt;RASTER_MODE&lt;comma&gt;write&lt;comma&gt;RASTER_FORMAT&lt;comma&gt;tiff&lt;comma&gt;TEXTURE_FORMAT&lt;comma&gt;NONE&lt;comma&gt;KML_WRT_NET_PARA&lt;comma&gt;&lt;comma&gt;AUTO_CREATE_NETWORK_LINKS&lt;comma&gt;no&lt;comma&gt;KML21_TARGET_HREF&lt;comma&gt;&lt;comma&gt;KML_WRT_PIPE_PARA&lt;comma&gt;&lt;comma&gt;REGIONATE_DATA&lt;comma&gt;no&lt;comma&gt;EXEC_PO_PIPELINE&lt;comma&gt;no&lt;comma&gt;KML_WRT_WRT_PARA&lt;comma&gt;&lt;comma&gt;WRITE_3D_GEOM_AS_POLYGONS&lt;comma&gt;no&lt;comma&gt;WRITE_TEXTURES_TXT_FILE&lt;comma&gt;no&lt;comma&gt;DATASET_HINT&lt;comma&gt;&lt;comma&gt;COPY_ICON&lt;comma&gt;yes&lt;comma&gt;OUTPUT_SCHEMA&lt;comma&gt;yes&lt;comma&gt;ORIENTATION&lt;comma&gt;none&lt;comma&gt;LOG_VERBOSE&lt;comma&gt;no&lt;comma&gt;CREATE_EMPTY_FOLDERS&lt;comma&gt;no&lt;comma&gt;MOVE_TO_KML_LOCAL_COORDSYS&lt;comma&gt;yes&lt;comma&gt;KML21_FANOUT_TYPE&lt;comma&gt;folder&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,OGCKML"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="OGCKML_DOCUMENT_VISIBILITY,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Document&lt;space&gt;visible&lt;space&gt;on&lt;space&gt;load:,OGCKML_CREATE_EMPTY_FOLDERS,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Create&lt;space&gt;Empty&lt;space&gt;Folders:,OGCKML_ORIENTATION,&quot;OPTIONAL LOOKUP_CHOICE None,none%Left,left%Right,right&quot;,OGCKML&lt;space&gt;Force&lt;space&gt;Geometry&lt;space&gt;Orientation:,OGCKML_RASTER_FORMAT,&quot;OPTIONAL CHOICE jpeg%tiff%png%gif&quot;,OGCKML&lt;space&gt;Raster&lt;space&gt;Output&lt;space&gt;Format:,OGCKML_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,OGCKML&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,OGCKML_OMIT_DOCUMENT_ELEMENT,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Omit&lt;space&gt;Document&lt;space&gt;Element:,OGCKML_REGIONATE_DATA,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Regionate&lt;space&gt;Vectors&lt;space&gt;&lt;openparen&gt;Beta&lt;closeparen&gt;:,OGCKML_ATOM_AUTHOR_NAME,&quot;OPTIONAL TEXT_ENCODED&quot;,OGCKML&lt;space&gt;Author&lt;space&gt;Name:,OGCKML_WATERMARK_ICON,&quot;OPTIONAL TEXT&quot;,OGCKML&lt;space&gt;Watermark&lt;space&gt;Overlay&lt;space&gt;Icon:,OGCKML_TEXTURE_FORMAT,&quot;OPTIONAL LOOKUP_CHOICE Auto,NONE%PNG,PNGRASTER%JPEG%GIF,GIFRASTER%BMP%TIFF&quot;,OGCKML&lt;space&gt;Preferred&lt;space&gt;Texture&lt;space&gt;Format:,OGCKML_KML21_FANOUT_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Folder,folder%Subfolder,subfolder&quot;,OGCKML&lt;space&gt;Feature&lt;space&gt;Type&lt;space&gt;Fanout&lt;space&gt;Mode:,OGCKML_EXEC_PO_PIPELINE,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Pyramid&lt;space&gt;PhotoOverlays&lt;space&gt;&lt;openparen&gt;Beta&lt;closeparen&gt;:,OGCKML_COPY_ICON,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Copy&lt;space&gt;icons&lt;space&gt;to&lt;space&gt;destination&lt;space&gt;dataset:,OGCKML_LOG_VERBOSE,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Verbose&lt;space&gt;Logging:,OGCKML_DETECT_RASTERS,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Generate&lt;space&gt;Raster&lt;space&gt;Ground&lt;space&gt;Overlays:,OGCKML_KML21_TARGET_HREF,&quot;OPTIONAL TEXT&quot;,OGCKML&lt;space&gt;Update&lt;space&gt;Target&lt;space&gt;HREF&lt;space&gt;&lt;openparen&gt;Network&lt;space&gt;Link&lt;space&gt;Control&lt;closeparen&gt;:,OGCKML_WATERMARK_SNIPPET,&quot;OPTIONAL TEXT_ENCODED&quot;,OGCKML&lt;space&gt;Watermark&lt;space&gt;Snippet&lt;space&gt;Text:,OGCKML_KML_WRT_RAST_PARA,&quot;OPTIONAL DISCLOSUREGROUP DETECT_RASTERS%EXEC_GO_PIPELINE%RASTER_MODE%RASTER_FORMAT%TEXTURE_FORMAT&quot;,OGCKML&lt;space&gt;Raster&lt;space&gt;Features,OGCKML_RASTER_MODE,&quot;OPTIONAL LOOKUP_CHOICE Write,write%Copy,copy%Relative,relative&quot;,OGCKML&lt;space&gt;Raster&lt;space&gt;Handling&lt;space&gt;Mode:,OGCKML_KML_WRT_WRT_PARA,&quot;OPTIONAL DISCLOSUREGROUP WRITE_3D_GEOM_AS_POLYGONS%WRITE_TEXTURES_TXT_FILE%DATASET_HINT%COPY_ICON%OUTPUT_SCHEMA%ORIENTATION%LOG_VERBOSE%CREATE_EMPTY_FOLDERS%MOVE_TO_KML_LOCAL_COORDSYS%KML21_FANOUT_TYPE&quot;,OGCKML&lt;space&gt;Writer&lt;space&gt;Parameters,OGCKML_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,OGCKML&lt;space&gt;,OGCKML_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,OGCKML&lt;space&gt;,OGCKML_WATERMARK_NAME,&quot;OPTIONAL TEXT_ENCODED&quot;,OGCKML&lt;space&gt;Watermark&lt;space&gt;Name:,OGCKML_EXEC_GO_PIPELINE,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Generate&lt;space&gt;Super-Overlays:,OGCKML_ATOM_AUTHOR_EMAIL,&quot;OPTIONAL TEXT_ENCODED&quot;,OGCKML&lt;space&gt;Author&lt;space&gt;Email:,OGCKML_ATOM_LINK_HREF,&quot;OPTIONAL TEXT_ENCODED&quot;,OGCKML&lt;space&gt;Author&lt;space&gt;URL:,OGCKML_ATTR_IN_DESCRIPTION,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Create&lt;space&gt;Attribute&lt;space&gt;Table&lt;space&gt;in&lt;space&gt;Description&lt;space&gt;Balloon:,OGCKML_KML_WRT_EXTD_PARA,&quot;OPTIONAL DISCLOSUREGROUP STYLE_DOC%SCHEMA_DOC&quot;,OGCKML&lt;space&gt;External&lt;space&gt;Documents,OGCKML_SCHEMA_DOC,&quot;OPTIONAL TEXT&quot;,OGCKML&lt;space&gt;External&lt;space&gt;Schema&lt;space&gt;Document:,OGCKML_WRITE_TEXTURES_TXT_FILE,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Write&lt;space&gt;texture&lt;space&gt;list&lt;space&gt;to&lt;space&gt;textures.txt:,OGCKML_MOVE_TO_KML_LOCAL_COORDSYS,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Move&lt;space&gt;To&lt;space&gt;Local&lt;space&gt;Coordinate&lt;space&gt;System:,OGCKML_HTML_DESCRIPTIONS,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Use&lt;space&gt;HTML&lt;space&gt;in&lt;space&gt;Description&lt;space&gt;Balloon:,OGCKML_DOCUMENT_DESC,&quot;OPTIONAL TEXT_ENCODED&quot;,OGCKML&lt;space&gt;Document&lt;space&gt;description:,OGCKML_STYLE_DOC,&quot;OPTIONAL TEXT&quot;,OGCKML&lt;space&gt;External&lt;space&gt;Style&lt;space&gt;Document:,OGCKML_DOCUMENT_NAME,&quot;OPTIONAL TEXT_ENCODED&quot;,OGCKML&lt;space&gt;Root&lt;space&gt;Document&lt;space&gt;name:,OGCKML_OUTPUT_SCHEMA,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Create&lt;space&gt;KML&lt;space&gt;Schema&lt;space&gt;Elements:,OGCKML_DATASET_HINT,&quot;OPTIONAL TEXT&quot;,OGCKML&lt;space&gt;KML&lt;space&gt;hint&lt;space&gt;attribute:,OGCKML_KML_WRT_NET_PARA,&quot;OPTIONAL DISCLOSUREGROUP AUTO_CREATE_NETWORK_LINKS%KML21_TARGET_HREF&quot;,OGCKML&lt;space&gt;Network&lt;space&gt;Link&lt;space&gt;Parameters,OGCKML_WRITE_3D_GEOM_AS_POLYGONS,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Write&lt;space&gt;3D&lt;space&gt;Geometry&lt;space&gt;As&lt;space&gt;Polygons:,OGCKML_KML_WRT_PIPE_PARA,&quot;OPTIONAL DISCLOSUREGROUP REGIONATE_DATA%EXEC_PO_PIPELINE&quot;,OGCKML&lt;space&gt;Pipelined&lt;space&gt;Features,OGCKML_AUTO_CREATE_NETWORK_LINKS,&quot;OPTIONAL CHOICE yes%no&quot;,OGCKML&lt;space&gt;Create&lt;space&gt;Network&lt;space&gt;Links&lt;space&gt;for&lt;space&gt;Referenced&lt;space&gt;Documents:"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_ATOM_AUTHOR_EMAIL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_ATOM_AUTHOR_NAME" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_ATOM_LINK_HREF" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_ATTR_IN_DESCRIPTION" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_AUTO_CREATE_NETWORK_LINKS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_COPY_ICON" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_CREATE_EMPTY_FOLDERS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_DATASET_HINT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_DETECT_RASTERS" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_DOCUMENT_DESC" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_DOCUMENT_NAME" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_DOCUMENT_VISIBILITY" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_EXEC_GO_PIPELINE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_EXEC_PO_PIPELINE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_HTML_DESCRIPTIONS" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_KML21_FANOUT_TYPE" PARM_VALUE="folder"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_KML21_TARGET_HREF" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_KML_WRT_EXTD_PARA" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_KML_WRT_NET_PARA" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_KML_WRT_PIPE_PARA" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_KML_WRT_RAST_PARA" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_KML_WRT_WRT_PARA" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_LOG_VERBOSE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_MOVE_TO_KML_LOCAL_COORDSYS" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_OMIT_DOCUMENT_ELEMENT" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_ORIENTATION" PARM_VALUE="none"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_OUTPUT_SCHEMA" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_RASTER_FORMAT" PARM_VALUE="tiff"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_RASTER_MODE" PARM_VALUE="write"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_REGIONATE_DATA" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_SCHEMA_DOC" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_STYLE_DOC" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_TEXTURE_FORMAT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_WATERMARK_ICON" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_WATERMARK_NAME" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_WATERMARK_SNIPPET" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OGCKML_WRITE_3D_GEOM_AS_POLYGONS" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OGCKML_WRITE_TEXTURES_TXT_FILE" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="Passed:Passed,ftp_feature_type_name,Passed,ftp_writer,OGCKML,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,path_unix&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_windows&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_rootname&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_filename&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_extension&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_directory_unix&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_directory_windows&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;path_type&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;10&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_geometry&lt;lt&gt;opencurly&lt;gt&gt;0&lt;lt&gt;closecurly&lt;gt&gt;&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;200&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_feature_type_name&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;attribute&lt;lt&gt;opencurly&lt;gt&gt;&lt;lt&gt;closecurly&lt;gt&gt;.name&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;attribute&lt;lt&gt;opencurly&lt;gt&gt;&lt;lt&gt;closecurly&lt;gt&gt;.fme_data_type&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;attribute&lt;lt&gt;opencurly&lt;gt&gt;&lt;lt&gt;closecurly&lt;gt&gt;.native_data_type&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_format_short_name&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_format_long_name&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;fme_schema_handling&lt;comma&gt;kml_char&lt;lt&gt;openparen&lt;gt&gt;255&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_parameters,ogc_layer_group&lt;comma&gt;&lt;comma&gt;ogc_appearance_group&lt;comma&gt;&lt;comma&gt;ogc_advanced_group&lt;comma&gt;&lt;comma&gt;KML21_INFORMATION_POINT_ICON&lt;comma&gt;&lt;comma&gt;KML21_ICON_COLOR&lt;comma&gt;&lt;comma&gt;KML21_FILL_COLOR&lt;comma&gt;&lt;comma&gt;KML21_FILL_OPACITY&lt;comma&gt;&lt;comma&gt;KML21_PEN_COLOR&lt;comma&gt;&lt;comma&gt;KML21_PEN_OPACITY&lt;comma&gt;&lt;comma&gt;KML21_SORT_BY_ATTRIBUTE&lt;comma&gt;&lt;comma&gt;KML21_HTML_DESCRIPTIONS&lt;comma&gt;&lt;comma&gt;KML21_ATTR_IN_DESCRIPTION&lt;comma&gt;&lt;comma&gt;KML21_DOCUMENT_FILENAME&lt;comma&gt;&lt;comma&gt;KML21_CREATE_FOLDER_FOR_FEATURE_TYPE&lt;comma&gt;yes"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,99999,ATTR_TYPE_MAP,kml_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;kml_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;kml_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;kml_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;kml_char&lt;openparen&gt;255&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;kml_char&lt;openparen&gt;255&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;kml_char&lt;openparen&gt;255&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;kml_char&lt;openparen&gt;255&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;kml_buffer&lt;comma&gt;fme_buffer&lt;comma&gt;kml_datetime&lt;comma&gt;fme_datetime&lt;comma&gt;kml_date&lt;comma&gt;fme_date&lt;comma&gt;kml_time&lt;comma&gt;fme_time&lt;comma&gt;kml_int16&lt;comma&gt;fme_int16&lt;comma&gt;kml_int16&lt;comma&gt;fme_int8&lt;comma&gt;kml_int16&lt;comma&gt;fme_uint8&lt;comma&gt;kml_int32&lt;comma&gt;fme_int32&lt;comma&gt;kml_int32&lt;comma&gt;fme_uint16&lt;comma&gt;kml_real32&lt;comma&gt;fme_real32&lt;comma&gt;kml_real64&lt;comma&gt;fme_real64&lt;comma&gt;kml_real64&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;kml_real64&lt;comma&gt;fme_uint32&lt;comma&gt;kml_char&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_int64&lt;comma&gt;kml_char&lt;openparen&gt;20&lt;closeparen&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;kml_boolean&lt;comma&gt;fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;OGCKML.fmf,FORMAT_NAME,OGCKML,GEOM_MAP,kml_no_geom&lt;comma&gt;fme_no_geom&lt;comma&gt;kml_aggregate&lt;comma&gt;fme_collection&lt;comma&gt;kml_point&lt;comma&gt;fme_point&lt;comma&gt;kml_point&lt;comma&gt;fme_text&lt;comma&gt;kml_line&lt;comma&gt;fme_line&lt;comma&gt;kml_area&lt;comma&gt;fme_polygon&lt;comma&gt;kml_area&lt;comma&gt;fme_rectangle&lt;comma&gt;kml_area&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;kml_raster&lt;comma&gt;fme_raster&lt;comma&gt;kml_surface&lt;comma&gt;fme_surface&lt;comma&gt;kml_solid&lt;comma&gt;fme_solid&lt;comma&gt;kml_area&lt;comma&gt;fme_ellipse&lt;comma&gt;kml_line&lt;comma&gt;fme_arc&lt;comma&gt;kml_area&lt;comma&gt;fme_point_cloud&lt;comma&gt;kml_area&lt;comma&gt;fme_voxel_grid&lt;comma&gt;kml_no_geom&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,OGCKML,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;ogc_layer_group&lt;space&gt;ogc_appearance_group%ogc_advanced_group&lt;space&gt;&lt;space&gt;Feature&lt;space&gt;Type&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;ogc_appearance_group&lt;space&gt;KML21_FILL_COLOR%KML21_FILL_OPACITY%KML21_PEN_COLOR%KML21_PEN_OPACITY%KML21_SORT_BY_ATTRIBUTE%KML21_INFORMATION_POINT_ICON%KML21_ICON_COLOR&lt;space&gt;&lt;space&gt;Appearance&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;ogc_advanced_group&lt;space&gt;KML21_ATTR_IN_DESCRIPTION%KML21_DOCUMENT_FILENAME%KML21_CREATE_FOLDER_FOR_FEATURE_TYPE%KML21_HTML_DESCRIPTIONS&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ICONPICK&lt;space&gt;KML21_INFORMATION_POINT_ICON&lt;space&gt;icons&lt;space&gt;Information&lt;space&gt;Point&lt;space&gt;Icon&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;COLOR_PICK&lt;space&gt;KML21_ICON_COLOR&lt;space&gt;Icon&lt;space&gt;Color&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;COLOR_PICK&lt;space&gt;KML21_FILL_COLOR&lt;space&gt;Fill&lt;space&gt;Color&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;KML21_FILL_OPACITY&lt;space&gt;Fill&lt;space&gt;Opacity&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;COLOR_PICK&lt;space&gt;KML21_PEN_COLOR&lt;space&gt;Pen&lt;space&gt;Color&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;FLOAT&lt;space&gt;KML21_PEN_OPACITY&lt;space&gt;Pen&lt;space&gt;Opacity&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ATTR&lt;space&gt;KML21_SORT_BY_ATTRIBUTE&lt;space&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;space&gt;Sort&lt;space&gt;By&lt;space&gt;Attribute&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;KML21_HTML_DESCRIPTIONS&lt;space&gt;yes%no&lt;space&gt;Use&lt;space&gt;HTML&lt;space&gt;in&lt;space&gt;Description&lt;space&gt;Balloon&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;CHOICE&lt;space&gt;KML21_ATTR_IN_DESCRIPTION&lt;space&gt;yes%no&lt;space&gt;Create&lt;space&gt;Attribute&lt;space&gt;Table&lt;space&gt;in&lt;space&gt;Description&lt;space&gt;Balloon&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;KML21_DOCUMENT_FILENAME&lt;space&gt;Parent&lt;space&gt;Document&lt;space&gt;File&lt;space&gt;Name&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;KML21_CREATE_FOLDER_FOR_FEATURE_TYPE&lt;space&gt;yes%no&lt;space&gt;Create&lt;space&gt;Folder&lt;space&gt;for&lt;space&gt;Feature&lt;space&gt;Type&lt;quote&gt;&lt;comma&gt;yes,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;KML21_INFORMATION_POINT_ICON&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_OPACITY&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_FILL_OPACITY&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_PEN_OPACITY&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_ICON_COLOR&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_FILL_COLOR&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_PEN_COLOR&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_SORT_BY_ATTRIBUTE&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_ATTR_IN_DESCRIPTION&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_HTML_DESCRIPTIONS&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_DOCUMENT_FILENAME&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;KML21_CREATE_FOLDER_FOR_FEATURE_TYPE&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;KML21_INFORMATION_POINT_ICON&lt;space&gt;KML21_REGIONATOR_PIPELINE&lt;space&gt;KML21_GO_PYRAMIDER_PIPELINE&lt;space&gt;KML21_PO_PYRAMIDER_PIPELINE&lt;quote&gt;&lt;comma&gt;MIME_TYPE&lt;comma&gt;&lt;quote&gt;.kml&lt;space&gt;application&lt;solidus&gt;vnd.google-earth.kml+xml&lt;space&gt;.kmz&lt;space&gt;application&lt;solidus&gt;vnd.google-earth.kmz&lt;space&gt;ADD_DISPOSITION&lt;quote&gt;&lt;comma&gt;SUPPORTS_ESTABLISHED_CACHE&lt;comma&gt;YES&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;ALWAYS&lt;comma&gt;NETWORK_PROXY&lt;comma&gt;NO&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Google&lt;space&gt;Earth&lt;space&gt;KML&lt;space&gt;file&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;name&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Google&lt;space&gt;Earth&lt;space&gt;KML&lt;space&gt;File&lt;quote&gt;,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1062.1210312103121 159.37659376593768"
#!   BOUNDING_RECT="1062.1210312103121 159.37659376593768 457.00106825772946 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="Decelerator"
#!   VERSION="0"
#!   POSITION="495.3040780407805 98.313390633906366"
#!   BOUNDING_RECT="495.3040780407805 98.313390633906366 454 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DECEL_TYPE" PARM_VALUE="Per Feature Delay"/>
#!     <XFORM_PARM PARM_NAME="DELAY" PARM_VALUE="10"/>
#!     <XFORM_PARM PARM_NAME="FEATURES_PER_SECOND" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Decelerator"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="13"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 &lt;lt&gt;SCHEMA&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 Passed"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="14"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="9"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="6"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO PARAMETER 3

DEFAULT_MACRO PARAMETER_2 $(FME_MF_DIR)paramet\a

DEFAULT_MACRO PARAMETER_3 $(FME_MF_DIR)paramet\b

DEFAULT_MACRO PARAMETER_4 YES

DEFAULT_MACRO PARAMETER_5 

DEFAULT_MACRO PARAMETER_6 默认大概多少公司

DEFAULT_MACRO PARAMETER_7 

# ============================================================================
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_2$encode)} == {}} { puts_real {Parameter 'PARAMETER_2' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_4$encode)} == {}} { puts_real {Parameter 'PARAMETER_4' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_5$encode)} == {}} { puts_real {Parameter 'PARAMETER_5' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_6$encode)} == {}} { puts_real {Parameter 'PARAMETER_6' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(PARAMETER_7$encode)} == {}} { puts_real {Parameter 'PARAMETER_7' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)shp转KML.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME shp转KML
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
# This is just a simple delay, which is given a # of seconds to stall
Tcl2 proc Decelerator_delay {seconds unused} {     after [expr round($seconds * 1000)]; }
Tcl2 set Decelerator_clockMillisecondsWhenLastFeatureWentBy [clock clicks -milliseconds];
# This proc ensures that no more than some maximum number of features get through each second
Tcl2 proc Decelerator_maxpersecond {unused maxFeaturesPerSecond} {     global Decelerator_clockMillisecondsWhenLastFeatureWentBy;     set minimumDelayInMilliseconds [expr 1000.0 / $maxFeaturesPerSecond];     set clockMillisecondsNow [clock clicks -milliseconds];     set milliSecondsSinceLastFeature [expr $clockMillisecondsNow -                    [set Decelerator_clockMillisecondsWhenLastFeatureWentBy]];     if {$milliSecondsSinceLastFeature < $minimumDelayInMilliseconds} {         after [expr round($minimumDelayInMilliseconds - $milliSecondsSinceLastFeature)];     };     set Decelerator_clockMillisecondsWhenLastFeatureWentBy [clock clicks -milliseconds]; }
# Now set up which of the above two delay procedures we'll actually
# use by renaming the one we want to "Decelerator_Waiter"
# Then that is what we'll actually call when a feature goes by.
Tcl2 if { {Per Feature Delay} == {Per Feature Delay} } {         rename Decelerator_delay Decelerator_Waiter;      } else {         rename Decelerator_maxpersecond Decelerator_Waiter;      };
# If we're in Per Feature Delay mode, AND the Per Feature Delay is 0, let's very efficiently split
# any feature table into individual features. If an individual feature would go in, this is a no op
INCLUDE [    if { ( {Per Feature Delay} == {Per Feature Delay} ) && ( {10} == {0} ) } {       puts "MACRO SPLIT_FEATURE_TABLE *";       puts "MACRO NORMAL_DECELERATION _NO_";    } else {       puts "MACRO SPLIT_FEATURE_TABLE _NO_";       puts "MACRO NORMAL_DECELERATION *";    } ]
FACTORY_DEF {$(SPLIT_FEATURE_TABLE)} FeatureTableSplitterFactory    FACTORY_NAME { Decelerator }    INPUT  FEATURE_TYPE Tester_PASSED    OUTPUT { FEATURES FEATURE_TYPE Decelerator_OUTPUT          }
# Above for feature table splitting case (when per feature delay is 0, below for everything else
FACTORY_DEF {$(NORMAL_DECELERATION)} TestFactory    FACTORY_NAME { Decelerator_DelayRejector }    INPUT  FEATURE_TYPE Tester_PASSED    TEST { "10" TYPE NUM ENCODED }    TEST { PERFEATDELAY != PERFEATDELAY }    BOOLEAN_OPERATOR OR    OUTPUT { FAILED FEATURE_TYPE Decelerator_<REJECTED>       fme_rejection_code "INVALID_PARAMETER_DELAY_PER_FEATURE"        }    OUTPUT { PASSED FEATURE_TYPE Decelerator__toMaximumRejector__ }
FACTORY_DEF {$(NORMAL_DECELERATION)} TestFactory    FACTORY_NAME { Decelerator_MaximumRejector }    INPUT { FEATURE_TYPE Decelerator__toMaximumRejector__ }    TEST { "<Unused>" TYPE NUM ENCODED }    TEST { PERFEATDELAY != FEATPERSEC }    BOOLEAN_OPERATOR OR    OUTPUT { FAILED FEATURE_TYPE Decelerator_<REJECTED>       fme_rejection_code "INVALID_PARAMETER_MAXIMUM_FEATURES_PER_SECOND"        }    OUTPUT { PASSED FEATURE_TYPE Decelerator__toOutput__ }
FACTORY_DEF {$(NORMAL_DECELERATION)} TeeFactory    FACTORY_NAME { Decelerator_Decelerator }    INPUT { FEATURE_TYPE Decelerator__toOutput__ }    OUTPUT { FEATURE_TYPE Decelerator_OUTPUT         @Tcl2("Decelerator_Waiter {10} {<Unused>}")          }
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_2_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Decelerator_OUTPUT    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { SCHEMA FEATURE_TYPE FeatureReader_2_<SCHEMA>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_2_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_2_<Rejected> INPUT FEATURE_TYPE FeatureReader_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_7$encode)<backslash><at>Value<openparen>path_rootname<closeparen>.kml,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { OGCKML }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,DOCUMENT_NAME<comma><comma>DOCUMENT_DESC<comma><comma>DOCUMENT_VISIBILITY<comma>yes<comma>OMIT_DOCUMENT_ELEMENT<comma>no<comma>ATOM_AUTHOR_NAME<comma><comma>ATOM_AUTHOR_EMAIL<comma><comma>ATOM_LINK_HREF<comma><comma>WATERMARK_NAME<comma><comma>WATERMARK_SNIPPET<comma><comma>WATERMARK_ICON<comma><comma>ATTR_IN_DESCRIPTION<comma>yes<comma>HTML_DESCRIPTIONS<comma>yes<comma>KML_WRT_EXTD_PARA<comma><comma>STYLE_DOC<comma><comma>SCHEMA_DOC<comma><comma>KML_WRT_RAST_PARA<comma><comma>DETECT_RASTERS<comma>yes<comma>EXEC_GO_PIPELINE<comma>no<comma>RASTER_MODE<comma>write<comma>RASTER_FORMAT<comma>tiff<comma>TEXTURE_FORMAT<comma>NONE<comma>KML_WRT_NET_PARA<comma><comma>AUTO_CREATE_NETWORK_LINKS<comma>no<comma>KML21_TARGET_HREF<comma><comma>KML_WRT_PIPE_PARA<comma><comma>REGIONATE_DATA<comma>no<comma>EXEC_PO_PIPELINE<comma>no<comma>KML_WRT_WRT_PARA<comma><comma>WRITE_3D_GEOM_AS_POLYGONS<comma>no<comma>WRITE_TEXTURES_TXT_FILE<comma>no<comma>DATASET_HINT<comma><comma>COPY_ICON<comma>yes<comma>OUTPUT_SCHEMA<comma>yes<comma>ORIENTATION<comma>none<comma>LOG_VERBOSE<comma>no<comma>CREATE_EMPTY_FOLDERS<comma>no<comma>MOVE_TO_KML_LOCAL_COORDSYS<comma>yes<comma>KML21_FANOUT_TYPE<comma>folder<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,OGCKML" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,99999,ATTR_TYPE_MAP,kml_char<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>kml_char<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>kml_char<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>kml_char<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>kml_char<openparen>255<closeparen><comma>fme_buffer<comma>kml_char<openparen>255<closeparen><comma>fme_binarybuffer<comma>kml_char<openparen>255<closeparen><comma>fme_xml<comma>kml_char<openparen>255<closeparen><comma>fme_json<comma>kml_buffer<comma>fme_buffer<comma>kml_datetime<comma>fme_datetime<comma>kml_date<comma>fme_date<comma>kml_time<comma>fme_time<comma>kml_int16<comma>fme_int16<comma>kml_int16<comma>fme_int8<comma>kml_int16<comma>fme_uint8<comma>kml_int32<comma>fme_int32<comma>kml_int32<comma>fme_uint16<comma>kml_real32<comma>fme_real32<comma>kml_real64<comma>fme_real64<comma>kml_real64<comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma>kml_real64<comma>fme_uint32<comma>kml_char<openparen>20<closeparen><comma>fme_int64<comma>kml_char<openparen>20<closeparen><comma>fme_uint64<comma>kml_boolean<comma>fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>OGCKML.fmf,FORMAT_NAME,OGCKML,GEOM_MAP,kml_no_geom<comma>fme_no_geom<comma>kml_aggregate<comma>fme_collection<comma>kml_point<comma>fme_point<comma>kml_point<comma>fme_text<comma>kml_line<comma>fme_line<comma>kml_area<comma>fme_polygon<comma>kml_area<comma>fme_rectangle<comma>kml_area<comma>fme_rounded_rectangle<comma>kml_raster<comma>fme_raster<comma>kml_surface<comma>fme_surface<comma>kml_solid<comma>fme_solid<comma>kml_area<comma>fme_ellipse<comma>kml_line<comma>fme_arc<comma>kml_area<comma>fme_point_cloud<comma>kml_area<comma>fme_voxel_grid<comma>kml_no_geom<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,OGCKML,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>ogc_layer_group<space>ogc_appearance_group%ogc_advanced_group<space><space>Feature<space>Type<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>ogc_appearance_group<space>KML21_FILL_COLOR%KML21_FILL_OPACITY%KML21_PEN_COLOR%KML21_PEN_OPACITY%KML21_SORT_BY_ATTRIBUTE%KML21_INFORMATION_POINT_ICON%KML21_ICON_COLOR<space><space>Appearance<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>ogc_advanced_group<space>KML21_ATTR_IN_DESCRIPTION%KML21_DOCUMENT_FILENAME%KML21_CREATE_FOLDER_FOR_FEATURE_TYPE%KML21_HTML_DESCRIPTIONS<space>Advanced<quote><comma><comma><quote>GUI<space>OPTIONAL<space>ICONPICK<space>KML21_INFORMATION_POINT_ICON<space>icons<space>Information<space>Point<space>Icon<quote><comma><comma><quote>GUI<space>OPTIONAL<space>COLOR_PICK<space>KML21_ICON_COLOR<space>Icon<space>Color<quote><comma><comma><quote>GUI<space>OPTIONAL<space>COLOR_PICK<space>KML21_FILL_COLOR<space>Fill<space>Color<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>KML21_FILL_OPACITY<space>Fill<space>Opacity<quote><comma><comma><quote>GUI<space>OPTIONAL<space>COLOR_PICK<space>KML21_PEN_COLOR<space>Pen<space>Color<quote><comma><comma><quote>GUI<space>OPTIONAL<space>FLOAT<space>KML21_PEN_OPACITY<space>Pen<space>Opacity<quote><comma><comma><quote>GUI<space>OPTIONAL<space>ATTR<space>KML21_SORT_BY_ATTRIBUTE<space><quote><quote><quote><quote><space>Sort<space>By<space>Attribute<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>KML21_HTML_DESCRIPTIONS<space>yes%no<space>Use<space>HTML<space>in<space>Description<space>Balloon<quote><comma><comma><quote>GUI<space>OPTIONAL<space>CHOICE<space>KML21_ATTR_IN_DESCRIPTION<space>yes%no<space>Create<space>Attribute<space>Table<space>in<space>Description<space>Balloon<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT<space>KML21_DOCUMENT_FILENAME<space>Parent<space>Document<space>File<space>Name<quote><comma><comma><quote>GUI<space>CHOICE<space>KML21_CREATE_FOLDER_FOR_FEATURE_TYPE<space>yes%no<space>Create<space>Folder<space>for<space>Feature<space>Type<quote><comma>yes,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>KML21_INFORMATION_POINT_ICON<comma><quote><quote><quote><quote><quote><quote><comma>KML21_OPACITY<comma><quote><quote><quote><quote><quote><quote><comma>KML21_FILL_OPACITY<comma><quote><quote><quote><quote><quote><quote><comma>KML21_PEN_OPACITY<comma><quote><quote><quote><quote><quote><quote><comma>KML21_ICON_COLOR<comma><quote><quote><quote><quote><quote><quote><comma>KML21_FILL_COLOR<comma><quote><quote><quote><quote><quote><quote><comma>KML21_PEN_COLOR<comma><quote><quote><quote><quote><quote><quote><comma>KML21_SORT_BY_ATTRIBUTE<comma><quote><quote><quote><quote><quote><quote><comma>KML21_ATTR_IN_DESCRIPTION<comma><quote><quote><quote><quote><quote><quote><comma>KML21_HTML_DESCRIPTIONS<comma><quote><quote><quote><quote><quote><quote><comma>KML21_DOCUMENT_FILENAME<comma><quote><quote><quote><quote><quote><quote><comma>KML21_CREATE_FOLDER_FOR_FEATURE_TYPE<comma><quote><quote><quote><quote><quote><quote>,WRITER_FORMAT_PARAMETER,ADVANCED_PARMS<comma><quote>KML21_INFORMATION_POINT_ICON<space>KML21_REGIONATOR_PIPELINE<space>KML21_GO_PYRAMIDER_PIPELINE<space>KML21_PO_PYRAMIDER_PIPELINE<quote><comma>MIME_TYPE<comma><quote>.kml<space>application<solidus>vnd.google-earth.kml+xml<space>.kmz<space>application<solidus>vnd.google-earth.kmz<space>ADD_DISPOSITION<quote><comma>SUPPORTS_ESTABLISHED_CACHE<comma>YES<comma>NETWORK_AUTHENTICATION<comma>ALWAYS<comma>NETWORK_PROXY<comma>NO<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Google<space>Earth<space>KML<space>file<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>name<space>for<space>the<space>Google<space>Earth<space>KML<space>File<quote>,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "Passed:Passed,ftp_feature_type_name,Passed,ftp_writer,OGCKML,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,path_unix<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>path_windows<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>path_rootname<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>path_filename<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>path_extension<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>path_directory_unix<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>path_directory_windows<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>path_type<comma>kml_char<lt>openparen<gt>10<lt>closeparen<gt><comma>fme_geometry<lt>opencurly<gt>0<lt>closecurly<gt><comma>kml_char<lt>openparen<gt>200<lt>closeparen<gt><comma>fme_feature_type_name<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>attribute<lt>opencurly<gt><lt>closecurly<gt>.name<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>attribute<lt>opencurly<gt><lt>closecurly<gt>.fme_data_type<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>attribute<lt>opencurly<gt><lt>closecurly<gt>.native_data_type<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>fme_format_short_name<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>fme_format_long_name<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt><comma>fme_schema_handling<comma>kml_char<lt>openparen<gt>255<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma><comma>,ftp_format_parameters,ogc_layer_group<comma><comma>ogc_appearance_group<comma><comma>ogc_advanced_group<comma><comma>KML21_INFORMATION_POINT_ICON<comma><comma>KML21_ICON_COLOR<comma><comma>KML21_FILL_COLOR<comma><comma>KML21_FILL_OPACITY<comma><comma>KML21_PEN_COLOR<comma><comma>KML21_PEN_OPACITY<comma><comma>KML21_SORT_BY_ATTRIBUTE<comma><comma>KML21_HTML_DESCRIPTIONS<comma><comma>KML21_ATTR_IN_DESCRIPTION<comma><comma>KML21_DOCUMENT_FILENAME<comma><comma>KML21_CREATE_FOLDER_FOR_FEATURE_TYPE<comma>yes" }    WRITER_PARAMS { "ATOM_AUTHOR_EMAIL,,ATOM_AUTHOR_NAME,,ATOM_LINK_HREF,,ATTR_IN_DESCRIPTION,yes,AUTO_CREATE_NETWORK_LINKS,no,COORDINATE_SYSTEM_GRANULARITY,FEATURE,COPY_ICON,yes,CREATE_EMPTY_FOLDERS,no,DATASET_HINT,,DESTINATION_DATASETTYPE_VALIDATION,Yes,DETECT_RASTERS,yes,DOCUMENT_DESC,,DOCUMENT_NAME,,DOCUMENT_VISIBILITY,yes,EXEC_GO_PIPELINE,no,EXEC_PO_PIPELINE,no,HTML_DESCRIPTIONS,yes,KML21_FANOUT_TYPE,folder,KML21_TARGET_HREF,,KML_WRT_EXTD_PARA,,KML_WRT_NET_PARA,,KML_WRT_PIPE_PARA,,KML_WRT_RAST_PARA,,KML_WRT_WRT_PARA,,LOG_VERBOSE,no,MOVE_TO_KML_LOCAL_COORDSYS,yes,NETWORK_AUTHENTICATION,,OMIT_DOCUMENT_ELEMENT,no,ORIENTATION,none,OUTPUT_SCHEMA,yes,RASTER_FORMAT,tiff,RASTER_MODE,write,REGIONATE_DATA,no,SCHEMA_DOC,,STYLE_DOC,,TEXTURE_FORMAT,NONE,WATERMARK_ICON,,WATERMARK_NAME,,WATERMARK_SNIPPET,,WRITE_3D_GEOM_AS_POLYGONS,no,WRITE_TEXTURES_TXT_FILE,no" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Passed FEATURE_TYPE FeatureReader_2_<SCHEMA>  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,Passed,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

