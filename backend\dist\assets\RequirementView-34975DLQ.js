import{d as J,r as v,M as N,o as X,c as S,e as o,w as r,l as D,S as G,f as p,i as h,E as i,I as H,J as K,y as b,A as z,p as Q,b as y,a5 as W,T as ee,k as M,_ as te}from"./index-LgsLWi3x.js";import{u as oe}from"./user-r4pQk38q.js";const ae={class:"requirement-container"},re=J({__name:"RequirementView",setup(le){const V=oe(),U=v(),m=v([]),q=v([]),w=v(0),x=v(!1),f=v(""),a=N({toolName:"",project:"",approvals:"",deliveryDate:"",runMode:"online",testData:"",description:"",contact:"",phone:""}),T=async()=>{try{const t=await h.get("/api/approvers");t.data.success?q.value=t.data.data:i.error("获取审批人员列表失败")}catch(t){console.error("获取审批人员列表失败:",t),i.error("获取审批人员列表失败")}};X(()=>{T()});const j=N({toolName:[{required:!0,message:"请输入工具名称",trigger:["blur","input"]},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:["blur","input"]}],project:[{required:!0,message:"请输入所属项目",trigger:["blur","input"]}],approvals:[{required:!0,message:"请选择审批人员",trigger:["change","blur"]}],deliveryDate:[{required:!0,message:"请选择预计交接时间",trigger:["change","blur"]}],runMode:[{required:!0,message:"请选择运行方式",trigger:["change","blur"]}],description:[{required:!0,message:"请输入需求描述",trigger:["blur","input"]},{min:10,message:"描述不能少于10个字符",trigger:["blur","input"]}],contact:[{required:!0,message:"请输入联系人",trigger:["blur","input"]}],phone:[{required:!0,message:"请输入联系电话",trigger:["blur","input"]},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:["blur","input"]}]}),R=t=>{const e=[".zip",".rar",".7z"],d=t.name.substring(t.name.lastIndexOf(".")).toLowerCase();if(!e.includes(d)){i.error("只能上传ZIP、RAR、7Z格式的压缩包"),m.value=[];return}const n=500*1024*1024;if(t.size&&t.size>n){i.error("文件大小不能超过500MB"),m.value=[];return}m.value=[t],a.testData=t.name},F=t=>{x.value=!0,w.value=Math.round(t.percent),f.value="",t.percent===100&&(setTimeout(()=>{x.value=!1,w.value=0,f.value="success"},500),setTimeout(()=>{f.value=""},1500))},k=()=>{x.value=!1,f.value="exception",setTimeout(()=>{f.value="",w.value=0},1500)},A=t=>{t.success?(i.success("文件上传成功"),a.testData=t.data.path):(i.error(t.message||"文件上传失败"),k())},I=async t=>{const{file:e,onProgress:d,onSuccess:n,onError:s}=t,_=new FormData;_.append("file",e);try{const u=await h.post("/api/upload",_,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${V.token}`,"X-Username":V.user.username},onUploadProgress:c=>{if(c.total){const g=Math.round(c.loaded*100/c.total);d({percent:g})}}});n(u.data)}catch(u){s(u)}},P=async t=>{t&&await t.validate(async(e,d)=>{var n;if(e)try{await ee.confirm("确认提交工具需求申请吗？","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const s=sessionStorage.getItem("user");if(!s){i.error("请先登录");return}if(!JSON.parse(s).username){i.error("请先登录");return}const u=new FormData;if(u.append("title",a.toolName),u.append("description",a.description),u.append("category",a.project),u.append("priority",a.runMode),u.append("contact",a.contact),u.append("phone",a.phone),u.append("delivery_date",a.deliveryDate),u.append("approvals",a.approvals),m.value.length>0){const g=m.value[0].raw;g&&u.append("files",g)}const c=await h.post("/api/requirements/submit",u,{headers:{"Content-Type":"multipart/form-data"}});c.data.success?(i.success("需求提交成功"),B(t)):i.error(c.data.message||"提交失败")}catch(s){if(s!=null&&s.toString().includes("cancel"))return;console.error("提交失败:",s),((n=s.response)==null?void 0:n.status)===401?i.error("请先登录"):i.error("提交失败，请稍后重试")}else if(console.log("表单验证失败:",d),d){const s=Object.values(d)[0];s&&s[0]&&i.error(s[0].message)}})},B=t=>{t&&(t.resetFields(),m.value=[])};return(t,e)=>{const d=p("el-input"),n=p("el-form-item"),s=p("el-option"),_=p("el-select"),u=p("el-date-picker"),c=p("el-radio"),g=p("el-radio-group"),O=p("el-icon"),$=p("el-upload"),Y=p("el-progress"),C=p("el-button"),Z=p("el-form"),E=p("el-card"),L=p("el-config-provider");return M(),S("div",ae,[o(L,{locale:D(G)},{default:r(()=>[o(E,{class:"requirement-form"},{header:r(()=>e[10]||(e[10]=[y("div",{class:"card-header"},[y("h2",null,"工具需求申请")],-1)])),default:r(()=>[o(Z,{ref_key:"formRef",ref:U,model:a,rules:j,"label-width":"120px","label-position":"right",class:"form-content"},{default:r(()=>[o(n,{label:"工具名称",prop:"toolName"},{default:r(()=>[o(d,{modelValue:a.toolName,"onUpdate:modelValue":e[0]||(e[0]=l=>a.toolName=l),placeholder:"请输入工具名称"},null,8,["modelValue"])]),_:1}),o(n,{label:"所属项目",prop:"project"},{default:r(()=>[o(d,{modelValue:a.project,"onUpdate:modelValue":e[1]||(e[1]=l=>a.project=l),placeholder:"请输入所属项目名称"},null,8,["modelValue"])]),_:1}),o(n,{label:"审批人员",prop:"approvals"},{default:r(()=>[o(_,{modelValue:a.approvals,"onUpdate:modelValue":e[2]||(e[2]=l=>a.approvals=l),placeholder:"请选择审批人员",style:{width:"100%"}},{default:r(()=>[(M(!0),S(H,null,K(q.value,l=>(M(),z(s,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"预计交接时间",prop:"deliveryDate"},{default:r(()=>[o(u,{modelValue:a.deliveryDate,"onUpdate:modelValue":e[3]||(e[3]=l=>a.deliveryDate=l),type:"date",placeholder:"请选择预计交接时间",style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),o(n,{label:"运行方式",prop:"runMode"},{default:r(()=>[o(g,{modelValue:a.runMode,"onUpdate:modelValue":e[4]||(e[4]=l=>a.runMode=l)},{default:r(()=>[o(c,{value:"online"},{default:r(()=>e[11]||(e[11]=[b("在线")])),_:1}),o(c,{value:"offline"},{default:r(()=>e[12]||(e[12]=[b("离线")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"测试数据",prop:"testData"},{default:r(()=>[o($,{class:"upload-demo",drag:"",action:"/api/upload","auto-upload":!0,"http-request":I,"on-change":R,"on-progress":F,"on-success":A,"on-error":k,"file-list":m.value,accept:".zip,.rar,.7z",headers:{Authorization:`Bearer ${D(V).token}`,"X-Username":D(V).user.username}},{tip:r(()=>e[13]||(e[13]=[y("div",{class:"el-upload__tip"}," 支持上传ZIP、RAR、7Z格式的压缩包，单个文件不超过500MB ",-1)])),default:r(()=>[o(O,{class:"el-icon--upload"},{default:r(()=>[o(D(W))]),_:1}),e[14]||(e[14]=y("div",{class:"el-upload__text"},[b(" 将压缩包拖到此处，或"),y("em",null,"点击上传")],-1))]),_:1},8,["file-list","headers"]),x.value||f.value?(M(),z(Y,{key:0,percentage:w.value,status:f.value,format:()=>"",style:{"margin-top":"10px"}},null,8,["percentage","status"])):Q("",!0)]),_:1}),o(n,{label:"需求描述",prop:"description"},{default:r(()=>[o(d,{modelValue:a.description,"onUpdate:modelValue":e[5]||(e[5]=l=>a.description=l),type:"textarea",rows:4,placeholder:"请详细描述工具的功能需求、使用场景等"},null,8,["modelValue"])]),_:1}),o(n,{label:"联系人",prop:"contact"},{default:r(()=>[o(d,{modelValue:a.contact,"onUpdate:modelValue":e[6]||(e[6]=l=>a.contact=l),placeholder:"请输入联系人姓名"},null,8,["modelValue"])]),_:1}),o(n,{label:"联系电话",prop:"phone"},{default:r(()=>[o(d,{modelValue:a.phone,"onUpdate:modelValue":e[7]||(e[7]=l=>a.phone=l),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),o(n,null,{default:r(()=>[o(C,{type:"primary",onClick:e[8]||(e[8]=l=>P(U.value))},{default:r(()=>e[15]||(e[15]=[b("提交申请")])),_:1}),o(C,{onClick:e[9]||(e[9]=l=>B(U.value))},{default:r(()=>e[16]||(e[16]=[b("重置")])),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1})]),_:1},8,["locale"])])}}}),ue=te(re,[["__scopeId","data-v-829e604f"]]);export{ue as default};
