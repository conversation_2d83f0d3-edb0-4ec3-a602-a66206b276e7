import{d as Ga,u as Na,r as i,I as E,K as de,o as Pe,a as lt,i as D,E as u,a6 as qa,c as N,e as l,w as s,f as m,X as st,M,Y as q,b as n,L as Ha,x as Te,y as d,l as b,z as ot,s as C,a7 as Wa,U as Xa,A as v,a8 as nt,a3 as De,$ as Ya,v as pe,a9 as Ja,a0 as rt,N as Za,O as It,F as ut,G as it,Q as Ee,k as y,a5 as K,H as Ka,_ as Qa}from"./index-Dn7OnccA.js";import{f as el}from"./format-CBpsKyOP.js";const tl={class:"tools-container"},al={key:"convert"},ll={class:"steps-progress"},sl={class:"start-container"},ol={class:"start-content"},nl={class:"step-content"},rl={style:{"margin-bottom":"20px","margin-top":"20px","padding-left":"50px","text-align":"left",display:"flex",gap:"12px","align-items":"center"}},ul={style:{display:"none"}},il={class:"step-footer"},dl={class:"step-content"},pl={class:"upload-container"},cl={class:"upload-sections"},fl={class:"upload-section"},ml={class:"section-title"},vl={class:"el-upload-list__item custom-upload-item"},gl={class:"el-upload-list__item-name"},_l={class:"custom-status-label"},yl={class:"upload-section"},hl={class:"section-title"},wl={class:"el-upload-list__item custom-upload-item"},bl={class:"el-upload-list__item-name",style:{"text-align":"left",display:"block"}},xl={class:"custom-status-label"},Cl={class:"file-list-table"},kl={class:"table-header",style:{"justify-content":"flex-start","flex-direction":"row","align-items":"center"}},Vl={class:"license-tags",style:{display:"flex","align-items":"center","margin-top":"0px"}},Tl={class:"step-footer"},Dl={class:"step-content"},Sl={class:"output-settings-container"},Ll={class:"info-confirmation"},Ul={class:"section-card"},Al={class:"section-card"},$l={class:"output-types"},zl={class:"section-card"},Rl={class:"geometry-types"},Ol={class:"button-group"},Ml={class:"tip-text"},Fl={class:"step-footer"},Il={key:"history"},Pl={class:"table-container"},El={style:{display:"flex","justify-content":"center"}},jl={class:"pagination-container"},Bl={key:0,class:"message-content"},Gl={key:6,class:"color-picker-wrapper"},Nl={class:"color-value"},ql={key:1,class:"no-params"},Hl={class:"dialog-footer"},Wl={class:"dialog-footer"},Xl={class:"dialog-footer"},Yl={style:{display:"flex","justify-content":"center"}},Jl={class:"pagination-container"},Zl={class:"error-log-content"},Kl={class:"dialog-footer"},Ql={class:"error-message"},es={class:"error-message"},ts={class:"error-message"},as={class:"error-message"},ls={class:"error-message"},ss={class:"dialog-footer"},os=5e3,ns=Ga({__name:"CADToGISView",setup(rs){const w=Na(),Pt=i(!1),Q=i([]),ee=i([]),dt=i(""),pt=i(""),te=i("convert"),ce=i(1),Et=i(1),jt=i(1),ct=i(10),ft=i(10),Se=i(10),fe=i(0);i(0);const U=i(localStorage.getItem("token")),j={GET_TOOLS_LIST:"/api/cad2gis/tools/list",GET_TOOLS_COUNT:"/api/cad2gis/tools/count",UPLOAD_TOOL:"/api/cad2gis/tools/upload",DELETE_TOOL:"/api/cad2gis/tools/delete",APPLY_TOOL:"/api/cad2gis/tools/apply",APPROVE_TOOL:"/api/cad2gis/tools/approve",REJECT_TOOL:"/api/cad2gis/tools/reject",GET_MY_APPLICATIONS:"/api/tools/my-applications",GET_MY_APPROVALS:"/api/cad2gis/tools/my-approvals",RUN_TOOL:"/api/cad2gis/tools/run",GET_RUN_RECORDS:"/api/cad2gis/tools/run_records",UPLOAD_FILE:"/api/upload",DOWNLOAD_RESULT:"/api/tools/download_result"},Bt=t=>{t.name==="history"&&xe()},Gt=E(()=>Q.value.filter(t=>t.fmw_name.toLowerCase().includes(dt.value.toLowerCase())||(t.user_project||"").toLowerCase().includes(dt.value.toLowerCase()))),Nt=E(()=>ee.value.filter(t=>t.fmw_name.toLowerCase().includes(pt.value.toLowerCase())||t.project.toLowerCase().includes(pt.value.toLowerCase())));E(()=>{const t=(jt.value-1)*ct.value,e=t+ct.value;return Gt.value.slice(t,e)}),E(()=>{const t=(Et.value-1)*ft.value,e=t+ft.value;return Nt.value.slice(t,e)});const Le=async()=>{var t;try{if(!((t=w.user)!=null&&t.username)){Q.value=[];return}const e=await D.post(j.GET_TOOLS_LIST,{username:w.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});e.data.success?Q.value=e.data.data.map(o=>({...o,user_project:o.user_project||o.project||"未指定项目",created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取工具列表失败"),Q.value=[])}catch(e){console.error("获取工具列表失败:",e),Q.value=[]}},qt=async()=>{var t;try{if(!((t=w.user)!=null&&t.username)){ee.value=[];return}const e=await D.get("/api/tools/my-applications",{params:{source:"cad2gisView",fmw_id:"cad2gis"},headers:{"X-Username":w.user.username}});e.data.success?ee.value=e.data.data.filter(o=>o.status==="已通过").map(o=>({...o,count:parseInt(o.count)||0,usage_count:parseInt(o.usage_count)||0,remaining_count:(parseInt(o.usage_count)||0)-(parseInt(o.count)||0),user_project:o.user_project||"未指定项目",end_date:o.end_date||null,created_at:o.created_at&&!isNaN(new Date(o.created_at).getTime())?o.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取申请列表失败"),ee.value=[])}catch{ee.value=[]}},Ue=(t,e="yyyy-MM-dd HH:mm")=>{if(!t)return"--";try{return el(new Date(t),e)}catch{return"--"}};de(()=>w.user,t=>{t!=null&&t.username?Le():(Q.value=[],ee.value=[])},{immediate:!0});const me=i(!1),$=i(null),ae=i([]),_=i({}),A=i(null),je=i({}),Ht=i(!1);i(!1);const Be=i(!1),le=i([]);de(Be,t=>{t||(ce.value=1)});const Wt=t=>({running:"warning",success:"success",failed:"danger"})[t]||"info",Xt=t=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[t]||t,mt=t=>{if(!t)return"-";const e=Math.floor(t/60),o=t%60;return`${e}分${o}秒`},Yt=async t=>{try{await Ee.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=le.value.findIndex(o=>o.task_id===t.task_id);e!==-1&&(le.value.splice(e,1),fe.value--),D.post("/api/cad2gis/tools/delete_result",{task_id:t.task_id},{headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}}).then(o=>{o.data.success?u.success("删除成功"):(le.value.splice(e,0,t),fe.value++,u.error(o.data.message||"删除失败"))}).catch(o=>{le.value.splice(e,0,t),fe.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&u.error("删除失败，请稍后重试")}},vt=async()=>{try{if(!$.value)return;const t=await D.post(j.GET_RUN_RECORDS,{fmw_id:$.value.fmw_id,username:w.user.username,page:ce.value,page_size:Se.value},{headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});t.data.success?(le.value=t.data.data.records,fe.value=t.data.data.pagination.total):u.error(t.data.message||"获取运行记录失败")}catch(t){console.error("获取运行记录失败:",t),u.error("获取运行记录失败，请检查网络连接")}},Jt=t=>{console.log("页码改变:",t),console.log("当前工具信息:",$.value),ce.value=t,vt()};Pe(()=>{Le(),Y.value=Xe(),$.value={fmw_id:"cad2gis",fmw_name:"CAD转GIS",fmw_path:"tools/cad2gis/cad2gis.fmw"}}),lt(()=>{});const ve=i(!1),ge=i(!1),S=i({fmw_name:"",project:"",description:"",file:null}),H=i({fmw_id:"",file:null}),Zt={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},W=i(null),X=i(null),Kt=()=>{me.value=!1},Qt=()=>{var t;_.value={},ae.value=[],A.value&&A.value.resetFields(),(t=A.value)!=null&&t.$el&&A.value.$el.querySelectorAll(".el-upload").forEach(o=>{var p;const c=(p=o.__vueParentComponent)==null?void 0:p.ctx;c&&typeof c.clearFiles=="function"&&c.clearFiles()})},ea=()=>{ve.value=!1},ta=()=>{B.value&&B.value.resetFields(),S.value={fmw_name:"",project:"",description:"",file:null},W.value&&typeof W.value.clearFiles=="function"&&W.value.clearFiles()},aa=()=>{ge.value=!1},la=()=>{Ae.value&&Ae.value.resetFields(),H.value={fmw_id:"",file:null},X.value&&typeof X.value.clearFiles=="function"&&X.value.clearFiles()},B=i(),Ae=i();de(()=>_.value,t=>{const e={};ae.value.forEach(o=>{Ge(o)&&je.value[o.prop]&&(e[o.prop]=je.value[o.prop])}),A.value&&(A.value.clearValidate(),A.value.rules=e)},{deep:!0});const se=i(!1),sa=()=>{Ee.confirm("确定要提交任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{se.value=!0,wa().finally(()=>{se.value=!1})})},oa=async()=>{var t;se.value=!0;try{if(!$.value){u.error("工具信息不完整"),se.value=!1;return}const e=ae.value.filter(r=>Ge(r));console.log("可见的表单项:",e.map(r=>r.prop));for(const r of e)if(r.required&&!_.value[r.prop]){let g="";r.type==="file"||r.type==="upload"?g=`请上传${r.label}`:r.type==="select"||r.type==="dropdown"||r.type==="listbox"?g=`请选择${r.label}`:g=`请填写${r.label}`,u.error(g);return}const c={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:$.value.fmw_id,fmw_name:$.value.fmw_name,fmw_path:$.value.fmw_path,params:{}};for(const r of e){const g=_.value[r.prop];g!=null&&!r.prop.endsWith("_value")&&(r.type==="color"?c.params[r.prop]=_.value[`${r.prop}_value`]:c.params[r.prop]=g)}if(console.log("提交的请求数据:",c),Ht.value)try{const r=await D.post(j.UPDATE_COUNT,{id:$.value.id,username:w.user.username});if(!r.data.success){u.error(r.data.message||"更新使用次数失败");return}}catch(r){console.error("更新使用次数失败:",r),u.error("更新使用次数失败，请稍后重试");return}const p=await D.post("/api/cad2gis/run_fme",c,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});if(p.data.success){try{const r=await D.post("/api/cad2gis/tools/update-run-times",{},{headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});r.data.success||console.error("更新运行次数失败:",r.data.message)}catch(r){console.error("更新运行次数失败:",r)}u.success("任务提交成功"),_.value={},ae.value=[],A.value&&A.value.resetFields(),(t=A.value)!=null&&t.$el&&A.value.$el.querySelectorAll(".el-upload").forEach(g=>{var f;const T=(f=g.__vueParentComponent)==null?void 0:f.ctx;T&&typeof T.clearFiles=="function"&&T.clearFiles()}),me.value=!1,window.location.reload(),await qt(),te.value="history"}else u.error(p.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),u.error("提交失败，请稍后重试")}finally{se.value=!1}},na=async()=>{if(B.value)try{if(await B.value.validate(),!S.value.file){u.error("请上传工具文件");return}const t=new FormData;t.append("file",S.value.file);const e=await D.post(`${j.UPLOAD_FILE}`,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const o={fmw_id,fmw_name:S.value.fmw_name,project:S.value.project,description:S.value.description,fmw_path:e.data.data.path,file_path:e.data.data.path,data:new Date().toISOString()},c=await D.post(`${j.UPLOAD_TOOL}`,o,{headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});c.data.success?(u.success("工具上传成功"),ve.value=!1,S.value={fmw_name:"",project:"",description:"",file:null},B.value&&B.value.resetFields(),W.value&&typeof W.value.clearFiles=="function"&&W.value.clearFiles(),await Le()):u.error(c.data.message||"上传失败")}catch(t){console.error("上传工具失败:",t),u.error("参数未填写完整")}},ra=t=>t.name.toLowerCase().endsWith(".fmw")?t.size/1024/1024<50?!0:(u.error("文件大小不能超过50MB"),!1):(u.error("只能上传FMW文件"),!1),ua=t=>(H.value.file=t.raw,!1),ia=async()=>{if(!H.value.file){u.error("请选择更新文件");return}try{const t=new FormData;t.append("file",H.value.file);const e=await D.post(`${j.UPLOAD_FILE}`,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const o={fmw_id:H.value.fmw_id,file_path:e.data.data.path},c=await D.post(`${j.UPDATE_TOOL}`,o,{headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});c.data.success?(u.success("工具更新成功"),ge.value=!1,H.value={fmw_id:"",file:null},Ae.value&&Ae.value.resetFields(),X.value&&typeof X.value.clearFiles=="function"&&X.value.clearFiles(),await Le()):u.error(c.data.message||"更新失败")}catch(t){console.error("更新工具失败:",t),u.error("更新失败，请检查网络连接")}},da=(t,e)=>{if(!e){_.value[t]="rgb(255, 255, 255)";return}_.value[t]=e;const o=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(o){const[,c,p,r]=o;_.value[`${t}_value`]=`${c},${p},${r}`}else _.value[`${t}_value`]="255,255,255"},Ge=t=>{var o;if(!((o=t.component)!=null&&o.visibility))return!0;const e=t.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const c of e.if){const{condition:p,then:r}=c;let g=!1;if(p.allOf)g=p.allOf.every(T=>{if(T.equals){const{parameter:f,value:P}=T.equals;return _.value[f]===P}else if(T.isEnabled){const{parameter:f}=T.isEnabled;return!!_.value[f]}return!1});else if(p.equals){const{parameter:T,value:f}=p.equals;g=_.value[T]===f}else if(p.isEnabled){const{parameter:T}=p.isEnabled;g=!!_.value[T]}if(g)return r==="visibleEnabled"||r==="visibleDisabled"}return!1},$e=i(!1),gt=i(""),pa=t=>{Se.value=t,vt()},oe=i(!1),_e=i(),Ne=i(!1),_t=i([]),k=i({tool_name:"CAD转GIS",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),ca={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},fa=t=>t.getTime()<Date.now()-864e5,ma=()=>{oe.value=!0,va()},va=async()=>{try{const t=await D.get("/api/admin-users");t.data.success?_t.value=t.data.data:u.error("获取审批人失败")}catch{u.error("获取审批人失败")}},ga=async()=>{_e.value&&await _e.value.validate(async t=>{if(t){Ne.value=!0;try{const e=await D.post("/api/cad2gis/tools/apply",{fmw_id:"cad2gis",fmw_name:"CAD转GIS",applicant:w.user.username,reason:k.value.reason,end_date:k.value.end_date,usage_count:k.value.usage_count,user_project:k.value.user_project,reviewer:k.value.approver},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});e.data.success?(u.success("申请提交成功"),oe.value=!1,yt(),await Re()):u.error(e.data.message||"申请提交失败")}catch{u.error("申请提交失败")}finally{Ne.value=!1}}})},yt=()=>{_e.value&&_e.value.resetFields(),Object.assign(k.value,{tool_name:"CAD转GIS",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})},ze=i([]),qe=i(!1),F=i(null),h=i(0),ht=i(0),_a=()=>{h.value>0&&h.value--},ya=()=>{if(h.value===0)h.value++;else if(h.value===1){if(!F.value){u.warning("请先选择一个可用的许可");return}h.value++}else if(h.value===2){if(V.value.length===0){u.warning("请先上传要转换的文件");return}h.value++}else if(h.value===3&&I.value.length===0){u.warning("请至少选择一种输出几何类型");return}};de(h,t=>{ht.value=t});const z=i(!1),G=async t=>{if(!z.value){z.value=!0;try{t==="next"?await ya():t==="prev"&&await _a()}catch(e){console.error("步骤切换失败:",e),u.error("步骤切换失败，请重试")}finally{setTimeout(()=>{z.value=!1},250)}}},wt=t=>{t.key==="ArrowLeft"&&h.value>0?(t.preventDefault(),G("prev")):t.key==="ArrowRight"&&h.value<3&&(t.preventDefault(),G("next"))};Pe(()=>{Re(),document.addEventListener("keydown",wt)}),lt(()=>{document.removeEventListener("keydown",wt)});const Re=async()=>{qe.value=!0;try{const t=await D.get("/api/tools/my-applications",{params:{source:"cad2gisView",fmw_id:"cad2gis"},headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});if(t.data.success){ze.value=t.data.data;const e=ze.value.find(o=>bt(o));e&&(F.value=e.id)}else u.error(t.data.message||"获取许可列表失败")}catch(t){console.error("获取许可列表失败:",t),u.error("获取许可列表失败")}finally{qe.value=!1}};function ye(t){return{审批中:{type:"info",text:"审批中"},已通过:{type:"success",text:"已通过"},已驳回:{type:"danger",text:"已驳回"},已过期:{type:"warning",text:"已过期"},已耗尽:{type:"warning",text:"已耗尽"},可用:{type:"success",text:"可用"}}[t]||{type:"default",text:t}}const ne=i(!1),ha=qa(async()=>{if(!ne.value){ne.value=!0;try{await Re()}finally{ne.value=!1}}},1e3,{leading:!0,trailing:!1}),wa=async()=>{if(h.value===3)try{const t=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,e={点:"Point",多点:"Multipoint",线:"Line",面:"Area",文本:"Text",多文本:"Multitext",曲线:"Curve",圆弧:"Arc",椭圆:"Ellipse",其他:"Other"},o={task_id:t,fmw_id:"cad2gis",fmw_name:"CAD转GIS",fmw_path:"tools/cad2gis/cad2gis.fmw",params:{dwg_path:`temp/${Y.value}`,output_Choice:I.value.join(" "),save_path:`tools/cad2gis/output/${t}`},up_nums:V.value.length},c=sessionStorage.getItem("user");if(!c){u.error("未登录,请先登录");return}try{const p=JSON.parse(c);if(!p.username){u.error("用户信息不完整,请重新登录");return}const r=await fetch("/api/cad2gis/run_fme",{method:"POST",headers:{"Content-Type":"application/json","X-Username":p.username},body:JSON.stringify(o)}).then(g=>g.json());if(r.success){const g=await fetch("/api/cad2gis/tools/update_usacount",{method:"POST",headers:{"Content-Type":"application/json","X-Username":p.username},body:JSON.stringify({id:F.value,username:p.username,file_count:V.value.length})}).then(T=>T.json());g.success||console.error("更新使用次数失败:",g.message),u.success("任务提交成功"),await new Promise(T=>setTimeout(T,1e3)),await xe("cad2gis"),za(),h.value=0,F.value=null,V.value=[],I.value=[],Y.value=Xe(),He.value=[],We.value=!1,ne.value=!1,await Re(),te.value="history"}else u.error(r.message||"任务提交失败")}catch(p){console.error("解析用户信息失败:",p),u.error("用户信息解析失败,请重新登录")}}catch(t){console.error("提交任务失败:",t),u.error("任务提交失败，请重试")}else{if(h.value===2&&!Ta.value){u.warning("请先上传DWG文件");return}h.value++}},ba=t=>{if(ye(t.status).text!=="已通过")return;const e=new Date(t.end_date),o=new Date;if(o.setHours(0,0,0,0),e<o){u.warning("该许可已过期");return}if(t.count>=t.usage_count){u.warning("该许可使用次数已达上限");return}if(h.value===2&&V.value.length>0&&(t.usage_count||0)-(t.count||0)-V.value.length<0){u.warning(`当前选择的许可剩余次数不足，无法处理 ${V.value.length} 个文件`);return}F.value=t.id},xa=({row:t})=>{if(ye(t.status).text!=="已通过")return"disabled-row";const e=new Date(t.end_date),o=new Date;return o.setHours(0,0,0,0),e<o||t.count>=t.usage_count?"disabled-row":"clickable-row"},bt=t=>{if(ye(t.status).text!=="已通过")return!1;const e=new Date(t.end_date),o=new Date;return o.setHours(0,0,0,0),!(e<o||t.count>=t.usage_count)},He=i([]),V=i([]),We=i(!1),Y=i(""),Xe=()=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return Array.from({length:40},()=>t.charAt(Math.floor(Math.random()*t.length))).join("")};Pe(()=>{Y.value=Xe()});const Ca=t=>t.name.toLowerCase().endsWith(".dwg")?!0:(u.error("只能上传DWG格式的文件！"),!1),ka=t=>[".zip",".rar",".7z"].some(o=>t.name.toLowerCase().endsWith(o))?!0:(u.error("只能上传ZIP、RAR、7Z格式的压缩包！"),!1),xt=(t,e)=>{t.success?([".zip",".rar",".7z"].some(c=>e.name.toLowerCase().endsWith(c))&&(e.extracting=!1,e.parsing=!1,e.parsed=!0,e.dwgCount=t.files.length),V.value=[...V.value,...t.files],u.success("文件上传成功")):u.error(t.message||"文件上传失败")},Ct=t=>{console.error("Upload error:",t),u.error("文件上传失败")},Ye=async t=>{try{const e=await fetch("/api/cad2gis/delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({folderId:Y.value,fileName:t.name})}).then(o=>o.json());if(e.success){const o=V.value.findIndex(c=>c.name===t.name);o!==-1&&V.value.splice(o,1),u.success("文件删除成功")}else u.error(e.message||"文件删除失败")}catch(e){console.error("Delete error:",e),u.error("文件删除失败")}},Va=t=>{Ee.confirm(`确定要移除文件 "${t.name}" 吗？`,"移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Ye({name:t.name})}).catch(()=>{})},kt=t=>{if(!t)return"-";if(typeof t=="string")return t;const e=["B","KB","MB","GB"];let o=0,c=t;for(;c>=1024&&o<e.length-1;)c/=1024,o++;return`${c.toFixed(2)} ${e[o]}`},Ta=E(()=>V.value.length>0),re=E(()=>F.value?ze.value.find(t=>t.id===F.value):null),he=E(()=>{var t,e;return(((t=re.value)==null?void 0:t.usage_count)||0)-(((e=re.value)==null?void 0:e.count)||0)-V.value.length}),Je=E(()=>he.value>=0),I=i([]),Ze=["点（Point）","富点（MultiPoint）","线（Line）","面（Area）","文本（Text）","富文本（MultiText）","曲线（Curve）","圆弧（Arc）","椭圆（Ellipse）","其他（Other）"],Da=()=>{I.value=[...Ze]},Sa=()=>{const t=new Set(I.value);I.value=Ze.filter(e=>!t.has(e))},J=i([]),Ke=i(!1),ue=i(1),we=i(10),be=i(0),Oe=i(null),Qe=i(!1),La=t=>({running:"warning",success:"success",failed:"danger"})[t]||"info",Ua=t=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[t]||t,Aa=t=>{we.value=t,ue.value=1},$a=t=>{ue.value=t},xe=async(t=(e=>(e=$.value)==null?void 0:e.fmw_id)()||"cad2gis")=>{try{Ke.value=!0;const o=await D.post("/api/cad2gis/tools/run_records",{fmw_id:t,username:w.user.username,page:ue.value,page_size:we.value},{headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}});o.data.success?(J.value=o.data.data.records,be.value=o.data.data.pagination.total):u.error(o.data.message||"获取历史记录失败")}catch(o){console.error("获取历史记录失败:",o),u.error("获取历史记录失败")}finally{Ke.value=!1}},za=()=>{Qe.value||(Qe.value=!0,Oe.value=setInterval(async()=>{J.value.some(e=>e.status==="running"||e.status==="pending")?(console.log("检测到运行中的任务，正在更新状态..."),await xe()):(console.log("没有运行中的任务，停止轮询"),Vt())},os))},Vt=()=>{Oe.value&&(clearInterval(Oe.value),Oe.value=null),Qe.value=!1},Ra=async t=>{try{await Ee.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=J.value.findIndex(o=>o.task_id===t.task_id);e!==-1&&(J.value.splice(e,1),be.value--),D.post("/api/cad2gis/tools/delete_result",{task_id:t.task_id},{headers:{Authorization:`Bearer ${U.value}`,"X-Username":w.user.username}}).then(o=>{o.data.success?u.success("删除成功"):(J.value.splice(e,0,t),be.value++,u.error(o.data.message||"删除失败"))}).catch(o=>{J.value.splice(e,0,t),be.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&(console.error("删除历史记录失败:",e),u.error("删除历史记录失败"))}},Tt=async t=>{try{const e=`tools/cad2gis/output/${t.task_id}/${t.file_name}`,o=`${j.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`,c=document.createElement("a");c.style.display="none",document.body.appendChild(c),c.href=o,c.download=t.file_name,c.click(),setTimeout(()=>{document.body.removeChild(c)},100),u.success("开始下载")}catch(e){console.error("下载历史记录失败:",e),u.error("下载失败，请稍后重试")}},Dt=async t=>{try{t.error_message?(gt.value=t.error_message,$e.value=!0):u.warning("暂无错误信息")}catch(e){console.error("显示日志失败:",e),u.error("显示日志失败")}};de([ue,we],()=>{xe()}),Pe(()=>{xe()}),lt(()=>{Vt()});const Me=i(null);de(h,async()=>{if(await Ka(),Me.value){const t=Me.value.querySelector('.step-content[v-show="true"]');t&&(Me.value.style.height=`${t.scrollHeight}px`)}});const Oa=E(()=>I.value.length>0);return(t,e)=>{var Mt,Ft;const o=m("el-step"),c=m("el-steps"),p=m("el-icon"),r=m("el-button"),g=m("el-skeleton-item"),T=m("el-skeleton"),f=m("el-table-column"),P=m("el-tag"),Ma=m("el-radio"),Ce=m("el-table"),et=m("ArrowLeft"),ke=m("el-upload"),Fe=m("el-descriptions-item"),Fa=m("el-descriptions"),Ia=m("el-alert"),R=m("el-checkbox"),Pa=m("el-checkbox-group"),St=m("el-card"),Lt=m("el-tab-pane"),Ea=m("el-tooltip"),Ut=m("el-button-group"),At=m("el-pagination"),ja=m("el-tabs"),$t=m("el-option"),zt=m("el-select"),Rt=m("el-input-number"),Ot=m("el-date-picker"),Ba=m("el-color-picker"),Z=m("el-input"),O=m("el-form-item"),tt=m("el-form"),ie=m("el-dialog"),at=Za("loading");return y(),N("div",tl,[l(ja,{modelValue:te.value,"onUpdate:modelValue":e[10]||(e[10]=a=>te.value=a),class:"cad-tabs",onTabClick:Bt},{default:s(()=>[l(Lt,{label:"转换工具",name:"convert"},{default:s(()=>[l(st,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(n("div",al,[l(St,{class:"license-card",style:{"margin-top":"0"}},{default:s(()=>[l(c,{active:h.value,"finish-status":"success",simple:""},{default:s(()=>[l(o,{title:"开始"}),l(o,{title:"选择许可"}),l(o,{title:"上传文件"}),l(o,{title:"设置输出类型"})]),_:1},8,["active"]),n("div",ll,[n("div",{class:"progress-bar",style:Ha({width:`${ht.value/4*100}%`})},null,4)]),n("div",{class:Te(["step-content-container",{transitioning:z.value}]),ref_key:"stepContentRef",ref:Me},[l(st,{name:"step-fade",mode:"out-in"},{default:s(()=>[(y(),N("div",{key:h.value},[M(n("div",sl,[n("div",ol,[l(r,{type:"primary",size:"large",class:"start-button",onClick:e[0]||(e[0]=a=>G("next")),disabled:z.value},{default:s(()=>[l(p,null,{default:s(()=>[l(b(ot))]),_:1}),e[34]||(e[34]=d(" 开始转换 "))]),_:1},8,["disabled"])])],512),[[q,h.value===0]]),M(n("div",nl,[n("div",rl,[l(r,{type:"primary",onClick:ma},{default:s(()=>[l(p,null,{default:s(()=>[l(b(Wa))]),_:1}),e[35]||(e[35]=d(" 申请许可 "))]),_:1}),l(r,{type:"primary",onClick:b(ha),disabled:ne.value},{default:s(()=>[l(p,{class:Te({"refresh-rotate":ne.value})},{default:s(()=>[l(b(Xa))]),_:1},8,["class"]),e[36]||(e[36]=d(" 刷新许可 "))]),_:1},8,["onClick","disabled"])]),qe.value?(y(),C(T,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:s(()=>[l(g,{variant:"text",style:{width:"80px","margin-right":"16px"}}),l(g,{variant:"text",style:{width:"150px","margin-right":"16px"}}),l(g,{variant:"text",style:{width:"150px","margin-right":"16px"}}),l(g,{variant:"text",style:{width:"100px","margin-right":"16px"}}),l(g,{variant:"text",style:{width:"100px","margin-right":"16px"}}),l(g,{variant:"text",style:{width:"180px","margin-right":"16px"}}),l(g,{variant:"text",style:{width:"100px","margin-right":"16px"}}),l(g,{variant:"text",style:{width:"80px"}})]),_:1})):(y(),C(Ce,{key:1,data:ze.value,style:{width:"100%"},onRowClick:ba,"row-class-name":xa},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"user_project",label:"项目","min-width":"150","show-overflow-tooltip":""}),l(f,{prop:"reason",label:"原因","min-width":"150","show-overflow-tooltip":""}),l(f,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),l(f,{prop:"count",label:"已用次数",width:"100",align:"center"}),l(f,{prop:"end_date",label:"截止时间",width:"180",align:"center"},{default:s(({row:a})=>[d(v(Ue(a.end_date,"yyyy-MM-dd")),1)]),_:1}),l(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:a})=>[l(P,{type:ye(a.status).type},{default:s(()=>[d(v(ye(a.status).text),1)]),_:2},1032,["type"])]),_:1}),l(f,{label:"选择",width:"80",align:"center"},{default:s(({row:a})=>[l(Ma,{modelValue:F.value,"onUpdate:modelValue":e[1]||(e[1]=L=>F.value=L),label:a.id,disabled:!bt(a),class:"custom-radio"},{default:s(()=>[n("span",ul,v(a.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"])),n("div",il,[l(r,{onClick:e[2]||(e[2]=a=>G("prev")),disabled:z.value},{default:s(()=>[l(p,null,{default:s(()=>[l(et)]),_:1}),e[37]||(e[37]=d(" 上一步 "))]),_:1},8,["disabled"]),l(r,{type:"primary",onClick:e[3]||(e[3]=a=>G("next")),disabled:!F.value||z.value||he.value<0},{default:s(()=>[e[38]||(e[38]=d(" 下一步 ")),l(p,null,{default:s(()=>[l(b(ot))]),_:1})]),_:1},8,["disabled"])])],512),[[q,h.value===1]]),M(n("div",dl,[n("div",pl,[e[49]||(e[49]=n("div",{style:{"margin-bottom":"20px",display:"flex","justify-content":"space-between","align-items":"center"}},[n("h3",{style:{margin:"0"}},"上传文件")],-1)),n("div",cl,[n("div",fl,[n("div",ml,[l(p,null,{default:s(()=>[l(b(nt))]),_:1}),e[39]||(e[39]=n("span",null,"直接上传DWG文件",-1))]),e[42]||(e[42]=n("div",{class:"section-desc"},"支持直接上传单个或多个DWG文件",-1)),l(ke,{class:"upload-component",drag:"",action:"/api/cad2gis/upload","auto-upload":!0,"on-success":xt,"on-error":Ct,"on-remove":Ye,"file-list":He.value,"before-upload":Ca,accept:".dwg",multiple:"","show-file-list":!0,data:{folderId:Y.value}},{tip:s(()=>e[40]||(e[40]=[n("div",{class:"el-upload__tip"}," 支持 .dwg 格式文件 ",-1)])),file:s(({file:a})=>[n("div",vl,[n("span",gl,v(a.name),1),n("span",_l,v(a.status==="success"?"上传成功":a.status==="uploading"?`上传中 ${Math.round(a.percentage)}%`:"上传中"),1)])]),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(De))]),_:1}),e[41]||(e[41]=n("div",{class:"el-upload__text"},[d(" 将DWG文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["file-list","data"])]),n("div",yl,[n("div",hl,[l(p,null,{default:s(()=>[l(b(Ya))]),_:1}),e[43]||(e[43]=n("span",null,"上传压缩包",-1))]),e[46]||(e[46]=n("div",{class:"section-desc"},"支持上传包含DWG文件的ZIP/RAR/7Z压缩包，将自动解压并提取DWG文件",-1)),l(ke,{class:"upload-component",drag:"",action:"/api/cad2gis/upload","auto-upload":!0,"on-success":xt,"on-error":Ct,"on-remove":Ye,"file-list":He.value,"before-upload":ka,accept:".zip,.rar,.7z",multiple:"","show-file-list":!0,data:{folderId:Y.value}},{tip:s(()=>e[44]||(e[44]=[n("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包 ",-1)])),file:s(({file:a})=>[n("div",wl,[n("span",bl,v(a.name),1),n("span",xl,v(a.status==="success"?a.extracting?"解压中":a.parsing?"解析中":a.parsed?`找到${a.dwgCount||0}个DWG文件`:"解压中":a.status==="uploading"?`上传中 ${Math.round(a.percentage)}%`:"上传中"),1)])]),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(De))]),_:1}),e[45]||(e[45]=n("div",{class:"el-upload__text"},[d(" 将压缩包拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["file-list","data"])])]),n("div",Cl,[n("div",kl,[e[47]||(e[47]=n("span",{class:"table-title"},"解析后CAD文件",-1)),n("div",Vl,[l(P,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var a;return[d("当前许可次数："+v(((a=re.value)==null?void 0:a.usage_count)||0),1)]}),_:1}),l(P,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var a;return[d("已用次数："+v(((a=re.value)==null?void 0:a.count)||0),1)]}),_:1}),l(P,{type:"warning",style:{"margin-right":"10px"}},{default:s(()=>[d("消耗次数："+v(V.value.length),1)]),_:1}),l(P,{type:Je.value?"success":"danger",style:{"margin-right":"10px"}},{default:s(()=>[d(" 消耗后剩余次数："+v(he.value)+" ",1),Je.value?pe("",!0):(y(),C(p,{key:0,style:{"margin-left":"4px"}},{default:s(()=>[l(b(Ja))]),_:1}))]),_:1},8,["type"])])]),M((y(),C(Ce,{data:V.value,style:{width:"100%"},border:""},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"name",label:"文件名","min-width":"200","show-overflow-tooltip":""}),l(f,{prop:"size",label:"文件大小",width:"120",align:"center"},{default:s(({row:a})=>[d(v(kt(a.size)),1)]),_:1}),l(f,{label:"操作",width:"120",align:"center"},{default:s(({row:a})=>[l(r,{type:"danger",size:"small",onClick:L=>Va(a),disabled:We.value},{default:s(()=>[l(p,null,{default:s(()=>[l(b(rt))]),_:1}),e[48]||(e[48]=d(" 移除 "))]),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[at,We.value]])])]),n("div",Tl,[l(r,{onClick:e[4]||(e[4]=a=>G("prev")),disabled:z.value},{default:s(()=>[l(p,null,{default:s(()=>[l(et)]),_:1}),e[50]||(e[50]=d(" 上一步 "))]),_:1},8,["disabled"]),l(r,{type:"primary",onClick:e[5]||(e[5]=a=>G("next")),disabled:V.value.length===0||z.value||he.value<0},{default:s(()=>[e[51]||(e[51]=d(" 下一步 ")),l(p,null,{default:s(()=>[l(b(ot))]),_:1})]),_:1},8,["disabled"])])],512),[[q,h.value===2]]),M(n("div",Dl,[n("div",Sl,[n("div",Ll,[n("div",Ul,[e[52]||(e[52]=n("h3",null,"待转换文件",-1)),l(Ce,{data:V.value,style:{width:"100%"},size:"small"},{default:s(()=>[l(f,{prop:"name",label:"文件名"}),l(f,{prop:"size",label:"大小",width:"120"},{default:s(a=>[d(v(kt(a.row.size)),1)]),_:1})]),_:1},8,["data"])]),n("div",Al,[e[53]||(e[53]=n("h3",null,"许可信息",-1)),l(Fa,{column:1,border:""},{default:s(()=>[l(Fe,{label:"当前许可次数"},{default:s(()=>{var a;return[d(v(((a=re.value)==null?void 0:a.usage_count)||0),1)]}),_:1}),l(Fe,{label:"已用次数"},{default:s(()=>{var a;return[d(v(((a=re.value)==null?void 0:a.count)||0),1)]}),_:1}),l(Fe,{label:"消耗次数"},{default:s(()=>[d(v(V.value.length),1)]),_:1}),l(Fe,{label:"消耗后剩余次数"},{default:s(()=>[n("span",{class:Te({"text-danger":!Je.value})},v(he.value),3)]),_:1})]),_:1})])]),n("div",$l,[n("div",zl,[e[66]||(e[66]=n("h3",null,"选择输出Geometry类型",-1)),n("div",Rl,[n("div",Ol,[l(r,{type:"primary",onClick:Da},{default:s(()=>e[54]||(e[54]=[d("全选")])),_:1}),l(r,{onClick:Sa},{default:s(()=>e[55]||(e[55]=[d("反选")])),_:1})]),M(n("div",Ml,[l(Ia,{title:"为了避免图形丢失，建议选择全部类型",type:"warning",closable:!1,"show-icon":""})],512),[[q,I.value.length!==Ze.length]]),l(Pa,{modelValue:I.value,"onUpdate:modelValue":e[6]||(e[6]=a=>I.value=a)},{default:s(()=>[l(R,{label:"点（Point）"},{default:s(()=>e[56]||(e[56]=[d("点（Point）")])),_:1}),l(R,{label:"富点（MultiPoint）"},{default:s(()=>e[57]||(e[57]=[d("富点（MultiPoint）")])),_:1}),l(R,{label:"线（Line）"},{default:s(()=>e[58]||(e[58]=[d("线（Line）")])),_:1}),l(R,{label:"面（Area）"},{default:s(()=>e[59]||(e[59]=[d("面（Area）")])),_:1}),l(R,{label:"文本（Text）"},{default:s(()=>e[60]||(e[60]=[d("文本（Text）")])),_:1}),l(R,{label:"富文本（MultiText）"},{default:s(()=>e[61]||(e[61]=[d("富文本（MultiText）")])),_:1}),l(R,{label:"曲线（Curve）"},{default:s(()=>e[62]||(e[62]=[d("曲线（Curve）")])),_:1}),l(R,{label:"圆弧（Arc）"},{default:s(()=>e[63]||(e[63]=[d("圆弧（Arc）")])),_:1}),l(R,{label:"椭圆（Ellipse）"},{default:s(()=>e[64]||(e[64]=[d("椭圆（Ellipse）")])),_:1}),l(R,{label:"其他（Other）"},{default:s(()=>e[65]||(e[65]=[d("其他（Other）")])),_:1})]),_:1},8,["modelValue"])])])])]),n("div",Fl,[l(r,{onClick:e[7]||(e[7]=a=>G("prev")),disabled:z.value},{default:s(()=>[l(p,null,{default:s(()=>[l(et)]),_:1}),e[67]||(e[67]=d(" 上一步 "))]),_:1},8,["disabled"]),h.value===3?(y(),C(r,{key:0,type:"primary",loading:se.value,onClick:sa,disabled:!Oa.value||z.value},{default:s(()=>e[68]||(e[68]=[d("提交任务")])),_:1},8,["loading","disabled"])):pe("",!0)])],512),[[q,h.value===3]])]))]),_:1})],2)]),_:1})],512),[[q,te.value==="convert"]])]),_:1})]),_:1}),l(Lt,{label:"历史记录",name:"history"},{default:s(()=>[l(st,{name:"slide-fade",mode:"out-in"},{default:s(()=>[M(n("div",Il,[l(St,{class:"history-card",style:{"margin-top":"0px"}},{header:s(()=>e[69]||(e[69]=[])),default:s(()=>[n("div",Pl,[M((y(),C(Ce,{data:J.value,style:{width:"100%"}},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"submit_time",label:"提交时间",width:"350",align:"center"},{default:s(({row:a})=>[l(Ea,{content:Ue(a.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:s(()=>[n("span",null,v(Ue(a.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),l(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:a})=>[l(P,{type:La(a.status)},{default:s(()=>[d(v(Ua(a.status)),1)]),_:2},1032,["type"])]),_:1}),l(f,{prop:"time_consuming",label:"运行耗时",width:"250",align:"center"},{default:s(({row:a})=>[d(v(mt(a.time_consuming)),1)]),_:1}),l(f,{prop:"file_size",label:"文件大小",width:"150",align:"center"},{default:s(({row:a})=>[d(v(a.file_size),1)]),_:1}),l(f,{prop:"up_nums",label:"转换文件数量","min-width":"130",align:"center","show-overflow-tooltip":""}),l(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:a})=>[n("div",El,[l(Ut,null,{default:s(()=>[a.status==="success"&&a.up_nums>0&&a.file_size!=="0.0MB"?(y(),C(r,{key:0,type:"success",size:"small",onClick:L=>Tt(a),disabled:a.status!=="success"},{default:s(()=>[l(p,null,{default:s(()=>[l(b(It))]),_:1}),e[70]||(e[70]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):pe("",!0),a.error_message?(y(),C(r,{key:1,type:"info",size:"small",onClick:L=>Dt(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(nt))]),_:1}),e[71]||(e[71]=d(" 日志 "))]),_:2},1032,["onClick"])):pe("",!0),l(r,{type:"danger",size:"small",onClick:L=>Ra(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(rt))]),_:1}),e[72]||(e[72]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[at,Ke.value]]),n("div",jl,[l(At,{"current-page":ue.value,"onUpdate:currentPage":e[8]||(e[8]=a=>ue.value=a),"page-size":we.value,"onUpdate:pageSize":e[9]||(e[9]=a=>we.value=a),"page-sizes":[10,20,50,100],total:be.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Aa,onCurrentChange:$a},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[q,te.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(ie,{modelValue:me.value,"onUpdate:modelValue":e[12]||(e[12]=a=>me.value=a),title:`运行工具 - ${(Mt=$.value)==null?void 0:Mt.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:Kt,onAfterClose:Qt},{footer:s(()=>[n("span",Hl,[l(r,{onClick:e[11]||(e[11]=a=>me.value=!1)},{default:s(()=>e[74]||(e[74]=[d("取消")])),_:1}),l(r,{type:"primary",onClick:oa},{default:s(()=>e[75]||(e[75]=[d("提交任务")])),_:1})])]),default:s(()=>[l(tt,{ref_key:"formRef",ref:A,model:_.value,rules:je.value,"label-width":"200px",size:"small",class:"run-form"},{default:s(()=>[ae.value.length>0?(y(!0),N(ut,{key:0},it(ae.value,a=>M((y(),C(O,{key:a.prop,label:a.type==="message"?"":a.label,prop:a.prop,required:a.required,class:Te({"message-form-item":a.type==="message"})},{default:s(()=>{var L,Ve,Ie;return[a.type==="message"?(y(),N("div",Bl,v(a.component.content),1)):a.type==="upload"?(y(),C(ke,K({key:1,ref_for:!0},a.component.props,{class:["upload-area",{"is-error":((Ie=(Ve=(L=A.value)==null?void 0:L.fields)==null?void 0:Ve.find(x=>x.prop===a.prop))==null?void 0:Ie.validateState)==="error"}],drag:""}),{default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(De))]),_:1}),e[73]||(e[73]=n("div",{class:"el-upload__text"},[d(" 拖拽文件到此处"),n("br"),d("或"),n("em",null,"点击上传")],-1))]),_:2},1040,["class"])):a.type==="select"?(y(),C(zt,K({key:2,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{style:{width:"100%"}}),{default:s(()=>[(y(!0),N(ut,null,it(a.component.options,x=>(y(),C($t,{key:x.value,label:x.label,value:x.value,title:x.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue"])):a.type==="number"?(y(),C(Rt,K({key:3,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):a.type==="datetime"?(y(),C(Ot,K({key:4,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props),null,16,["modelValue","onUpdate:modelValue"])):a.type==="checkbox"?(y(),C(R,K({key:5,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):a.type==="color"?(y(),N("div",Gl,[l(Ba,K({modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props,{onChange:x=>da(a.prop,x),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),n("span",Nl,v(_.value[`${a.prop}_value`]||"255,255,255"),1)])):(y(),C(Z,K({key:7,modelValue:_.value[a.prop],"onUpdate:modelValue":x=>_.value[a.prop]=x,ref_for:!0},a.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[q,Ge(a)]])),128)):(y(),N("div",ql," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),l(ie,{modelValue:ve.value,"onUpdate:modelValue":e[17]||(e[17]=a=>ve.value=a),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:ea,onAfterClose:ta},{footer:s(()=>[n("span",Wl,[l(r,{onClick:e[16]||(e[16]=a=>ve.value=!1)},{default:s(()=>e[78]||(e[78]=[d("取消")])),_:1}),l(r,{type:"primary",onClick:na},{default:s(()=>e[79]||(e[79]=[d("确定")])),_:1})])]),default:s(()=>[l(tt,{ref_key:"uploadFormRef",ref:B,model:S.value,rules:Zt,"label-width":"100px",size:"small"},{default:s(()=>[l(O,{label:"工具名称",prop:"fmw_name"},{default:s(()=>[l(Z,{modelValue:S.value.fmw_name,"onUpdate:modelValue":e[13]||(e[13]=a=>S.value.fmw_name=a),placeholder:""},null,8,["modelValue"])]),_:1}),l(O,{label:"所属项目",prop:"project"},{default:s(()=>[l(Z,{modelValue:S.value.project,"onUpdate:modelValue":e[14]||(e[14]=a=>S.value.project=a),placeholder:""},null,8,["modelValue"])]),_:1}),l(O,{label:"工具描述",prop:"description"},{default:s(()=>[l(Z,{modelValue:S.value.description,"onUpdate:modelValue":e[15]||(e[15]=a=>S.value.description=a),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),l(O,{label:"工具文件",prop:"file"},{default:s(()=>{var a,L,Ve;return[l(ke,{ref_key:"uploadRef",ref:W,class:Te(["upload-demo",{"is-error":((Ve=(L=(a=B.value)==null?void 0:a.fields)==null?void 0:L.find(Ie=>Ie.prop==="file"))==null?void 0:Ve.validateState)==="error"}]),drag:"","auto-upload":!1,"on-change":t.handleFileChange,"before-upload":ra,limit:1,accept:".fmw","file-list":[]},{tip:s(()=>e[76]||(e[76]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(De))]),_:1}),e[77]||(e[77]=n("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},8,["class","on-change"])]}),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(ie,{modelValue:ge.value,"onUpdate:modelValue":e[19]||(e[19]=a=>ge.value=a),title:"更新工具",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:aa,onAfterClose:la},{footer:s(()=>[n("span",Xl,[l(r,{onClick:e[18]||(e[18]=a=>ge.value=!1)},{default:s(()=>e[82]||(e[82]=[d("取消")])),_:1}),l(r,{type:"primary",onClick:ia,disabled:!H.value.file},{default:s(()=>e[83]||(e[83]=[d("确认更新")])),_:1},8,["disabled"])])]),default:s(()=>[l(ke,{ref_key:"updateUploadRef",ref:X,class:"upload-demo","auto-upload":!1,"on-change":ua,limit:1,accept:".fmw",drag:""},{tip:s(()=>e[80]||(e[80]=[n("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[l(p,{class:"el-icon--upload"},{default:s(()=>[l(b(De))]),_:1}),e[81]||(e[81]=n("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),n("em",null,"点击上传")],-1))]),_:1},512)]),_:1},8,["modelValue"]),l(ie,{modelValue:Be.value,"onUpdate:modelValue":e[22]||(e[22]=a=>Be.value=a),title:`运行成果 - ${(Ft=$.value)==null?void 0:Ft.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:s(()=>[M((y(),C(Ce,{data:le.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:s(()=>[l(f,{type:"index",label:"序号",width:"80",align:"center"}),l(f,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:s(({row:a})=>[d(v(Ue(a.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),l(f,{prop:"status",label:"运行状态",width:"100",align:"center"},{default:s(({row:a})=>[l(P,{type:Wt(a.status)},{default:s(()=>[d(v(Xt(a.status)),1)]),_:2},1032,["type"])]),_:1}),l(f,{prop:"time_consuming",label:"运行耗时",width:"100",align:"center"},{default:s(({row:a})=>[d(v(mt(a.time_consuming)),1)]),_:1}),l(f,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),l(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:a})=>[n("div",Yl,[l(Ut,null,{default:s(()=>[a.status==="success"&&a.up_nums>0&&a.file_size!=="0.0MB"?(y(),C(r,{key:0,type:"success",size:"small",onClick:L=>Tt(a),disabled:a.status!=="success"},{default:s(()=>[l(p,null,{default:s(()=>[l(b(It))]),_:1}),e[84]||(e[84]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):pe("",!0),a.error_message?(y(),C(r,{key:1,type:"info",size:"small",onClick:L=>Dt(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(nt))]),_:1}),e[85]||(e[85]=d(" 日志 "))]),_:2},1032,["onClick"])):pe("",!0),l(r,{type:"danger",size:"small",onClick:L=>Yt(a)},{default:s(()=>[l(p,null,{default:s(()=>[l(b(rt))]),_:1}),e[86]||(e[86]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[at,Pt.value]]),n("div",Jl,[l(At,{"current-page":ce.value,"onUpdate:currentPage":e[20]||(e[20]=a=>ce.value=a),"page-size":Se.value,"onUpdate:pageSize":e[21]||(e[21]=a=>Se.value=a),"page-sizes":[10,20,50,100],total:fe.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:pa,onCurrentChange:Jt},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"]),l(ie,{modelValue:$e.value,"onUpdate:modelValue":e[24]||(e[24]=a=>$e.value=a),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:s(()=>[n("span",Kl,[l(r,{onClick:e[23]||(e[23]=a=>$e.value=!1)},{default:s(()=>e[87]||(e[87]=[d("关闭")])),_:1})])]),default:s(()=>[n("div",Zl,[n("pre",null,v(gt.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"]),l(ie,{modelValue:oe.value,"onUpdate:modelValue":e[32]||(e[32]=a=>oe.value=a),title:"申请许可-CAD转GIS",width:"500px","close-on-click-modal":!1,onClose:e[33]||(e[33]=a=>oe.value=!1),onAfterClose:yt},{footer:s(()=>[n("span",ss,[l(r,{onClick:e[31]||(e[31]=a=>oe.value=!1)},{default:s(()=>e[88]||(e[88]=[d("取消")])),_:1}),l(r,{type:"primary",loading:Ne.value,onClick:ga},{default:s(()=>e[89]||(e[89]=[d("提交")])),_:1},8,["loading"])])]),default:s(()=>[l(tt,{ref_key:"licenseFormRef",ref:_e,model:k.value,rules:ca,"label-width":"100px",class:"apply-form",size:"small"},{default:s(()=>[l(O,{label:"工具名称",prop:"tool_name"},{default:s(()=>[l(Z,{modelValue:k.value.tool_name,"onUpdate:modelValue":e[25]||(e[25]=a=>k.value.tool_name=a),value:"CAD转GIS",disabled:""},null,8,["modelValue"])]),_:1}),l(O,{label:"使用项目",prop:"user_project"},{error:s(({error:a})=>[n("span",Ql,v(a),1)]),default:s(()=>[l(Z,{modelValue:k.value.user_project,"onUpdate:modelValue":e[26]||(e[26]=a=>k.value.user_project=a),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),l(O,{label:"申请原因",prop:"reason"},{error:s(({error:a})=>[n("span",es,v(a),1)]),default:s(()=>[l(Z,{modelValue:k.value.reason,"onUpdate:modelValue":e[27]||(e[27]=a=>k.value.reason=a),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),l(O,{label:"有效期",prop:"end_date"},{error:s(({error:a})=>[n("span",ts,v(a),1)]),default:s(()=>[l(Ot,{modelValue:k.value.end_date,"onUpdate:modelValue":e[28]||(e[28]=a=>k.value.end_date=a),type:"date",placeholder:"请选择有效期","disabled-date":fa,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),l(O,{label:"申请次数",prop:"usage_count"},{error:s(({error:a})=>[n("span",as,v(a),1)]),default:s(()=>[l(Rt,{modelValue:k.value.usage_count,"onUpdate:modelValue":e[29]||(e[29]=a=>k.value.usage_count=a),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),l(O,{label:"审批人",prop:"approver"},{error:s(({error:a})=>[n("span",ls,v(a),1)]),default:s(()=>[l(zt,{modelValue:k.value.approver,"onUpdate:modelValue":e[30]||(e[30]=a=>k.value.approver=a),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:s(()=>[(y(!0),N(ut,null,it(_t.value,a=>(y(),C($t,{key:a.username,label:a.real_name,value:a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),ps=Qa(ns,[["__scopeId","data-v-9d80a1e7"]]);export{ps as default};
