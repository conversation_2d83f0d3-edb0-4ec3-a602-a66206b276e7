{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/descriptions/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Descriptions from './src/description.vue'\nimport DescriptionsItem from './src/description-item'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDescriptions: SFCWithInstall<typeof Descriptions> & {\n  DescriptionsItem: typeof DescriptionsItem\n} = withInstall(Descriptions, {\n  DescriptionsItem,\n})\n\nexport const ElDescriptionsItem: SFCWithInstall<typeof DescriptionsItem> =\n  withNoopInstall(DescriptionsItem)\n\nexport default ElDescriptions\n\nexport * from './src/description'\nexport * from './src/description-item'\n"], "names": ["DescriptionsItem"], "mappings": ";;;;;;AAGY,MAAC,cAAc,GAAG,WAAW,CAAC,YAAY,EAAE;AACxD,oBAAEA,eAAgB;AAClB,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG,eAAe,CAACA,eAAgB;;;;"}