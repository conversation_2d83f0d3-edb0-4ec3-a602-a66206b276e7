{"version": 3, "file": "button-group.mjs", "sources": ["../../../../../../packages/components/button/src/button-group.ts"], "sourcesContent": ["import { buttonProps } from './button'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const buttonGroupProps = {\n  /**\n   * @description control the size of buttons in this button-group\n   */\n  size: buttonProps.size,\n  /**\n   * @description control the type of buttons in this button-group\n   */\n  type: buttonProps.type,\n} as const\nexport type ButtonGroupProps = ExtractPropTypes<typeof buttonGroupProps>\n"], "names": [], "mappings": ";;AACY,MAAC,gBAAgB,GAAG;AAChC,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;AACxB,EAAE,IAAI,EAAE,WAAW,CAAC,IAAI;AACxB;;;;"}