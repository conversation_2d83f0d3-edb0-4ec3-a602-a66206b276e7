#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\成片开发方案核查.fmw
#          --PARAMETER "陆上风电项目"
#          --开发方案shp "$(FME_MF_DIR)2024\12\1211\苏州市陆上风电项目清单"
#          --两区gdb "E:\YC\数据\两区\调整前\LQDK.gdb"
#          --file "E:\YC\数据\两区\调整后\合并\Export_Output.shp"
#          --项目区图斑shp "E:\YC\数据\最新项目区内高标准农田\项目区内高标准农田（截至2023）\GD\项目区（截至2023）_GD.shp"
#          --输出结果目录 "$(FME_MF_DIR)2024\12\1211\陆上风电项目占用高标准农田"
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="12792.5 4208.33"
#!   DOC_TOP_LEFT="-2587.24 -1992.18"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="14fe0308-2f65-4cb4-81fa-6138a1610c91"
#!   FME_DOCUMENT_PRIORGUID="05f52547-738e-4afe-8ce0-70d4fc509c67,2878da42-62e1-4691-8eec-192aa892a655,cb16f56d-a520-45e4-9a13-186cfddd9a64,ef18b58e-062d-4af2-b3c3-a6aea68b8961,ee269940-3137-4b80-ba24-909e71d3c0c4,831a20b5-b792-4487-9f27-2f12a1f8179e,ebbe234f-3f18-4665-95ef-d95f8e3ddbde,10a830e4-702e-43aa-afb6-c1a303cc3c6a,bb47c332-ab7c-442e-bff3-d3d63e1220d1,c8e7a7e2-b02b-4f09-9b36-888c51617946,9f9ce95c-1e9c-462a-bc7b-4f613536bfdf,2ed42cfc-f8f6-4d79-8de1-ce5c72a75252,9a35479f-1754-4821-85f0-84eb2c4720eb,bbfc1a57-1f80-413f-ad6f-113aee5c6b49,54cea0a5-fc60-4142-bd2e-b27484714a17,ad6afddc-43d1-4b94-ada5-07e156e6656c,614a7919-454f-48c4-888e-3eea02d3490b,0fdf3779-aa6a-43cc-926e-ac9271b6507b,2c6b0c51-daa1-4588-8844-4fd53628f529,70ce8046-4dbe-43f2-a888-f9b8e0bb52bd,df45c9fc-f094-47a7-906e-7473cfbbe5cf,727c7100-6378-4ad2-aba5-a85b12dcda93,d7473125-244d-4465-983a-b054201eac83,080ec582-d573-45d5-8f2c-dc742e2340a2,e8402d9d-3031-4553-8d8e-891e02ed41c2,a2ceb5f6-ed83-4692-812d-e7ea2eb99e77,5c89e954-880d-405b-91ef-573284a04436,41423480-8f44-494a-964f-fa4b13ff62d7,d5c29dfa-1478-4bbe-ad92-93a364aa7b05,9678b7f7-5cd9-4955-87b6-b15a9c55f7bc,ff4ed5a6-3dee-444b-98cc-d297bea0ea95,59c5a956-71f1-4099-9106-e0f41ad3db84,a7113b07-2799-4267-907c-3943bb559847,37245f77-6eb6-47c4-a5b9-acdcbf428a2f,0f1888e0-1d9d-4373-9f80-d89168dec83d,c622ea8d-523d-4dfb-8df4-51b9cdece32f,e13eeb4e-5dd0-4abc-b465-cade6f5c9c77,dac31c58-f5f1-44a6-91f2-12e878824b5d,7fd774f9-3aca-43be-9cac-7ded9bc73e81,8788c0a0-7815-4d80-8853-6139a4338aae,518b7445-ee04-4e1a-9091-1c924fa9d881,3185f6cd-3bc5-4b44-8a23-97a2c8bf5a55,ff3cf154-09a8-4e45-8beb-124f083d5b96,04a01033-9e6a-49c2-997e-a6c0d974407b,037892c7-938a-455f-9b58-793f48fc7e32,bdcb0a67-788f-4c18-ae83-57cd5fa66565,8d8c1c94-6811-4700-9d55-d4771e731aaf,15a5118d-bae3-465c-b629-1c46ca36e5c6"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2024-12-11T16:39:11"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="NO"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="3165 1424.43"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="86"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <SERVICES>
#! <SERVICE
#!   NAME="fmejobsubmitter"
#! >
#! <PROPERTIES>
#! <PROPERTY
#!   CATEGORY="FMEUSERPROPDATA"
#!   NAME="ADVANCED"
#!   VALUE=""
#! />
#! <PROPERTY
#!   CATEGORY="FMEUSERPROPDATA"
#!   NAME="FAILURE_TOPICS"
#!   VALUE=""
#! />
#! <PROPERTY
#!   CATEGORY="FMEUSERPROPDATA"
#!   NAME="HTTP_DATASET"
#!   VALUE="FeatureReader_2"
#! />
#! <PROPERTY
#!   CATEGORY="FMEUSERPROPDATA"
#!   NAME="NOTIFICATION_WRITER"
#!   VALUE=""
#! />
#! <PROPERTY
#!   CATEGORY="FMEUSERPROPDATA"
#!   NAME="SUCCESS_TOPICS"
#!   VALUE=""
#! />
#! </PROPERTIES>
#! </SERVICE>
#! </SERVICES>
#! <REPOSITORY
#!   NAME="高标准农田"
#! />
#! <ITEM
#!   NAME="成片开发方案核查.fmw"
#! />
#! <MODIFICATIONS>
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SERVER_DEST_DIR"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SHAREDRESOURCE_BACKUP"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SHAREDRESOURCE_DASHBOARD"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SHAREDRESOURCE_DATA"
#!   VALUE="C:\ProgramData\Safe Software\FMEFlow\resources\data"
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SHAREDRESOURCE_ENGINE"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SHAREDRESOURCE_LOG"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SHAREDRESOURCE_SYSTEM"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_SHAREDRESOURCE_TEMP"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:FME_TOPIC_MESSAGE"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:file"
#!   VALUE="$(FME_SHAREDRESOURCE_DATA)/成片开发方案核查/调整后两区（shp）/合并/Export_Output.shp"
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:两区gdb"
#!   VALUE="$(FME_SHAREDRESOURCE_DATA)/成片开发方案核查/LQDK.gdb"
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:开发方案shp"
#!   VALUE="$(FME_MF_DIR)2024\4\0415\新建文件夹 (2)\张家港市新闸工业园片区（CP320582-2022-01-03）土地征收成片开发调整方案片区范围和拟征地范围\片区范围.shp"
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:输出结果目录"
#!   VALUE="$(FME_SHAREDRESOURCE_DATA)\成片开发方案核查\result"
#! />
#! <MODIFICATION
#!   UID=":GlobalParameterMgr:项目区图斑shp"
#!   VALUE="$(FME_SHAREDRESOURCE_DATA)/成片开发方案核查/GBZNT/项目区（截至2023）_GD.shp"
#! />
#! <MODIFICATION
#!   UID=":WBDoc:DESTINATION_ROUTING_FILE"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":WBDoc:LOG_FILE"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":WBDoc:SOURCE_ROUTING_FILE"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID=":WBParameterNode_102:DATASET"
#!   VALUE="$(file)"
#! />
#! <MODIFICATION
#!   UID=":WBParameterNode_107:DATASET"
#!   VALUE="$(项目区图斑shp)"
#! />
#! <MODIFICATION
#!   UID=":WBParameterNode_135:DATASET"
#!   VALUE="$(输出结果目录)&lt;backslash&gt;result.xlsx"
#! />
#! <MODIFICATION
#!   UID=":WBParameterNode_52:DATASET"
#!   VALUE="$(两区gdb)"
#! />
#! <MODIFICATION
#!   UID=":WBParameterNode_63:DATASET"
#!   VALUE="$(开发方案shp)"
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SERVER_DEST_DIR"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SHAREDRESOURCE_BACKUP"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SHAREDRESOURCE_DASHBOARD"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SHAREDRESOURCE_DATA"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SHAREDRESOURCE_ENGINE"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SHAREDRESOURCE_LOG"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SHAREDRESOURCE_SYSTEM"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_SHAREDRESOURCE_TEMP"
#!   VALUE=""
#! />
#! <MODIFICATION
#!   UID="面积亩:GlobalParameterMgr:FME_TOPIC_MESSAGE"
#!   VALUE=""
#! />
#! </MODIFICATIONS>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="FeatureReader_2/Export_Output.shp"
#! />
#! <DATASET
#!   NAME="FeatureReader_3"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_3"
#!   DATASET="@Value(path_windows)"
#! />
#! <DATASET
#!   NAME="FeatureReader_4"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_4"
#!   DATASET="@Value(path_windows)"
#! />
#! <DATASET
#!   NAME="FeatureReader_5"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_5"
#!   DATASET="FeatureReader_5/LQDK.gdb"
#! />
#! <DATASET
#!   NAME="FeatureReader_8"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_8"
#!   DATASET="FeatureReader_8/项目区（截至2023）_GD.shp"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/result.xlsx"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI STRING_OR_ATTR PARAMETER 方案编号"
#!   DEFAULT_VALUE="陆上风电项目"
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR 开发方案shp INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp 开发方案(shp)"
#!   DEFAULT_VALUE="$(FME_MF_DIR)2024\12\1211\苏州市陆上风电项目清单"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI SOURCE_GEODATABASE_CFG_OR_ATTR 两区gdb SWIZZLER:YES 两区gdb"
#!   DEFAULT_VALUE="E:\YC\数据\两区\调整前\LQDK.gdb"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 调整后两区shp"
#!   DEFAULT_VALUE="E:\YC\数据\两区\调整后\合并\Export_Output.shp"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR 项目区图斑shp INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 项目区图斑shp"
#!   DEFAULT_VALUE="E:\YC\数据\最新项目区内高标准农田\项目区内高标准农田（截至2023）\GD\项目区（截至2023）_GD.shp"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI DIRNAME_OR_ATTR 输出结果目录 输出结果目录"
#!   DEFAULT_VALUE="$(FME_MF_DIR)2024\12\1211\陆上风电项目占用高标准农田"
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI IGNORE NO_EDIT OPTIONAL DIRNAME_SRC FME_SHAREDRESOURCE_DATA FME Flow Data Directory"
#!   DEFAULT_VALUE="C:\ProgramData\Safe Software\FMEFlow\resources\data"
#!   IS_STAND_ALONE="FME_SERVER"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="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"
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE="陆上风电项目"
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI STRING_OR_ATTR PARAMETER 方案编号"
#! />
#!     <INFO NAME="开发方案shp" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)2024\12\1211\苏州市陆上风电项目清单"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR 开发方案shp INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp 开发方案(shp)"
#! />
#!     <INFO NAME="两区gdb" 
#!   DEFAULT_VALUE="E:\YC\数据\两区\调整前\LQDK.gdb"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI SOURCE_GEODATABASE_CFG_OR_ATTR 两区gdb SWIZZLER:YES 两区gdb"
#! />
#!     <INFO NAME="file" 
#!   DEFAULT_VALUE="E:\YC\数据\两区\调整后\合并\Export_Output.shp"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR file INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 调整后两区shp"
#! />
#!     <INFO NAME="项目区图斑shp" 
#!   DEFAULT_VALUE="E:\YC\数据\最新项目区内高标准农田\项目区内高标准农田（截至2023）\GD\项目区（截至2023）_GD.shp"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIFILE_OR_ATTR 项目区图斑shp INCLUDE_WEB_BROWSER%Esri_Shapefiles(*.shp)|*.shp|Compressed_Esri_Shapefiles(*.shz)|*.shz|Compressed_Files(*.bz2;*.gz)|*.bz2;*.gz|Archive_Files(*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx)|*.7z;*.7zip;*.rar;*.rvz;*.tar;*.tar.bz2;*.tar.gz;*.tgz;*.zip;*.zipx|All_Files(*)|* 项目区图斑shp"
#! />
#!     <INFO NAME="输出结果目录" 
#!   DEFAULT_VALUE="$(FME_MF_DIR)2024\12\1211\陆上风电项目占用高标准农田"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI DIRNAME_OR_ATTR 输出结果目录 输出结果目录"
#! />
#!     <INFO NAME="FME_SHAREDRESOURCE_DATA" 
#!   DEFAULT_VALUE="C:\ProgramData\Safe Software\FMEFlow\resources\data"
#!   SCOPE="FME_SERVER"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI IGNORE NO_EDIT OPTIONAL DIRNAME_SRC FME_SHAREDRESOURCE_DATA FME Flow Data Directory"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! <COMMENT
#!   IDENTIFIER="85"
#!   COMMENT_VALUE="&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;&#10;&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;&#10;p, li { white-space: pre-wrap; }&#10;hr { height: 1px; border-width: 0; }&#10;li.unchecked::marker { content: &quot;\2610&quot;; }&#10;li.checked::marker { content: &quot;\2612&quot;; }&#10;&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;新宋体&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;&#10;&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;去重叠&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;"
#!   POSITION="3333.3666670000034 1650.5660523271902"
#!   TOP_LEFT="3333.3666670000034 1650.5660523271902"
#!   BOTTOM_RIGHT="3838.3677352577329 1649.5660523271902"
#!   BOUNDING_RECT="3333.3666670000034 1650.5660523271902 505.00106825772946 1"
#!   ORDER="500000000000026"
#!   FOLLOW_ANCHOR="true"
#!   INFO_NODE="false"
#!   CUSTOM_USER_COLOR="false"
#!   UUID="{8cd91073-78bd-4c9b-8f60-6a967220234a}"
#!   COLOUR="1,1,0.95686274509803926,0.78431372549019607"
#!   SIZE_POLICY="10"
#!   ANCHORED_NODE="69"
#! />
#! <COMMENT
#!   IDENTIFIER="104"
#!   COMMENT_VALUE="&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;&#10;&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;&#10;p, li { white-space: pre-wrap; }&#10;hr { height: 1px; border-width: 0; }&#10;li.unchecked::marker { content: &quot;\2610&quot;; }&#10;li.checked::marker { content: &quot;\2612&quot;; }&#10;&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;新宋体&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;&#10;&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;去除重叠&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;"
#!   POSITION="4271.1918158982826 69.527728725168174"
#!   TOP_LEFT="4271.1918158982826 69.527728725168174"
#!   BOTTOM_RIGHT="4776.192884156012 68.527728725168174"
#!   BOUNDING_RECT="4271.1918158982826 69.527728725168174 505.00106825772946 1"
#!   ORDER="500000000000034"
#!   FOLLOW_ANCHOR="true"
#!   INFO_NODE="false"
#!   CUSTOM_USER_COLOR="false"
#!   UUID="{36b68d74-9875-4de8-aa44-2e097b5af3e2}"
#!   COLOUR="0.59215686274509804,0.8901960784313725,0.90980392156862744,0.78431372549019607"
#!   SIZE_POLICY="10"
#!   ANCHORED_NODE="101"
#! />
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! <BOOKMARK
#!   IDENTIFIER="19"
#!   NAME="两区"
#!   DESCRIPTION=""
#!   TOP_LEFT="-450.00450004500044 2034.146406464065"
#!   ORDER="500000000000002"
#!   PALETTE_COLOR="Color1"
#!   BOTTOM_RIGHT="1837.************ 780.14640646406451"
#!   BOUNDING_RECT="-450.00450004500044 2034.146406464065 2287.5228752287544 1254.0000000000005"
#!   STICKY="true"
#!   COLOUR="0.59999999999999998,0.80000000000000004,0.80000000000000004,1"
#!   CONTENTS="79 82 20 83 77 84 52 80 78 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="51"
#!   NAME="开发方案"
#!   DESCRIPTION=""
#!   TOP_LEFT="-2587.2351746773288 485.87745202281087"
#!   ORDER="500000000000012"
#!   PALETTE_COLOR="Color1"
#!   BOTTOM_RIGHT="718.75718757187497 -108.62254797718913"
#!   BOUNDING_RECT="-2587.2351746773288 485.87745202281087 3305.9923622492038 594.5"
#!   STICKY="true"
#!   COLOUR="0.59999999999999998,0.80000000000000004,0.80000000000000004,1"
#!   CONTENTS="63 146 149 153 151 53 163 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="70"
#!   NAME="占用两区"
#!   DESCRIPTION=""
#!   TOP_LEFT="2297.5209544612544 1517.646406464065"
#!   ORDER="500000000000014"
#!   PALETTE_COLOR="Color2"
#!   BOTTOM_RIGHT="3568.1558872278183 425.75816340164147"
#!   BOUNDING_RECT="2297.5209544612544 1517.646406464065 1270.6349327665639 1091.8882430624235"
#!   STICKY="true"
#!   COLOUR="0.63529411764705879,0.80000000000000004,0.59999999999999998,1"
#!   CONTENTS="40 30 41 69 81 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="106"
#!   NAME="项目区图斑"
#!   DESCRIPTION=""
#!   TOP_LEFT="1212.5968314629811 -616.77114888592678"
#!   ORDER="500000000000032"
#!   PALETTE_COLOR="Color5"
#!   BOTTOM_RIGHT="3147.5830222219747 -1268.1812864640274"
#!   BOUNDING_RECT="1212.5968314629811 -616.77114888592678 1934.9861907589936 651.4101375781006"
#!   STICKY="true"
#!   COLOUR="0.59607843137254901,0.91764705882352937,0.72549019607843135,1"
#!   CONTENTS="110 107 111 100 108 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="109"
#!   NAME="占用最新项目区内高标准农田"
#!   DESCRIPTION=""
#!   TOP_LEFT="2347.5209544612544 165.77704121829308"
#!   ORDER="500000000000035"
#!   PALETTE_COLOR="Color6"
#!   BOTTOM_RIGHT="5463.9721654948507 -484.07721399092577"
#!   BOUNDING_RECT="2347.5209544612544 165.77704121829308 3116.4512110335963 649.85425520921888"
#!   STICKY="true"
#!   COLOUR="0.59215686274509804,0.8901960784313725,0.90980392156862744,1"
#!   CONTENTS="101 97 114 113 94 95 99 104 103 96 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="117"
#!   NAME="张家港调整后两区"
#!   DESCRIPTION=""
#!   TOP_LEFT="2372.5865015351178 2216.1497424768058"
#!   ORDER="500000000000041"
#!   PALETTE_COLOR="Color2"
#!   BOTTOM_RIGHT="3475.278323710696 1621.6497424768058"
#!   BOUNDING_RECT="2372.5865015351178 2216.1497424768058 1102.6918221755782 594.5"
#!   STICKY="true"
#!   COLOUR="0.63529411764705879,0.80000000000000004,0.59999999999999998,1"
#!   CONTENTS="115 102 116 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="127"
#!   NAME="按项目统计"
#!   DESCRIPTION=""
#!   TOP_LEFT="6564.4776847418616 -540.05093845685246"
#!   ORDER="500000000000056"
#!   PALETTE_COLOR="Color7"
#!   BOTTOM_RIGHT="8991.0311010485857 -998.67874701341884"
#!   BOUNDING_RECT="6564.4776847418616 -540.05093845685246 2426.5534163067232 458.62780855656638"
#!   STICKY="true"
#!   COLOUR="0.59215686274509804,0.75294117647058822,0.90980392156862744,1"
#!   CONTENTS="89 91 90 93 88 92 125 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="128"
#!   NAME="按项目统计"
#!   DESCRIPTION=""
#!   TOP_LEFT="6813.5945684558728 1859.2195896377573"
#!   ORDER="500000000000056"
#!   PALETTE_COLOR="Color7"
#!   BOTTOM_RIGHT="9240.147984762596 1400.5917810811909"
#!   BOUNDING_RECT="6813.5945684558728 1859.2195896377573 2426.5534163067232 458.62780855656638"
#!   STICKY="true"
#!   COLOUR="0.59215686274509804,0.75294117647058822,0.90980392156862744,1"
#!   CONTENTS="76 86 87 73 74 71 75 "
#! >
#! </BOOKMARK>
#! <BOOKMARK
#!   IDENTIFIER="145"
#!   NAME="项目区"
#!   DESCRIPTION=""
#!   TOP_LEFT="2549.7415336944073 -1397.6780524781993"
#!   ORDER="500000000000075"
#!   PALETTE_COLOR="Color5"
#!   BOTTOM_RIGHT="4536.3729073249569 -1992.1780524781993"
#!   BOUNDING_RECT="2549.7415336944073 -1397.6780524781993 1986.6313736305497 594.5"
#!   STICKY="true"
#!   COLOUR="0.59607843137254901,0.91764705882352937,0.72549019607843135,1"
#!   CONTENTS="132 139 138 141 140 "
#! >
#! </BOOKMARK>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="20"
#!   TYPE="GeometryFilter"
#!   VERSION="8"
#!   POSITION="1268.7626876268782 1393.6464064640645"
#!   BOUNDING_RECT="1268.7626876268782 1393.6464064640645 454 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Area"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_featura_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;UNFILTERED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_featura_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILTER_MODE" PARM_VALUE="SIMPLE"/>
#!     <XFORM_PARM PARM_NAME="FILTER_MULTI" PARM_VALUE="FILTER_TYPES]Area]FME_CONTROLLER_QUERY_FILE]]FME_CONTROLLER_CHOICE]Simple"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="GeometryFilter"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="30"
#!   TYPE="Clipper"
#!   VERSION="16"
#!   POSITION="2347.5209544612544 1407.646406464065"
#!   BOUNDING_RECT="2347.5209544612544 1407.646406464065 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="INSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="OUTSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_featura_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ADD_NODATA" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BOUNDARY_CASES" PARM_VALUE="INSIDE"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CLIPPED_ATTR" PARM_VALUE="_clipped"/>
#!     <XFORM_PARM PARM_NAME="CLIPPERS_FIRST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="CLIPPER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INVALID_PARTS_HANDLING" PARM_VALUE="REJECT_WHOLE"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MISSING_M_MODE" PARM_VALUE="Linear Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MISSING_Z_MODE" PARM_VALUE="Planar Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MULTIPLE_CLIPPERS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="M_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVERLAPPING_CLIPPERS" PARM_VALUE="CLIP_OUTSIDE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_RASTER_EXTENTS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_MODE" PARM_VALUE="CENTER"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Clipper_3"/>
#!     <XFORM_PARM PARM_NAME="Z_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Z_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="40"
#!   TYPE="SubDocumentTransformer"
#!   VERSION="1"
#!   POSITION="2673.5439741057858 803.0155001550014"
#!   BOUNDING_RECT="2673.5439741057858 803.0155001550014 430 71"
#!   ORDER="500000000000000"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="result"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="PATH_FILENAME" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_filename&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUB_DOC_NAME" PARM_VALUE="面积亩"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="面积亩_3"/>
#!     <XFORM_PARM PARM_NAME="__COMPOUND_PARAMETERS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="53"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-2426.7115123800486 262.84052699074101"
#!   BOUNDING_RECT="-2426.7115123800486 262.84052699074101 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="63"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-273.38644479468087 353.56458215744954"
#!   BOUNDING_RECT="-273.38644479468087 353.56458215744954 443.00106825772946 71"
#!   ORDER="500000000000013"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE="fme_basename,varchar(50) fme_color,varchar(50) fme_dataset,varchar(50) fme_feature_type,varchar(50) fme_fill_color,varchar(50) fme_primary_axis,double fme_rotation,double fme_secondary_axis,double fme_start_angle,double fme_sweep_angle,double fme_text_size,double fme_text_string,varchar(50) fme_type,varchar(50) multi_reader_full_id,long multi_reader_id,long multi_reader_keyword,varchar(50) multi_reader_type,varchar(50) shape_geometry_error,varchar(254) shapefile_type,varchar(30)"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="52"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-272.38644479468087 1517.3958439584389"
#!   BOUNDING_RECT="-272.38644479468087 1517.3958439584389 442.00106825772946 71"
#!   ORDER="500000000000020"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(两区gdb)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_ADVANCED" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_BEGIN_SQL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_END_SQL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_EXPOSE_ATTRS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_FILEGDB_EXPOSE_FORMAT_ATTRS" PARM_VALUE="fme_basename,text(50) fme_color,text(50) fme_dataset,text(50) fme_feature_type,text(50) fme_fill_color,text(50) fme_primary_axis,double fme_rotation,double fme_secondary_axis,double fme_start_angle,double fme_sweep_angle,double fme_text_size,double fme_text_string,text(50) fme_type,text(50) multi_reader_full_id,int multi_reader_id,int multi_reader_keyword,text(50) multi_reader_type,text(50)"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_GEOMETRY" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_REMOVE_FEATURE_DATASET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_SIMPLE_DONUT_GEOMETRY" PARM_VALUE="simple"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_STRIP_GUID_GLOBALID_BRACES" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_TABLELIST" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILEGDB_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="FILEGDB"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,FILEGDB"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="FILEGDB_GEOMETRY,&quot;OPTIONAL DISCLOSUREGROUP SIMPLE_DONUT_GEOMETRY&quot;,FILEGDB&lt;space&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;,FILEGDB_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP BEGIN_SQL%END_SQL%GEOMETRY%STRIP_GUID_GLOBALID_BRACES&quot;,FILEGDB&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,FILEGDB_REMOVE_FEATURE_DATASET,&quot;OPTIONAL ACTIVECHECK YES%NO&quot;,FILEGDB&lt;space&gt;Remove&lt;space&gt;Feature&lt;space&gt;Dataset:,FILEGDB_SIMPLE_DONUT_GEOMETRY,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,simple%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,complex&quot;,FILEGDB&lt;space&gt;&lt;u73af&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u53d1&gt;&lt;u73b0&gt;,FILEGDB_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,FILEGDB&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u641c&gt;&lt;u7d22&gt;&lt;u8303&gt;&lt;u56f4&gt;,FILEGDB_END_SQL,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,FILEGDB&quot;,FILEGDB&lt;space&gt;&lt;u8bfb&gt;&lt;u53d6&gt;&lt;u540e&gt;&lt;u6267&gt;&lt;u884c&gt;&lt;u7684&gt;SQL,FILEGDB_BEGIN_SQL,&quot;OPTIONAL TEXT_EDIT_SQL_CFG_ENCODED MODE,SQL;FORMAT,FILEGDB&quot;,FILEGDB&lt;space&gt;&lt;u8bfb&gt;&lt;u53d6&gt;&lt;u524d&gt;&lt;u6267&gt;&lt;u884c&gt;&lt;u7684&gt;SQL,FILEGDB_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP FILEGDB_EXPOSE_FORMAT_ATTRS&quot;,FILEGDB&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,FILEGDB_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%使用网络验证%PROMPT_TYPE%NETWORK&quot;,FILEGDB&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u7f51&gt;&lt;u7edc&gt;&lt;u9a8c&gt;&lt;u8bc1&gt;,FILEGDB_QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,&quot;OPTIONAL NO_EDIT TEXT&quot;,FILEGDB&lt;space&gt;,FILEGDB_TABLELIST,&quot;IGNORE TEXT&quot;,FILEGDB&lt;space&gt;&lt;u8868&gt;:,FILEGDB_FILEGDB_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS FILEGDB%Source&quot;,FILEGDB&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,FILEGDB_STRIP_GUID_GLOBALID_BRACES,&quot;OPTIONAL CHOICE yes%no&quot;,FILEGDB&lt;space&gt;&lt;u4ece&gt;GlobalID&lt;u548c&gt;GUID&lt;u4e2d&gt;&lt;u53bb&gt;&lt;u6389&gt;&lt;u5927&gt;&lt;u62ec&gt;&lt;u53f7&gt;:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="77"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-303.12803128031283 1821.8932189321893"
#!   BOUNDING_RECT="-303.12803128031283 1821.8932189321893 430 71"
#!   ORDER="500000000000022"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="80"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="743.75743757437567 1431.264312643126"
#!   BOUNDING_RECT="743.75743757437567 1431.264312643126 454 71"
#!   ORDER="500000000000023"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_featura_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_featura_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="CASE_INSENSITIVE_TEST &lt;at&gt;Value&lt;openparen&gt;fme_featura_type&lt;closeparen&gt; CONTAINS &lt;u82cf&gt;&lt;u5dde&gt;&lt;u5e02&gt;"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="79"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="228.1272812728127 1340.6384063840633"
#!   BOUNDING_RECT="228.1272812728127 1340.6384063840633 454 71"
#!   ORDER="500000000000024"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_featura_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="fme_featura_type,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="69"
#!   TYPE="AreaOnAreaOverlayer"
#!   VERSION="9"
#!   POSITION="2906.2790627906279 1287.646406464065"
#!   BOUNDING_RECT="2906.2790627906279 1287.646406464065 505.00106825772946 71"
#!   ORDER="500000000000025"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AREA"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ACCUM_ATTRS_NAME" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="First Wins"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVC_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaOnAreaOverlayer_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="94"
#!   TYPE="Clipper"
#!   VERSION="16"
#!   POSITION="2981.2801310483574 55.777041218293135"
#!   BOUNDING_RECT="2981.2801310483574 55.777041218293135 430 71"
#!   ORDER="500000000000029"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="INSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="OUTSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="BSM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XZQDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XZQMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="QX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMBH" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMBM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="XMMJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="DKMJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="IFQ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="TBMJ_M" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_basename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_color" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_dataset" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_fill_color" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_primary_axis" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rotation" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_secondary_axis" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_start_angle" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_sweep_angle" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_size" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_full_id" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_id" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_keyword" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="shape_geometry_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ADD_NODATA" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BOUNDARY_CASES" PARM_VALUE="INSIDE"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CLIPPED_ATTR" PARM_VALUE="_clipped"/>
#!     <XFORM_PARM PARM_NAME="CLIPPERS_FIRST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="CLIPPER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INVALID_PARTS_HANDLING" PARM_VALUE="REJECT_WHOLE"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="XZQMC"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="SELECTED"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="项目区"/>
#!     <XFORM_PARM PARM_NAME="MEASURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MISSING_M_MODE" PARM_VALUE="Linear Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MISSING_Z_MODE" PARM_VALUE="Planar Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MULTIPLE_CLIPPERS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="M_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVERLAPPING_CLIPPERS" PARM_VALUE="CLIP_OUTSIDE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_RASTER_EXTENTS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_MODE" PARM_VALUE="CENTER"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Clipper_4"/>
#!     <XFORM_PARM PARM_NAME="Z_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Z_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="97"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="2419.4125459544885 -138.53231315818311"
#!   BOUNDING_RECT="2419.4125459544885 -138.53231315818311 454 71"
#!   ORDER="500000000000030"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="GHFQMC,,KFPQMC,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="96"
#!   TYPE="SubDocumentTransformer"
#!   VERSION="1"
#!   POSITION="4940.1274264932554 55.777041218293135"
#!   BOUNDING_RECT="4940.1274264932554 55.777041218293135 430 71"
#!   ORDER="500000000000000"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="result"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="PATH_FILENAME" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_filename&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUB_DOC_NAME" PARM_VALUE="面积亩"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="面积亩_4"/>
#!     <XFORM_PARM PARM_NAME="__COMPOUND_PARAMETERS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="101"
#!   TYPE="AreaOnAreaOverlayer"
#!   VERSION="9"
#!   POSITION="4271.1918158982826 -64.222958781706893"
#!   BOUNDING_RECT="4271.1918158982826 -64.222958781706893 505.00106825772946 71"
#!   ORDER="500000000000031"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="AREA"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_PARM PARM_NAME="ACCUM_ATTRS_NAME" PARM_VALUE="Use Attributes From One Feature"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="First Wins"/>
#!     <XFORM_PARM PARM_NAME="DEAGGREGATE_INPUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVC_ATTR" PARM_VALUE="_overlaps"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaOnAreaOverlayer_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="100"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="1304.5968314629811 -843.27137470819241"
#!   BOUNDING_RECT="1304.5968314629811 -843.27137470819241 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="107"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1846.0236148753233 -744.20847301256515"
#!   BOUNDING_RECT="1846.0236148753233 -744.20847301256515 443.00106825772946 71"
#!   ORDER="500000000000033"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;u9879&gt;&lt;u76ee&gt;&lt;u533a&gt;&lt;uff08&gt;&lt;u622a&gt;&lt;u81f3&gt;2023&lt;uff09&gt;_GD"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BSM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XZQDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="QX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMBH" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMBM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="XMMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="DKMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="IFQ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="TBMJ_M" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_basename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_color" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_dataset" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_fill_color" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_primary_axis" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_rotation" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_secondary_axis" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_start_angle" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_sweep_angle" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_size" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_full_id" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_id" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_keyword" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="shape_geometry_error" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="项目区（截至2023）_GD,&quot;OBJECTID,BSM,XZQDM,XZQMC,QX,XMBH,XMBM,XMMC,XMMJ,DKMJ,IFQ,TBMJ_M,Shape_Leng,Shape_Area,fme_basename,fme_color,fme_dataset,fme_feature_type,fme_fill_color,fme_primary_axis,fme_rotation,fme_secondary_axis,fme_start_angle,fme_sweep_angle,fme_text_size,fme_text_string,fme_type,multi_reader_full_id,multi_reader_id,multi_reader_keyword,multi_reader_type,shape_geometry_error,shapefile_type&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="项目区（截至2023）_GD,&quot;int32,varchar(18),varchar(9),varchar(100),varchar(10),varchar(254),varchar(254),varchar(254),real64,real64,varchar(50),real64,real64,real64,varchar(50),varchar(50),varchar(50),varchar(50),varchar(50),real64,real64,real64,real64,real64,real64,varchar(50),varchar(50),int32,int32,varchar(50),varchar(50),varchar(254),varchar(30)&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%项目区（截至2023）_GD"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(项目区图斑shp)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="&lt;u9879&gt;&lt;u76ee&gt;&lt;u533a&gt;&lt;uff08&gt;&lt;u622a&gt;&lt;u81f3&gt;2023&lt;uff09&gt;_GD"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="项目区（截至2023）_GD,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;&lt;u73af&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u53d1&gt;&lt;u73b0&gt;,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u628a&gt;&lt;u6d4b&gt;&lt;u91cf&gt;&lt;u4f5c&gt;&lt;u4e3a&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u641c&gt;&lt;u7d22&gt;&lt;u8303&gt;&lt;u56f4&gt;,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u4fee&gt;&lt;u526a&gt;&lt;u524d&gt;&lt;u90e8&gt;&lt;u7684&gt;&lt;u7a7a&gt;&lt;u683c&gt;,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u62a5&gt;&lt;u544a&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u5f02&gt;&lt;u5e38&gt;,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;&lt;u5b57&gt;&lt;u7b26&gt;&lt;u7f16&gt;&lt;u7801&gt;,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;&lt;u7a7a&gt;&lt;u767d&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u8bfb&gt;&lt;u4f5c&gt;&lt;uff1a&gt;,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;&lt;u6570&gt;&lt;u503c&gt;&lt;u578b&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;u5904&gt;&lt;u7406&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE="fme_basename,varchar(50) fme_color,varchar(50) fme_dataset,varchar(50) fme_feature_type,varchar(50) fme_fill_color,varchar(50) fme_primary_axis,double fme_rotation,double fme_secondary_axis,double fme_start_angle,double fme_sweep_angle,double fme_text_size,double fme_text_string,varchar(50) fme_type,varchar(50) multi_reader_full_id,long multi_reader_id,long multi_reader_keyword,varchar(50) multi_reader_type,varchar(50) shape_geometry_error,varchar(254) shapefile_type,varchar(30)"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_8"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="110"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="2471.0298649378256 -808.64210703962692"
#!   BOUNDING_RECT="2471.0298649378256 -808.64210703962692 454 71"
#!   ORDER="500000000000036"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="BSM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZQDM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="QX" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMBH" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMBM" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="XMMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="DKMJ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="IFQ" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="TBMJ_M" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_basename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_color" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_fill_color" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_primary_axis" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_rotation" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_secondary_axis" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_start_angle" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_sweep_angle" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_size" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_text_string" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_full_id" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_id" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_keyword" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="multi_reader_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shape_geometry_error" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="shapefile_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="XZQMC,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="95"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="3634.0569591262929 -138.53231315818311"
#!   BOUNDING_RECT="3634.0569591262929 -138.53231315818311 454 71"
#!   ORDER="500000000000037"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&lt;u9879&gt;&lt;u76ee&gt;&lt;u533a&gt;&lt;opencurly&gt;&lt;closecurly&gt;.XZQMC &lt;u9879&gt;&lt;u76ee&gt;&lt;u533a&gt;&lt;opencurly&gt;&lt;closecurly&gt;.XZQMC  varchar&lt;openparen&gt;100&lt;closeparen&gt; NO_OP GHFQMC GHFQMC  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP _clipped _clipped  char&lt;openparen&gt;3&lt;closeparen&gt; REMOVE  &lt;u9879&gt;&lt;u76ee&gt;&lt;u533a&gt; &lt;at&gt;Value&lt;openparen&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u533a&gt;&lt;opencurly&gt;0&lt;closecurly&gt;.XZQMC&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO path_unix path_unix  buffer NO_OP path_windows path_windows  buffer NO_OP path_rootname path_rootname  buffer NO_OP path_filename path_filename  buffer NO_OP path_extension path_extension  buffer NO_OP path_directory_unix path_directory_unix  buffer NO_OP path_directory_windows path_directory_windows  buffer NO_OP path_type path_type  varchar&lt;openparen&gt;10&lt;closeparen&gt; NO_OP fme_geometry&lt;opencurly&gt;0&lt;closecurly&gt; fme_geometry&lt;opencurly&gt;0&lt;closecurly&gt;  fme_no_geom NO_OP KFPQMC KFPQMC  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP fme_geometry&lt;opencurly&gt;&lt;closecurly&gt; fme_geometry&lt;opencurly&gt;&lt;closecurly&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="102"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="2983.2772554529665 2087.6497424768058"
#!   BOUNDING_RECT="2983.2772554529665 2087.6497424768058 442.00106825772946 71"
#!   ORDER="500000000000039"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="Export_Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQDKDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQDKMJ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQDKMJM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="QYHFLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQPKDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BSM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_3" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_4" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_5" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="TZLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQPKDM_1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQPKMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="PKLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQHFLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="LQDKMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="ZLDJDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="QSDWDM" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="PDJB" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="SFGBZNT" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JYZTLX" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="JYZTMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_1" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="FRDBS" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="QSDWMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_2" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_3" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_4" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_5" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="Export_Output,&quot;OBJECTID_1,LQDKDM,LQDKMJ,LQDKMJM,QYHFLX,LQPKDM,BSM,YSDM,BZ,OBJECTID_2,OBJECTID_3,OBJECTID_4,OBJECTID_5,TZLX,LQPKDM_1,LQPKMC,PKLX,LQLX,LQHFLX,LQDKMC,ZLDJDM,QSDWDM,PDJB,SFGBZNT,JYZTLX,JYZTMC,OBJECTID,Shape_Leng,Shape_Le_1,FRDBS,QSDWMC,Shape_Le_2,Shape_Le_3,Shape_Le_4,Shape_Le_5,Shape_Area&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="Export_Output,&quot;int32,varchar(50),real64,real64,varchar(50),varchar(50),varchar(50),varchar(50),varchar(50),int32,real64,real64,real64,varchar(4),varchar(50),varchar(50),varchar(50),varchar(2),varchar(2),varchar(50),varchar(2),varchar(12),varchar(2),varchar(2),varchar(50),varchar(50),real64,real64,real64,varchar(1),varchar(254),real64,real64,real64,real64,real64&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%Export_Output"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(file)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="Export_Output"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="Export_Output,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u4fee&gt;&lt;u526a&gt;&lt;u524d&gt;&lt;u90e8&gt;&lt;u7684&gt;&lt;u7a7a&gt;&lt;u683c&gt;,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u641c&gt;&lt;u7d22&gt;&lt;u8303&gt;&lt;u56f4&gt;,SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u628a&gt;&lt;u6d4b&gt;&lt;u91cf&gt;&lt;u4f5c&gt;&lt;u4e3a&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;&lt;u7a7a&gt;&lt;u767d&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u8bfb&gt;&lt;u4f5c&gt;&lt;uff1a&gt;,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;&lt;u5b57&gt;&lt;u7b26&gt;&lt;u7f16&gt;&lt;u7801&gt;,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;&lt;u73af&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u53d1&gt;&lt;u73b0&gt;,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u62a5&gt;&lt;u544a&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u5f02&gt;&lt;u5e38&gt;,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;&lt;u6570&gt;&lt;u503c&gt;&lt;u578b&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;u5904&gt;&lt;u7406&gt;,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="115"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="2464.5865015351178 2036.1028352551566"
#!   BOUNDING_RECT="2464.5865015351178 2036.1028352551566 430 71"
#!   ORDER="500000000000040"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="118"
#!   TYPE="Clipper"
#!   VERSION="16"
#!   POSITION="3789.3639826362423 2145.7898587616892"
#!   BOUNDING_RECT="3789.3639826362423 2145.7898587616892 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="INSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="OUTSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_1" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQDKDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQDKMJ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQDKMJM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="QYHFLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQPKDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="BSM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="YSDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="BZ" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_2" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_3" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_4" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID_5" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="TZLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQPKDM_1" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQPKMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="PKLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQHFLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="LQDKMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="ZLDJDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="QSDWDM" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="PDJB" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="SFGBZNT" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JYZTLX" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="JYZTMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="OBJECTID" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Leng" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_1" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="FRDBS" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="QSDWMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_2" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_3" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_4" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Le_5" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="Shape_Area" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ADD_NODATA" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BOUNDARY_CASES" PARM_VALUE="INSIDE"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CLIPPED_ATTR" PARM_VALUE="_clipped"/>
#!     <XFORM_PARM PARM_NAME="CLIPPERS_FIRST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="CLIPPER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INVALID_PARTS_HANDLING" PARM_VALUE="REJECT_WHOLE"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MISSING_M_MODE" PARM_VALUE="Linear Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MISSING_Z_MODE" PARM_VALUE="Planar Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MULTIPLE_CLIPPERS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="M_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVERLAPPING_CLIPPERS" PARM_VALUE="CLIP_OUTSIDE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_RASTER_EXTENTS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_MODE" PARM_VALUE="CENTER"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Clipper_6"/>
#!     <XFORM_PARM PARM_NAME="Z_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Z_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="123"
#!   TYPE="SubDocumentTransformer"
#!   VERSION="1"
#!   POSITION="758.02502889058792 600.13583712552759"
#!   BOUNDING_RECT="758.02502889058792 600.13583712552759 430 71"
#!   ORDER="500000000000000"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="result"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="PATH_FILENAME" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_filename&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUB_DOC_NAME" PARM_VALUE="面积亩"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="面积亩_6"/>
#!     <XFORM_PARM PARM_NAME="__COMPOUND_PARAMETERS" PARM_VALUE=""/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="135"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="7867.8909900080243 893.60491518837568"
#!   BOUNDING_RECT="7867.8909900080243 893.60491518837568 430 71"
#!   ORDER="500000000000048"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(输出结果目录)&lt;backslash&gt;result.xlsx"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_FILE&lt;comma&gt;No&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;comma&gt;TEMPLATE_SHEET&lt;comma&gt;&lt;comma&gt;REMOVE_UNCHANGED_TEMPLATE_SHEET&lt;comma&gt;No&lt;comma&gt;MULTIPLE_TEMPLATE_SHEETS&lt;comma&gt;Yes&lt;comma&gt;INSERT_IGNORE_DB_OP&lt;comma&gt;Yes&lt;comma&gt;DROP_TABLE&lt;comma&gt;No&lt;comma&gt;TRUNCATE_TABLE&lt;comma&gt;No&lt;comma&gt;FIELD_NAMES_OUT&lt;comma&gt;Yes&lt;comma&gt;FIELD_NAMES_FORMATTING&lt;comma&gt;Yes&lt;comma&gt;WRITER_MODE&lt;comma&gt;Insert&lt;comma&gt;RASTER_FORMAT&lt;comma&gt;PNG&lt;comma&gt;PROTECT_SHEET&lt;comma&gt;NO&lt;comma&gt;PROTECT_SHEET_PASSWORD&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_LEVEL&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_PERMISSIONS&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING&lt;comma&gt;yes&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE&lt;comma&gt;CUSTOM_NUMBER_FORMATTING&lt;comma&gt;ENABLE_NATIVE&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXW_PROTECT_SHEET,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP PROTECT_SHEET_PASSWORD%PROTECT_SHEET_LEVEL%PROTECT_SHEET_PERMISSIONS&quot;,XLSXW&lt;space&gt;Protect&lt;space&gt;Sheet,XLSXW_DROP_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_WRITER_MODE,&quot;OPTIONAL CHOICE Insert%Update%Delete&quot;,XLSXW&lt;space&gt;Default&lt;space&gt;Feature&lt;space&gt;Type&lt;space&gt;Writer&lt;space&gt;Mode:,XLSXW_INSERT_IGNORE_DB_OP,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_TRUNCATE_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_OVERWRITE_FILE,&quot;OPTIONAL ACTIVECHOICE Yes%No,TEMPLATEFILE,TEMPLATE_SHEET,REMOVE_UNCHANGED_TEMPLATE_SHEET&quot;,XLSXW&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;File:,XLSXW_MULTIPLE_TEMPLATE_SHEETS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXW&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_CUSTOM_NUMBER_FORMATTING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_RASTER_FORMAT,&quot;OPTIONAL CHOICE BMP%JPEG%PNG&quot;,XLSXW&lt;space&gt;Raster&lt;space&gt;Format:,XLSXW_FIELD_NAMES_OUT,&quot;OPTIONAL ACTIVECHOICE Yes%No,FIELD_NAMES_FORMATTING,++FIELD_NAMES_FORMATTING+No&quot;,XLSXW&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:,XLSXW_FIELD_NAMES_FORMATTING,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;u5360&gt;&lt;u7528&gt;&lt;u9ad8&gt;&lt;u6807&gt;&lt;u51c6&gt;&lt;u519c&gt;&lt;u7530&gt;:&lt;u4ea9&gt;,ftp_feature_type_name,&lt;u5360&gt;&lt;u7528&gt;&lt;u9ad8&gt;&lt;u6807&gt;&lt;u51c6&gt;&lt;u519c&gt;&lt;u7530&gt;,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u65b9&lt;gt&gt;&lt;lt&gt;u6848&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u4ef6&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5360&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u4ea9&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5360&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u516c&lt;gt&gt;&lt;lt&gt;u9877&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_attributes,fme_feature_type,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No;&lt;u5360&gt;&lt;u7528&gt;&lt;u8c03&gt;&lt;u6574&gt;&lt;u524d&gt;&lt;u4e24&gt;&lt;u533a&gt;:Output,ftp_feature_type_name,&lt;u5360&gt;&lt;u7528&gt;&lt;u8c03&gt;&lt;u6574&gt;&lt;u524d&gt;&lt;u4e24&gt;&lt;u533a&gt;,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u65b9&lt;gt&gt;&lt;lt&gt;u6848&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u4ef6&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5360&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u4ea9&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5360&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u516c&lt;gt&gt;&lt;lt&gt;u9877&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_attributes,fme_feature_type,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No;&lt;u5360&gt;&lt;u7528&gt;&lt;u8c03&gt;&lt;u6574&gt;&lt;u540e&gt;&lt;u4e24&gt;&lt;u533a&gt;:result,ftp_feature_type_name,&lt;u5360&gt;&lt;u7528&gt;&lt;u8c03&gt;&lt;u6574&gt;&lt;u540e&gt;&lt;u4e24&gt;&lt;u533a&gt;,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u65b9&lt;gt&gt;&lt;lt&gt;u6848&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u6587&lt;gt&gt;&lt;lt&gt;u4ef6&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5360&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u4ea9&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5360&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u516c&lt;gt&gt;&lt;lt&gt;u9877&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;20&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;NO&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;&lt;comma&gt;&lt;comma&gt;,ftp_format_attributes,fme_feature_type,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_xml&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_json&lt;comma&gt;&lt;quote&gt;auto&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;datetime&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_datetime&lt;comma&gt;&lt;quote&gt;time&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_time&lt;comma&gt;&lt;quote&gt;date&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_date&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;boolean&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;openbracket&gt;&lt;closebracket&gt;*&lt;backslash&gt;&lt;backslash&gt;?:&lt;apos&gt;,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none&lt;comma&gt;fme_no_geom&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point&lt;comma&gt;xlsx_point&lt;comma&gt;fme_point&lt;comma&gt;xlsx_none&lt;comma&gt;fme_line&lt;comma&gt;xlsx_none&lt;comma&gt;fme_polygon&lt;comma&gt;xlsx_none&lt;comma&gt;fme_text&lt;comma&gt;xlsx_none&lt;comma&gt;fme_ellipse&lt;comma&gt;xlsx_none&lt;comma&gt;fme_arc&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_collection&lt;comma&gt;xlsx_none&lt;comma&gt;fme_surface&lt;comma&gt;xlsx_none&lt;comma&gt;fme_solid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_raster&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point_cloud&lt;comma&gt;xlsx_none&lt;comma&gt;fme_voxel_grid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;xlsx_layer_group&lt;space&gt;xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group&lt;space&gt;Sheet&lt;space&gt;Settings&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_truncate_group&lt;space&gt;xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet&lt;space&gt;Drop&lt;solidus&gt;Truncate&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_rowcolumn_group&lt;space&gt;xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Position&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVEDISCLOSUREGROUP&lt;space&gt;xlsx_protect_sheet&lt;space&gt;xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions&lt;space&gt;Protect&lt;space&gt;Sheet&lt;quote&gt;&lt;comma&gt;NO&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_template_group&lt;space&gt;xlsx_template_sheet%xlsx_remove_unchanged_template_sheet&lt;space&gt;Template&lt;space&gt;Options&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_advanced_group&lt;space&gt;xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_drop_sheet&lt;space&gt;Yes%No&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_trunc_sheet&lt;space&gt;Yes%No&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_sheet_order&lt;space&gt;1%MAX&lt;space&gt;Sheet&lt;space&gt;Order&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_freeze_end_row&lt;space&gt;1%MAX&lt;space&gt;Freeze&lt;space&gt;First&lt;space&gt;Row&lt;openparen&gt;s&lt;closeparen&gt;&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_field_names_out&lt;space&gt;Yes%No&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;++xlsx_field_names_formatting+No&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_field_names_formatting&lt;space&gt;Yes%No&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_names_are_positions&lt;space&gt;Yes%No&lt;space&gt;Use&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;As&lt;space&gt;Column&lt;space&gt;Positions:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_start_col&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_start_row&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_offset_col&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_raster_type&lt;space&gt;BMP%JPEG%PNG&lt;space&gt;Raster&lt;space&gt;Format:&lt;quote&gt;&lt;comma&gt;PNG&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;PASSWORD_ENCODED&lt;space&gt;xlsx_protect_sheet_password&lt;space&gt;Password:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;xlsx_protect_sheet_level&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_DEFAULT&lt;comma&gt;xlsx_protect_sheet_permissions%View&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_ALL&lt;comma&gt;xlsx_protect_sheet_permissions%Specific&lt;lt&gt;space&lt;gt&gt;Permissions&lt;space&gt;Protection&lt;space&gt;Level:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;LOOKUP_LISTBOX&lt;space&gt;xlsx_protect_sheet_permissions&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;locked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_LOCKED_CELLS%Select&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_UNLOCKED_CELLS%Format&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_FORMAT_CELLS%Format&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_FORMAT_COLUMNS%Format&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_FORMAT_ROWS%Insert&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_INSERT_COLUMNS%Insert&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_INSERT_ROWS%Add&lt;lt&gt;space&lt;gt&gt;hyperlinks&lt;lt&gt;space&lt;gt&gt;to&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_INSERT_HYPERLINKS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_DELETE_COLUMNS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_DELETE_ROWS%Sort&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;solidus&gt;rows&lt;solidus&gt;columns&lt;comma&gt;PROT_SORT%Use&lt;lt&gt;space&lt;gt&gt;Autofilter&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_AUTOFILTER%Use&lt;lt&gt;space&lt;gt&gt;PivotTable&lt;lt&gt;space&lt;gt&gt;&lt;amp&gt;&lt;lt&gt;space&lt;gt&gt;PivotChart&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_PIVOTTABLES%Edit&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;objects&lt;comma&gt;PROT_OBJECTS%Edit&lt;lt&gt;space&lt;gt&gt;unprotected&lt;lt&gt;space&lt;gt&gt;scenarios&lt;comma&gt;PROT_SCENARIOS&lt;space&gt;Specific&lt;space&gt;Permissions:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_table_writer_mode&lt;space&gt;Insert&lt;comma&gt;+xlsx_row_id_column+&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;%Update&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id%Delete&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id&lt;space&gt;Writer&lt;space&gt;Mode:&lt;quote&gt;&lt;comma&gt;Insert&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ATTR&lt;space&gt;xlsx_row_id_column&lt;space&gt;ALLOW_NEW&lt;space&gt;Row&lt;space&gt;Number&lt;space&gt;Attribute:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT_EDIT&lt;space&gt;xlsx_template_sheet&lt;space&gt;Template&lt;space&gt;Sheet:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_remove_unchanged_template_sheet&lt;space&gt;Yes%No&lt;space&gt;Remove&lt;space&gt;Template&lt;space&gt;Sheet&lt;space&gt;if&lt;space&gt;Unchanged:&lt;quote&gt;&lt;comma&gt;No,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;NO&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;No&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,DEFAULT_READER&lt;comma&gt;XLSXR&lt;comma&gt;ALLOW_DATASET_CONFLICT&lt;comma&gt;YES&lt;comma&gt;MIME_TYPE&lt;comma&gt;&lt;quote&gt;application&lt;solidus&gt;vnd.openxmlformats-officedocument.spreadsheetml.sheet&lt;space&gt;ADD_DISPOSITION&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;DEFAULT_ATTR_TYPE&lt;comma&gt;auto&lt;comma&gt;USER_ATTRIBUTES_COLUMNS&lt;comma&gt;&lt;quote&gt;Width&lt;comma&gt;Cell&lt;space&gt;Width%Precision&lt;comma&gt;Formatting&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Sheet&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Sheet1&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;name&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Microsoft&lt;space&gt;Excel&lt;space&gt;file&lt;quote&gt;,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_CUSTOM_NUMBER_FORMATTING" PARM_VALUE="ENABLE_NATIVE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DROP_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_FORMATTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_OUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_INSERT_IGNORE_DB_OP" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_MULTIPLE_TEMPLATE_SHEETS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXW_OVERWRITE_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_PROTECT_SHEET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_RASTER_FORMAT" PARM_VALUE="PNG"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_TRUNCATE_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_WRITER_MODE" PARM_VALUE="Insert"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="58"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="6255.5821319178758 1194.2420096293988"
#!   BOUNDING_RECT="6255.5821319178758 1194.2420096293988 454 71"
#!   ORDER="500000000000049"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="方案" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="文件" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="fme_feature_type fme_feature_type  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt; &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt; &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt;  real64 NO_OP &lt;u56fe&gt;&lt;u5c42&gt; &lt;u65b9&gt;&lt;u6848&gt; $(PARAMETER) varchar&lt;openparen&gt;200&lt;closeparen&gt; RENAME_SET_VALUE path_filename &lt;u6587&gt;&lt;u4ef6&gt;  buffer RENAME_SET_VALUE"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="71"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="6885.3251748063021 1752.4147241207477"
#!   BOUNDING_RECT="6885.3251748063021 1752.4147241207477 454 71"
#!   ORDER="500000000000052"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="KFPQMC,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="73"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="7505.3482010958542 1752.4147241207477"
#!   BOUNDING_RECT="7505.3482010958542 1752.4147241207477 454 71"
#!   ORDER="500000000000053"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="75"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="8076.7384800724649 1673.3000089229627"
#!   BOUNDING_RECT="8076.7384800724649 1673.3000089229627 454 71"
#!   ORDER="500000000000054"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u4ea9&gt; SET_TO &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0015&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u516c&gt;&lt;u9877&gt; SET_TO &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0001&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="86"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="8689.8775228552968 1629.7869155641813"
#!   BOUNDING_RECT="8689.8775228552968 1629.7869155641813 484.00106825772946 71"
#!   ORDER="500000000000055"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="&lt;u516c&gt;&lt;u9877&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,;&lt;u4ea9&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="KFPQMC"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="88"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="6652.4776847418616 -650.05093845685246"
#!   BOUNDING_RECT="6652.4776847418616 -650.05093845685246 454 71"
#!   ORDER="500000000000052"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="KFPQMC,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="89"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="7272.5007110314136 -650.05093845685246"
#!   BOUNDING_RECT="7272.5007110314136 -650.05093845685246 454 71"
#!   ORDER="500000000000053"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="90"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="7843.8909900080243 -729.16565365463748"
#!   BOUNDING_RECT="7843.8909900080243 -729.16565365463748 454 71"
#!   ORDER="500000000000054"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u4ea9&gt; SET_TO &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0015&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u516c&gt;&lt;u9877&gt; SET_TO &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0001&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="91"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="8457.0300327908553 -772.67874701341884"
#!   BOUNDING_RECT="8457.0300327908553 -772.67874701341884 484.00106825772946 71"
#!   ORDER="500000000000055"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="&lt;u516c&gt;&lt;u9877&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,;&lt;u4ea9&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="KFPQMC"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="129"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="9389.6287800087302 1629.7869155641813"
#!   BOUNDING_RECT="9389.6287800087302 1629.7869155641813 454 71"
#!   ORDER="500000000000057"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="报告" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u62a5&gt;&lt;u544a&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;KFPQMC&lt;closeparen&gt;&lt;u6d89&gt;&lt;u53ca&gt;&lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u516c&gt;&lt;u9877&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff08&gt;&lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u4ea9&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt;&lt;uff09&gt;&lt;u4ea9&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="131"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="9751.2963545894108 -772.67874701341884"
#!   BOUNDING_RECT="9751.2963545894108 -772.67874701341884 454 71"
#!   ORDER="500000000000057"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="报告" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u62a5&gt;&lt;u544a&gt; SET_TO &lt;at&gt;Value&lt;openparen&gt;KFPQMC&lt;closeparen&gt;&lt;u6d89&gt;&lt;u53ca&gt;&lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u516c&gt;&lt;u9877&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff08&gt;&lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u4ea9&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt;&lt;uff09&gt;&lt;u4ea9&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="133"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="9124.3644994589486 -772.67874701341884"
#!   BOUNDING_RECT="9124.3644994589486 -772.67874701341884 454 71"
#!   ORDER="500000000000058"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;&lt;u4ea9&gt;.sum&lt;closeparen&gt; != 0"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="147"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="4633.0405164516778 -686.78012361518961"
#!   BOUNDING_RECT="4633.0405164516778 -686.78012361518961 454 71"
#!   ORDER="500000000000067"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="152"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="5225.3429511039294 -686.78012361518961"
#!   BOUNDING_RECT="5225.3429511039294 -686.78012361518961 454 71"
#!   ORDER="500000000000068"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="mjm" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; mjm SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0015&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_7"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="156"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="5832.1804148274041 -729.16565365463748"
#!   BOUNDING_RECT="5832.1804148274041 -729.16565365463748 523.00106825772946 71"
#!   ORDER="500000000000069"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="mjm.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="mjm" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="项目区{}.XZQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="项目区" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_overlaps" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="mjm.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="mjm,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="KFPQMC"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="132"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="2641.7415336944073 -1526.1780524781993"
#!   BOUNDING_RECT="2641.7415336944073 -1526.1780524781993 430 71"
#!   ORDER="500000000000071"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(FME_MF_DIR_ENCODED)2024&lt;backslash&gt;4&lt;backslash&gt;0425&lt;backslash&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u533a&gt;&lt;backslash&gt;&lt;u9879&gt;&lt;u76ee&gt;&lt;u6309&gt;&lt;u5e74&gt;&lt;u4efd&gt;&lt;backslash&gt;22-23&lt;uff08&gt;&lt;u5f85&gt;&lt;u63d0&gt;&lt;u53d6&gt;&lt;uff09&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;&lt;u63d0&gt;&lt;u53d6&gt;&lt;space&gt;&lt;u6587&gt;&lt;u4ef6&gt;&lt;space&gt;&lt;u5c5e&gt;&lt;u6027&gt;:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;&lt;u8def&gt;&lt;u5f84&gt;&lt;u8fc7&gt;&lt;u6ee4&gt;:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;&lt;u5141&gt;&lt;u8bb8&gt;&lt;u8def&gt;&lt;u5f84&gt;&lt;u7c7b&gt;&lt;u578b&gt;:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;&lt;u9012&gt;&lt;u5f52&gt;&lt;u5230&gt;&lt;u5b50&gt;&lt;u6587&gt;&lt;u4ef6&gt;&lt;u5939&gt;&lt;uff1a&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="138"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="3263.1140264891023 -1667.8945859226035"
#!   BOUNDING_RECT="3263.1140264891023 -1667.8945859226035 454 71"
#!   ORDER="500000000000072"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="PER_TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="140"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="4044.3718390672275 -1586.1780524781993"
#!   BOUNDING_RECT="4044.3718390672275 -1586.1780524781993 442.00106825772946 71"
#!   ORDER="500000000000073"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Merge Initiator and Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;&lt;u6a21&gt;&lt;u5f0f&gt;&lt;u5c5e&gt;&lt;u6027&gt;,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;&lt;u73af&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u53d1&gt;&lt;u73b0&gt;,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u628a&gt;&lt;u6d4b&gt;&lt;u91cf&gt;&lt;u4f5c&gt;&lt;u4e3a&gt;&lt;u9ad8&gt;&lt;u7a0b&gt;,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;&lt;u4f7f&gt;&lt;u7528&gt;&lt;u641c&gt;&lt;u7d22&gt;&lt;u8303&gt;&lt;u56f4&gt;,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;&lt;u9ad8&gt;&lt;u7ea7&gt;&lt;u7684&gt;,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u4fee&gt;&lt;u526a&gt;&lt;u524d&gt;&lt;u90e8&gt;&lt;u7684&gt;&lt;u7a7a&gt;&lt;u683c&gt;,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;&lt;u62a5&gt;&lt;u544a&gt;&lt;u51e0&gt;&lt;u4f55&gt;&lt;u5bf9&gt;&lt;u8c61&gt;&lt;u5f02&gt;&lt;u5e38&gt;,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;&lt;u5b57&gt;&lt;u7b26&gt;&lt;u7f16&gt;&lt;u7801&gt;,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;&lt;u7a7a&gt;&lt;u767d&gt;&lt;u5b57&gt;&lt;u6bb5&gt;&lt;u8bfb&gt;&lt;u4f5c&gt;&lt;uff1a&gt;,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;&lt;u989d&gt;&lt;u5916&gt;&lt;u7684&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u8981&gt;&lt;u66b4&gt;&lt;u9732&gt;:,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;&lt;u6570&gt;&lt;u503c&gt;&lt;u578b&gt;&lt;u5c5e&gt;&lt;u6027&gt;&lt;u7c7b&gt;&lt;u578b&gt;&lt;u5904&gt;&lt;u7406&gt;"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE="fme_basename,varchar(50) fme_color,varchar(50) fme_dataset,varchar(50) fme_feature_type,varchar(50) fme_fill_color,varchar(50) fme_primary_axis,double fme_rotation,double fme_secondary_axis,double fme_start_angle,double fme_sweep_angle,double fme_text_size,double fme_text_string,varchar(50) fme_type,varchar(50) multi_reader_full_id,long multi_reader_id,long multi_reader_keyword,varchar(50) multi_reader_type,varchar(50) shape_geometry_error,varchar(254) shapefile_type,varchar(30)"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="142"
#!   TYPE="Clipper"
#!   VERSION="16"
#!   POSITION="5134.4990194087986 -1526.1780524781993"
#!   BOUNDING_RECT="5134.4990194087986 -1526.1780524781993 430 71"
#!   ORDER="500000000000074"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="INSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="OUTSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="GHFQMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="KFPQMC" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ADD_NODATA" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BOUNDARY_CASES" PARM_VALUE="INSIDE"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="Automatic"/>
#!     <XFORM_PARM PARM_NAME="CLIPPED_ATTR" PARM_VALUE="_clipped"/>
#!     <XFORM_PARM PARM_NAME="CLIPPERS_FIRST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="CLIPPER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INVALID_PARTS_HANDLING" PARM_VALUE="REJECT_WHOLE"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MISSING_M_MODE" PARM_VALUE="Linear Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MISSING_Z_MODE" PARM_VALUE="Planar Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MULTIPLE_CLIPPERS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="M_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVERLAPPING_CLIPPERS" PARM_VALUE="CLIP_OUTSIDE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_RASTER_EXTENTS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_MODE" PARM_VALUE="CENTER"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Clipper"/>
#!     <XFORM_PARM PARM_NAME="Z_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Z_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="154"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="5755.8715122034937 -1736.935974010903"
#!   BOUNDING_RECT="5755.8715122034937 -1736.935974010903 454 71"
#!   ORDER="500000000000077"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="159"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="6423.8772232683568 -1704.2321586006556"
#!   BOUNDING_RECT="6423.8772232683568 -1704.2321586006556 524.00106825772946 71"
#!   ORDER="500000000000078"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="_area,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="161"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="7136.0936477581836 -1764.2321586006556"
#!   BOUNDING_RECT="7136.0936477581836 -1764.2321586006556 454 71"
#!   ORDER="500000000000079"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="mu" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; mu SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area.sum&lt;closeparen&gt;*0.0015&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="146"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="-1722.4009449396819 328.12828128281285"
#!   BOUNDING_RECT="-1722.4009449396819 328.12828128281285 430 71"
#!   ORDER="500000000000080"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(开发方案shp)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="151"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="-970.21319050399791 215.81301964182438"
#!   BOUNDING_RECT="-970.21319050399791 215.81301964182438 430 71"
#!   ORDER="500000000000081"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="150"
#!   TYPE="Junction"
#!   VERSION="0"
#!   POSITION="903.55081538310731 215.81301964182438"
#!   BOUNDING_RECT="903.55081538310731 215.81301964182438 61 61"
#!   ORDER="500000000000082"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Junction"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="120"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="301.60185322783457 215.81301964182438"
#!   BOUNDING_RECT="301.60185322783457 215.81301964182438 430 71"
#!   ORDER="500000000000083"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="164"
#!   TYPE="SubDocumentTransformer"
#!   VERSION="1"
#!   POSITION="4571.2666428989896 1788.3002916483251"
#!   BOUNDING_RECT="4571.2666428989896 1788.3002916483251 430 71"
#!   ORDER="500000000000000"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="Output"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="SUB_DOC_NAME" PARM_VALUE="mj"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="mj"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="121"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="5417.9320862976056 1366.2927326947688"
#!   BOUNDING_RECT="5417.9320862976056 1366.2927326947688 430 71"
#!   ORDER="500000000000084"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="path_filename,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_6"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="171"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="6124.8097579164296 798.59104563412336"
#!   BOUNDING_RECT="6124.8097579164296 798.59104563412336 454 71"
#!   ORDER="500000000000049"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="方案" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="文件" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="fme_feature_type fme_feature_type  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt; &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt; &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt;  real64 NO_OP &lt;u56fe&gt;&lt;u5c42&gt; &lt;u65b9&gt;&lt;u6848&gt; $(PARAMETER) varchar&lt;openparen&gt;200&lt;closeparen&gt; RENAME_SET_VALUE path_filename &lt;u6587&gt;&lt;u4ef6&gt;  buffer RENAME_SET_VALUE"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager_5"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="174"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="6124.8097579164296 424.31206987131486"
#!   BOUNDING_RECT="6124.8097579164296 424.31206987131486 454 71"
#!   ORDER="500000000000049"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="方案" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="文件" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="fme_feature_type fme_feature_type  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt; &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt; &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt;  real64 NO_OP &lt;u56fe&gt;&lt;u5c42&gt; &lt;u65b9&gt;&lt;u6848&gt; $(PARAMETER) varchar&lt;openparen&gt;200&lt;closeparen&gt; RENAME_SET_VALUE path_filename &lt;u6587&gt;&lt;u4ef6&gt;  buffer RENAME_SET_VALUE"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager_3"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="149"
#!   SOURCE_NODE="53"
#!   TARGET_NODE="146"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="78"
#!   SOURCE_NODE="77"
#!   TARGET_NODE="52"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="108"
#!   SOURCE_NODE="100"
#!   TARGET_NODE="107"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="137"
#!   SOURCE_NODE="100"
#!   TARGET_NODE="132"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="116"
#!   SOURCE_NODE="115"
#!   TARGET_NODE="102"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="32"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="30"
#!   SOURCE_PORT_DESC="fo 0 Area"
#!   TARGET_PORT_DESC="fi 0 CLIPPER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="41"
#!   SOURCE_NODE="30"
#!   TARGET_NODE="69"
#!   SOURCE_PORT_DESC="fo 0 INSIDE"
#!   TARGET_PORT_DESC="fi 0 AREA"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="173"
#!   SOURCE_NODE="40"
#!   TARGET_NODE="171"
#!   SOURCE_PORT_DESC="fo 0 result"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="60"
#!   SOURCE_NODE="58"
#!   TARGET_NODE="135"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 2 result"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="81"
#!   SOURCE_NODE="69"
#!   TARGET_NODE="40"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 &lt;u8f93&gt;&lt;u5165&gt;"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="72"
#!   SOURCE_NODE="69"
#!   TARGET_NODE="71"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="74"
#!   SOURCE_NODE="71"
#!   TARGET_NODE="73"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="76"
#!   SOURCE_NODE="73"
#!   TARGET_NODE="75"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="87"
#!   SOURCE_NODE="75"
#!   TARGET_NODE="86"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="84"
#!   SOURCE_NODE="79"
#!   TARGET_NODE="80"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="130"
#!   SOURCE_NODE="86"
#!   TARGET_NODE="129"
#!   SOURCE_PORT_DESC="fo 0 SUMMARY"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="92"
#!   SOURCE_NODE="88"
#!   TARGET_NODE="89"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="93"
#!   SOURCE_NODE="89"
#!   TARGET_NODE="90"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="125"
#!   SOURCE_NODE="90"
#!   TARGET_NODE="91"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="134"
#!   SOURCE_NODE="91"
#!   TARGET_NODE="133"
#!   SOURCE_PORT_DESC="fo 0 SUMMARY"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="113"
#!   SOURCE_NODE="94"
#!   TARGET_NODE="95"
#!   SOURCE_PORT_DESC="fo 0 INSIDE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="114"
#!   SOURCE_NODE="95"
#!   TARGET_NODE="101"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 AREA"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="177"
#!   SOURCE_NODE="96"
#!   TARGET_NODE="174"
#!   SOURCE_PORT_DESC="fo 0 result"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="99"
#!   SOURCE_NODE="97"
#!   TARGET_NODE="94"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 CANDIDATE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="143"
#!   SOURCE_NODE="97"
#!   TARGET_NODE="142"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 CLIPPER"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="103"
#!   SOURCE_NODE="101"
#!   TARGET_NODE="96"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 &lt;u8f93&gt;&lt;u5165&gt;"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="126"
#!   SOURCE_NODE="101"
#!   TARGET_NODE="88"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="148"
#!   SOURCE_NODE="101"
#!   TARGET_NODE="147"
#!   SOURCE_PORT_DESC="fo 0 AREA"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="2"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="112"
#!   SOURCE_NODE="110"
#!   TARGET_NODE="94"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 CLIPPER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="172"
#!   SOURCE_NODE="118"
#!   TARGET_NODE="164"
#!   SOURCE_PORT_DESC="fo 0 INSIDE"
#!   TARGET_PORT_DESC="fi 0 Input"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="169"
#!   SOURCE_NODE="120"
#!   TARGET_NODE="150"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="170"
#!   SOURCE_NODE="121"
#!   TARGET_NODE="58"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="136"
#!   SOURCE_NODE="133"
#!   TARGET_NODE="131"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="141"
#!   SOURCE_NODE="138"
#!   TARGET_NODE="140"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="158"
#!   SOURCE_NODE="142"
#!   TARGET_NODE="154"
#!   SOURCE_PORT_DESC="fo 0 INSIDE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="155"
#!   SOURCE_NODE="147"
#!   TARGET_NODE="152"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="165"
#!   SOURCE_NODE="150"
#!   TARGET_NODE="30"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 1 CANDIDATE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="166"
#!   SOURCE_NODE="150"
#!   TARGET_NODE="97"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="167"
#!   SOURCE_NODE="150"
#!   TARGET_NODE="118"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 1 CANDIDATE"
#!   ENABLED="true"
#!   EXECUTION_IDX="2"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="168"
#!   SOURCE_NODE="150"
#!   TARGET_NODE="123"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 &lt;u8f93&gt;&lt;u5165&gt;"
#!   ENABLED="true"
#!   EXECUTION_IDX="3"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="163"
#!   SOURCE_NODE="151"
#!   TARGET_NODE="63"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="157"
#!   SOURCE_NODE="152"
#!   TARGET_NODE="156"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="160"
#!   SOURCE_NODE="154"
#!   TARGET_NODE="159"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="162"
#!   SOURCE_NODE="159"
#!   TARGET_NODE="161"
#!   SOURCE_PORT_DESC="fo 0 SUMMARY"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="122"
#!   SOURCE_NODE="164"
#!   TARGET_NODE="121"
#!   SOURCE_PORT_DESC="fo 0 Output"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="175"
#!   SOURCE_NODE="171"
#!   TARGET_NODE="135"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 1 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="178"
#!   SOURCE_NODE="174"
#!   TARGET_NODE="135"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 &lt;lt&gt;u4ea9&lt;gt&gt;"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="83"
#!   SOURCE_NODE="52"
#!   TARGET_NODE="79"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="124"
#!   SOURCE_NODE="63"
#!   TARGET_NODE="120"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="82"
#!   SOURCE_NODE="80"
#!   TARGET_NODE="20"
#!   SOURCE_PORT_DESC="fo 1 FAILED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="119"
#!   SOURCE_NODE="102"
#!   TARGET_NODE="118"
#!   SOURCE_PORT_DESC="fo 1 Export_Output"
#!   TARGET_PORT_DESC="fi 0 CLIPPER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS="3608 1914"
#! />
#! <FEAT_LINK
#!   IDENTIFIER="111"
#!   SOURCE_NODE="107"
#!   TARGET_NODE="110"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;u9879&lt;gt&gt;&lt;lt&gt;u76ee&lt;gt&gt;&lt;lt&gt;u533a&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u622a&lt;gt&gt;&lt;lt&gt;u81f3&lt;gt&gt;2023&lt;lt&gt;uff09&lt;gt&gt;_GD"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="139"
#!   SOURCE_NODE="132"
#!   TARGET_NODE="138"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="144"
#!   SOURCE_NODE="140"
#!   TARGET_NODE="142"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 1 CANDIDATE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="153"
#!   SOURCE_NODE="146"
#!   TARGET_NODE="151"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! <SUBDOCUMENT
#!   NAME="mj"
#!   ARCGIS_COMPATIBILITY=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DOC_DYNAMIC_INPUT_ATTRS="0"
#!   DOC_EXTENTS="7174.42 590.38"
#!   DOC_TOP_LEFT="-1876.27 -761.444"
#!   FME_DOCUMENT_GUID=""
#!   FME_DOCUMENT_PRIORGUID=""
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_PROCESS_COUNT="NO_PARALLELISM"
#!   FME_PROCESS_GROUPS_ORDERED="No"
#!   FME_PROCESS_GROUP_BY=""
#!   FME_PROCESS_PRESERVE_GROUP_ATTR="No"
#!   FMX_ATTRIBUTE_PROPOGATION_MODE="AUTO"
#!   HISTORY=""
#!   IS_VISIBLE="true"
#!   LAST_SAVE_BUILD=""
#!   LAST_SAVE_DATE=""
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   PYTHON_COMPATIBILITY=""
#!   REPLACED_BY=""
#!   SUPPRESS_UPGRADE="false"
#!   TITLE="mj"
#!   USAGE=""
#!   USE_MARKDOWN="NO"
#!   VIEW_POSITION="-34.3753 112.501"
#!   XFORM_DEPRECATED="No"
#!   ZOOM_SCALE="100"
#! >
#! <GLOBAL_PARAMETERS>
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbXX0="
#! >
#! <PARAMETER_INFO>
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="4"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="-319.18898974054446 -180.43877531136758"
#!   BOUNDING_RECT="-319.18898974054446 -180.43877531136758 454 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="368.31788532820633 -180.43877531136758"
#!   BOUNDING_RECT="368.31788532820633 -180.43877531136758 454 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="_area _area  real64 NO_OP  &lt;u4ea9&gt; &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0015&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO  &lt;u516c&gt;&lt;u9877&gt; &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0001&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="6"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="-909.81989604960756 -171.0646815704302"
#!   BOUNDING_RECT="-909.81989604960756 -171.0646815704302 454 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="CGCS2000/GK3d-40_FME"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="1802.7072292216444 -352.31549407855522"
#!   BOUNDING_RECT="1802.7072292216444 -352.31549407855522 484.00106825772946 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="&lt;u4ea9&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,;&lt;u516c&gt;&lt;u9877&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="8"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="2499.5891980413335 -352.31549407855522"
#!   BOUNDING_RECT="2499.5891980413335 -352.31549407855522 454 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="fme_feature_type,,path_filename,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="3396.4731668810214 -412.31549407855522"
#!   BOUNDING_RECT="3396.4731668810214 -412.31549407855522 454 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="_area _area  real64 REMOVE &lt;u4ea9&gt; &lt;u4ea9&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u516c&gt;&lt;u9877&gt; &lt;u516c&gt;&lt;u9877&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u4ea9&gt;.sum &lt;u4ea9&gt;.sum  real64 NO_OP &lt;u516c&gt;&lt;u9877&gt;.sum &lt;u516c&gt;&lt;u9877&gt;.sum  real64 NO_OP fme_feature_type fme_feature_type  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP  &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt; &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u4ea9&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO  &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt; &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u516c&gt;&lt;u9877&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt; real64 SET_TO  &lt;u56fe&gt;&lt;u5c42&gt; &lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="10"
#!   TYPE="AttributeRemover"
#!   VERSION="1"
#!   POSITION="4090.230104450397 -630.4442753663684"
#!   BOUNDING_RECT="4090.230104450397 -630.4442753663684 454 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_ATTRS" PARM_VALUE="&lt;u4ea9&gt;,&lt;u4ea9&gt;.sum,&lt;u516c&gt;&lt;u9877&gt;,&lt;u516c&gt;&lt;u9877&gt;.sum"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeRemover"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="19"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="392.31788532820633 -587.50587505875046"
#!   BOUNDING_RECT="392.31788532820633 -587.50587505875046 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u4ea9&gt; SET_TO 0 uint64  &lt;u516c&gt;&lt;u9877&gt; SET_TO 0 uint64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="20"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="-178.1267812678127 -587.50587505875046"
#!   BOUNDING_RECT="-178.1267812678127 -587.50587505875046 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="23"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="-1343.7634376343767 -353.12853128531287"
#!   BOUNDING_RECT="-1343.7634376343767 -353.12853128531287 430 71"
#!   ORDER="500000000000012"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="path_filename,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer_2"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <SUBDOCUMENT_IOS>
#! <SUBDOCUMENT_IO
#!   IDENTIFIER="2"
#!   NAME="Input"
#!   POSITION="-1876.274062740627 -180.43877531136758"
#!   BOUNDING_RECT="-1876.274062740627 -180.43877531136758 430 71"
#!   COLLAPSED="true"
#!   PUBLISHED="true"
#!   IS_SOURCE="true"
#!   ATTR_MODE="0"
#! >
#! </SUBDOCUMENT_IO>
#! <SUBDOCUMENT_IO
#!   IDENTIFIER="3"
#!   NAME="Output"
#!   POSITION="4868.1477814778154 -690.4442753663684"
#!   BOUNDING_RECT="4868.1477814778154 -690.4442753663684 430 71"
#!   COLLAPSED="true"
#!   IS_SOURCE="false"
#!   ATTR_MODE="0"
#! >
#! </SUBDOCUMENT_IO>
#! </SUBDOCUMENT_IOS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="24"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="23"
#!   SOURCE_PORT_DESC="0"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="21"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="19"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="4"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="12"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="13"
#!   SOURCE_NODE="6"
#!   TARGET_NODE="4"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="14"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="8"
#!   SOURCE_PORT_DESC="fo 0 SUMMARY"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="8"
#!   TARGET_NODE="9"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="16"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="10"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="18"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="0"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="22"
#!   SOURCE_NODE="19"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="23"
#!   TARGET_NODE="6"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! </SUBDOCUMENT>
#! <SUBDOCUMENT
#!   NAME="面积亩"
#!   ARCGIS_COMPATIBILITY=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DOC_DYNAMIC_INPUT_ATTRS="0"
#!   DOC_EXTENTS="6534.79 508.005"
#!   DOC_TOP_LEFT="-664.762 -651.131"
#!   FME_DOCUMENT_GUID=""
#!   FME_DOCUMENT_PRIORGUID=""
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_PROCESS_COUNT="NO_PARALLELISM"
#!   FME_PROCESS_GROUPS_ORDERED="No"
#!   FME_PROCESS_GROUP_BY=""
#!   FME_PROCESS_PRESERVE_GROUP_ATTR="No"
#!   FMX_ATTRIBUTE_PROPOGATION_MODE="AUTO"
#!   HISTORY=""
#!   IS_VISIBLE="true"
#!   LAST_SAVE_BUILD=""
#!   LAST_SAVE_DATE=""
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   PYTHON_COMPATIBILITY=""
#!   REPLACED_BY=""
#!   SUPPRESS_UPGRADE="false"
#!   TITLE="面积亩"
#!   USAGE=""
#!   USE_MARKDOWN="NO"
#!   VIEW_POSITION="131.251 137.501"
#!   XFORM_DEPRECATED="No"
#!   ZOOM_SCALE="100"
#! >
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI TEXT_EDIT_OR_ATTR PATH_FILENAME FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES Path Filename:"
#!   DEFAULT_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_filename&lt;closeparen&gt;"
#!   IS_STAND_ALONE="false"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJkZWZhdWx0VmFsdWUiOiI8YXQ+VmFsdWU8b3BlbnBhcmVuPnBhdGhfZmlsZW5hbWU8Y2xvc2VwYXJlbj4iLCJlZGl0b3IiOiJwbGFpbnRleHQiLCJuYW1lIjoiUEFUSF9GSUxFTkFNRSIsInByb21wdCI6IlBhdGggRmlsZW5hbWU6IiwicmVxdWlyZWQiOnRydWUsInNob3dBcml0aG1ldGljRWRpdG9yIjp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6InRleHQiLCJ2YWx1ZVR5cGUiOiJzdHJpbmdFbmNvZGVkIn1dfQ=="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="PATH_FILENAME" 
#!   DEFAULT_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_filename&lt;closeparen&gt;"
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI TEXT_EDIT_OR_ATTR PATH_FILENAME FME_SYNTAX%FME%FME_SUPPORTSNUMERIC%YES Path Filename:"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="987.50987509875085 -203.12603126031257"
#!   BOUNDING_RECT="987.50987509875085 -203.12603126031257 454 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="1675.0167501675016 -203.12603126031257"
#!   BOUNDING_RECT="1675.0167501675016 -203.12603126031257 454 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="_area _area  real64 NO_OP  &lt;u4ea9&gt; &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0015&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO  &lt;u516c&gt;&lt;u9877&gt; &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area&lt;closeparen&gt;*0.0001&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="396.87896878968775 -193.75193751937519"
#!   BOUNDING_RECT="396.87896878968775 -193.75193751937519 454 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="CGCS2000/GK3d-40_FME"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="2493.7749377493769 -203.12603126031257"
#!   BOUNDING_RECT="2493.7749377493769 -203.12603126031257 484.00106825772946 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="&lt;u4ea9&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,;&lt;u516c&gt;&lt;u9877&gt;,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="14"
#!   TYPE="AttributeExposer"
#!   VERSION="2"
#!   POSITION="3190.656906569066 -203.12603126031257"
#!   BOUNDING_RECT="3190.656906569066 -203.12603126031257 454 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTR_LIST_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="fme_feature_type,"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeExposer"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="17"
#!   TYPE="AttributeManager"
#!   VERSION="5"
#!   POSITION="4440.6694066940672 -143.12603126031257"
#!   BOUNDING_RECT="4440.6694066940672 -143.12603126031257 454 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="_area _area  real64 REMOVE &lt;u4ea9&gt; &lt;u4ea9&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u516c&gt;&lt;u9877&gt; &lt;u516c&gt;&lt;u9877&gt;  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP &lt;u4ea9&gt;.sum &lt;u4ea9&gt;.sum  real64 NO_OP &lt;u516c&gt;&lt;u9877&gt;.sum &lt;u516c&gt;&lt;u9877&gt;.sum  real64 NO_OP fme_feature_type fme_feature_type  varchar&lt;openparen&gt;200&lt;closeparen&gt; NO_OP  &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt; &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u4ea9&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO  &lt;u5360&gt;&lt;u7528&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u516c&gt;&lt;u9877&gt;&lt;uff09&gt; &lt;at&gt;Format&lt;openparen&gt;%.4f&lt;comma&gt;&lt;at&gt;Value&lt;openparen&gt;&lt;u516c&gt;&lt;u9877&gt;.sum&lt;closeparen&gt;&lt;closeparen&gt; real64 SET_TO  &lt;u56fe&gt;&lt;u5c42&gt; &lt;at&gt;Value&lt;openparen&gt;fme_feature_type&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt; SET_TO"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_EXPOSED_ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeManager_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="19"
#!   TYPE="AttributeRemover"
#!   VERSION="1"
#!   POSITION="4781.2978129781295 -481.25481254812564"
#!   BOUNDING_RECT="4781.2978129781295 -481.25481254812564 454 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用面积（公顷）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="图层" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="REMOVE_ATTRS" PARM_VALUE="&lt;u4ea9&gt;,&lt;u4ea9&gt;.sum,&lt;u516c&gt;&lt;u9877&gt;,&lt;u516c&gt;&lt;u9877&gt;.sum"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeRemover"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="18"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="1742.116601926059 -580.13104992500121"
#!   BOUNDING_RECT="1742.116601926059 -580.13104992500121 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="亩" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="公顷" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u4ea9&gt; SET_TO 0 uint64  &lt;u516c&gt;&lt;u9877&gt; SET_TO 0 uint64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="20"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="1171.6719353300402 -580.13104992500121"
#!   BOUNDING_RECT="1171.6719353300402 -580.13104992500121 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <SUBDOCUMENT_IOS>
#! <SUBDOCUMENT_IO
#!   IDENTIFIER="2"
#!   NAME="输入"
#!   POSITION="-664.76218762187602 -253.75193751937519"
#!   BOUNDING_RECT="-664.76218762187602 -253.75193751937519 454 71"
#!   COLLAPSED="true"
#!   PUBLISHED="true"
#!   IS_SOURCE="true"
#!   ATTR_MODE="0"
#! >
#!     <XFORM_ATTR ATTR_NAME="path_filename" ATTR_TYPE="buffer" ATTR_VALUE="$(PATH_FILENAME)" />
#! </SUBDOCUMENT_IO>
#! <SUBDOCUMENT_IO
#!   IDENTIFIER="3"
#!   NAME="result"
#!   POSITION="5416.0285002850032 -203.12603126031257"
#!   BOUNDING_RECT="5416.0285002850032 -203.12603126031257 454 71"
#!   COLLAPSED="true"
#!   IS_SOURCE="false"
#!   ATTR_MODE="0"
#! >
#! </SUBDOCUMENT_IO>
#! </SUBDOCUMENT_IOS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="10"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="9"
#!   SOURCE_PORT_DESC="0"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="24"
#!   SOURCE_NODE="20"
#!   TARGET_NODE="18"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="8"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="13"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="11"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="14"
#!   SOURCE_PORT_DESC="fo 0 SUMMARY"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="21"
#!   SOURCE_NODE="14"
#!   TARGET_NODE="17"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="22"
#!   SOURCE_NODE="17"
#!   TARGET_NODE="19"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="25"
#!   SOURCE_NODE="18"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="23"
#!   SOURCE_NODE="19"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="0"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! </SUBDOCUMENT>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO PARAMETER 陆上风电项目

DEFAULT_MACRO 开发方案shp $(FME_MF_DIR)2024\12\1211\苏州市陆上风电项目清单

DEFAULT_MACRO 两区gdb E:\YC\数据\两区\调整前\LQDK.gdb

DEFAULT_MACRO file E:\YC\数据\两区\调整后\合并\Export_Output.shp

DEFAULT_MACRO 项目区图斑shp E:\YC\数据\最新项目区内高标准农田\项目区内高标准农田（截至2023）\GD\项目区（截至2023）_GD.shp

DEFAULT_MACRO 输出结果目录 $(FME_MF_DIR)2024\12\1211\陆上风电项目占用高标准农田

DEFAULT_MACRO FME_SHAREDRESOURCE_DATA C:\ProgramData\Safe Software\FMEFlow\resources\data

# ============================================================================
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(开发方案shp$encode)} == {}} { puts_real {Parameter '开发方案shp' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(两区gdb$encode)} == {}} { puts_real {Parameter '两区gdb' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(file$encode)} == {}} { puts_real {Parameter 'file' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(项目区图斑shp$encode)} == {}} { puts_real {Parameter '项目区图斑shp' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(输出结果目录$encode)} == {}} { puts_real {Parameter '输出结果目录' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)成片开发方案核查.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 成片开发方案核查
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_2_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_2_XML     NOT_ACTIVATED
MACRO Creator_2_CLASSIC NOT_ACTIVATED
MACRO Creator_2_2D3D    2D_GEOMETRY
MACRO Creator_2_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_2_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2_2D3D 3D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_2: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_2_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
FACTORY_DEF {$(Creator_2_XML)} CreationFactory    FACTORY_NAME { Creator_2_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_2_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_2_CLASSIC_Creator }    $(Creator_2_2D3D) { $(Creator_2_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_2_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_2_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_2_CREATED        fme_feature_type Creator_2         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_2_CREATED Brancher -1 149"   INPUT FEATURE_TYPE Creator_2_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_4_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_4_XML     NOT_ACTIVATED
MACRO Creator_4_CLASSIC NOT_ACTIVATED
MACRO Creator_4_2D3D    2D_GEOMETRY
MACRO Creator_4_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_4_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_4_2D3D 2D_GEOMETRY};            puts {MACRO Creator_4_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_4_2D3D 3D_GEOMETRY};            puts {MACRO Creator_4_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_4: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_4_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_4_2D3D 2D_GEOMETRY};            puts {MACRO Creator_4_CLASSIC *} } ]
FACTORY_DEF {$(Creator_4_XML)} CreationFactory    FACTORY_NAME { Creator_4_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_4_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_4_CLASSIC_Creator }    $(Creator_4_2D3D) { $(Creator_4_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_4_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_4_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_4_CREATED        fme_feature_type Creator_4         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_4_CREATED Brancher -1 78"   INPUT FEATURE_TYPE Creator_4_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_5_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_5_XML     NOT_ACTIVATED
MACRO Creator_5_CLASSIC NOT_ACTIVATED
MACRO Creator_5_2D3D    2D_GEOMETRY
MACRO Creator_5_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_5_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_5_2D3D 2D_GEOMETRY};            puts {MACRO Creator_5_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_5_2D3D 3D_GEOMETRY};            puts {MACRO Creator_5_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_5: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_5_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_5_2D3D 2D_GEOMETRY};            puts {MACRO Creator_5_CLASSIC *} } ]
FACTORY_DEF {$(Creator_5_XML)} CreationFactory    FACTORY_NAME { Creator_5_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_5_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_5_CLASSIC_Creator }    $(Creator_5_2D3D) { $(Creator_5_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_5_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_5_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_5_CREATED        fme_feature_type Creator_5         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Creator_5 CREATED Splitter"   INPUT FEATURE_TYPE Creator_5_CREATED   OUTPUT FEATURE_TYPE Creator_5_CREATED_0_UJgJHijWoAA=   OUTPUT FEATURE_TYPE Creator_5_CREATED_1_oQR6Jbla0Tw=
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_5_CREATED_0_UJgJHijWoAA= Brancher -1 108"   INPUT FEATURE_TYPE Creator_5_CREATED_0_UJgJHijWoAA=   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_5_CREATED_1_oQR6Jbla0Tw= Brancher -1 137"   INPUT FEATURE_TYPE Creator_5_CREATED_1_oQR6Jbla0Tw=   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_6_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_6_XML     NOT_ACTIVATED
MACRO Creator_6_CLASSIC NOT_ACTIVATED
MACRO Creator_6_2D3D    2D_GEOMETRY
MACRO Creator_6_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_6_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_6_2D3D 2D_GEOMETRY};            puts {MACRO Creator_6_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_6_2D3D 3D_GEOMETRY};            puts {MACRO Creator_6_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_6: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_6_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_6_2D3D 2D_GEOMETRY};            puts {MACRO Creator_6_CLASSIC *} } ]
FACTORY_DEF {$(Creator_6_XML)} CreationFactory    FACTORY_NAME { Creator_6_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_6_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_6_CLASSIC_Creator }    $(Creator_6_2D3D) { $(Creator_6_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_6_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_6_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_6_CREATED        fme_feature_type Creator_6         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_6_CREATED Brancher -1 116"   INPUT FEATURE_TYPE Creator_6_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED Export_Output
MACRO FeatureReader_2_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(file$encode),FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Creator_6_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "Export_Output" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT Export_Output FEATURE_TYPE FeatureReader_2_Export_Output
# -------------------------------------------------------------------------
MACRO FeatureReader_8_OUTPUT_PORTS_ENCODED <u9879><u76ee><u533a><uff08><u622a><u81f3>2023<uff09>_GD
MACRO FeatureReader_8_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,FME_DISCLOSURE_OPEN,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,fme_basename<comma>varchar<openparen>50<closeparen><space>fme_color<comma>varchar<openparen>50<closeparen><space>fme_dataset<comma>varchar<openparen>50<closeparen><space>fme_feature_type<comma>varchar<openparen>50<closeparen><space>fme_fill_color<comma>varchar<openparen>50<closeparen><space>fme_primary_axis<comma>double<space>fme_rotation<comma>double<space>fme_secondary_axis<comma>double<space>fme_start_angle<comma>double<space>fme_sweep_angle<comma>double<space>fme_text_size<comma>double<space>fme_text_string<comma>varchar<openparen>50<closeparen><space>fme_type<comma>varchar<openparen>50<closeparen><space>multi_reader_full_id<comma>long<space>multi_reader_id<comma>long<space>multi_reader_keyword<comma>varchar<openparen>50<closeparen><space>multi_reader_type<comma>varchar<openparen>50<closeparen><space>shape_geometry_error<comma>varchar<openparen>254<closeparen><space>shapefile_type<comma>varchar<openparen>30<closeparen>,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_8_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_8_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_8_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_8 @EvaluateExpression(FDIV,STRING_ENCODED,$(项目区图斑shp$encode),FeatureReader_8)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_8 }    INPUT  FEATURE_TYPE Creator_5_CREATED_0_UJgJHijWoAA=    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_8_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_8)" }    QUERYFCT_IDS        { "<u9879><u76ee><u533a><uff08><u622a><u81f3>2023<uff09>_GD" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_8_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_8_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT <u9879><u76ee><u533a><uff08><u622a><u81f3>2023<uff09>_GD FEATURE_TYPE FeatureReader_8_<u9879><u76ee><u533a><uff08><u622a><u81f3>2023<uff09>_GD
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer_4 }    INPUT  FEATURE_TYPE FeatureReader_8_<u9879><u76ee><u533a><uff08><u622a><u81f3>2023<uff09>_GD    OUTPUT { FEATURE_TYPE AttributeExposer_4_OUTPUT          }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(FME_MF_DIR_ENCODED)2024<backslash>4<backslash>0425<backslash><u9879><u76ee><u533a><backslash><u9879><u76ee><u6309><u5e74><u4efd><backslash>22-23<uff08><u5f85><u63d0><u53d6><uff09>,FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_5_CREATED_1_oQR6Jbla0Tw=    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester_2)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_3_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,FME_DISCLOSURE_OPEN,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,fme_basename<comma>varchar<openparen>50<closeparen><space>fme_color<comma>varchar<openparen>50<closeparen><space>fme_dataset<comma>varchar<openparen>50<closeparen><space>fme_feature_type<comma>varchar<openparen>50<closeparen><space>fme_fill_color<comma>varchar<openparen>50<closeparen><space>fme_primary_axis<comma>double<space>fme_rotation<comma>double<space>fme_secondary_axis<comma>double<space>fme_start_angle<comma>double<space>fme_sweep_angle<comma>double<space>fme_text_size<comma>double<space>fme_text_string<comma>varchar<openparen>50<closeparen><space>fme_type<comma>varchar<openparen>50<closeparen><space>multi_reader_full_id<comma>long<space>multi_reader_id<comma>long<space>multi_reader_keyword<comma>varchar<openparen>50<closeparen><space>multi_reader_type<comma>varchar<openparen>50<closeparen><space>shape_geometry_error<comma>varchar<openparen>254<closeparen><space>shapefile_type<comma>varchar<openparen>30<closeparen>,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE Tester_2_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_3_<OTHER>           }
# -------------------------------------------------------------------------
MACRO FeatureReader_5_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_5_DIRECTIVES ADVANCED,FME_DISCLOSURE_OPEN,BEGIN_SQL,,END_SQL,,EXPOSE_ATTRS_GROUP,FME_DISCLOSURE_OPEN,FILEGDB_EXPOSE_FORMAT_ATTRS,fme_basename<comma>text<openparen>50<closeparen><space>fme_color<comma>text<openparen>50<closeparen><space>fme_dataset<comma>text<openparen>50<closeparen><space>fme_feature_type<comma>text<openparen>50<closeparen><space>fme_fill_color<comma>text<openparen>50<closeparen><space>fme_primary_axis<comma>double<space>fme_rotation<comma>double<space>fme_secondary_axis<comma>double<space>fme_start_angle<comma>double<space>fme_sweep_angle<comma>double<space>fme_text_size<comma>double<space>fme_text_string<comma>text<openparen>50<closeparen><space>fme_type<comma>text<openparen>50<closeparen><space>multi_reader_full_id<comma>int<space>multi_reader_id<comma>int<space>multi_reader_keyword<comma>text<openparen>50<closeparen><space>multi_reader_type<comma>text<openparen>50<closeparen>,GEOMETRY,,NETWORK_AUTHENTICATION,,QUERY_FEATURE_TYPES_FOR_MERGE_FILTERS,Yes,REMOVE_FEATURE_DATASET,NO,SIMPLE_DONUT_GEOMETRY,simple,STRIP_GUID_GLOBALID_BRACES,no,TABLELIST,,USE_SEARCH_ENVELOPE,NO
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_5_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_5_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_5_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_5 @EvaluateExpression(FDIV,STRING_ENCODED,$(两区gdb$encode),FeatureReader_5)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_5 }    INPUT  FEATURE_TYPE Creator_4_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_5_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { FILEGDB  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_5)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,FILEGDB }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_5_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_5_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_5_<OTHER>           }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer }    INPUT  FEATURE_TYPE FeatureReader_5_<OTHER>    OUTPUT { FEATURE_TYPE AttributeExposer_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_3 }    INPUT  FEATURE_TYPE AttributeExposer_OUTPUT    CASE_INSENSITIVE_TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>fme_featura_type<closeparen>,Tester_3)" CONTAINS <u82cf><u5dde><u5e02> ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { FAILED FEATURE_TYPE Tester_3_FAILED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} GeometryFilterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { GeometryFilter }    INPUT  FEATURE_TYPE Tester_3_FAILED    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    FILTER_TYPES { Area }    INSTANCES { INSTANTIATE }    BREAK_AGG { Yes }    OUTPUT { <UNFILTERED> FEATURE_TYPE GeometryFilter_<UNFILTERED>  }    OUTPUT { Area FEATURE_TYPE GeometryFilter_Area  }
FACTORY_DEF * TeeFactory   FACTORY_NAME "GeometryFilter <UNFILTERED> Transformer Output Nuker"   INPUT FEATURE_TYPE GeometryFilter_<UNFILTERED>
# -------------------------------------------------------------------------
MACRO FeatureReader_6_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_6_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_6_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_6_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_6_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_6 @EvaluateExpression(FDIV,STRING_ENCODED,$(开发方案shp$encode),FeatureReader_6)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_6 }    INPUT  FEATURE_TYPE Creator_2_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_6_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_6)" }    QUERYFCT_IDS        { "PATH" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_6_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_6_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT PATH FEATURE_TYPE FeatureReader_6_PATH
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_4 }    INPUT  FEATURE_TYPE FeatureReader_6_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester_4)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_4_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_4_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_4_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,FME_DISCLOSURE_OPEN,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,fme_basename<comma>varchar<openparen>50<closeparen><space>fme_color<comma>varchar<openparen>50<closeparen><space>fme_dataset<comma>varchar<openparen>50<closeparen><space>fme_feature_type<comma>varchar<openparen>50<closeparen><space>fme_fill_color<comma>varchar<openparen>50<closeparen><space>fme_primary_axis<comma>double<space>fme_rotation<comma>double<space>fme_secondary_axis<comma>double<space>fme_start_angle<comma>double<space>fme_sweep_angle<comma>double<space>fme_text_size<comma>double<space>fme_text_string<comma>varchar<openparen>50<closeparen><space>fme_type<comma>varchar<openparen>50<closeparen><space>multi_reader_full_id<comma>long<space>multi_reader_id<comma>long<space>multi_reader_keyword<comma>varchar<openparen>50<closeparen><space>multi_reader_type<comma>varchar<openparen>50<closeparen><space>shape_geometry_error<comma>varchar<openparen>254<closeparen><space>shapefile_type<comma>varchar<openparen>30<closeparen>,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_4_COMBINE_ATTRS
INCLUDE [       if { {MERGE} == {MERGE} } {          puts "MACRO FeatureReader_4_COMBINE_ATTRS PREFER_RESULT";       } else {          puts "MACRO FeatureReader_4_COMBINE_ATTRS MERGE";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_4 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_4)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_4 }    INPUT  FEATURE_TYPE Tester_4_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_4_COMBINE_ATTRS) }    IGNORE_NULLS        { No }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_4)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_4_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_4_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_4_<OTHER>           }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE FeatureReader_4_<OTHER>    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Junction }    INPUT  FEATURE_TYPE Reprojector_REPROJECTED    OUTPUT { FEATURE_TYPE Junction_Output         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "Junction Output Splitter"   INPUT FEATURE_TYPE Junction_Output   OUTPUT FEATURE_TYPE Junction_Output_0_sH9l3UTsGrw=   OUTPUT FEATURE_TYPE Junction_Output_1_/QQSbUuR6ZI=   OUTPUT FEATURE_TYPE Junction_Output_2_VzwNptf57aI=   OUTPUT FEATURE_TYPE Junction_Output_3_Es4q/I3oBKE=
# -------------------------------------------------------------------------
FACTORY_DEF {*} ClipperFactory    FACTORY_NAME { Clipper_3 }    INPUT CLIPPER FEATURE_TYPE GeometryFilter_Area    INPUT CANDIDATE FEATURE_TYPE Junction_Output_0_sH9l3UTsGrw=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    MULTIPLE_CLIPPERS { YES }    CLIPPERS_FIRST { NO }    OVERLAPPING_CLIPPERS { CLIP_OUTSIDE }    INVALID_PARTS_HANDLING { REJECT_WHOLE }    ADD_NODATA { YES }    Z_VAL_SOURCE { CANDIDATE }    MISSING_Z_MODE { PLANAR }    CONNECT_Z_MODE { <Unused> }    M_VAL_SOURCE { CANDIDATE }    MISSING_M_MODE { LINEAR }    CLEANING_TOLERANCE { AUTO }    CANDIDATE_ON_BOUNDARY { INSIDE }    PRESERVE_RASTER_EXTENTS { NO }    RASTER_CELL_MODE { CENTER }    CLIPPED_INDICATOR_ATTR { "_clipped" }    MERGE_CLIPPER_ATTRIBUTES { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    CLIPPER_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { INSIDE FEATURE_TYPE Clipper_3_INSIDE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { AreaOnAreaOverlayer_2 }    INPUT POLYGON FEATURE_TYPE Clipper_3_INSIDE    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    ACCUMULATE_ATTRIBUTES { ONE }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    CONNECT_Z_MODE { FIRST_WINS }    DEAGGREGATE_INPUT { Yes }    OVERLAP_COUNT_ATTRIBUTE { _overlaps }    MODE COMPLETE    OVERLAY_TYPE AREA_ON_AREA    CLEANING_TOLERANCE { AUTO }    OUTPUT_REMNANTS    SELF_INTERSECTION NO    OUTPUT { POLYGON  FEATURE_TYPE AreaOnAreaOverlayer_2_AREA         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AreaOnAreaOverlayer_2 AREA Splitter"   INPUT FEATURE_TYPE AreaOnAreaOverlayer_2_AREA   OUTPUT FEATURE_TYPE AreaOnAreaOverlayer_2_AREA_0_89l04jsGBc0=   OUTPUT FEATURE_TYPE AreaOnAreaOverlayer_2_AREA_1_Gtq/LTkZdl8=
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer_2 }    INPUT  FEATURE_TYPE AreaOnAreaOverlayer_2_AREA_1_Gtq/LTkZdl8=    OUTPUT { FEATURE_TYPE AttributeExposer_2_OUTPUT          }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE AttributeExposer_2_OUTPUT    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $(AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AreaCalculator_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4ea9>" "SET_TO" "<at>Format<openparen>%.4f<comma><at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0015<closeparen><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u516c><u9877>" "SET_TO" "<at>Format<openparen>%.4f<comma><at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0001<closeparen><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    GROUP_BY { KFPQMC }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { <u516c><u9877>,NUMERIC_MODE,,,,SUM,,,,,,,,,;<u4ea9>,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { <u4ea9>,varchar<openparen>200<closeparen>,<u516c><u9877>,varchar<openparen>200<closeparen>,path_unix,buffer,path_windows,buffer,path_rootname,buffer,path_filename,buffer,path_extension,buffer,path_directory_unix,buffer,path_directory_windows,buffer,path_type,varchar<openparen>10<closeparen>,fme_geometry<opencurly>0<closecurly>,fme_no_geom,_clipped,char<openparen>3<closeparen>,_overlaps,uint32,KFPQMC,varchar<openparen>200<closeparen>,_area,real64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE StatisticsCalculator_SUMMARY        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_3 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_SUMMARY    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u62a5><u544a>" "SET_TO" "<at>Value<openparen>KFPQMC<closeparen><u6d89><u53ca><u5360><u7528><u9762><u79ef><at>Format<openparen>%.4f<comma><at>Value<openparen><u516c><u9877>.sum<closeparen><closeparen><u516c><u9877><uff08><at>Format<openparen>%.4f<comma><at>Value<openparen><u4ea9>.sum<closeparen><closeparen><uff09><u4ea9>" "varchar<openparen>200<closeparen>" }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer_3 }    INPUT  FEATURE_TYPE Junction_Output_1_/QQSbUuR6ZI=    OUTPUT { FEATURE_TYPE AttributeExposer_3_OUTPUT          }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AttributeExposer_3 OUTPUT Splitter"   INPUT FEATURE_TYPE AttributeExposer_3_OUTPUT   OUTPUT FEATURE_TYPE AttributeExposer_3_OUTPUT_0_SPTf0BWC8SY=   OUTPUT FEATURE_TYPE AttributeExposer_3_OUTPUT_1_IVBOgCe3lxI=
# -------------------------------------------------------------------------
FACTORY_DEF {*} ClipperFactory    FACTORY_NAME { Clipper_4 }    INPUT CLIPPER FEATURE_TYPE AttributeExposer_4_OUTPUT    INPUT CANDIDATE FEATURE_TYPE AttributeExposer_3_OUTPUT_0_SPTf0BWC8SY=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    MULTIPLE_CLIPPERS { YES }    CLIPPERS_FIRST { NO }    OVERLAPPING_CLIPPERS { CLIP_OUTSIDE }    INVALID_PARTS_HANDLING { REJECT_WHOLE }    ADD_NODATA { YES }    Z_VAL_SOURCE { CANDIDATE }    MISSING_Z_MODE { PLANAR }    CONNECT_Z_MODE { <Unused> }    M_VAL_SOURCE { CANDIDATE }    MISSING_M_MODE { LINEAR }    CLEANING_TOLERANCE { AUTO }    CANDIDATE_ON_BOUNDARY { INSIDE }    PRESERVE_RASTER_EXTENTS { NO }    RASTER_CELL_MODE { CENTER }    CLIPPED_INDICATOR_ATTR { "_clipped" }    MERGE_CLIPPER_ATTRIBUTES { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    CLIPPER_PREFIX { "<Unused>" }    LIST_NAME { "项目区" }    LIST_ATTRS_TO_INCLUDE { XZQMC }    LIST_ATTRS_TO_INCLUDE_MODE { SELECTED }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { INSIDE FEATURE_TYPE Clipper_4_INSIDE         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { AttributeManager }    INPUT  FEATURE_TYPE Clipper_4_INSIDE    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "<u9879><u76ee><u533a><opencurly><closecurly>.XZQMC" "<u9879><u76ee><u533a><opencurly><closecurly>.XZQMC" "" "varchar<openparen>100<closeparen>" "NO_OP" }      ATTR_ACTION { "GHFQMC" "GHFQMC" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "_clipped" "_clipped" "" "char<openparen>3<closeparen>" "REMOVE" }      ATTR_ACTION { "" "<u9879><u76ee><u533a>" "<at>Value<openparen><u9879><u76ee><u533a><opencurly>0<closecurly>.XZQMC<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "path_unix" "path_unix" "" "buffer" "NO_OP" }      ATTR_ACTION { "path_windows" "path_windows" "" "buffer" "NO_OP" }      ATTR_ACTION { "path_rootname" "path_rootname" "" "buffer" "NO_OP" }      ATTR_ACTION { "path_filename" "path_filename" "" "buffer" "NO_OP" }      ATTR_ACTION { "path_extension" "path_extension" "" "buffer" "NO_OP" }      ATTR_ACTION { "path_directory_unix" "path_directory_unix" "" "buffer" "NO_OP" }      ATTR_ACTION { "path_directory_windows" "path_directory_windows" "" "buffer" "NO_OP" }      ATTR_ACTION { "path_type" "path_type" "" "varchar<openparen>10<closeparen>" "NO_OP" }      ATTR_ACTION { "fme_geometry<opencurly>0<closecurly>" "fme_geometry<opencurly>0<closecurly>" "" "fme_no_geom" "NO_OP" }      ATTR_ACTION { "KFPQMC" "KFPQMC" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "fme_geometry<opencurly><closecurly>" "fme_geometry<opencurly><closecurly>" "" "varchar<openparen>200<closeparen>" "NO_OP" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeManager_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} OverlayFactory    FACTORY_NAME { AreaOnAreaOverlayer_3 }    INPUT POLYGON FEATURE_TYPE AttributeManager_OUTPUT    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    ACCUMULATE_ATTRIBUTES { ONE }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    CONNECT_Z_MODE { FIRST_WINS }    DEAGGREGATE_INPUT { Yes }    OVERLAP_COUNT_ATTRIBUTE { _overlaps }    MODE COMPLETE    OVERLAY_TYPE AREA_ON_AREA    CLEANING_TOLERANCE { AUTO }    OUTPUT_REMNANTS    SELF_INTERSECTION NO    OUTPUT { POLYGON  FEATURE_TYPE AreaOnAreaOverlayer_3_AREA         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AreaOnAreaOverlayer_3 AREA Splitter"   INPUT FEATURE_TYPE AreaOnAreaOverlayer_3_AREA   OUTPUT FEATURE_TYPE AreaOnAreaOverlayer_3_AREA_0_+1KskSXFs8g=   OUTPUT FEATURE_TYPE AreaOnAreaOverlayer_3_AREA_1_n0t1JjDZZiY=   OUTPUT FEATURE_TYPE AreaOnAreaOverlayer_3_AREA_2_MCQhQOOxibE=
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "面积亩_4 输入 Input Collector"   INPUT FEATURE_TYPE AreaOnAreaOverlayer_3_AREA_0_+1KskSXFs8g=   OUTPUT FEATURE_TYPE 面积亩_4_输入
MACRO 面积亩_WORKSPACE_NAME 面积亩_4
MACRO $(面积亩_WORKSPACE_NAME)_TRANSFORMER_GROUP 
MACRO $(面积亩_WORKSPACE_NAME)_XFORMER_NAME 面积亩_4
MACRO $(面积亩_WORKSPACE_NAME)___COMPOUND_PARAMETERS 
MACRO $(面积亩_WORKSPACE_NAME)_PATH_FILENAME <at>Value<openparen>path_filename<closeparen>
MACRO $(面积亩_WORKSPACE_NAME)_SUB_DOC_NAME 面积亩
MACRO $(面积亩_WORKSPACE_NAME)_SUB_DOC_NAME 面积亩
DEFAULT_MACRO 面积亩_WORKSPACE_NAME ""
INCLUDE [puts {MACRO WB_OLD_CONTEXT_$(面积亩_WORKSPACE_NAME) $(WB_CURRENT_CONTEXT)};          puts {MACRO WB_CURRENT_CONTEXT $(面积亩_WORKSPACE_NAME)}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_输入1733906351 Input Splitter"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入"   OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入" "@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING_FDIV_TYPED,path_filename,$($(面积亩_WORKSPACE_NAME)_PATH_FILENAME),UTF8, FEATURE_TYPE)"
# -------------------------------------------------------------------------
Tcl2 proc $(面积亩_WORKSPACE_NAME)_Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO $(面积亩_WORKSPACE_NAME)_Creator_XML     NOT_ACTIVATED
MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC NOT_ACTIVATED
MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D    2D_GEOMETRY
MACRO $(面积亩_WORKSPACE_NAME)_Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 3D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {$(面积亩_WORKSPACE_NAME)_Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO $(面积亩_WORKSPACE_NAME)_Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
FACTORY_DEF {$($(面积亩_WORKSPACE_NAME)_Creator_XML)} CreationFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$($(面积亩_WORKSPACE_NAME)_Creator_CLASSIC)} CreationFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC_Creator }    $($(面积亩_WORKSPACE_NAME)_Creator_2D3D) { $($(面积亩_WORKSPACE_NAME)_Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2($(面积亩_WORKSPACE_NAME)_Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"        fme_feature_type $(面积亩_WORKSPACE_NAME)_Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_Creator_CREATED Brancher 1 24"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4ea9>" "SET_TO" "0" "uint64" }      ATTR_ACTION { "" "<u516c><u9877>" "SET_TO" "0" "uint64" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeCreator_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Reprojector }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Reprojector_REPROJECTED"         @Reproject("","CGCS2000/GK3d-40_FME",NearestNeighbor,PreserveCells,$(面积亩_WORKSPACE_NAME)_Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO $(面积亩_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO $(面积亩_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Reprojector_REPROJECTED"    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $($(面积亩_WORKSPACE_NAME)_AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AreaCalculator_OUTPUT"       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeManager }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AreaCalculator_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "NO_OP" }      ATTR_ACTION { "" "<u4ea9>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0015<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u516c><u9877>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0001<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_StatisticsCalculator }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_OUTPUT"    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeCreator_OUTPUT"    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FEAT_STATS { <u4ea9>,NUMERIC_MODE,,,,SUM,,,,,,,,,;<u516c><u9877>,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { _area,real64,<u4ea9>,varchar<openparen>200<closeparen>,<u516c><u9877>,varchar<openparen>200<closeparen>,path_filename,buffer,_creation_instance,uint64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeExposer }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeExposer_OUTPUT"          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeManager_2 }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeExposer_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "REMOVE" }      ATTR_ACTION { "<u4ea9>" "<u4ea9>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>" "<u516c><u9877>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u4ea9>.sum" "<u4ea9>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>.sum" "<u516c><u9877>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "fme_feature_type" "fme_feature_type" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u4ea9>.sum<closeparen><closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u516c><u9877>.sum<closeparen><closeparen>" "real64" "SET_TO" }      ATTR_ACTION { "" "<u56fe><u5c42>" "<at>Value<openparen>fme_feature_type<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"        }
# -------------------------------------------------------------------------
# Build the List removal function and regular expression if there was any list attributes to be removed.
# If not, then we will not have any extra list removal call to @RemoveAttributes, which speeds the
# normal, non-list removal especially when in Bulk Mode.  Note that this computation of the regular expressions is done
# once during mapping file parse time.
INCLUDE [    set listAttributeRemoveRegexps {};    set anyList {no};    foreach attr [split ""] {       set attr [FME_DecodeText $attr];       set attr [regsub "{}$" $attr "{}.*"];       set attr [regsub -all "{}" $attr "\\{\[0-9\]+\\}"];       append listAttributeRemoveRegexps ",^$attr$";       set anyList {yes};    };    if { ${anyList} == {no} } {        puts {MACRO $(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC }    } else {        puts "MACRO $(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC @RemoveAttributes(fme_pcre_match\"$listAttributeRemoveRegexps\")"    }; ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeRemover }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeRemover_OUTPUT"        @RemoveAttributes(fme_encoded,<u4ea9>,<u4ea9>.sum,<u516c><u9877>,<u516c><u9877>.sum)        $($(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC)         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_result1733906351 Output Collector"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeRemover_OUTPUT"   OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_result"
INCLUDE [puts {MACRO WB_CURRENT_CONTEXT $(WB_OLD_CONTEXT_$(面积亩_WORKSPACE_NAME))}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "面积亩_4 result Output Renamer/Nuker"   INPUT FEATURE_TYPE 面积亩_4_result   OUTPUT FEATURE_TYPE 面积亩_4_result
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { AttributeManager_3 }    INPUT  FEATURE_TYPE 面积亩_4_result    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "fme_feature_type" "fme_feature_type" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "" "real64" "NO_OP" }      ATTR_ACTION { "<u56fe><u5c42>" "<u65b9><u6848>" "$(PARAMETER$encode)" "varchar<openparen>200<closeparen>" "RENAME_SET_VALUE" }      ATTR_ACTION { "path_filename" "<u6587><u4ef6>" "" "buffer" "RENAME_SET_VALUE" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeManager_3_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer_5 }    INPUT  FEATURE_TYPE AreaOnAreaOverlayer_3_AREA_1_n0t1JjDZZiY=    OUTPUT { FEATURE_TYPE AttributeExposer_5_OUTPUT          }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_2_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_2_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_2_AreaCalculatorInput }    INPUT  FEATURE_TYPE AttributeExposer_5_OUTPUT    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_2_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $(AreaCalculator_2_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_2_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_2_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AreaCalculator_2_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4ea9>" "SET_TO" "<at>Format<openparen>%.4f<comma><at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0015<closeparen><closeparen>" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u516c><u9877>" "SET_TO" "<at>Format<openparen>%.4f<comma><at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0001<closeparen><closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator_2 }    INPUT  FEATURE_TYPE AttributeCreator_2_OUTPUT    GROUP_BY { KFPQMC }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { <u516c><u9877>,NUMERIC_MODE,,,,SUM,,,,,,,,,;<u4ea9>,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { <u4ea9>,varchar<openparen>200<closeparen>,<u516c><u9877>,varchar<openparen>200<closeparen>,GHFQMC,varchar<openparen>200<closeparen>,<u9879><u76ee><u533a>,varchar<openparen>200<closeparen>,path_unix,buffer,path_windows,buffer,path_rootname,buffer,path_filename,buffer,path_extension,buffer,path_directory_unix,buffer,path_directory_windows,buffer,path_type,varchar<openparen>10<closeparen>,fme_geometry<opencurly>0<closecurly>,fme_no_geom,KFPQMC,varchar<openparen>200<closeparen>,_overlaps,uint32,_area,real64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE StatisticsCalculator_2_SUMMARY        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE StatisticsCalculator_2_SUMMARY    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen><u4ea9>.sum<closeparen>,Tester)" != 0 ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_4 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Tester_PASSED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u62a5><u544a>" "SET_TO" "<at>Value<openparen>KFPQMC<closeparen><u6d89><u53ca><u5360><u7528><u9762><u79ef><at>Format<openparen>%.4f<comma><at>Value<openparen><u516c><u9877>.sum<closeparen><closeparen><u516c><u9877><uff08><at>Format<openparen>%.4f<comma><at>Value<openparen><u4ea9>.sum<closeparen><closeparen><uff09><u4ea9>" "varchar<openparen>200<closeparen>" }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_3_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_3_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_3_AreaCalculatorInput }    INPUT  FEATURE_TYPE AreaOnAreaOverlayer_3_AREA_2_MCQhQOOxibE=    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_3_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $(AreaCalculator_3_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_3_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_3_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_7 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE AreaCalculator_3_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "mjm" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0015<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_7_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator_3 }    INPUT  FEATURE_TYPE AttributeCreator_7_OUTPUT    GROUP_BY { KFPQMC }    FLUSH_WHEN_GROUPS_CHANGE { No }    FEAT_STATS { mjm,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { mjm,varchar<openparen>200<closeparen>,GHFQMC,varchar<openparen>200<closeparen>,<u9879><u76ee><u533a>,varchar<openparen>200<closeparen>,path_unix,buffer,path_windows,buffer,path_rootname,buffer,path_filename,buffer,path_extension,buffer,path_directory_unix,buffer,path_directory_windows,buffer,path_type,varchar<openparen>10<closeparen>,fme_geometry<opencurly>0<closecurly>,fme_no_geom,KFPQMC,varchar<openparen>200<closeparen>,_overlaps,uint32,_area,real64 }    TREAT_INVALID_AS_NULL Yes
# -------------------------------------------------------------------------
FACTORY_DEF {*} ClipperFactory    FACTORY_NAME { Clipper }    INPUT CLIPPER FEATURE_TYPE AttributeExposer_3_OUTPUT_1_IVBOgCe3lxI=    INPUT CANDIDATE FEATURE_TYPE FeatureReader_3_<OTHER>    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    MULTIPLE_CLIPPERS { YES }    CLIPPERS_FIRST { NO }    OVERLAPPING_CLIPPERS { CLIP_OUTSIDE }    INVALID_PARTS_HANDLING { REJECT_WHOLE }    ADD_NODATA { YES }    Z_VAL_SOURCE { CANDIDATE }    MISSING_Z_MODE { PLANAR }    CONNECT_Z_MODE { <Unused> }    M_VAL_SOURCE { CANDIDATE }    MISSING_M_MODE { LINEAR }    CLEANING_TOLERANCE { AUTO }    CANDIDATE_ON_BOUNDARY { INSIDE }    PRESERVE_RASTER_EXTENTS { NO }    RASTER_CELL_MODE { CENTER }    CLIPPED_INDICATOR_ATTR { "_clipped" }    MERGE_CLIPPER_ATTRIBUTES { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    CLIPPER_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { INSIDE FEATURE_TYPE Clipper_INSIDE         }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_4_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_4_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_4_AreaCalculatorInput }    INPUT  FEATURE_TYPE Clipper_INSIDE    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_4_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $(AreaCalculator_4_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_4_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_4_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator_4 }    INPUT  FEATURE_TYPE AreaCalculator_4_OUTPUT    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FEAT_STATS { _area,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { path_unix,buffer,path_windows,buffer,path_rootname,buffer,path_filename,buffer,path_extension,buffer,path_directory_unix,buffer,path_directory_windows,buffer,path_type,varchar<openparen>10<closeparen>,fme_geometry<opencurly>0<closecurly>,fme_no_geom,_clipped,char<openparen>3<closeparen>,_area,real64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE StatisticsCalculator_4_SUMMARY        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_5 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_4_SUMMARY    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "mu" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_area.sum<closeparen>*0.0015<closeparen>" "varchar<openparen>200<closeparen>" }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ClipperFactory    FACTORY_NAME { Clipper_6 }    INPUT CLIPPER FEATURE_TYPE FeatureReader_2_Export_Output    INPUT CANDIDATE FEATURE_TYPE Junction_Output_2_VzwNptf57aI=    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    MULTIPLE_CLIPPERS { YES }    CLIPPERS_FIRST { NO }    OVERLAPPING_CLIPPERS { CLIP_OUTSIDE }    INVALID_PARTS_HANDLING { REJECT_WHOLE }    ADD_NODATA { YES }    Z_VAL_SOURCE { CANDIDATE }    MISSING_Z_MODE { PLANAR }    CONNECT_Z_MODE { <Unused> }    M_VAL_SOURCE { CANDIDATE }    MISSING_M_MODE { LINEAR }    CLEANING_TOLERANCE { AUTO }    CANDIDATE_ON_BOUNDARY { INSIDE }    PRESERVE_RASTER_EXTENTS { NO }    RASTER_CELL_MODE { CENTER }    CLIPPED_INDICATOR_ATTR { "_clipped" }    MERGE_CLIPPER_ATTRIBUTES { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    CLIPPER_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { INSIDE FEATURE_TYPE Clipper_6_INSIDE         }
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "mj Input Input Collector"   INPUT FEATURE_TYPE Clipper_6_INSIDE   OUTPUT FEATURE_TYPE mj_Input
MACRO mj_WORKSPACE_NAME mj
MACRO $(mj_WORKSPACE_NAME)_TRANSFORMER_GROUP 
MACRO $(mj_WORKSPACE_NAME)_XFORMER_NAME mj
MACRO $(mj_WORKSPACE_NAME)_SUB_DOC_NAME mj
MACRO $(mj_WORKSPACE_NAME)_SUB_DOC_NAME mj
DEFAULT_MACRO mj_WORKSPACE_NAME ""
INCLUDE [puts {MACRO WB_OLD_CONTEXT_$(mj_WORKSPACE_NAME) $(WB_CURRENT_CONTEXT)};          puts {MACRO WB_CURRENT_CONTEXT $(mj_WORKSPACE_NAME)}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(mj_WORKSPACE_NAME)_Input1733906351 Input Splitter"   INPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Input"   OUTPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Input"
# -------------------------------------------------------------------------
Tcl2 proc $(mj_WORKSPACE_NAME)_Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO $(mj_WORKSPACE_NAME)_Creator_XML     NOT_ACTIVATED
MACRO $(mj_WORKSPACE_NAME)_Creator_CLASSIC NOT_ACTIVATED
MACRO $(mj_WORKSPACE_NAME)_Creator_2D3D    2D_GEOMETRY
MACRO $(mj_WORKSPACE_NAME)_Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO $(mj_WORKSPACE_NAME)_Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO $(mj_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(mj_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO $(mj_WORKSPACE_NAME)_Creator_2D3D 3D_GEOMETRY};            puts {MACRO $(mj_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {$(mj_WORKSPACE_NAME)_Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO $(mj_WORKSPACE_NAME)_Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO $(mj_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(mj_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
FACTORY_DEF {$($(mj_WORKSPACE_NAME)_Creator_XML)} CreationFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$($(mj_WORKSPACE_NAME)_Creator_CLASSIC)} CreationFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_Creator_CLASSIC_Creator }    $($(mj_WORKSPACE_NAME)_Creator_2D3D) { $($(mj_WORKSPACE_NAME)_Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2($(mj_WORKSPACE_NAME)_Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Creator_CREATED"        fme_feature_type $(mj_WORKSPACE_NAME)_Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "$(mj_WORKSPACE_NAME)_Creator_CREATED Brancher 0 21"   INPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Creator_CREATED"   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Creator_CREATED"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4ea9>" "SET_TO" "0" "uint64" }      ATTR_ACTION { "" "<u516c><u9877>" "SET_TO" "0" "uint64" }    OUTPUT { OUTPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeCreator_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AttributeExposer_2 }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Input"    OUTPUT { FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeExposer_2_OUTPUT"          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_Reprojector }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeExposer_2_OUTPUT"    OUTPUT { FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Reprojector_REPROJECTED"         @Reproject("","CGCS2000/GK3d-40_FME",NearestNeighbor,PreserveCells,$(mj_WORKSPACE_NAME)_Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO $(mj_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO $(mj_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Reprojector_REPROJECTED"    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $($(mj_WORKSPACE_NAME)_AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AreaCalculator_OUTPUT"       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AttributeManager }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AreaCalculator_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "NO_OP" }      ATTR_ACTION { "" "<u4ea9>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0015<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u516c><u9877>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0001<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeManager_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_StatisticsCalculator }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeManager_OUTPUT"    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeCreator_OUTPUT"    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FEAT_STATS { <u4ea9>,NUMERIC_MODE,,,,SUM,,,,,,,,,;<u516c><u9877>,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { _area,real64,<u4ea9>,varchar<openparen>200<closeparen>,<u516c><u9877>,varchar<openparen>200<closeparen>,path_filename,varchar<openparen>200<closeparen>,_creation_instance,uint64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE "$(mj_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AttributeExposer }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"    OUTPUT { FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeExposer_OUTPUT"          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AttributeManager_2 }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeExposer_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "REMOVE" }      ATTR_ACTION { "<u4ea9>" "<u4ea9>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>" "<u516c><u9877>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u4ea9>.sum" "<u4ea9>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>.sum" "<u516c><u9877>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "fme_feature_type" "fme_feature_type" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u4ea9>.sum<closeparen><closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u516c><u9877>.sum<closeparen><closeparen>" "real64" "SET_TO" }      ATTR_ACTION { "" "<u56fe><u5c42>" "<at>Value<openparen>fme_feature_type<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"        }
# -------------------------------------------------------------------------
# Build the List removal function and regular expression if there was any list attributes to be removed.
# If not, then we will not have any extra list removal call to @RemoveAttributes, which speeds the
# normal, non-list removal especially when in Bulk Mode.  Note that this computation of the regular expressions is done
# once during mapping file parse time.
INCLUDE [    set listAttributeRemoveRegexps {};    set anyList {no};    foreach attr [split ""] {       set attr [FME_DecodeText $attr];       set attr [regsub "{}$" $attr "{}.*"];       set attr [regsub -all "{}" $attr "\\{\[0-9\]+\\}"];       append listAttributeRemoveRegexps ",^$attr$";       set anyList {yes};    };    if { ${anyList} == {no} } {        puts {MACRO $(mj_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC }    } else {        puts "MACRO $(mj_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC @RemoveAttributes(fme_pcre_match\"$listAttributeRemoveRegexps\")"    }; ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(mj_WORKSPACE_NAME)_AttributeRemover }    INPUT  FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"    OUTPUT { FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeRemover_OUTPUT"        @RemoveAttributes(fme_encoded,<u4ea9>,<u4ea9>.sum,<u516c><u9877>,<u516c><u9877>.sum)        $($(mj_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC)         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(mj_WORKSPACE_NAME)_Output1733906351 Output Collector"   INPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_AttributeRemover_OUTPUT"   OUTPUT FEATURE_TYPE "$(mj_WORKSPACE_NAME)_Output"
INCLUDE [puts {MACRO WB_CURRENT_CONTEXT $(WB_OLD_CONTEXT_$(mj_WORKSPACE_NAME))}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "mj Output Output Renamer/Nuker"   INPUT FEATURE_TYPE mj_Output   OUTPUT FEATURE_TYPE mj_Output
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AttributeExposer_6 }    INPUT  FEATURE_TYPE mj_Output    OUTPUT { FEATURE_TYPE AttributeExposer_6_OUTPUT          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { AttributeManager_2 }    INPUT  FEATURE_TYPE AttributeExposer_6_OUTPUT    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "fme_feature_type" "fme_feature_type" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "" "real64" "NO_OP" }      ATTR_ACTION { "<u56fe><u5c42>" "<u65b9><u6848>" "$(PARAMETER$encode)" "varchar<openparen>200<closeparen>" "RENAME_SET_VALUE" }      ATTR_ACTION { "path_filename" "<u6587><u4ef6>" "" "buffer" "RENAME_SET_VALUE" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeManager_2_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "面积亩_6 输入 Input Collector"   INPUT FEATURE_TYPE Junction_Output_3_Es4q/I3oBKE=   OUTPUT FEATURE_TYPE 面积亩_6_输入
MACRO 面积亩_WORKSPACE_NAME 面积亩_6
MACRO $(面积亩_WORKSPACE_NAME)_TRANSFORMER_GROUP 
MACRO $(面积亩_WORKSPACE_NAME)_XFORMER_NAME 面积亩_6
MACRO $(面积亩_WORKSPACE_NAME)___COMPOUND_PARAMETERS 
MACRO $(面积亩_WORKSPACE_NAME)_PATH_FILENAME <at>Value<openparen>path_filename<closeparen>
MACRO $(面积亩_WORKSPACE_NAME)_SUB_DOC_NAME 面积亩
MACRO $(面积亩_WORKSPACE_NAME)_SUB_DOC_NAME 面积亩
DEFAULT_MACRO 面积亩_WORKSPACE_NAME ""
INCLUDE [puts {MACRO WB_OLD_CONTEXT_$(面积亩_WORKSPACE_NAME) $(WB_CURRENT_CONTEXT)};          puts {MACRO WB_CURRENT_CONTEXT $(面积亩_WORKSPACE_NAME)}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_输入1733906351 Input Splitter"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入"   OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入" "@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING_FDIV_TYPED,path_filename,$($(面积亩_WORKSPACE_NAME)_PATH_FILENAME),UTF8, FEATURE_TYPE)"
# -------------------------------------------------------------------------
Tcl2 proc $(面积亩_WORKSPACE_NAME)_Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO $(面积亩_WORKSPACE_NAME)_Creator_XML     NOT_ACTIVATED
MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC NOT_ACTIVATED
MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D    2D_GEOMETRY
MACRO $(面积亩_WORKSPACE_NAME)_Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 3D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {$(面积亩_WORKSPACE_NAME)_Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO $(面积亩_WORKSPACE_NAME)_Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
FACTORY_DEF {$($(面积亩_WORKSPACE_NAME)_Creator_XML)} CreationFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$($(面积亩_WORKSPACE_NAME)_Creator_CLASSIC)} CreationFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC_Creator }    $($(面积亩_WORKSPACE_NAME)_Creator_2D3D) { $($(面积亩_WORKSPACE_NAME)_Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2($(面积亩_WORKSPACE_NAME)_Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"        fme_feature_type $(面积亩_WORKSPACE_NAME)_Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_Creator_CREATED Brancher 1 24"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4ea9>" "SET_TO" "0" "uint64" }      ATTR_ACTION { "" "<u516c><u9877>" "SET_TO" "0" "uint64" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeCreator_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Reprojector }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Reprojector_REPROJECTED"         @Reproject("","CGCS2000/GK3d-40_FME",NearestNeighbor,PreserveCells,$(面积亩_WORKSPACE_NAME)_Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO $(面积亩_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO $(面积亩_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Reprojector_REPROJECTED"    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $($(面积亩_WORKSPACE_NAME)_AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AreaCalculator_OUTPUT"       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeManager }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AreaCalculator_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "NO_OP" }      ATTR_ACTION { "" "<u4ea9>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0015<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u516c><u9877>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0001<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_StatisticsCalculator }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_OUTPUT"    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeCreator_OUTPUT"    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FEAT_STATS { <u4ea9>,NUMERIC_MODE,,,,SUM,,,,,,,,,;<u516c><u9877>,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { _area,real64,<u4ea9>,varchar<openparen>200<closeparen>,<u516c><u9877>,varchar<openparen>200<closeparen>,path_filename,buffer,_creation_instance,uint64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeExposer }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeExposer_OUTPUT"          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeManager_2 }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeExposer_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "REMOVE" }      ATTR_ACTION { "<u4ea9>" "<u4ea9>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>" "<u516c><u9877>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u4ea9>.sum" "<u4ea9>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>.sum" "<u516c><u9877>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "fme_feature_type" "fme_feature_type" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u4ea9>.sum<closeparen><closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u516c><u9877>.sum<closeparen><closeparen>" "real64" "SET_TO" }      ATTR_ACTION { "" "<u56fe><u5c42>" "<at>Value<openparen>fme_feature_type<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"        }
# -------------------------------------------------------------------------
# Build the List removal function and regular expression if there was any list attributes to be removed.
# If not, then we will not have any extra list removal call to @RemoveAttributes, which speeds the
# normal, non-list removal especially when in Bulk Mode.  Note that this computation of the regular expressions is done
# once during mapping file parse time.
INCLUDE [    set listAttributeRemoveRegexps {};    set anyList {no};    foreach attr [split ""] {       set attr [FME_DecodeText $attr];       set attr [regsub "{}$" $attr "{}.*"];       set attr [regsub -all "{}" $attr "\\{\[0-9\]+\\}"];       append listAttributeRemoveRegexps ",^$attr$";       set anyList {yes};    };    if { ${anyList} == {no} } {        puts {MACRO $(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC }    } else {        puts "MACRO $(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC @RemoveAttributes(fme_pcre_match\"$listAttributeRemoveRegexps\")"    }; ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeRemover }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeRemover_OUTPUT"        @RemoveAttributes(fme_encoded,<u4ea9>,<u4ea9>.sum,<u516c><u9877>,<u516c><u9877>.sum)        $($(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC)         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_result1733906351 Output Collector"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeRemover_OUTPUT"   OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_result"
INCLUDE [puts {MACRO WB_CURRENT_CONTEXT $(WB_OLD_CONTEXT_$(面积亩_WORKSPACE_NAME))}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "面积亩_6 result Output Renamer/Nuker"   INPUT FEATURE_TYPE 面积亩_6_result
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "面积亩_3 输入 Input Collector"   INPUT FEATURE_TYPE AreaOnAreaOverlayer_2_AREA_0_89l04jsGBc0=   OUTPUT FEATURE_TYPE 面积亩_3_输入
MACRO 面积亩_WORKSPACE_NAME 面积亩_3
MACRO $(面积亩_WORKSPACE_NAME)_TRANSFORMER_GROUP 
MACRO $(面积亩_WORKSPACE_NAME)_XFORMER_NAME 面积亩_3
MACRO $(面积亩_WORKSPACE_NAME)___COMPOUND_PARAMETERS 
MACRO $(面积亩_WORKSPACE_NAME)_PATH_FILENAME <at>Value<openparen>path_filename<closeparen>
MACRO $(面积亩_WORKSPACE_NAME)_SUB_DOC_NAME 面积亩
MACRO $(面积亩_WORKSPACE_NAME)_SUB_DOC_NAME 面积亩
DEFAULT_MACRO 面积亩_WORKSPACE_NAME ""
INCLUDE [puts {MACRO WB_OLD_CONTEXT_$(面积亩_WORKSPACE_NAME) $(WB_CURRENT_CONTEXT)};          puts {MACRO WB_CURRENT_CONTEXT $(面积亩_WORKSPACE_NAME)}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_输入1733906351 Input Splitter"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入"   OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入" "@EvaluateExpression(ATTR_CREATE_EXPR_PROPAGATE_MISSING_FDIV_TYPED,path_filename,$($(面积亩_WORKSPACE_NAME)_PATH_FILENAME),UTF8, FEATURE_TYPE)"
# -------------------------------------------------------------------------
Tcl2 proc $(面积亩_WORKSPACE_NAME)_Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO $(面积亩_WORKSPACE_NAME)_Creator_XML     NOT_ACTIVATED
MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC NOT_ACTIVATED
MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D    2D_GEOMETRY
MACRO $(面积亩_WORKSPACE_NAME)_Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 3D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {$(面积亩_WORKSPACE_NAME)_Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO $(面积亩_WORKSPACE_NAME)_Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_2D3D 2D_GEOMETRY};            puts {MACRO $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC *} } ]
FACTORY_DEF {$($(面积亩_WORKSPACE_NAME)_Creator_XML)} CreationFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$($(面积亩_WORKSPACE_NAME)_Creator_CLASSIC)} CreationFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_CLASSIC_Creator }    $($(面积亩_WORKSPACE_NAME)_Creator_2D3D) { $($(面积亩_WORKSPACE_NAME)_Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2($(面积亩_WORKSPACE_NAME)_Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"        fme_feature_type $(面积亩_WORKSPACE_NAME)_Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_Creator_CREATED Brancher 1 24"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Creator_CREATED"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u4ea9>" "SET_TO" "0" "uint64" }      ATTR_ACTION { "" "<u516c><u9877>" "SET_TO" "0" "uint64" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeCreator_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_Reprojector }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_输入"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Reprojector_REPROJECTED"         @Reproject("","CGCS2000/GK3d-40_FME",NearestNeighbor,PreserveCells,$(面积亩_WORKSPACE_NAME)_Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO $(面积亩_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO $(面积亩_WORKSPACE_NAME)_AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_Reprojector_REPROJECTED"    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $($(面积亩_WORKSPACE_NAME)_AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AreaCalculator_OUTPUT"       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeManager }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AreaCalculator_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "NO_OP" }      ATTR_ACTION { "" "<u4ea9>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0015<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u516c><u9877>" "<at>Evaluate<openparen><at>Value<openparen>_area<closeparen>*0.0001<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_OUTPUT"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_StatisticsCalculator }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_OUTPUT"    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeCreator_OUTPUT"    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FEAT_STATS { <u4ea9>,NUMERIC_MODE,,,,SUM,,,,,,,,,;<u516c><u9877>,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { _area,real64,<u4ea9>,varchar<openparen>200<closeparen>,<u516c><u9877>,varchar<openparen>200<closeparen>,path_filename,buffer,_creation_instance,uint64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeExposer }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_StatisticsCalculator_SUMMARY"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeExposer_OUTPUT"          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeManager_2 }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeExposer_OUTPUT"    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "_area" "_area" "" "real64" "REMOVE" }      ATTR_ACTION { "<u4ea9>" "<u4ea9>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>" "<u516c><u9877>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u4ea9>.sum" "<u4ea9>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "<u516c><u9877>.sum" "<u516c><u9877>.sum" "" "real64" "NO_OP" }      ATTR_ACTION { "fme_feature_type" "fme_feature_type" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u4ea9>.sum<closeparen><closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }      ATTR_ACTION { "" "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "<at>Format<openparen>%.4f<comma><at>Value<openparen><u516c><u9877>.sum<closeparen><closeparen>" "real64" "SET_TO" }      ATTR_ACTION { "" "<u56fe><u5c42>" "<at>Value<openparen>fme_feature_type<closeparen>" "varchar<openparen>200<closeparen>" "SET_TO" }    OUTPUT { OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"        }
# -------------------------------------------------------------------------
# Build the List removal function and regular expression if there was any list attributes to be removed.
# If not, then we will not have any extra list removal call to @RemoveAttributes, which speeds the
# normal, non-list removal especially when in Bulk Mode.  Note that this computation of the regular expressions is done
# once during mapping file parse time.
INCLUDE [    set listAttributeRemoveRegexps {};    set anyList {no};    foreach attr [split ""] {       set attr [FME_DecodeText $attr];       set attr [regsub "{}$" $attr "{}.*"];       set attr [regsub -all "{}" $attr "\\{\[0-9\]+\\}"];       append listAttributeRemoveRegexps ",^$attr$";       set anyList {yes};    };    if { ${anyList} == {no} } {        puts {MACRO $(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC }    } else {        puts "MACRO $(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC @RemoveAttributes(fme_pcre_match\"$listAttributeRemoveRegexps\")"    }; ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { $(面积亩_WORKSPACE_NAME)_AttributeRemover }    INPUT  FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeManager_2_OUTPUT"    OUTPUT { FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeRemover_OUTPUT"        @RemoveAttributes(fme_encoded,<u4ea9>,<u4ea9>.sum,<u516c><u9877>,<u516c><u9877>.sum)        $($(面积亩_WORKSPACE_NAME)_AttributeRemover_LIST_FUNC)         }
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(面积亩_WORKSPACE_NAME)_result1733906351 Output Collector"   INPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_AttributeRemover_OUTPUT"   OUTPUT FEATURE_TYPE "$(面积亩_WORKSPACE_NAME)_result"
INCLUDE [puts {MACRO WB_CURRENT_CONTEXT $(WB_OLD_CONTEXT_$(面积亩_WORKSPACE_NAME))}]
FACTORY_DEF * TeeFactory   FACTORY_NAME "面积亩_3 result Output Renamer/Nuker"   INPUT FEATURE_TYPE 面积亩_3_result   OUTPUT FEATURE_TYPE 面积亩_3_result
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FACTORY_NAME { AttributeManager_5 }    INPUT  FEATURE_TYPE 面积亩_3_result    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    ACTION_COLUMN 4    DEF_VAL_COLUMN 2    NUM_OF_COLUMNS 5    MISSING_INPUT_ATTR_HANDLING RENAME_SET_VALUE REMOVE    ATTR_ACTION { "fme_feature_type" "fme_feature_type" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "<u5360><u7528><u9762><u79ef><uff08><u4ea9><uff09>" "" "varchar<openparen>200<closeparen>" "NO_OP" }      ATTR_ACTION { "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "<u5360><u7528><u9762><u79ef><uff08><u516c><u9877><uff09>" "" "real64" "NO_OP" }      ATTR_ACTION { "<u56fe><u5c42>" "<u65b9><u6848>" "$(PARAMETER$encode)" "varchar<openparen>200<closeparen>" "RENAME_SET_VALUE" }      ATTR_ACTION { "path_filename" "<u6587><u4ef6>" "" "buffer" "RENAME_SET_VALUE" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeManager_5_OUTPUT        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(输出结果目录$encode)<backslash>result.xlsx,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { XLSXW }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_FILE<comma>No<comma>TEMPLATEFILE<comma><comma>TEMPLATE_SHEET<comma><comma>REMOVE_UNCHANGED_TEMPLATE_SHEET<comma>No<comma>MULTIPLE_TEMPLATE_SHEETS<comma>Yes<comma>INSERT_IGNORE_DB_OP<comma>Yes<comma>DROP_TABLE<comma>No<comma>TRUNCATE_TABLE<comma>No<comma>FIELD_NAMES_OUT<comma>Yes<comma>FIELD_NAMES_FORMATTING<comma>Yes<comma>WRITER_MODE<comma>Insert<comma>RASTER_FORMAT<comma>PNG<comma>PROTECT_SHEET<comma>NO<comma>PROTECT_SHEET_PASSWORD<comma><lt>Unused<gt><comma>PROTECT_SHEET_LEVEL<comma><lt>Unused<gt><comma>PROTECT_SHEET_PERMISSIONS<comma><lt>Unused<gt><comma>STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING<comma>yes<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE<comma>CUSTOM_NUMBER_FORMATTING<comma>ENABLE_NATIVE<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,XLSXW" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,<quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varchar<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varbinary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_char<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binarybuffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_xml<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_json<comma><quote>auto<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>datetime<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_datetime<comma><quote>time<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_time<comma><quote>date<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_date<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int8<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint8<comma><quote>boolean<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<openbracket><closebracket>*<backslash><backslash>?:<apos>,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none<comma>fme_no_geom<comma>xlsx_none<comma>fme_point<comma>xlsx_point<comma>fme_point<comma>xlsx_none<comma>fme_line<comma>xlsx_none<comma>fme_polygon<comma>xlsx_none<comma>fme_text<comma>xlsx_none<comma>fme_ellipse<comma>xlsx_none<comma>fme_arc<comma>xlsx_none<comma>fme_rectangle<comma>xlsx_none<comma>fme_rounded_rectangle<comma>xlsx_none<comma>fme_collection<comma>xlsx_none<comma>fme_surface<comma>xlsx_none<comma>fme_solid<comma>xlsx_none<comma>fme_raster<comma>xlsx_none<comma>fme_point_cloud<comma>xlsx_none<comma>fme_voxel_grid<comma>xlsx_none<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>xlsx_layer_group<space>xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group<space>Sheet<space>Settings<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_truncate_group<space>xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet<space>Drop<solidus>Truncate<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_rowcolumn_group<space>xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row<space>Start<space>Position<quote><comma><comma><quote>GUI<space>ACTIVEDISCLOSUREGROUP<space>xlsx_protect_sheet<space>xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions<space>Protect<space>Sheet<quote><comma>NO<comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_template_group<space>xlsx_template_sheet%xlsx_remove_unchanged_template_sheet<space>Template<space>Options<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_advanced_group<space>xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type<space>Advanced<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_drop_sheet<space>Yes%No<space>Drop<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>CHOICE<space>xlsx_trunc_sheet<space>Yes%No<space>Truncate<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_sheet_order<space>1%MAX<space>Sheet<space>Order<space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_freeze_end_row<space>1%MAX<space>Freeze<space>First<space>Row<openparen>s<closeparen><space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_field_names_out<space>Yes%No<comma>xlsx_field_names_formatting<comma>++xlsx_field_names_formatting+No<space>Output<space>Field<space>Names:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_field_names_formatting<space>Yes%No<space>Format<space>Field<space>Names<space>Row:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_names_are_positions<space>Yes%No<space>Use<space>Attribute<space>Names<space>As<space>Column<space>Positions:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_start_col<space>Named<space>Range<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_start_row<space>Named<space>Range<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_offset_col<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_offset_row<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_raster_type<space>BMP%JPEG%PNG<space>Raster<space>Format:<quote><comma>PNG<comma><quote>GUI<space>OPTIONAL<space>PASSWORD_ENCODED<space>xlsx_protect_sheet_password<space>Password:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>xlsx_protect_sheet_level<space>Select<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_DEFAULT<comma>xlsx_protect_sheet_permissions%View<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_ALL<comma>xlsx_protect_sheet_permissions%Specific<lt>space<gt>Permissions<space>Protection<space>Level:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>OPTIONAL<space>LOOKUP_LISTBOX<space>xlsx_protect_sheet_permissions<space>Select<lt>space<gt>locked<lt>space<gt>cells<comma>PROT_SEL_LOCKED_CELLS%Select<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_SEL_UNLOCKED_CELLS%Format<lt>space<gt>cells<comma>PROT_FORMAT_CELLS%Format<lt>space<gt>columns<comma>PROT_FORMAT_COLUMNS%Format<lt>space<gt>rows<comma>PROT_FORMAT_ROWS%Insert<lt>space<gt>columns<comma>PROT_INSERT_COLUMNS%Insert<lt>space<gt>rows<comma>PROT_INSERT_ROWS%Add<lt>space<gt>hyperlinks<lt>space<gt>to<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_INSERT_HYPERLINKS%Delete<lt>space<gt>unlocked<lt>space<gt>columns<comma>PROT_DELETE_COLUMNS%Delete<lt>space<gt>unlocked<lt>space<gt>rows<comma>PROT_DELETE_ROWS%Sort<lt>space<gt>unlocked<lt>space<gt>cells<solidus>rows<solidus>columns<comma>PROT_SORT%Use<lt>space<gt>Autofilter<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_AUTOFILTER%Use<lt>space<gt>PivotTable<lt>space<gt><amp><lt>space<gt>PivotChart<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_PIVOTTABLES%Edit<lt>space<gt>unlocked<lt>space<gt>objects<comma>PROT_OBJECTS%Edit<lt>space<gt>unprotected<lt>space<gt>scenarios<comma>PROT_SCENARIOS<space>Specific<space>Permissions:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_table_writer_mode<space>Insert<comma>+xlsx_row_id_column+<quote><quote><quote><quote>%Update<comma>+xlsx_row_id_column+xlsx_row_id%Delete<comma>+xlsx_row_id_column+xlsx_row_id<space>Writer<space>Mode:<quote><comma>Insert<comma><quote>GUI<space>OPTIONAL<space>ATTR<space>xlsx_row_id_column<space>ALLOW_NEW<space>Row<space>Number<space>Attribute:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT_EDIT<space>xlsx_template_sheet<space>Template<space>Sheet:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_remove_unchanged_template_sheet<space>Yes%No<space>Remove<space>Template<space>Sheet<space>if<space>Unchanged:<quote><comma>No,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_freeze_end_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_names_are_positions<comma>No<comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_start_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_start_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_raster_type<comma>PNG<comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_protect_sheet<comma><quote><quote><quote>NO<quote><quote><quote><comma>xlsx_protect_sheet_level<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_password<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_permissions<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_template_sheet<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_remove_unchanged_template_sheet<comma><quote><quote><quote>No<quote><quote><quote>,WRITER_FORMAT_PARAMETER,DEFAULT_READER<comma>XLSXR<comma>ALLOW_DATASET_CONFLICT<comma>YES<comma>MIME_TYPE<comma><quote>application<solidus>vnd.openxmlformats-officedocument.spreadsheetml.sheet<space>ADD_DISPOSITION<quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>DEFAULT_ATTR_TYPE<comma>auto<comma>USER_ATTRIBUTES_COLUMNS<comma><quote>Width<comma>Cell<space>Width%Precision<comma>Formatting<quote><comma>FEATURE_TYPE_NAME<comma>Sheet<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Sheet1<comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>name<space>for<space>the<space>Microsoft<space>Excel<space>file<quote>,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<u5360><u7528><u9ad8><u6807><u51c6><u519c><u7530>:<u4ea9>,ftp_feature_type_name,<u5360><u7528><u9ad8><u6807><u51c6><u519c><u7530>,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u65b9<gt><lt>u6848<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u6587<gt><lt>u4ef6<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5360<gt><lt>u7528<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u4ea9<gt><lt>uff09<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5360<gt><lt>u7528<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u516c<gt><lt>u9877<gt><lt>uff09<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma>,ftp_format_attributes,fme_feature_type,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No;<u5360><u7528><u8c03><u6574><u524d><u4e24><u533a>:Output,ftp_feature_type_name,<u5360><u7528><u8c03><u6574><u524d><u4e24><u533a>,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u65b9<gt><lt>u6848<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u6587<gt><lt>u4ef6<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5360<gt><lt>u7528<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u4ea9<gt><lt>uff09<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5360<gt><lt>u7528<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u516c<gt><lt>u9877<gt><lt>uff09<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma>,ftp_format_attributes,fme_feature_type,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No;<u5360><u7528><u8c03><u6574><u540e><u4e24><u533a>:result,ftp_feature_type_name,<u5360><u7528><u8c03><u6574><u540e><u4e24><u533a>,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u65b9<gt><lt>u6848<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u6587<gt><lt>u4ef6<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5360<gt><lt>u7528<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u4ea9<gt><lt>uff09<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt><comma><lt>u5360<gt><lt>u7528<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u516c<gt><lt>u9877<gt><lt>uff09<gt><comma>string<lt>openparen<gt>20<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>NO<lt>closeparen<gt>,ftp_user_attribute_values,<comma><comma><comma>,ftp_format_attributes,fme_feature_type,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No" }    WRITER_PARAMS { "COORDINATE_SYSTEM_GRANULARITY,FEATURE,CUSTOM_NUMBER_FORMATTING,ENABLE_NATIVE,DESTINATION_DATASETTYPE_VALIDATION,Yes,DROP_TABLE,No,FIELD_NAMES_FORMATTING,Yes,FIELD_NAMES_OUT,Yes,INSERT_IGNORE_DB_OP,Yes,MULTIPLE_TEMPLATE_SHEETS,Yes,NETWORK_AUTHENTICATION,,OVERWRITE_FILE,No,PROTECT_SHEET,NO,RASTER_FORMAT,PNG,STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,yes,TRUNCATE_TABLE,No,WRITER_MODE,Insert" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT <u4ea9> FEATURE_TYPE AttributeManager_3_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u5360><u7528><u9ad8><u6807><u51c6><u519c><u7530>,FeatureWriter))    INPUT Output FEATURE_TYPE AttributeManager_5_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u5360><u7528><u8c03><u6574><u524d><u4e24><u533a>,FeatureWriter))    INPUT result FEATURE_TYPE AttributeManager_2_OUTPUT  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,<u5360><u7528><u8c03><u6574><u540e><u4e24><u533a>,FeatureWriter))
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

