#! <?xml version="1.0" encoding="UTF-8" ?>
#! <WORKSPACE
#    Command line to run this workspace:
#        "C:\Program Files\FME\fme.exe" E:\YC\每日任务\2024\11\1128\淮安市成片开发方案核查\淮安市成片开发方案核查.fmw
#          --PARAMETER ""
#          --dir ""
#          --dir_2 ""
#          --PARAMETER_2 ""
#          --FME_LAUNCH_VIEWER_APP "YES"
#    
#!   A0_PREVIEW_IMAGE="data:image/png;base64,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"
#!   ARCGIS_COMPATIBILITY="ARCGIS_AUTO"
#!   ATTR_TYPE_ENCODING="SDF"
#!   BEGIN_PYTHON=""
#!   BEGIN_TCL=""
#!   CATEGORY=""
#!   DESCRIPTION=""
#!   DESTINATION="NONE"
#!   DESTINATION_ROUTING_FILE=""
#!   DOC_EXTENTS="6576.94 1117.89"
#!   DOC_TOP_LEFT="937.509 -1624.14"
#!   END_PYTHON=""
#!   END_TCL=""
#!   EXPLICIT_BOOKMARK_ORDER="false"
#!   FME_BUILD_NUM="23619"
#!   FME_BULK_MODE_THRESHOLD="2000"
#!   FME_DOCUMENT_GUID="c26d1a54-b8b4-4c7c-a3fc-07d9902873c4"
#!   FME_DOCUMENT_PRIORGUID="46cfd767-9e9c-4e33-b080-4849cd9597f6"
#!   FME_GEOMETRY_HANDLING="Enhanced"
#!   FME_IMPLICIT_CSMAP_REPROJECTION_MODE="Auto"
#!   FME_NAMES_ENCODING="UTF-8"
#!   FME_REPROJECTION_ENGINE="FME"
#!   FME_SERVER_SERVICES=""
#!   FME_STROKE_MAX_DEVIATION="0"
#!   HISTORY=""
#!   IGNORE_READER_FAILURE="No"
#!   LAST_SAVE_BUILD="FME(R) 2023.1.0.0 (******** - Build 23619 - WIN64)"
#!   LAST_SAVE_DATE="2024-11-28T09:11:35"
#!   LOG_FILE=""
#!   LOG_MAX_RECORDED_FEATURES="200"
#!   MARKDOWN_DESCRIPTION=""
#!   MARKDOWN_USAGE=""
#!   MAX_LOG_FEATURES="200"
#!   MULTI_WRITER_DATASET_ORDER="BY_ID"
#!   PASSWORD=""
#!   PYTHON_COMPATIBILITY="311"
#!   REDIRECT_TERMINATORS="NONE"
#!   SAVE_ON_PROMPT_AND_RUN="Yes"
#!   SHOW_ANNOTATIONS="true"
#!   SHOW_INFO_NODES="true"
#!   SOURCE="NONE"
#!   SOURCE_ROUTING_FILE=""
#!   TERMINATE_REJECTED="YES"
#!   TITLE=""
#!   USAGE=""
#!   USE_MARKDOWN=""
#!   VIEW_POSITION="2965.65 -187.502"
#!   WARN_INVALID_XFORM_PARAM="Yes"
#!   WORKSPACE_VERSION="1"
#!   ZOOM_SCALE="100"
#! >
#! <DATASETS>
#! </DATASETS>
#! <DATA_TYPES>
#! </DATA_TYPES>
#! <GEOM_TYPES>
#! </GEOM_TYPES>
#! <FEATURE_TYPES>
#! </FEATURE_TYPES>
#! <FMESERVER>
#! <READER_DATASETS>
#! <DATASET
#!   NAME="FeatureReader_2"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_2"
#!   DATASET="@Value(path_windows)"
#! />
#! <DATASET
#!   NAME="FeatureReader_4"
#!   OVERRIDE="--FeatureReaderDataset_FeatureReader_4"
#!   DATASET="@Value(path_windows)"
#! />
#! </READER_DATASETS>
#! <WRITER_DATASETS>
#! <DATASET
#!   NAME="FeatureWriter"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter"
#!   DATASET="FeatureWriter/核查结果.xlsx"
#! />
#! <DATASET
#!   NAME="FeatureWriter_2"
#!   OVERRIDE="--FeatureWriterDataset_FeatureWriter_2"
#!   DATASET="FeatureWriter_2/重叠矢量图层"
#! />
#! </WRITER_DATASETS>
#! </FMESERVER>
#! <GLOBAL_PARAMETERS>
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI STRING PARAMETER 成片开发方案名称"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 成片开发方案压缩包"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 变更调查耕地图层压缩包"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="false"
#! />
#! <GLOBAL_PARAMETER
#!   GUI_LINE="GUI OPTIONAL DIRNAME_OR_ATTR PARAMETER_2 Select a File"
#!   DEFAULT_VALUE=""
#!   IS_STAND_ALONE="true"
#! />
#! </GLOBAL_PARAMETERS>
#! <USER_PARAMETERS
#!   FORM="eyJwYXJhbWV0ZXJzIjpbeyJkZWZhdWx0VmFsdWUiOiIiLCJuYW1lIjoiUEFSQU1FVEVSIiwicHJvbXB0Ijoi5oiQ54mH5byA5Y+R5pa55qGI5ZCN56ewIiwicmVxdWlyZWQiOnRydWUsInNob3dFZGl0QnV0dG9uIjpmYWxzZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6InRleHQiLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifSx7ImFjY2Vzc01vZGUiOiJyZWFkIiwiZGVmYXVsdFZhbHVlIjoiIiwiaW5jbHVkZVdlYkJyb3dzZXIiOnRydWUsIml0ZW1zVG9TZWxlY3QiOiJmb2xkZXJzIiwibmFtZSI6ImRpciIsInByb21wdCI6IuaIkOeJh+W8gOWPkeaWueahiOWOi+e8qeWMhSIsInJlcXVpcmVkIjp0cnVlLCJzZWxlY3RNdWx0aXBsZSI6dHJ1ZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOmZhbHNlLCJ2YWx1ZVR5cGUiOiJzdHJpbmcifSx7ImFjY2Vzc01vZGUiOiJyZWFkIiwiZGVmYXVsdFZhbHVlIjoiIiwiaW5jbHVkZVdlYkJyb3dzZXIiOnRydWUsIml0ZW1zVG9TZWxlY3QiOiJmb2xkZXJzIiwibmFtZSI6ImRpcl8yIiwicHJvbXB0Ijoi5Y+Y5pu06LCD5p+l6ICV5Zyw5Zu+5bGC5Y6L57yp5YyFIiwicmVxdWlyZWQiOnRydWUsInNlbGVjdE11bHRpcGxlIjp0cnVlLCJzaG93UHJvbXB0Ijp0cnVlLCJzdXBwb3J0ZWRWYWx1ZVR5cGVzIjpbImV4cHJlc3Npb24iLCJnbG9iYWxQYXJhbWV0ZXIiXSwidHlwZSI6ImZpbGUiLCJ2YWxpZGF0ZUV4aXN0ZW5jZSI6ZmFsc2UsInZhbHVlVHlwZSI6InN0cmluZyJ9LHsiYWNjZXNzTW9kZSI6IndyaXRlIiwiYWxsb3dVUkwiOmZhbHNlLCJkZWZhdWx0VmFsdWUiOiIiLCJpdGVtc1RvU2VsZWN0IjoiZm9sZGVycyIsIm5hbWUiOiJQQVJBTUVURVJfMiIsInByb21wdCI6IlNlbGVjdCBhIEZpbGUiLCJyZXF1aXJlZCI6ZmFsc2UsInNlbGVjdE11bHRpcGxlIjpmYWxzZSwic2hvd1Byb21wdCI6dHJ1ZSwic3VwcG9ydGVkVmFsdWVUeXBlcyI6WyJleHByZXNzaW9uIiwiZ2xvYmFsUGFyYW1ldGVyIl0sInR5cGUiOiJmaWxlIiwidmFsaWRhdGVFeGlzdGVuY2UiOnRydWUsInZhbHVlVHlwZSI6InN0cmluZyJ9XX0="
#! >
#! <PARAMETER_INFO>
#!     <INFO NAME="PARAMETER" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI STRING PARAMETER 成片开发方案名称"
#! />
#!     <INFO NAME="dir" 
#!   DEFAULT_VALUE=""
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir INCLUDE_WEB_BROWSER 成片开发方案压缩包"
#! />
#!     <INFO NAME="dir_2" 
#!   DEFAULT_VALUE=""
#!   SCOPE="DEPENDENT"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI MULTIDIRECTORY_OR_ATTR dir_2 INCLUDE_WEB_BROWSER 变更调查耕地图层压缩包"
#! />
#!     <INFO NAME="PARAMETER_2" 
#!   DEFAULT_VALUE=""
#!   SCOPE="STAND_ALONE"
#!   GENERATED_GUI_LINE="true"
#!   GUI_LINE="GUI OPTIONAL DIRNAME_OR_ATTR PARAMETER_2 Select a File"
#! />
#! </PARAMETER_INFO>
#! </USER_PARAMETERS>
#! <COMMENTS>
#! </COMMENTS>
#! <CONSTANTS>
#! </CONSTANTS>
#! <BOOKMARKS>
#! </BOOKMARKS>
#! <TRANSFORMERS>
#! <TRANSFORMER
#!   IDENTIFIER="2"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="937.5093750937508 -590.63090630906299"
#!   BOUNDING_RECT="937.5093750937508 -590.63090630906299 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="3"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1550.0155001550015 -506.25506255062544"
#!   BOUNDING_RECT="1550.0155001550015 -506.25506255062544 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="5"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="2121.8962189621898 -665.63165631656318"
#!   BOUNDING_RECT="2121.8962189621898 -665.63165631656318 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="7"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="2712.5271252712523 -590.63090630906299"
#!   BOUNDING_RECT="2712.5271252712523 -590.63090630906299 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="9"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="937.5093750937508 -1458.2648625131271"
#!   BOUNDING_RECT="937.5093750937508 -1458.2648625131271 430 71"
#!   ORDER="500000000000001"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="10"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="1550.0155001550015 -1284.8255430983415"
#!   BOUNDING_RECT="1550.0155001550015 -1284.8255430983415 430 71"
#!   ORDER="500000000000002"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="PATH"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="4"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="4" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE="PATH,&quot;path_unix,path_windows,path_rootname,path_filename,path_extension,path_directory_unix,path_directory_windows,path_type,fme_geometry{0}&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE="PATH,&quot;buffer,buffer,buffer,buffer,buffer,buffer,buffer,varchar(10),fme_no_geom&quot;"/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_%PATH"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(dir_2)"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="on_parm_change"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE="PATH,"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,PATH"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="PATH_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,PATH&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,PATH_GLOB_PATTERN,&quot;OPTIONAL TEXT_ENCODED&quot;,PATH&lt;space&gt;Path&lt;space&gt;Filter:,PATH_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP PATH_EXPOSE_FORMAT_ATTRS&quot;,PATH&lt;space&gt;Schema&lt;space&gt;Attributes,PATH_RETRIEVE_FILE_PROPERTIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Retrieve&lt;space&gt;file&lt;space&gt;properties:,PATH_OFFSET_DATETIME,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_USE_NON_STRING_ATTR,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_PATH_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS PATH%Source&quot;,PATH&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:,PATH_NO_EMPTY_PROPERTIES,&quot;OPTIONAL NO_EDIT TEXT&quot;,PATH&lt;space&gt;,PATH_TYPE,&quot;OPTIONAL LOOKUP_CHOICE Any,ANY%Directory,DIRECTORY%File,FILE&quot;,PATH&lt;space&gt;Allowed&lt;space&gt;Path&lt;space&gt;Type:,PATH_HIDDEN_FILES,&quot;OPTIONAL LOOKUP_CHOICE Include,INCLUDE%Exclude,EXCLUDE&quot;,PATH&lt;space&gt;Hidden&lt;space&gt;Files&lt;space&gt;and&lt;space&gt;Folders:,PATH_RECURSE_DIRECTORIES,&quot;OPTIONAL CHOICE YES%NO&quot;,PATH&lt;space&gt;Recurse&lt;space&gt;Into&lt;space&gt;Subfolders:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="PORTS_FROM_FTTR"/>
#!     <XFORM_PARM PARM_NAME="PATH_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_GLOB_PATTERN" PARM_VALUE="*"/>
#!     <XFORM_PARM PARM_NAME="PATH_HIDDEN_FILES" PARM_VALUE="INCLUDE"/>
#!     <XFORM_PARM PARM_NAME="PATH_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_NO_EMPTY_PROPERTIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_OFFSET_DATETIME" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PATH_PATH_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATH_RECURSE_DIRECTORIES" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="PATH_RETRIEVE_FILE_PROPERTIES" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PATH_TYPE" PARM_VALUE="ANY"/>
#!     <XFORM_PARM PARM_NAME="PATH_USE_NON_STRING_ATTR" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="11"
#!   TYPE="Tester"
#!   VERSION="3"
#!   POSITION="2121.8962189621898 -1444.2021368642793"
#!   BOUNDING_RECT="2121.8962189621898 -1444.2021368642793 430 71"
#!   ORDER="500000000000003"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="PASSED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="FAILED"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="BOOL_OP" PARM_VALUE="OR"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_MSG" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMPOSITE_TEST" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="Per Output Port"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE" PARM_VALUE="TEST &lt;at&gt;Value&lt;openparen&gt;path_extension&lt;closeparen&gt; = shp"/>
#!     <XFORM_PARM PARM_NAME="TEST_CLAUSE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEST_MODE" PARM_VALUE="TEST"/>
#!     <XFORM_PARM PARM_NAME="TEST_PREVIEW_GROUP" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Tester_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="12"
#!   TYPE="FeatureReader"
#!   VERSION="14"
#!   POSITION="2712.5271252712523 -1369.2013868567792"
#!   BOUNDING_RECT="2712.5271252712523 -1369.2013868567792 430 71"
#!   ORDER="500000000000004"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="&lt;SCHEMA&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_feature_type_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.fme_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="attribute{}.native_data_type" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_short_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_format_long_name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="fme_schema_handling" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="&lt;OTHER&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <OUTPUT_FEAT NAME="INITIATOR"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <XFORM_ATTR ATTR_NAME="_matched_records" IS_USER_CREATED="false" FEAT_INDEX="2" />
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="path_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_rootname" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_filename" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_extension" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_unix" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_directory_windows" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="path_type" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_geometry{0}" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="_reader_error" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_EXPOSE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="Only Use Result"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_IGNORE_NULLS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="AVAILABLE_FEATURE_TYPES" PARM_VALUE="_FEATUREREADER_OPTIONAL_FTTR_"/>
#!     <XFORM_PARM PARM_NAME="CACHE_TIMEOUT_HRS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COMBINE_GEOM" PARM_VALUE="Use Result"/>
#!     <XFORM_PARM PARM_NAME="CONSTRAINTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="&lt;at&gt;Value&lt;openparen&gt;path_windows&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ENABLE_CACHE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="FEATURETYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORCE_REFRESH_OUTPUTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_EXPOSE_ATTRS_GROUP,&quot;OPTIONAL DISCLOSUREGROUP SHAPEFILE_EXPOSE_FORMAT_ATTRS&quot;,SHAPEFILE&lt;space&gt;Schema&lt;space&gt;Attributes,SHAPEFILE_USE_SEARCH_ENVELOPE,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP SEARCH_ENVELOPE_MINX%SEARCH_ENVELOPE_MINY%SEARCH_ENVELOPE_MAXX%SEARCH_ENVELOPE_MAXY%SEARCH_ENVELOPE_COORDINATE_SYSTEM%CLIP_TO_ENVELOPE%SEARCH_METHOD%SEARCH_METHOD_FILTER%SEARCH_ORDER%SEARCH_FEATURE%DUMMY_SEARCH_ENVELOPE_PARAMETER&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Search&lt;space&gt;Envelope,SHAPEFILE_REPORT_BAD_GEOMETRY,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Report&lt;space&gt;Geometry&lt;space&gt;Anomalies,SHAPEFILE_READ_NUMERIC_PADDING_AS,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Zero (0)&quot;&quot;,ZERO%Blank,BLANK&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Fully&lt;space&gt;Padded&lt;space&gt;Numeric&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-source-encoding%UTF-8%ISO*%Big5%ibm*%Shift_JIS%GB2312%GBK%win*%KSC_5601%macintosh%x-mac*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Measures&lt;space&gt;as&lt;space&gt;Elevation,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP TRIM_PRECEDING_SPACES%READ_NUMERIC_PADDING_AS%READ_BLANK_AS%DONUT_DETECTION%MEASURES_AS_Z%REPORT_BAD_GEOMETRY%USE_STRING_FOR_NUMERIC_STORAGE&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_TRIM_PRECEDING_SPACES,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Trim&lt;space&gt;Preceding&lt;space&gt;Spaces,SHAPEFILE_DONUT_DETECTION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Orientation Only&quot;&quot;,ORIENTATION%&quot;&quot;Orientation and Spatial Relationship&quot;&quot;,SPATIAL&quot;,SHAPEFILE&lt;space&gt;Donut&lt;space&gt;Geometry&lt;space&gt;Detection,SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING,&quot;OPTIONAL LOOKUP_CHOICE Standard&lt;space&gt;Types,STANDARD_TYPES%Explicit&lt;space&gt;Width&lt;space&gt;and&lt;space&gt;Precision,EXPLICIT_WIDTH&quot;,SHAPEFILE&lt;space&gt;Numeric&lt;space&gt;Attribute&lt;space&gt;Type&lt;space&gt;Handling,SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Floating&lt;space&gt;Point&lt;space&gt;Values&lt;space&gt;as&lt;space&gt;Strings:,SHAPEFILE_READ_BLANK_AS,&quot;OPTIONAL LOOKUP_CHOICE Missing,MISSING%Null,NULL&quot;,SHAPEFILE&lt;space&gt;Read&lt;space&gt;Blank&lt;space&gt;Fields&lt;space&gt;as:,SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS,&quot;OPTIONAL LITERAL EXPOSED_ATTRS SHAPEFILE%Source&quot;,SHAPEFILE&lt;space&gt;Additional&lt;space&gt;Attributes&lt;space&gt;to&lt;space&gt;Expose:"/>
#!     <XFORM_PARM PARM_NAME="FTTR_SEPARATOR" PARM_VALUE="SPACE"/>
#!     <XFORM_PARM PARM_NAME="GENERIC_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="INTERACT" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="MAX_FEATURES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MERGE_HANDLING_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="ORDER_RESULTS" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_DISPLAY" PARM_VALUE="Schema and Data Features"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_FEATURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="SINGLE_PORT"/>
#!     <XFORM_PARM PARM_NAME="PORTS_FROM_FTTR" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="READER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="READ_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DONUT_DETECTION" PARM_VALUE="ORIENTATION"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="fme-source-encoding"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_EXPOSE_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NUMERIC_TYPE_ATTRIBUTE_HANDLING" PARM_VALUE="STANDARD_TYPES"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_BLANK_AS" PARM_VALUE="MISSING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_READ_NUMERIC_PADDING_AS" PARM_VALUE="ZERO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REPORT_BAD_GEOMETRY" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SHAPEFILE_EXPOSE_FORMAT_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_TRIM_PRECEDING_SPACES" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_SEARCH_ENVELOPE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_USE_STRING_FOR_NUMERIC_STORAGE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SINGLE_PORT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SUPPORTED_SPATIAL_INTERACTIONS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WHERE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureReader_4"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="16"
#!   TYPE="Clipper"
#!   VERSION="16"
#!   POSITION="4021.9152191521916 -1125.0112501125013"
#!   BOUNDING_RECT="4021.9152191521916 -1125.0112501125013 430 71"
#!   ORDER="500000000000005"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="INSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="OUTSIDE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <OUTPUT_FEAT NAME="REMNANTS"/>
#!     <FEAT_COLLAPSED COLLAPSED="2"/>
#!     <OUTPUT_FEAT NAME="&lt;REJECTED&gt;"/>
#!     <FEAT_COLLAPSED COLLAPSED="3"/>
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_code" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_ATTR ATTR_NAME="fme_rejection_message" IS_USER_CREATED="false" FEAT_INDEX="3" />
#!     <XFORM_PARM PARM_NAME="ADD_NODATA" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="ADVANCED_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ATTR_ACCUM_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="ATTR_CONFLICT_RES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BOUNDARY_CASES" PARM_VALUE="INSIDE"/>
#!     <XFORM_PARM PARM_NAME="CLEANING_TOLERANCE" PARM_VALUE="0.001"/>
#!     <XFORM_PARM PARM_NAME="CLIPPED_ATTR" PARM_VALUE="_clipped"/>
#!     <XFORM_PARM PARM_NAME="CLIPPERS_FIRST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="CLIPPER_PREFIX" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="CONNECT_Z_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GENERATE_LIST" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="INVALID_PARTS_HANDLING" PARM_VALUE="REJECT_WHOLE"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_ATTRS_TO_INCLUDE_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="LIST_NAME" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="MEASURES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="MISSING_M_MODE" PARM_VALUE="Linear Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MISSING_Z_MODE" PARM_VALUE="Planar Interpolation"/>
#!     <XFORM_PARM PARM_NAME="MULTIPLE_CLIPPERS" PARM_VALUE="YES"/>
#!     <XFORM_PARM PARM_NAME="M_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OVERLAPPING_CLIPPERS" PARM_VALUE="CLIP_OUTSIDE"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_FEATURE_ORDER" PARM_VALUE="PER_OUTPUT_PORT"/>
#!     <XFORM_PARM PARM_NAME="PRESERVE_RASTER_EXTENTS" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_MODE" PARM_VALUE="CENTER"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VECTOR_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Clipper"/>
#!     <XFORM_PARM PARM_NAME="Z_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="Z_VAL_SOURCE" PARM_VALUE="CANDIDATE"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="19"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="3356.2835628356279 -906.25906259062594"
#!   BOUNDING_RECT="3356.2835628356279 -906.25906259062594 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="22"
#!   TYPE="Reprojector"
#!   VERSION="5"
#!   POSITION="3356.2835628356279 -1369.2013868567792"
#!   BOUNDING_RECT="3356.2835628356279 -1369.2013868567792 430 71"
#!   ORDER="500000000000006"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="REPROJECTED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_PARM PARM_NAME="DEST" PARM_VALUE="EPSG:4528"/>
#!     <XFORM_PARM PARM_NAME="INTERPOLATION_TYPE_NAME" PARM_VALUE="Nearest Neighbor"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_CELL_SIZE" PARM_VALUE="Preserve Cells"/>
#!     <XFORM_PARM PARM_NAME="RASTER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="RASTER_TOLERANCE" PARM_VALUE="0.0"/>
#!     <XFORM_PARM PARM_NAME="SOURCE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Reprojector_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="25"
#!   TYPE="AreaCalculator"
#!   VERSION="5"
#!   POSITION="4618.7961879618815 -1290.6379063790641"
#!   BOUNDING_RECT="4618.7961879618815 -1290.6379063790641 430 71"
#!   ORDER="500000000000007"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="AREA_ATTR" PARM_VALUE="_area"/>
#!     <XFORM_PARM PARM_NAME="AREA_TYPE" PARM_VALUE="Plane Area"/>
#!     <XFORM_PARM PARM_NAME="MULT" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AreaCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="27"
#!   TYPE="StatisticsCalculator"
#!   VERSION="11"
#!   POSITION="5209.4270942709436 -1125.0112501125013"
#!   BOUNDING_RECT="5209.4270942709436 -1125.0112501125013 430 71"
#!   ORDER="500000000000008"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <OUTPUT_FEAT NAME="COMPLETE"/>
#!     <FEAT_COLLAPSED COLLAPSED="1"/>
#!     <XFORM_ATTR ATTR_NAME="_clipped" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="1" />
#!     <XFORM_PARM PARM_NAME="ADV_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="ADV_PARAMETERMAP_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CALCULATION_MODE" PARM_VALUE="NUMERIC"/>
#!     <XFORM_PARM PARM_NAME="CUMULATIVE_STATS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="FEAT_STATS" PARM_VALUE="_area,NUMERIC_MODE,,,,SUM,,,,,,,,,"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PREPEND_ATTR_NAME_OBS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATISTIC_SUFFIX_RENAME_MAP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="STATS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="StatisticsCalculator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="29"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="5759.4325943259428 -1185.0112501125013"
#!   BOUNDING_RECT="5759.4325943259428 -1185.0112501125013 430 71"
#!   ORDER="500000000000009"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="方案名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用耕地面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; &lt;u65b9&gt;&lt;u6848&gt;&lt;u540d&gt;&lt;u79f0&gt; SET_TO $(PARAMETER) varchar&lt;openparen&gt;200&lt;closeparen&gt;  &lt;u5360&gt;&lt;u7528&gt;&lt;u8015&gt;&lt;u5730&gt;&lt;u9762&gt;&lt;u79ef&gt;&lt;uff08&gt;&lt;u4ea9&gt;&lt;uff09&gt; SET_TO &lt;at&gt;Evaluate&lt;openparen&gt;&lt;at&gt;Value&lt;openparen&gt;_area.sum&lt;closeparen&gt;*0.0015&lt;closeparen&gt; varchar&lt;openparen&gt;200&lt;closeparen&gt;"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="31"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="7084.4458444584434 -978.13478134781371"
#!   BOUNDING_RECT="7084.4458444584434 -978.13478134781371 430 71"
#!   ORDER="500000000000010"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;$(PARAMETER)&lt;u6838&gt;&lt;u67e5&gt;&lt;u7ed3&gt;&lt;u679c&gt;.xlsx"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,OVERWRITE_FILE&lt;comma&gt;No&lt;comma&gt;TEMPLATEFILE&lt;comma&gt;&lt;comma&gt;TEMPLATE_SHEET&lt;comma&gt;&lt;comma&gt;REMOVE_UNCHANGED_TEMPLATE_SHEET&lt;comma&gt;No&lt;comma&gt;MULTIPLE_TEMPLATE_SHEETS&lt;comma&gt;Yes&lt;comma&gt;INSERT_IGNORE_DB_OP&lt;comma&gt;Yes&lt;comma&gt;DROP_TABLE&lt;comma&gt;No&lt;comma&gt;TRUNCATE_TABLE&lt;comma&gt;No&lt;comma&gt;FIELD_NAMES_OUT&lt;comma&gt;Yes&lt;comma&gt;FIELD_NAMES_FORMATTING&lt;comma&gt;Yes&lt;comma&gt;WRITER_MODE&lt;comma&gt;Insert&lt;comma&gt;RASTER_FORMAT&lt;comma&gt;PNG&lt;comma&gt;PROTECT_SHEET&lt;comma&gt;NO&lt;comma&gt;PROTECT_SHEET_PASSWORD&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_LEVEL&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;PROTECT_SHEET_PERMISSIONS&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING&lt;comma&gt;yes&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;COORDINATE_SYSTEM_GRANULARITY&lt;comma&gt;FEATURE&lt;comma&gt;CUSTOM_NUMBER_FORMATTING&lt;comma&gt;ENABLE_NATIVE&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,XLSXW"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="XLSXW_INSERT_IGNORE_DB_OP,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_RASTER_FORMAT,&quot;OPTIONAL CHOICE BMP%JPEG%PNG&quot;,XLSXW&lt;space&gt;Raster&lt;space&gt;Format:,XLSXW_OVERWRITE_FILE,&quot;OPTIONAL ACTIVECHOICE Yes%No,TEMPLATEFILE,TEMPLATE_SHEET,REMOVE_UNCHANGED_TEMPLATE_SHEET&quot;,XLSXW&lt;space&gt;Overwrite&lt;space&gt;Existing&lt;space&gt;File:,XLSXW_COORDINATE_SYSTEM_GRANULARITY,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_FIELD_NAMES_OUT,&quot;OPTIONAL ACTIVECHOICE Yes%No,FIELD_NAMES_FORMATTING,++FIELD_NAMES_FORMATTING+No&quot;,XLSXW&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:,XLSXW_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,XLSXW&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,XLSXW_TRUNCATE_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_PROTECT_SHEET,&quot;OPTIONAL ACTIVEDISCLOSUREGROUP PROTECT_SHEET_PASSWORD%PROTECT_SHEET_LEVEL%PROTECT_SHEET_PERMISSIONS&quot;,XLSXW&lt;space&gt;Protect&lt;space&gt;Sheet,XLSXW_MULTIPLE_TEMPLATE_SHEETS,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_WRITER_MODE,&quot;OPTIONAL CHOICE Insert%Update%Delete&quot;,XLSXW&lt;space&gt;Default&lt;space&gt;Feature&lt;space&gt;Type&lt;space&gt;Writer&lt;space&gt;Mode:,XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_DROP_TABLE,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheets&lt;solidus&gt;Named&lt;space&gt;Ranges:,XLSXW_FIELD_NAMES_FORMATTING,&quot;OPTIONAL CHOICE Yes%No&quot;,XLSXW&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:,XLSXW_CUSTOM_NUMBER_FORMATTING,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;,XLSXW_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,XLSXW&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="sheet1:Output,ftp_feature_type_name,sheet1,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,1,ftp_user_attributes,&lt;lt&gt;u65b9&lt;gt&gt;&lt;lt&gt;u6848&lt;gt&gt;&lt;lt&gt;u540d&lt;gt&gt;&lt;lt&gt;u79f0&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;50&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;14&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;&lt;lt&gt;u5360&lt;gt&gt;&lt;lt&gt;u7528&lt;gt&gt;&lt;lt&gt;u8015&lt;gt&gt;&lt;lt&gt;u5730&lt;gt&gt;&lt;lt&gt;u9762&lt;gt&gt;&lt;lt&gt;u79ef&lt;gt&gt;&lt;lt&gt;uff08&lt;gt&gt;&lt;lt&gt;u4ea9&lt;gt&gt;&lt;lt&gt;uff09&lt;gt&gt;&lt;comma&gt;string&lt;lt&gt;openparen&lt;gt&gt;30&lt;lt&gt;comma&lt;gt&gt;version&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;1&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;number_format_string&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u5b8b&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;u4f53&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;14&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;font_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;background_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_color&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;pattern_style&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_border_formatting&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_COLOR&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;lt&lt;lt&gt;gt&lt;gt&gt;comma&lt;lt&gt;lt&lt;gt&gt;gt&lt;lt&gt;gt&lt;gt&gt;0&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;CELL_BORDER_STYLE&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;BORDERSTYLE_THIN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_OPEN&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;horizontal_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;vertical_alignment&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;center&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;indent&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_orientation&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;text_control&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;cell_protection&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;FME_DISCLOSURE_CLOSED&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;hide_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;lock_cells&lt;lt&gt;lt&lt;gt&gt;semicolon&lt;lt&gt;gt&lt;gt&gt;&lt;lt&gt;closeparen&lt;gt&gt;,ftp_user_attribute_values,&lt;comma&gt;,ftp_format_parameters,xlsx_layer_group&lt;comma&gt;&lt;comma&gt;xlsx_truncate_group&lt;comma&gt;&lt;comma&gt;xlsx_rowcolumn_group&lt;comma&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;NO&lt;comma&gt;xlsx_template_group&lt;comma&gt;FME_DISCLOSURE_CLOSED&lt;comma&gt;xlsx_advanced_group&lt;comma&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;No"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_xml&lt;comma&gt;&lt;quote&gt;string&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_json&lt;comma&gt;&lt;quote&gt;auto&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;&lt;quote&gt;datetime&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_datetime&lt;comma&gt;&lt;quote&gt;time&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_time&lt;comma&gt;&lt;quote&gt;date&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_date&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;boolean&lt;openparen&gt;width&lt;comma&gt;xlsx_col_props&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;openbracket&gt;&lt;closebracket&gt;*&lt;backslash&gt;&lt;backslash&gt;?:&lt;apos&gt;,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none&lt;comma&gt;fme_no_geom&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point&lt;comma&gt;xlsx_point&lt;comma&gt;fme_point&lt;comma&gt;xlsx_none&lt;comma&gt;fme_line&lt;comma&gt;xlsx_none&lt;comma&gt;fme_polygon&lt;comma&gt;xlsx_none&lt;comma&gt;fme_text&lt;comma&gt;xlsx_none&lt;comma&gt;fme_ellipse&lt;comma&gt;xlsx_none&lt;comma&gt;fme_arc&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;xlsx_none&lt;comma&gt;fme_collection&lt;comma&gt;xlsx_none&lt;comma&gt;fme_surface&lt;comma&gt;xlsx_none&lt;comma&gt;fme_solid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_raster&lt;comma&gt;xlsx_none&lt;comma&gt;fme_point_cloud&lt;comma&gt;xlsx_none&lt;comma&gt;fme_voxel_grid&lt;comma&gt;xlsx_none&lt;comma&gt;fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;xlsx_layer_group&lt;space&gt;xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group&lt;space&gt;Sheet&lt;space&gt;Settings&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_truncate_group&lt;space&gt;xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet&lt;space&gt;Drop&lt;solidus&gt;Truncate&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_rowcolumn_group&lt;space&gt;xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Position&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVEDISCLOSUREGROUP&lt;space&gt;xlsx_protect_sheet&lt;space&gt;xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions&lt;space&gt;Protect&lt;space&gt;Sheet&lt;quote&gt;&lt;comma&gt;NO&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_template_group&lt;space&gt;xlsx_template_sheet%xlsx_remove_unchanged_template_sheet&lt;space&gt;Template&lt;space&gt;Options&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;DISCLOSUREGROUP&lt;space&gt;xlsx_advanced_group&lt;space&gt;xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type&lt;space&gt;Advanced&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_drop_sheet&lt;space&gt;Yes%No&lt;space&gt;Drop&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_trunc_sheet&lt;space&gt;Yes%No&lt;space&gt;Truncate&lt;space&gt;Existing&lt;space&gt;Sheet&lt;solidus&gt;Named&lt;space&gt;Range:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_sheet_order&lt;space&gt;1%MAX&lt;space&gt;Sheet&lt;space&gt;Order&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;RANGE_SLIDER&lt;space&gt;xlsx_freeze_end_row&lt;space&gt;1%MAX&lt;space&gt;Freeze&lt;space&gt;First&lt;space&gt;Row&lt;openparen&gt;s&lt;closeparen&gt;&lt;space&gt;&lt;openparen&gt;1&lt;space&gt;-&lt;space&gt;n&lt;closeparen&gt;:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_field_names_out&lt;space&gt;Yes%No&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;++xlsx_field_names_formatting+No&lt;space&gt;Output&lt;space&gt;Field&lt;space&gt;Names:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_field_names_formatting&lt;space&gt;Yes%No&lt;space&gt;Format&lt;space&gt;Field&lt;space&gt;Names&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;Yes&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_names_are_positions&lt;space&gt;Yes%No&lt;space&gt;Use&lt;space&gt;Attribute&lt;space&gt;Names&lt;space&gt;As&lt;space&gt;Column&lt;space&gt;Positions:&lt;quote&gt;&lt;comma&gt;No&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_start_col&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_start_row&lt;space&gt;Named&lt;space&gt;Range&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT&lt;space&gt;xlsx_offset_col&lt;space&gt;Start&lt;space&gt;Column:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;INTEGER&lt;space&gt;xlsx_offset_row&lt;space&gt;Start&lt;space&gt;Row:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_raster_type&lt;space&gt;BMP%JPEG%PNG&lt;space&gt;Raster&lt;space&gt;Format:&lt;quote&gt;&lt;comma&gt;PNG&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;PASSWORD_ENCODED&lt;space&gt;xlsx_protect_sheet_password&lt;space&gt;Password:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE_LOOKUP&lt;space&gt;xlsx_protect_sheet_level&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_DEFAULT&lt;comma&gt;xlsx_protect_sheet_permissions%View&lt;lt&gt;space&lt;gt&gt;Only&lt;lt&gt;space&lt;gt&gt;Permissions&lt;comma&gt;PROT_ALL&lt;comma&gt;xlsx_protect_sheet_permissions%Specific&lt;lt&gt;space&lt;gt&gt;Permissions&lt;space&gt;Protection&lt;space&gt;Level:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;LOOKUP_LISTBOX&lt;space&gt;xlsx_protect_sheet_permissions&lt;space&gt;Select&lt;lt&gt;space&lt;gt&gt;locked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_LOCKED_CELLS%Select&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_SEL_UNLOCKED_CELLS%Format&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_FORMAT_CELLS%Format&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_FORMAT_COLUMNS%Format&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_FORMAT_ROWS%Insert&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_INSERT_COLUMNS%Insert&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_INSERT_ROWS%Add&lt;lt&gt;space&lt;gt&gt;hyperlinks&lt;lt&gt;space&lt;gt&gt;to&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_INSERT_HYPERLINKS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;columns&lt;comma&gt;PROT_DELETE_COLUMNS%Delete&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;rows&lt;comma&gt;PROT_DELETE_ROWS%Sort&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;solidus&gt;rows&lt;solidus&gt;columns&lt;comma&gt;PROT_SORT%Use&lt;lt&gt;space&lt;gt&gt;Autofilter&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_AUTOFILTER%Use&lt;lt&gt;space&lt;gt&gt;PivotTable&lt;lt&gt;space&lt;gt&gt;&lt;amp&gt;&lt;lt&gt;space&lt;gt&gt;PivotChart&lt;lt&gt;space&lt;gt&gt;on&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;cells&lt;comma&gt;PROT_PIVOTTABLES%Edit&lt;lt&gt;space&lt;gt&gt;unlocked&lt;lt&gt;space&lt;gt&gt;objects&lt;comma&gt;PROT_OBJECTS%Edit&lt;lt&gt;space&lt;gt&gt;unprotected&lt;lt&gt;space&lt;gt&gt;scenarios&lt;comma&gt;PROT_SCENARIOS&lt;space&gt;Specific&lt;space&gt;Permissions:&lt;quote&gt;&lt;comma&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;ACTIVECHOICE&lt;space&gt;xlsx_table_writer_mode&lt;space&gt;Insert&lt;comma&gt;+xlsx_row_id_column+&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;%Update&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id%Delete&lt;comma&gt;+xlsx_row_id_column+xlsx_row_id&lt;space&gt;Writer&lt;space&gt;Mode:&lt;quote&gt;&lt;comma&gt;Insert&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;ATTR&lt;space&gt;xlsx_row_id_column&lt;space&gt;ALLOW_NEW&lt;space&gt;Row&lt;space&gt;Number&lt;space&gt;Attribute:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;OPTIONAL&lt;space&gt;TEXT_EDIT&lt;space&gt;xlsx_template_sheet&lt;space&gt;Template&lt;space&gt;Sheet:&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;CHOICE&lt;space&gt;xlsx_remove_unchanged_template_sheet&lt;space&gt;Yes%No&lt;space&gt;Remove&lt;space&gt;Template&lt;space&gt;Sheet&lt;space&gt;if&lt;space&gt;Unchanged:&lt;quote&gt;&lt;comma&gt;No,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;xlsx_drop_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_trunc_sheet&lt;comma&gt;No&lt;comma&gt;xlsx_sheet_order&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_freeze_end_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_names_are_positions&lt;comma&gt;No&lt;comma&gt;xlsx_field_names_out&lt;comma&gt;Yes&lt;comma&gt;xlsx_field_names_formatting&lt;comma&gt;Yes&lt;comma&gt;xlsx_start_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_start_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_col&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_offset_row&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_raster_type&lt;comma&gt;PNG&lt;comma&gt;xlsx_table_writer_mode&lt;comma&gt;Insert&lt;comma&gt;xlsx_row_id_column&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;NO&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_level&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_password&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_protect_sheet_permissions&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;lt&gt;Unused&lt;gt&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;&lt;comma&gt;xlsx_remove_unchanged_template_sheet&lt;comma&gt;&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;No&lt;quote&gt;&lt;quote&gt;&lt;quote&gt;,WRITER_FORMAT_PARAMETER,DEFAULT_READER&lt;comma&gt;XLSXR&lt;comma&gt;ALLOW_DATASET_CONFLICT&lt;comma&gt;YES&lt;comma&gt;MIME_TYPE&lt;comma&gt;&lt;quote&gt;application&lt;solidus&gt;vnd.openxmlformats-officedocument.spreadsheetml.sheet&lt;space&gt;ADD_DISPOSITION&lt;quote&gt;&lt;comma&gt;ATTRIBUTE_READING&lt;comma&gt;DEFLINE&lt;comma&gt;DEFAULT_ATTR_TYPE&lt;comma&gt;auto&lt;comma&gt;USER_ATTRIBUTES_COLUMNS&lt;comma&gt;&lt;quote&gt;Width&lt;comma&gt;Cell&lt;space&gt;Width%Precision&lt;comma&gt;Formatting&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Sheet&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Sheet1&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;name&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Microsoft&lt;space&gt;Excel&lt;space&gt;file&lt;quote&gt;,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_COORDINATE_SYSTEM_GRANULARITY" PARM_VALUE="FEATURE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_CUSTOM_NUMBER_FORMATTING" PARM_VALUE="ENABLE_NATIVE"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_DROP_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_FORMATTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_FIELD_NAMES_OUT" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_INSERT_IGNORE_DB_OP" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_MULTIPLE_TEMPLATE_SHEETS" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XLSXW_OVERWRITE_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_PROTECT_SHEET" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_RASTER_FORMAT" PARM_VALUE="PNG"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING" PARM_VALUE="yes"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_TRUNCATE_TABLE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="XLSXW_WRITER_MODE" PARM_VALUE="Insert"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="33"
#!   TYPE="ExcelStyler"
#!   VERSION="1"
#!   POSITION="6478.1897818978186 -1125.0112501125013"
#!   BOUNDING_RECT="6478.1897818978186 -1125.0112501125013 430 71"
#!   ORDER="500000000000011"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="ExcelStyled"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="方案名称" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="占用耕地面积（亩）" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_area.sum" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="xlsx_row_formatting" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRS_TO_STYLE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="BACKGROUND_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_COLOR" PARM_VALUE="0,0,0"/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="CELL_BORDER_STYLE" PARM_VALUE="BORDERSTYLE_THIN"/>
#!     <XFORM_PARM PARM_NAME="CELL_PROTECTION_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FILL_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FONT_NAME" PARM_VALUE="&lt;u5b8b&gt;&lt;u4f53&gt;&lt;comma&gt;14"/>
#!     <XFORM_PARM PARM_NAME="HIDDEN" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="HORIZONTAL_ALIGNMENT" PARM_VALUE="center"/>
#!     <XFORM_PARM PARM_NAME="INDENT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="LOCKED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NUMBER_FORMAT_STRING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NUMBER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATTERN_COLOR" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PATTERN_STYLE" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ROW_HEIGHT" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ROW_OR_CELL" PARM_VALUE="row"/>
#!     <XFORM_PARM PARM_NAME="SELECTION_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEXT_ALIGNMENT_GROUP" PARM_VALUE="FME_DISCLOSURE_OPEN"/>
#!     <XFORM_PARM PARM_NAME="TEXT_CONTROL" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TEXT_ORIENTATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="VERTICAL_ALIGNMENT" PARM_VALUE="center"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="ExcelStyler"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="32"
#!   TYPE="FeatureWriter"
#!   VERSION="0"
#!   POSITION="7068.8206882068807 -1553.1405314053138"
#!   BOUNDING_RECT="7068.8206882068807 -1553.1405314053138 430 71"
#!   ORDER="500000000000012"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="SUMMARY"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.count" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_feature_types{}.name" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_dataset" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_total_features_written" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="DATASET" PARM_VALUE="$(PARAMETER_2)&lt;backslash&gt;$(PARAMETER)&lt;u91cd&gt;&lt;u53e0&gt;&lt;u77e2&gt;&lt;u91cf&gt;&lt;u56fe&gt;&lt;u5c42&gt;"/>
#!     <XFORM_PARM PARM_NAME="DATASET_ATTR" PARM_VALUE="_dataset"/>
#!     <XFORM_PARM PARM_NAME="DYNGROUP_0" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPES_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="FEATURE_TYPE_LIST_ATTR" PARM_VALUE="_feature_types"/>
#!     <XFORM_PARM PARM_NAME="FORMAT" PARM_VALUE="SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_DIRECTIVES" PARM_VALUE="RUNTIME_MACROS,ENCODING&lt;comma&gt;utf-8&lt;comma&gt;SPATIAL_INDEXES&lt;comma&gt;NONE&lt;comma&gt;COMPRESSED_FILE&lt;comma&gt;No&lt;comma&gt;DIMENSION&lt;comma&gt;AUTO&lt;comma&gt;DATETIME_STORAGE&lt;comma&gt;TIMESTAMP_STRING&lt;comma&gt;ADVANCED&lt;comma&gt;&lt;comma&gt;SURFACE_SOLID_STORAGE&lt;comma&gt;PATCH&lt;comma&gt;MEASURES_AS_Z&lt;comma&gt;No&lt;comma&gt;DATASET_SPLITTING&lt;comma&gt;Yes&lt;comma&gt;ENFORCE_ATTRIBUTE_LIMIT&lt;comma&gt;No&lt;comma&gt;DESTINATION_DATASETTYPE_VALIDATION&lt;comma&gt;Yes&lt;comma&gt;REQUIRE_FIRST_ATTRNAME_ALPHA&lt;comma&gt;Yes&lt;comma&gt;PERMIT_NONASCII_FIRST_ATTRNAME&lt;comma&gt;Yes&lt;comma&gt;NETWORK_AUTHENTICATION&lt;comma&gt;,METAFILE,SHAPEFILE"/>
#!     <XFORM_PARM PARM_NAME="FORMAT_PARAMS" PARM_VALUE="SHAPEFILE_DIMENSION,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;Dimension From First Feature&quot;&quot;,AUTO%&quot;&quot;2D&quot;&quot;,2D%&quot;&quot;2D + Measures&quot;&quot;,2DM%&quot;&quot;3D + Measures&quot;&quot;,3DM&quot;,SHAPEFILE&lt;space&gt;Output&lt;space&gt;Dimension,SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Enforce&lt;space&gt;Number&lt;space&gt;of&lt;space&gt;Attributes&lt;space&gt;Limit,SHAPEFILE_DATETIME_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE &quot;&quot;As String &lt;openparen&gt;FME Datetime Format&lt;closeparen&gt;&quot;&quot;,TIMESTAMP_STRING%&quot;&quot;Date &lt;openparen&gt;YYYYMMDD only&lt;closeparen&gt;&quot;&quot;,DATE_ONLY&quot;,SHAPEFILE&lt;space&gt;Datetime&lt;space&gt;Type&lt;space&gt;Storage,SHAPEFILE_NETWORK_AUTHENTICATION,&quot;OPTIONAL AUTHENTICATOR CONTAINER%ACTIVEDISCLOSUREGROUP%CONTAINER_TITLE%Use Network Authentication%PROMPT_TYPE%NETWORK&quot;,SHAPEFILE&lt;space&gt;Use&lt;space&gt;Network&lt;space&gt;Authentication,SHAPEFILE_SURFACE_SOLID_STORAGE,&quot;OPTIONAL LOOKUP_CHOICE Multipatch,PATCH%Polygon,POLYGON&quot;,SHAPEFILE&lt;space&gt;Surface&lt;space&gt;and&lt;space&gt;Solid&lt;space&gt;Storage,SHAPEFILE_DATASET_SPLITTING,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Split&lt;space&gt;Dataset&lt;space&gt;into&lt;space&gt;2GB&lt;space&gt;Files,SHAPEFILE_ENCODING,&quot;OPTIONAL STRING_OR_ENCODING fme-system%*&quot;,SHAPEFILE&lt;space&gt;Character&lt;space&gt;Encoding,SHAPEFILE_MEASURES_AS_Z,&quot;OPTIONAL CHOICE Yes%No&quot;,SHAPEFILE&lt;space&gt;Treat&lt;space&gt;Elevation&lt;space&gt;as&lt;space&gt;Measures,SHAPEFILE_ADVANCED,&quot;OPTIONAL DISCLOSUREGROUP SURFACE_SOLID_STORAGE%MEASURES_AS_Z%DATASET_SPLITTING%ENFORCE_ATTRIBUTE_LIMIT&quot;,SHAPEFILE&lt;space&gt;Advanced,SHAPEFILE_COMPRESSED_FILE,&quot;OPTIONAL CHECKBOX Yes%No&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Compressed&lt;space&gt;Shapefile&lt;space&gt;&lt;openparen&gt;.shz&lt;closeparen&gt;,SHAPEFILE_SPATIAL_INDEXES,&quot;OPTIONAL LOOKUP_CHOICE None,NONE%&quot;&quot;ArcGIS Compatible Spatial Index (.sbn)&quot;&quot;,SBN%&quot;&quot;QGIS and MapServer Spatial Index (.qix)&quot;&quot;,QIX%&quot;&quot;Both ArcGIS and QGIS/MapServer Compatible (.sbn&lt;space&gt;and&lt;space&gt;.qix)&quot;&quot;,BOTH&quot;,SHAPEFILE&lt;space&gt;Create&lt;space&gt;Spatial&lt;space&gt;Index:,SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;,SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION,&quot;OPTIONAL NO_EDIT TEXT&quot;,SHAPEFILE&lt;space&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_BY_MODE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="GROUP_PROCESSING_GROUP" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="MORE_SUMMARY_ATTRS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="NO_OUTPUT_PORTS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="OUTPUTPORTS_GROUP" PARM_VALUE="FME_DISCLOSURE_CLOSED"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS" PARM_VALUE="&quot;&quot;"/>
#!     <XFORM_PARM PARM_NAME="OUTPUT_PORTS_MODE" PARM_VALUE="NO_OUTPUT_PORTS"/>
#!     <XFORM_PARM PARM_NAME="PER_EACH_INPUT" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SELECTED_PORTS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ADVANCED" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_COMPRESSED_FILE" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATASET_SPLITTING" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DATETIME_STORAGE" PARM_VALUE="TIMESTAMP_STRING"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DESTINATION_DATASETTYPE_VALIDATION" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_DIMENSION" PARM_VALUE="AUTO"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENCODING" PARM_VALUE="utf-8"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_ENFORCE_ATTRIBUTE_LIMIT" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_MEASURES_AS_Z" PARM_VALUE="No"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_NETWORK_AUTHENTICATION" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_PERMIT_NONASCII_FIRST_ATTRNAME" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_REQUIRE_FIRST_ATTRNAME_ALPHA" PARM_VALUE="Yes"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SPATIAL_INDEXES" PARM_VALUE="NONE"/>
#!     <XFORM_PARM PARM_NAME="SHAPEFILE_SURFACE_SOLID_STORAGE" PARM_VALUE="PATCH"/>
#!     <XFORM_PARM PARM_NAME="SUMMARY_ATTRS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TOTAL_FEATURES_WRITTEN_ATTR" PARM_VALUE="_total_features_written"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_DIRECTIVES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_FEATURE_TYPE_PARAMS" PARM_VALUE="&lt;dollar&gt;&lt;openparen&gt;PARAMETER&lt;closeparen&gt;&lt;u91cd&gt;&lt;u53e0&gt;&lt;u77e2&gt;&lt;u91cf&gt;&lt;u56fe&gt;&lt;u5c42&gt;:Output,ftp_feature_type_name_exp,$(PARAMETER)&lt;u91cd&gt;&lt;u53e0&gt;&lt;u77e2&gt;&lt;u91cf&gt;&lt;u56fe&gt;&lt;u5c42&gt;,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,&lt;lt&gt;lt&lt;gt&gt;Unused&lt;lt&gt;gt&lt;gt&gt;,ftp_attribute_source,0,ftp_user_attributes,Q__clipped&lt;comma&gt;varchar&lt;lt&gt;openparen&lt;gt&gt;3&lt;lt&gt;closeparen&lt;gt&gt;&lt;comma&gt;Q__area&lt;comma&gt;double,ftp_user_attribute_values,&lt;comma&gt;,ftp_format_parameters,shape_layer_group&lt;comma&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO"/>
#!     <XFORM_PARM PARM_NAME="WRITER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="WRITER_METAFILE" PARM_VALUE="ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_varbinary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_char&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;fme_binary&lt;openparen&gt;width&lt;closeparen&gt;&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_buffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_binarybuffer&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_xml&lt;comma&gt;varchar&lt;openparen&gt;254&lt;closeparen&gt;&lt;comma&gt;fme_json&lt;comma&gt;datetime&lt;comma&gt;fme_datetime&lt;comma&gt;varchar&lt;openparen&gt;12&lt;closeparen&gt;&lt;comma&gt;fme_time&lt;comma&gt;date&lt;comma&gt;fme_date&lt;comma&gt;double&lt;comma&gt;fme_real64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;31&lt;comma&gt;15&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real64&lt;comma&gt;double&lt;comma&gt;fme_uint32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint32&lt;comma&gt;float&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;15&lt;comma&gt;7&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_real32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int64&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;20&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint64&lt;comma&gt;logical&lt;comma&gt;fme_boolean&lt;comma&gt;short&lt;comma&gt;fme_int16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int16&lt;comma&gt;short&lt;comma&gt;fme_int8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int8&lt;comma&gt;short&lt;comma&gt;fme_uint8&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;4&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint8&lt;comma&gt;long&lt;comma&gt;fme_int32&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;11&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_int32&lt;comma&gt;long&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;6&lt;comma&gt;0&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;fme_uint16&lt;comma&gt;&lt;quote&gt;number&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;&lt;quote&gt;fme_decimal&lt;openparen&gt;width&lt;comma&gt;decimal&lt;closeparen&gt;&lt;quote&gt;,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,&lt;quote&gt;:?*&lt;lt&gt;&lt;gt&gt;|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile&lt;backslash&gt;SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point&lt;comma&gt;fme_point&lt;comma&gt;shapefile_multipoint&lt;comma&gt;fme_point&lt;comma&gt;shapefile_point&lt;comma&gt;fme_text&lt;comma&gt;shapefile_line&lt;comma&gt;fme_line&lt;comma&gt;shapefile_line&lt;comma&gt;fme_arc&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_polygon&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_rounded_rectangle&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_ellipse&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_surface&lt;comma&gt;shapefile_multipatch&lt;comma&gt;fme_solid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_null&lt;comma&gt;fme_no_geom&lt;comma&gt;shapefile_feature_table&lt;comma&gt;fme_feature_table&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_raster&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_point_cloud&lt;comma&gt;shapefile_polygon&lt;comma&gt;fme_voxel_grid&lt;comma&gt;shapefile_first_feature&lt;comma&gt;fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,&lt;quote&gt;GUI&lt;space&gt;NAMEDGROUP&lt;space&gt;shape_layer_group&lt;space&gt;shape_dimension&lt;space&gt;Shapefile&lt;quote&gt;&lt;comma&gt;&lt;comma&gt;&lt;quote&gt;GUI&lt;space&gt;LOOKUP_CHOICE&lt;space&gt;shape_dimension&lt;space&gt;Dimension&lt;lt&gt;space&lt;gt&gt;From&lt;lt&gt;space&lt;gt&gt;First&lt;lt&gt;space&lt;gt&gt;Feature&lt;comma&gt;AUTO%2D&lt;comma&gt;2D%2D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;2DM%3D&lt;lt&gt;space&lt;gt&gt;+&lt;lt&gt;space&lt;gt&gt;Measures&lt;comma&gt;3DM&lt;space&gt;Output&lt;space&gt;Dimension&lt;quote&gt;&lt;comma&gt;AUTO,WRITER_DEF_LINE_TEMPLATE,&lt;opencurly&gt;FME_GEN_GROUP_NAME&lt;closecurly&gt;&lt;comma&gt;shapefile_type&lt;comma&gt;&lt;opencurly&gt;FME_GEN_GEOMETRY&lt;closecurly&gt;&lt;comma&gt;shape_dimension&lt;comma&gt;AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE&lt;comma&gt;shapefile_first_feature&lt;comma&gt;ADVANCED_PARMS&lt;comma&gt;&lt;quote&gt;SHAPEFILE_OUT_SURFACE_SOLID_STORAGE&lt;space&gt;SHAPEFILE_OUT_MEASURES_AS_Z&lt;quote&gt;&lt;comma&gt;FEATURE_TYPE_NAME&lt;comma&gt;Shapefile&lt;comma&gt;FEATURE_TYPE_DEFAULT_NAME&lt;comma&gt;Shapefile1&lt;comma&gt;READER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Select&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;openparen&gt;s&lt;closeparen&gt;&lt;quote&gt;&lt;comma&gt;WRITER_DATASET_HINT&lt;comma&gt;&lt;quote&gt;Specify&lt;space&gt;a&lt;space&gt;folder&lt;space&gt;for&lt;space&gt;the&lt;space&gt;Esri&lt;space&gt;Shapefile&lt;quote&gt;,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes"/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="FeatureWriter_2"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="37"
#!   TYPE="Creator"
#!   VERSION="6"
#!   POSITION="4387.543875438756 -830.63090630906299"
#!   BOUNDING_RECT="4387.543875438756 -830.63090630906299 430 71"
#!   ORDER="500000000000014"
#!   PARMS_EDITED="false"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="CREATED"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATEND" PARM_VALUE="no"/>
#!     <XFORM_PARM PARM_NAME="COORDS" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="COORDSYS" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="CRE_ATTR" PARM_VALUE="_creation_instance"/>
#!     <XFORM_PARM PARM_NAME="GEOM" PARM_VALUE="&lt;lt&gt;?xml&lt;space&gt;version=&lt;quote&gt;1.0&lt;quote&gt;&lt;space&gt;encoding=&lt;quote&gt;US_ASCII&lt;quote&gt;&lt;space&gt;standalone=&lt;quote&gt;no&lt;quote&gt;&lt;space&gt;?&lt;gt&gt;&lt;lt&gt;geometry&lt;space&gt;dimension=&lt;quote&gt;2&lt;quote&gt;&lt;gt&gt;&lt;lt&gt;null&lt;solidus&gt;&lt;gt&gt;&lt;lt&gt;&lt;solidus&gt;geometry&lt;gt&gt;"/>
#!     <XFORM_PARM PARM_NAME="GEOMTYPE" PARM_VALUE="Geometry Object"/>
#!     <XFORM_PARM PARM_NAME="NUM" PARM_VALUE="1"/>
#!     <XFORM_PARM PARM_NAME="OAN_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="PARAMETERS_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="Creator_3"/>
#! </TRANSFORMER>
#! <TRANSFORMER
#!   IDENTIFIER="39"
#!   TYPE="AttributeCreator"
#!   VERSION="10"
#!   POSITION="5003.1750317503174 -881.25881258812592"
#!   BOUNDING_RECT="5003.1750317503174 -881.25881258812592 430 71"
#!   ORDER="500000000000015"
#!   PARMS_EDITED="true"
#!   ENABLED="true"
#!   LAST_PARM_EDIT="23619"
#! >
#!     <OUTPUT_FEAT NAME="OUTPUT"/>
#!     <FEAT_COLLAPSED COLLAPSED="0"/>
#!     <XFORM_ATTR ATTR_NAME="_area" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_ATTR ATTR_NAME="_creation_instance" IS_USER_CREATED="false" FEAT_INDEX="0" />
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_GRP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTRIBUTE_HANDLING" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="ATTR_TABLE" PARM_VALUE="&quot;&quot; _area SET_TO 0 uint64"/>
#!     <XFORM_PARM PARM_NAME="MULTI_FEATURE_MODE" PARM_VALUE="NO"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_MODE_DISPLAY" PARM_VALUE="No Substitution"/>
#!     <XFORM_PARM PARM_NAME="NULL_ATTR_VALUE" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_PRIOR_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="NUM_SUBSEQUENT_FEATURES" PARM_VALUE="&lt;Unused&gt;"/>
#!     <XFORM_PARM PARM_NAME="TRANSFORMER_GROUP" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="USER_MODIFIED_ATTRIBUTE_TYPES" PARM_VALUE=""/>
#!     <XFORM_PARM PARM_NAME="XFORMER_NAME" PARM_VALUE="AttributeCreator_2"/>
#! </TRANSFORMER>
#! </TRANSFORMERS>
#! <FEAT_LINKS>
#! <FEAT_LINK
#!   IDENTIFIER="4"
#!   SOURCE_NODE="2"
#!   TARGET_NODE="3"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="15"
#!   SOURCE_NODE="9"
#!   TARGET_NODE="10"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="40"
#!   SOURCE_NODE="37"
#!   TARGET_NODE="39"
#!   SOURCE_PORT_DESC="fo 0 CREATED"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="8"
#!   SOURCE_NODE="5"
#!   TARGET_NODE="7"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="13"
#!   SOURCE_NODE="11"
#!   TARGET_NODE="12"
#!   SOURCE_PORT_DESC="fo 0 PASSED"
#!   TARGET_PORT_DESC="fi 0 INITIATOR"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="26"
#!   SOURCE_NODE="16"
#!   TARGET_NODE="25"
#!   SOURCE_PORT_DESC="fo 0 INSIDE"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="21"
#!   SOURCE_NODE="19"
#!   TARGET_NODE="16"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 1 CANDIDATE"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="24"
#!   SOURCE_NODE="22"
#!   TARGET_NODE="16"
#!   SOURCE_PORT_DESC="fo 0 REPROJECTED"
#!   TARGET_PORT_DESC="fi 0 CLIPPER"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="28"
#!   SOURCE_NODE="25"
#!   TARGET_NODE="27"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="36"
#!   SOURCE_NODE="25"
#!   TARGET_NODE="32"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="1"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="30"
#!   SOURCE_NODE="27"
#!   TARGET_NODE="29"
#!   SOURCE_PORT_DESC="fo 0 SUMMARY"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="34"
#!   SOURCE_NODE="29"
#!   TARGET_NODE="33"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="35"
#!   SOURCE_NODE="33"
#!   TARGET_NODE="31"
#!   SOURCE_PORT_DESC="fo 0 ExcelStyled"
#!   TARGET_PORT_DESC="fi 0 Output"
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="41"
#!   SOURCE_NODE="39"
#!   TARGET_NODE="27"
#!   SOURCE_PORT_DESC="fo 0 OUTPUT"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="6"
#!   SOURCE_NODE="3"
#!   TARGET_NODE="5"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="20"
#!   SOURCE_NODE="7"
#!   TARGET_NODE="19"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="14"
#!   SOURCE_NODE="10"
#!   TARGET_NODE="11"
#!   SOURCE_PORT_DESC="fo 1 PATH"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! <FEAT_LINK
#!   IDENTIFIER="23"
#!   SOURCE_NODE="12"
#!   TARGET_NODE="22"
#!   SOURCE_PORT_DESC="fo 1 &lt;lt&gt;OTHER&lt;gt&gt;"
#!   TARGET_PORT_DESC="fi 0 "
#!   ENABLED="true"
#!   EXECUTION_IDX="0"
#!   HIDDEN="false"
#!   COLOUR="0.30196078431372547,0.30196078431372547,0.30196078431372547,1"
#!   PEN_WIDTH="4"
#!   PEN_STYLE="1"
#!   LINK_STYLE_NAME="&lt;Default&gt;"
#!   EXTRA_POINTS=""
#! />
#! </FEAT_LINKS>
#! <BREAKPOINTS>
#! </BREAKPOINTS>
#! <ATTR_LINKS>
#! </ATTR_LINKS>
#! <SUBDOCUMENTS>
#! </SUBDOCUMENTS>
#! <LOOKUP_TABLES>
#! </LOOKUP_TABLES>
#! </WORKSPACE>

FME_PYTHON_VERSION 311
ARCGIS_COMPATIBILITY ARCGIS_AUTO
# ============================================================================
DEFAULT_MACRO PARAMETER 

DEFAULT_MACRO dir 

DEFAULT_MACRO dir_2 

DEFAULT_MACRO PARAMETER_2 

# ============================================================================
INCLUDE [ if {{$(PARAMETER$encode)} == {}} { puts_real {Parameter 'PARAMETER' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir$encode)} == {}} { puts_real {Parameter 'dir' must be given a value.}; exit 1; }; ]
INCLUDE [ if {{$(dir_2$encode)} == {}} { puts_real {Parameter 'dir_2' must be given a value.}; exit 1; }; ]
#! START_HEADER
#! START_WB_HEADER
READER_TYPE MULTI_READER
WRITER_TYPE MULTI_WRITER
WRITER_KEYWORD MULTI_DEST
MULTI_DEST_DATASET null
#! END_WB_HEADER
#! START_WB_HEADER
#! END_WB_HEADER
#! END_HEADER

LOG_FILENAME "$(FME_MF_DIR)淮安市成片开发方案核查.log"
LOG_APPEND NO
LOG_FILTER_MASK -1
LOG_MAX_FEATURES 200
LOG_MAX_RECORDED_FEATURES 200
FME_REPROJECTION_ENGINE FME
FME_IMPLICIT_CSMAP_REPROJECTION_MODE Auto
FME_GEOMETRY_HANDLING Enhanced
FME_STROKE_MAX_DEVIATION 0
FME_NAMES_ENCODING UTF-8
FME_BULK_MODE_THRESHOLD 2000
LAST_SAVE_BUILD "FME 2023.1.0.0 (******** - Build 23619 - WIN64)"
# -------------------------------------------------------------------------

MULTI_READER_CONTINUE_ON_READER_FAILURE No

# -------------------------------------------------------------------------

MACRO WORKSPACE_NAME 淮安市成片开发方案核查
MACRO FME_VIEWER_APP fmedatainspector
DEFAULT_MACRO WB_CURRENT_CONTEXT
# -------------------------------------------------------------------------
Tcl2 proc Creator_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_XML     NOT_ACTIVATED
MACRO Creator_CLASSIC NOT_ACTIVATED
MACRO Creator_2D3D    2D_GEOMETRY
MACRO Creator_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2D3D 3D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2D3D 2D_GEOMETRY};            puts {MACRO Creator_CLASSIC *} } ]
FACTORY_DEF {$(Creator_XML)} CreationFactory    FACTORY_NAME { Creator_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_CLASSIC_Creator }    $(Creator_2D3D) { $(Creator_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_CREATED        fme_feature_type Creator         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_CREATED Brancher -1 4"   INPUT FEATURE_TYPE Creator_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_2_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_2_XML     NOT_ACTIVATED
MACRO Creator_2_CLASSIC NOT_ACTIVATED
MACRO Creator_2_2D3D    2D_GEOMETRY
MACRO Creator_2_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_2_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_2_2D3D 3D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_2: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_2_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_2_2D3D 2D_GEOMETRY};            puts {MACRO Creator_2_CLASSIC *} } ]
FACTORY_DEF {$(Creator_2_XML)} CreationFactory    FACTORY_NAME { Creator_2_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_2_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_2_CLASSIC_Creator }    $(Creator_2_2D3D) { $(Creator_2_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_2_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_2_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_2_CREATED        fme_feature_type Creator_2         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_2_CREATED Brancher -1 15"   INPUT FEATURE_TYPE Creator_2_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
Tcl2 proc Creator_3_CoordSysRemover {} {   global FME_CoordSys;   set FME_CoordSys {}; }
MACRO Creator_3_XML     NOT_ACTIVATED
MACRO Creator_3_CLASSIC NOT_ACTIVATED
MACRO Creator_3_2D3D    2D_GEOMETRY
MACRO Creator_3_COORDS  <Unused>
INCLUDE [ if { {Geometry Object} == {Geometry Object} } {            puts {MACRO Creator_3_XML *} } ]
INCLUDE [ if { {Geometry Object} == {2D Coordinate List} } {            puts {MACRO Creator_3_2D3D 2D_GEOMETRY};            puts {MACRO Creator_3_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {3D Coordinate List} } {            puts {MACRO Creator_3_2D3D 3D_GEOMETRY};            puts {MACRO Creator_3_CLASSIC *} } ]
INCLUDE [ if { {Geometry Object} == {2D Min/Max Box} } {            set comment {                We need to turn the COORDS which are                    minX minY maxX maxY                into a full polygon list of coordinates            };            set splitCoords [split [string trim {<Unused>}]];            if { [llength $splitCoords] > 4} {               set trimmedCoords {};               foreach item $splitCoords { if { $item != {} } {lappend trimmedCoords $item} };               set splitCoords $trimmedCoords;            };            if { [llength $splitCoords] != 4 } {                error {Creator_3: Coordinate list is expected to be a space delimited list of four numbers as 'minx miny maxx maxy' - `<Unused>' is invalid};            };            set minX [lindex $splitCoords 0];            set minY [lindex $splitCoords 1];            set maxX [lindex $splitCoords 2];            set maxY [lindex $splitCoords 3];            puts "MACRO Creator_3_COORDS $minX $minY $minX $maxY $maxX $maxY $maxX $minY $minX $minY";            puts {MACRO Creator_3_2D3D 2D_GEOMETRY};            puts {MACRO Creator_3_CLASSIC *} } ]
FACTORY_DEF {$(Creator_3_XML)} CreationFactory    FACTORY_NAME { Creator_3_XML_Creator }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______        @Geometry(FROM_ENCODED_STRING,<lt>?xml<space>version=<quote>1.0<quote><space>encoding=<quote>US_ASCII<quote><space>standalone=<quote>no<quote><space>?<gt><lt>geometry<space>dimension=<quote>2<quote><gt><lt>null<solidus><gt><lt><solidus>geometry<gt>) }
FACTORY_DEF {$(Creator_3_CLASSIC)} CreationFactory    FACTORY_NAME { Creator_3_CLASSIC_Creator }    $(Creator_3_2D3D) { $(Creator_3_COORDS) }    CREATE_AT_END { no }    OUTPUT { FEATURE_TYPE _____CREATED______ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Creator_3_Cloner }    INPUT FEATURE_TYPE _____CREATED______        @Tcl2(Creator_3_CoordSysRemover)        @CoordSys()    NUMBER_OF_COPIES { 1 }    COPY_NUMBER_ATTRIBUTE { "_creation_instance" }    OUTPUT { FEATURE_TYPE Creator_3_CREATED        fme_feature_type Creator_3         }
FACTORY_DEF * BranchingFactory   FACTORY_NAME "Creator_3_CREATED Brancher -1 40"   INPUT FEATURE_TYPE Creator_3_CREATED   TARGET_FACTORY "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   MAXIMUM_COUNT None   OUTPUT PASSED FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF * TeeFactory   FACTORY_NAME "$(WB_CURRENT_CONTEXT)_CREATOR_BRANCH_TARGET"   INPUT FEATURE_TYPE *  OUTPUT FEATURE_TYPE *
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator_2 }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE Creator_3_CREATED    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "_area" "SET_TO" "0" "uint64" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_2_OUTPUT        }
# -------------------------------------------------------------------------
MACRO FeatureReader_3_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_3_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_3_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_3_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_3_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_3 @EvaluateExpression(FDIV,STRING_ENCODED,$(dir_2$encode),FeatureReader_3)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_3 }    INPUT  FEATURE_TYPE Creator_2_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_3_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_3)" }    QUERYFCT_IDS        { "PATH" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_3_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_3_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_3_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_3_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_3_<Rejected> INPUT FEATURE_TYPE FeatureReader_3_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_3<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester_2 }    INPUT  FEATURE_TYPE FeatureReader_3_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester_2)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_2_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_4_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_4_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_4_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_4_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_4_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_4 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_4)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_4 }    INPUT  FEATURE_TYPE Tester_2_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_4_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_4)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_4_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_4_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_4_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_4_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_4_<Rejected> INPUT FEATURE_TYPE FeatureReader_4_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_4<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector_2 }    INPUT  FEATURE_TYPE FeatureReader_4_<OTHER>    OUTPUT { FEATURE_TYPE Reprojector_2_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector_2,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
MACRO FeatureReader_OUTPUT_PORTS_ENCODED PATH
MACRO FeatureReader_DIRECTIVES EXPOSE_ATTRS_GROUP,,GLOB_PATTERN,*,HIDDEN_FILES,INCLUDE,NETWORK_AUTHENTICATION,,NO_EMPTY_PROPERTIES,YES,OFFSET_DATETIME,yes,PATH_EXPOSE_FORMAT_ATTRS,,RECURSE_DIRECTORIES,YES,RETRIEVE_FILE_PROPERTIES,NO,TYPE,ANY,USE_NON_STRING_ATTR,yes
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {<Unused>} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "<Unused>"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader @EvaluateExpression(FDIV,STRING_ENCODED,$(dir$encode),FeatureReader)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader }    INPUT  FEATURE_TYPE Creator_CREATED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { PATH  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader)" }    QUERYFCT_IDS        { "PATH" }    READER_DIRECTIVES   { METAFILE,PATH }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_<REJECTED>           }    OUTPUT PATH FEATURE_TYPE FeatureReader_PATH
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_<Rejected> INPUT FEATURE_TYPE FeatureReader_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TestFactory    FACTORY_NAME { Tester }    INPUT  FEATURE_TYPE FeatureReader_PATH    TEST { "@EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_extension<closeparen>,Tester)" = shp ENCODED }    BOOLEAN_OPERATOR { OR }    COMPOSITE_TEST_EXPR { "<Unused>" }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { PASSED FEATURE_TYPE Tester_PASSED         }
# -------------------------------------------------------------------------
MACRO FeatureReader_2_OUTPUT_PORTS_ENCODED 
MACRO FeatureReader_2_DIRECTIVES ADVANCED,,DONUT_DETECTION,ORIENTATION,ENCODING,fme-source-encoding,EXPOSE_ATTRS_GROUP,,MEASURES_AS_Z,No,NUMERIC_TYPE_ATTRIBUTE_HANDLING,STANDARD_TYPES,READ_BLANK_AS,MISSING,READ_NUMERIC_PADDING_AS,ZERO,REPORT_BAD_GEOMETRY,No,SHAPEFILE_EXPOSE_FORMAT_ATTRS,,TRIM_PRECEDING_SPACES,Yes,USE_SEARCH_ENVELOPE,NO,USE_STRING_FOR_NUMERIC_STORAGE,No
# Always provide an INTERACTION, otherwise the factory defaults to ENVELOPE_INTERSECTS
INCLUDE [if { ( {NONE} == {<Unused>} ) || ( {($INTERACT)} == {} ) } {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION NONE};          } else {             puts {MACRO FCTQUERY_INTERACTION_LINE FCTQUERY_INTERACTION "NONE"};          }         ]
# Consolidate the attribute merge options to what the factory expects
DEFAULT_MACRO FeatureReader_2_COMBINE_ATTRS
INCLUDE [       if { {RESULT_ONLY} == {MERGE} } {          puts "MACRO FeatureReader_2_COMBINE_ATTRS <Unused>";       } else {          puts "MACRO FeatureReader_2_COMBINE_ATTRS RESULT_ONLY";       };    ]
INCLUDE [    puts {DEFAULT_MACRO FeatureReaderDataset_FeatureReader_2 @EvaluateExpression(FDIV,STRING_ENCODED,<at>Value<openparen>path_windows<closeparen>,FeatureReader_2)}; ]
FACTORY_DEF {*} QueryFactory    FACTORY_NAME { FeatureReader_2 }    INPUT  FEATURE_TYPE Tester_PASSED    $(FCTQUERY_INTERACTION_LINE)    COMBINE_ATTRIBUTES  { $(FeatureReader_2_COMBINE_ATTRS) }    IGNORE_NULLS        { <Unused> }    QUERYFCT_ATTRIBUTE_PREFIX { <Unused> }    COMBINE_GEOMETRY    { RESULT_ONLY }    ENABLE_CACHE        { NO }    QUERYFCT_TABLE_SEPARATOR { SPACE }    READER_TYPE         { SHAPEFILE  }    READER_DATASET      { "$(FeatureReaderDataset_FeatureReader_2)" }    QUERYFCT_IDS        { "" }    READER_DIRECTIVES   { METAFILE,SHAPEFILE }    QUERYFCT_OUTPUT     { "BASED_ON_CONNECTIONS" }    CONTINUE_ON_READER_ERROR YES    ORDER_RESULTS { "No" }    QUERYFCT_RESULT_TAGS { $(FeatureReader_2_OUTPUT_PORTS_ENCODED) }    QUERYFCT_SET_FME_FEATURE_TYPE YES    READER_PARAMS_WWJD     { $(FeatureReader_2_DIRECTIVES) }    TREAT_READER_PARAM_AMPERSANDS_AS_LITERALS YES    OUTPUT { RESULT FEATURE_TYPE FeatureReader_2_<OTHER>           }    OUTPUT { READER_ERROR FEATURE_TYPE FeatureReader_2_<REJECTED>           }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME FeatureReader_2_<Rejected> INPUT FEATURE_TYPE FeatureReader_2_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, FeatureReader_2<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { Reprojector }    INPUT  FEATURE_TYPE FeatureReader_2_<OTHER>    OUTPUT { FEATURE_TYPE Reprojector_REPROJECTED         @Reproject("","EPSG:4528",NearestNeighbor,PreserveCells,Reprojector,"COORD_SYS_WARNING",RASTER_TOLERANCE,"0.0")          }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ClipperFactory    FACTORY_NAME { Clipper }    INPUT CLIPPER FEATURE_TYPE Reprojector_2_REPROJECTED    INPUT CANDIDATE FEATURE_TYPE Reprojector_REPROJECTED    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    MULTIPLE_CLIPPERS { YES }    CLIPPERS_FIRST { NO }    OVERLAPPING_CLIPPERS { CLIP_OUTSIDE }    INVALID_PARTS_HANDLING { REJECT_WHOLE }    ADD_NODATA { YES }    Z_VAL_SOURCE { CANDIDATE }    MISSING_Z_MODE { PLANAR }    CONNECT_Z_MODE { <Unused> }    M_VAL_SOURCE { CANDIDATE }    MISSING_M_MODE { LINEAR }    CLEANING_TOLERANCE { 0.001 }    CANDIDATE_ON_BOUNDARY { INSIDE }    PRESERVE_RASTER_EXTENTS { NO }    RASTER_CELL_MODE { CENTER }    CLIPPED_INDICATOR_ATTR { "_clipped" }    MERGE_CLIPPER_ATTRIBUTES { NO }    ATTR_ACCUM_MODE { "<Unused>" }    ATTR_CONFLICT_RES { "<Unused>" }    CLIPPER_PREFIX { "<Unused>" }    LIST_NAME { "<Unused>" }    LIST_ATTRS_TO_INCLUDE { <Unused> }    LIST_ATTRS_TO_INCLUDE_MODE { <Unused> }    PRESERVE_FEATURE_ORDER { PER_OUTPUT_PORT }    OUTPUT { INSIDE FEATURE_TYPE Clipper_INSIDE         }    OUTPUT { REJECTED FEATURE_TYPE Clipper_<REJECTED>         }
DEFAULT_MACRO _WB_BYPASS_TERMINATION No
FACTORY_DEF * TeeFactory FACTORY_NAME Clipper_<Rejected> INPUT FEATURE_TYPE Clipper_<REJECTED>  NO_LOGGING   OUTPUT FAILED FEATURE_TYPE * @Abort(ENCODED, Clipper<space>output<space>a<space><lt>Rejected<gt><space>feature.<space><space>To<space>continue<space>translation<space>when<space>features<space>are<space>rejected<comma><space>change<space><apos>Workspace<space>Parameters<apos><space><gt><space>Translation<space><gt><space><apos>Rejected<space>Feature<space>Handling<apos><space>to<space><apos>Continue<space>Translation<apos>)
# -------------------------------------------------------------------------
INCLUDE [          if { ({Plane Area} == {Sloped Area}) } {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,SLOPED_AREAS,"1")};          } else {             puts {MACRO AreaCalculator_func @Area(REJECTABLE,ALLOW_NULLS,"1")};          }          ]
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculatorInput }    INPUT  FEATURE_TYPE Clipper_INSIDE    OUTPUT { FEATURE_TYPE ___TOAREACALCULATOR___ }
FACTORY_DEF {*} TeeFactory    FACTORY_NAME { AreaCalculator_AreaCalculator }    INPUT FEATURE_TYPE ___TOAREACALCULATOR___         @RenameAttributes(FME_STRICT,___fme_rejection_code___,fme_rejection_code)    OUTPUT { FEATURE_TYPE ___TOREJECTOR___         @SupplyAttributes(ENCODED, _area, $(AreaCalculator_func)) }
FACTORY_DEF {*} TestFactory    FACTORY_NAME { AreaCalculator_Rejector }    INPUT FEATURE_TYPE ___TOREJECTOR___    TEST @Value(fme_rejection_code) != ""    PRESERVE_FEATURE_ORDER PER_OUTPUT_PORT    OUTPUT { FAILED FEATURE_TYPE AreaCalculator_OUTPUT       @RenameAttributes(FME_STRICT,fme_rejection_code,___fme_rejection_code___)        }
FACTORY_DEF * TeeFactory   FACTORY_NAME "AreaCalculator OUTPUT Splitter"   INPUT FEATURE_TYPE AreaCalculator_OUTPUT   OUTPUT FEATURE_TYPE AreaCalculator_OUTPUT_0_Bo8PD6YOBGI=   OUTPUT FEATURE_TYPE AreaCalculator_OUTPUT_1_Z02x17GzxwM=
# -------------------------------------------------------------------------
FACTORY_DEF {*} StatisticsCalculatorFactory    FACTORY_NAME { StatisticsCalculator }    INPUT  FEATURE_TYPE AreaCalculator_OUTPUT_0_Bo8PD6YOBGI=    INPUT  FEATURE_TYPE AttributeCreator_2_OUTPUT    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FEAT_STATS { _area,NUMERIC_MODE,,,,SUM,,,,,,,,, }    ADVANCED_MODE { NO }    CUMULATIVE_STATS {  }    CALCULATION_MODE { NUMERIC }    ATTRIBUTE_NAMES_AND_TYPES { _clipped,char<openparen>3<closeparen>,_area,real64,_creation_instance,uint64 }    TREAT_INVALID_AS_NULL Yes    OUTPUT { SUMMARY FEATURE_TYPE StatisticsCalculator_SUMMARY        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} AttrSetFactory    FACTORY_NAME { AttributeCreator }    COMMAND_PARM_EVALUATION SINGLE_PASS    INPUT  FEATURE_TYPE StatisticsCalculator_SUMMARY    MULTI_FEATURE_MODE { NO }    NULL_ATTR_MODE { NO_OP }    ATTRSET_CREATE_DIRECTIVES _PROPAGATE_MISSING_FDIV    NUM_OF_COLUMNS 5    ATTR_ACTION { "" "<u65b9><u6848><u540d><u79f0>" "SET_TO" "$(PARAMETER$encode)" "varchar<openparen>200<closeparen>" }      ATTR_ACTION { "" "<u5360><u7528><u8015><u5730><u9762><u79ef><uff08><u4ea9><uff09>" "SET_TO" "<at>Evaluate<openparen><at>Value<openparen>_area.sum<closeparen>*0.0015<closeparen>" "varchar<openparen>200<closeparen>" }    OUTPUT { OUTPUT FEATURE_TYPE AttributeCreator_OUTPUT        }
# -------------------------------------------------------------------------
FACTORY_DEF {*} ExcelStyleFactory    FACTORY_NAME { ExcelStyler_Styler }    INPUT  FEATURE_TYPE AttributeCreator_OUTPUT    ROW_OR_CELL { "row" }    NUMBER_FORMAT_STRING { "" }    FONT_NAME { "<u5b8b><u4f53><comma>14" }    FONT_COLOR { "" }    PATTERN_COLOR { "" }    BACKGROUND_COLOR { "" }    PATTERN_STYLE { "" }    HORIZONTAL_ALIGNMENT { "center" }    VERTICAL_ALIGNMENT { "center" }    INDENT { "" }    TEXT_ORIENTATION { "" }    TEXT_CONTROL { "" }    CELL_BORDER_COLOR { "0,0,0" }    CELL_BORDER_STYLE { "BORDERSTYLE_THIN" }    ROW_HEIGHT { "" }    ATTRS_TO_STYLE { <Unused> }    HIDDEN { "" }    LOCKED { "" }    OUTPUT { ExcelStyled FEATURE_TYPE ExcelStyler_ExcelStyled        }
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash>$(PARAMETER$encode)<u6838><u67e5><u7ed3><u679c>.xlsx,FeatureWriter)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter }    WRITER_TYPE { XLSXW }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter)" }    WRITER_SETTINGS { "RUNTIME_MACROS,OVERWRITE_FILE<comma>No<comma>TEMPLATEFILE<comma><comma>TEMPLATE_SHEET<comma><comma>REMOVE_UNCHANGED_TEMPLATE_SHEET<comma>No<comma>MULTIPLE_TEMPLATE_SHEETS<comma>Yes<comma>INSERT_IGNORE_DB_OP<comma>Yes<comma>DROP_TABLE<comma>No<comma>TRUNCATE_TABLE<comma>No<comma>FIELD_NAMES_OUT<comma>Yes<comma>FIELD_NAMES_FORMATTING<comma>Yes<comma>WRITER_MODE<comma>Insert<comma>RASTER_FORMAT<comma>PNG<comma>PROTECT_SHEET<comma>NO<comma>PROTECT_SHEET_PASSWORD<comma><lt>Unused<gt><comma>PROTECT_SHEET_LEVEL<comma><lt>Unused<gt><comma>PROTECT_SHEET_PERMISSIONS<comma><lt>Unused<gt><comma>STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING<comma>yes<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>COORDINATE_SYSTEM_GRANULARITY<comma>FEATURE<comma>CUSTOM_NUMBER_FORMATTING<comma>ENABLE_NATIVE<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,XLSXW" }    WRITER_METAFILE { "ATTRIBUTE_CASE,ANY,ATTRIBUTE_INVALID_CHARS,,ATTRIBUTE_LENGTH,255,ATTR_TYPE_MAP,<quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varchar<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_varbinary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_char<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binary<openparen>width<closeparen><comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_binarybuffer<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_xml<comma><quote>string<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_json<comma><quote>auto<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_buffer<comma><quote>datetime<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_datetime<comma><quote>time<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_time<comma><quote>date<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_date<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote><comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint32<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint64<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_int8<comma><quote>number<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_uint8<comma><quote>boolean<openparen>width<comma>xlsx_col_props<closeparen><quote><comma>fme_boolean,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<openbracket><closebracket>*<backslash><backslash>?:<apos>,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,true,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>XLSXW.fmf,FORMAT_NAME,XLSXW,GEOM_MAP,xlsx_none<comma>fme_no_geom<comma>xlsx_none<comma>fme_point<comma>xlsx_point<comma>fme_point<comma>xlsx_none<comma>fme_line<comma>xlsx_none<comma>fme_polygon<comma>xlsx_none<comma>fme_text<comma>xlsx_none<comma>fme_ellipse<comma>xlsx_none<comma>fme_arc<comma>xlsx_none<comma>fme_rectangle<comma>xlsx_none<comma>fme_rounded_rectangle<comma>xlsx_none<comma>fme_collection<comma>xlsx_none<comma>fme_surface<comma>xlsx_none<comma>fme_solid<comma>xlsx_none<comma>fme_raster<comma>xlsx_none<comma>fme_point_cloud<comma>xlsx_none<comma>fme_voxel_grid<comma>xlsx_none<comma>fme_feature_table,READER_ATTR_INDEX_TYPES,,READER_FORMAT_TYPE,,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,yes,WORKBENCH_CANNED_SCHEMA,,WRITER,XLSXW,WRITER_ATTR_INDEX_TYPES,,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>xlsx_layer_group<space>xlsx_table_writer_mode%xlsx_field_names_out%xlsx_field_names_formatting%xlsx_names_are_positions%xlsx_row_id_column%xlsx_truncate_group%xlsx_table_group%xlsx_rowcolumn_group%xlsx_template_group%xlsx_protect_sheet%xlsx_advanced_group<space>Sheet<space>Settings<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_truncate_group<space>xlsx_row_id%xlsx_drop_sheet%xlsx_trunc_sheet<space>Drop<solidus>Truncate<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_rowcolumn_group<space>xlsx_start_col%xlsx_start_row%xlsx_offset_col%xlsx_offset_row<space>Start<space>Position<quote><comma><comma><quote>GUI<space>ACTIVEDISCLOSUREGROUP<space>xlsx_protect_sheet<space>xlsx_protect_sheet_password%xlsx_protect_sheet_level%xlsx_protect_sheet_permissions<space>Protect<space>Sheet<quote><comma>NO<comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_template_group<space>xlsx_template_sheet%xlsx_remove_unchanged_template_sheet<space>Template<space>Options<quote><comma><comma><quote>GUI<space>DISCLOSUREGROUP<space>xlsx_advanced_group<space>xlsx_sheet_order%xlsx_freeze_end_row%xlsx_raster_type<space>Advanced<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_drop_sheet<space>Yes%No<space>Drop<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>CHOICE<space>xlsx_trunc_sheet<space>Yes%No<space>Truncate<space>Existing<space>Sheet<solidus>Named<space>Range:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_sheet_order<space>1%MAX<space>Sheet<space>Order<space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>RANGE_SLIDER<space>xlsx_freeze_end_row<space>1%MAX<space>Freeze<space>First<space>Row<openparen>s<closeparen><space><openparen>1<space>-<space>n<closeparen>:<quote><comma><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_field_names_out<space>Yes%No<comma>xlsx_field_names_formatting<comma>++xlsx_field_names_formatting+No<space>Output<space>Field<space>Names:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_field_names_formatting<space>Yes%No<space>Format<space>Field<space>Names<space>Row:<quote><comma>Yes<comma><quote>GUI<space>CHOICE<space>xlsx_names_are_positions<space>Yes%No<space>Use<space>Attribute<space>Names<space>As<space>Column<space>Positions:<quote><comma>No<comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_start_col<space>Named<space>Range<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_start_row<space>Named<space>Range<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT<space>xlsx_offset_col<space>Start<space>Column:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>INTEGER<space>xlsx_offset_row<space>Start<space>Row:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_raster_type<space>BMP%JPEG%PNG<space>Raster<space>Format:<quote><comma>PNG<comma><quote>GUI<space>OPTIONAL<space>PASSWORD_ENCODED<space>xlsx_protect_sheet_password<space>Password:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE_LOOKUP<space>xlsx_protect_sheet_level<space>Select<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_DEFAULT<comma>xlsx_protect_sheet_permissions%View<lt>space<gt>Only<lt>space<gt>Permissions<comma>PROT_ALL<comma>xlsx_protect_sheet_permissions%Specific<lt>space<gt>Permissions<space>Protection<space>Level:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>OPTIONAL<space>LOOKUP_LISTBOX<space>xlsx_protect_sheet_permissions<space>Select<lt>space<gt>locked<lt>space<gt>cells<comma>PROT_SEL_LOCKED_CELLS%Select<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_SEL_UNLOCKED_CELLS%Format<lt>space<gt>cells<comma>PROT_FORMAT_CELLS%Format<lt>space<gt>columns<comma>PROT_FORMAT_COLUMNS%Format<lt>space<gt>rows<comma>PROT_FORMAT_ROWS%Insert<lt>space<gt>columns<comma>PROT_INSERT_COLUMNS%Insert<lt>space<gt>rows<comma>PROT_INSERT_ROWS%Add<lt>space<gt>hyperlinks<lt>space<gt>to<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_INSERT_HYPERLINKS%Delete<lt>space<gt>unlocked<lt>space<gt>columns<comma>PROT_DELETE_COLUMNS%Delete<lt>space<gt>unlocked<lt>space<gt>rows<comma>PROT_DELETE_ROWS%Sort<lt>space<gt>unlocked<lt>space<gt>cells<solidus>rows<solidus>columns<comma>PROT_SORT%Use<lt>space<gt>Autofilter<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_AUTOFILTER%Use<lt>space<gt>PivotTable<lt>space<gt><amp><lt>space<gt>PivotChart<lt>space<gt>on<lt>space<gt>unlocked<lt>space<gt>cells<comma>PROT_PIVOTTABLES%Edit<lt>space<gt>unlocked<lt>space<gt>objects<comma>PROT_OBJECTS%Edit<lt>space<gt>unprotected<lt>space<gt>scenarios<comma>PROT_SCENARIOS<space>Specific<space>Permissions:<quote><comma><lt>Unused<gt><comma><quote>GUI<space>ACTIVECHOICE<space>xlsx_table_writer_mode<space>Insert<comma>+xlsx_row_id_column+<quote><quote><quote><quote>%Update<comma>+xlsx_row_id_column+xlsx_row_id%Delete<comma>+xlsx_row_id_column+xlsx_row_id<space>Writer<space>Mode:<quote><comma>Insert<comma><quote>GUI<space>OPTIONAL<space>ATTR<space>xlsx_row_id_column<space>ALLOW_NEW<space>Row<space>Number<space>Attribute:<quote><comma><comma><quote>GUI<space>OPTIONAL<space>TEXT_EDIT<space>xlsx_template_sheet<space>Template<space>Sheet:<quote><comma><comma><quote>GUI<space>CHOICE<space>xlsx_remove_unchanged_template_sheet<space>Yes%No<space>Remove<space>Template<space>Sheet<space>if<space>Unchanged:<quote><comma>No,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_freeze_end_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_names_are_positions<comma>No<comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_start_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_start_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_col<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_offset_row<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_raster_type<comma>PNG<comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_protect_sheet<comma><quote><quote><quote>NO<quote><quote><quote><comma>xlsx_protect_sheet_level<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_password<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_protect_sheet_permissions<comma><quote><quote><quote><lt>Unused<gt><quote><quote><quote><comma>xlsx_template_sheet<comma><quote><quote><quote><quote><quote><quote><comma>xlsx_remove_unchanged_template_sheet<comma><quote><quote><quote>No<quote><quote><quote>,WRITER_FORMAT_PARAMETER,DEFAULT_READER<comma>XLSXR<comma>ALLOW_DATASET_CONFLICT<comma>YES<comma>MIME_TYPE<comma><quote>application<solidus>vnd.openxmlformats-officedocument.spreadsheetml.sheet<space>ADD_DISPOSITION<quote><comma>ATTRIBUTE_READING<comma>DEFLINE<comma>DEFAULT_ATTR_TYPE<comma>auto<comma>USER_ATTRIBUTES_COLUMNS<comma><quote>Width<comma>Cell<space>Width%Precision<comma>Formatting<quote><comma>FEATURE_TYPE_NAME<comma>Sheet<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Sheet1<comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>name<space>for<space>the<space>Microsoft<space>Excel<space>file<quote>,WRITER_FORMAT_TYPE,,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "sheet1:Output,ftp_feature_type_name,sheet1,ftp_writer,XLSXW,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,1,ftp_user_attributes,<lt>u65b9<gt><lt>u6848<gt><lt>u540d<gt><lt>u79f0<gt><comma>string<lt>openparen<gt>50<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>14<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt><comma><lt>u5360<gt><lt>u7528<gt><lt>u8015<gt><lt>u5730<gt><lt>u9762<gt><lt>u79ef<gt><lt>uff08<gt><lt>u4ea9<gt><lt>uff09<gt><comma>string<lt>openparen<gt>30<lt>comma<gt>version<lt>lt<gt>semicolon<lt>gt<gt>1<lt>lt<gt>semicolon<lt>gt<gt>number_format_string<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>font<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u5b8b<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>u4f53<lt>lt<gt>gt<lt>gt<gt><lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>14<lt>lt<gt>semicolon<lt>gt<gt>font_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>background_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_color<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>pattern_style<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_border_formatting<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_COLOR<lt>lt<gt>semicolon<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>lt<lt>gt<gt>comma<lt>lt<gt>gt<lt>gt<gt>0<lt>lt<gt>semicolon<lt>gt<gt>CELL_BORDER_STYLE<lt>lt<gt>semicolon<lt>gt<gt>BORDERSTYLE_THIN<lt>lt<gt>semicolon<lt>gt<gt>text_alignment<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_OPEN<lt>lt<gt>semicolon<lt>gt<gt>horizontal_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>vertical_alignment<lt>lt<gt>semicolon<lt>gt<gt>center<lt>lt<gt>semicolon<lt>gt<gt>indent<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_orientation<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>text_control<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>cell_protection<lt>lt<gt>semicolon<lt>gt<gt>FME_DISCLOSURE_CLOSED<lt>lt<gt>semicolon<lt>gt<gt>hide_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>lt<gt>semicolon<lt>gt<gt>lock_cells<lt>lt<gt>semicolon<lt>gt<gt><lt>closeparen<gt>,ftp_user_attribute_values,<comma>,ftp_format_parameters,xlsx_layer_group<comma><comma>xlsx_truncate_group<comma><comma>xlsx_rowcolumn_group<comma><comma>xlsx_protect_sheet<comma>NO<comma>xlsx_template_group<comma>FME_DISCLOSURE_CLOSED<comma>xlsx_advanced_group<comma><comma>xlsx_drop_sheet<comma>No<comma>xlsx_trunc_sheet<comma>No<comma>xlsx_sheet_order<comma><comma>xlsx_freeze_end_row<comma><comma>xlsx_field_names_out<comma>Yes<comma>xlsx_field_names_formatting<comma>Yes<comma>xlsx_names_are_positions<comma>No<comma>xlsx_start_col<comma><comma>xlsx_start_row<comma><comma>xlsx_offset_col<comma><comma>xlsx_offset_row<comma><comma>xlsx_raster_type<comma>PNG<comma>xlsx_protect_sheet_password<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_level<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_protect_sheet_permissions<comma><lt>lt<gt>Unused<lt>gt<gt><comma>xlsx_table_writer_mode<comma>Insert<comma>xlsx_row_id_column<comma><comma>xlsx_template_sheet<comma><comma>xlsx_remove_unchanged_template_sheet<comma>No" }    WRITER_PARAMS { "COORDINATE_SYSTEM_GRANULARITY,FEATURE,CUSTOM_NUMBER_FORMATTING,ENABLE_NATIVE,DESTINATION_DATASETTYPE_VALIDATION,Yes,DROP_TABLE,No,FIELD_NAMES_FORMATTING,Yes,FIELD_NAMES_OUT,Yes,INSERT_IGNORE_DB_OP,Yes,MULTIPLE_TEMPLATE_SHEETS,Yes,NETWORK_AUTHENTICATION,,OVERWRITE_FILE,No,PROTECT_SHEET,NO,RASTER_FORMAT,PNG,STRICT_SCHEMA_ADDITIONAL_ATTRIBUTE_MATCHING,yes,TRUNCATE_TABLE,No,WRITER_MODE,Insert" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE ExcelStyler_ExcelStyled  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,sheet1,FeatureWriter))
# -------------------------------------------------------------------------
INCLUDE [    puts {DEFAULT_MACRO FeatureWriterDataset_FeatureWriter_2 @EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER_2$encode)<backslash>$(PARAMETER$encode)<u91cd><u53e0><u77e2><u91cf><u56fe><u5c42>,FeatureWriter_2)}; ]
FACTORY_DEF {*} WriterFactory    COMMAND_PARM_EVALUATION SINGLE_PASS    FEATURE_TABLE_SHIM_SUPPORT INPUT_SPEC_ONLY    FLUSH_WHEN_GROUPS_CHANGE { <Unused> }    FACTORY_NAME { FeatureWriter_2 }    WRITER_TYPE { SHAPEFILE }    WRITER_DATASET { "$(FeatureWriterDataset_FeatureWriter_2)" }    WRITER_SETTINGS { "RUNTIME_MACROS,ENCODING<comma>utf-8<comma>SPATIAL_INDEXES<comma>NONE<comma>COMPRESSED_FILE<comma>No<comma>DIMENSION<comma>AUTO<comma>DATETIME_STORAGE<comma>TIMESTAMP_STRING<comma>ADVANCED<comma><comma>SURFACE_SOLID_STORAGE<comma>PATCH<comma>MEASURES_AS_Z<comma>No<comma>DATASET_SPLITTING<comma>Yes<comma>ENFORCE_ATTRIBUTE_LIMIT<comma>No<comma>DESTINATION_DATASETTYPE_VALIDATION<comma>Yes<comma>REQUIRE_FIRST_ATTRNAME_ALPHA<comma>Yes<comma>PERMIT_NONASCII_FIRST_ATTRNAME<comma>Yes<comma>NETWORK_AUTHENTICATION<comma>,METAFILE,SHAPEFILE" }    WRITER_METAFILE { "ATTRIBUTE_CASE,FIRST_LETTER,ATTRIBUTE_INVALID_CHARS,.,ATTRIBUTE_LENGTH,10,ATTR_TYPE_MAP,varchar<openparen>width<closeparen><comma>fme_varchar<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_varbinary<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_char<openparen>width<closeparen><comma>varchar<openparen>width<closeparen><comma>fme_binary<openparen>width<closeparen><comma>varchar<openparen>254<closeparen><comma>fme_buffer<comma>varchar<openparen>254<closeparen><comma>fme_binarybuffer<comma>varchar<openparen>254<closeparen><comma>fme_xml<comma>varchar<openparen>254<closeparen><comma>fme_json<comma>datetime<comma>fme_datetime<comma>varchar<openparen>12<closeparen><comma>fme_time<comma>date<comma>fme_date<comma>double<comma>fme_real64<comma><quote>number<openparen>31<comma>15<closeparen><quote><comma>fme_real64<comma>double<comma>fme_uint32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_uint32<comma>float<comma>fme_real32<comma><quote>number<openparen>15<comma>7<closeparen><quote><comma>fme_real32<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_int64<comma><quote>number<openparen>20<comma>0<closeparen><quote><comma>fme_uint64<comma>logical<comma>fme_boolean<comma>short<comma>fme_int16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_int16<comma>short<comma>fme_int8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_int8<comma>short<comma>fme_uint8<comma><quote>number<openparen>4<comma>0<closeparen><quote><comma>fme_uint8<comma>long<comma>fme_int32<comma><quote>number<openparen>11<comma>0<closeparen><quote><comma>fme_int32<comma>long<comma>fme_uint16<comma><quote>number<openparen>6<comma>0<closeparen><quote><comma>fme_uint16<comma><quote>number<openparen>width<comma>decimal<closeparen><quote><comma><quote>fme_decimal<openparen>width<comma>decimal<closeparen><quote>,DEST_ILLEGAL_ATTR_LIST,,FEATURE_TYPE_CASE,ANY,FEATURE_TYPE_INVALID_CHARS,<quote>:?*<lt><gt>|,FEATURE_TYPE_LENGTH,0,FEATURE_TYPE_LENGTH_INCLUDES_PREFIX,false,FEATURE_TYPE_RESERVED_WORDS,,FORMAT_METAFILE,$(FME_HOME_ENCODED)metafile<backslash>SHAPEFILE.fmf,FORMAT_NAME,SHAPEFILE,GEOM_MAP,shapefile_point<comma>fme_point<comma>shapefile_multipoint<comma>fme_point<comma>shapefile_point<comma>fme_text<comma>shapefile_line<comma>fme_line<comma>shapefile_line<comma>fme_arc<comma>shapefile_polygon<comma>fme_polygon<comma>shapefile_polygon<comma>fme_rectangle<comma>shapefile_polygon<comma>fme_rounded_rectangle<comma>shapefile_polygon<comma>fme_ellipse<comma>shapefile_multipatch<comma>fme_surface<comma>shapefile_multipatch<comma>fme_solid<comma>shapefile_first_feature<comma>fme_no_geom<comma>shapefile_null<comma>fme_no_geom<comma>shapefile_feature_table<comma>fme_feature_table<comma>shapefile_polygon<comma>fme_raster<comma>shapefile_polygon<comma>fme_point_cloud<comma>shapefile_polygon<comma>fme_voxel_grid<comma>shapefile_first_feature<comma>fme_collection,READER_ATTR_INDEX_TYPES,Indexed,READER_FORMAT_TYPE,DYNAMIC,READER_USES_DEF,yes,SOURCE,no,SUPPORTS_FEAT_TYPE_FANOUT,yes,SUPPORTS_MULTI_GEOM,no,WORKBENCH_CANNED_SCHEMA,,WRITER,SHAPEFILE,WRITER_ATTR_INDEX_TYPES,Indexed,WRITER_DEFLINE_PARMS,<quote>GUI<space>NAMEDGROUP<space>shape_layer_group<space>shape_dimension<space>Shapefile<quote><comma><comma><quote>GUI<space>LOOKUP_CHOICE<space>shape_dimension<space>Dimension<lt>space<gt>From<lt>space<gt>First<lt>space<gt>Feature<comma>AUTO%2D<comma>2D%2D<lt>space<gt>+<lt>space<gt>Measures<comma>2DM%3D<lt>space<gt>+<lt>space<gt>Measures<comma>3DM<space>Output<space>Dimension<quote><comma>AUTO,WRITER_DEF_LINE_TEMPLATE,<opencurly>FME_GEN_GROUP_NAME<closecurly><comma>shapefile_type<comma><opencurly>FME_GEN_GEOMETRY<closecurly><comma>shape_dimension<comma>AUTO,WRITER_FORMAT_PARAMETER,DEFAULT_GEOMETRY_TYPE<comma>shapefile_first_feature<comma>ADVANCED_PARMS<comma><quote>SHAPEFILE_OUT_SURFACE_SOLID_STORAGE<space>SHAPEFILE_OUT_MEASURES_AS_Z<quote><comma>FEATURE_TYPE_NAME<comma>Shapefile<comma>FEATURE_TYPE_DEFAULT_NAME<comma>Shapefile1<comma>READER_DATASET_HINT<comma><quote>Select<space>the<space>Esri<space>Shapefile<openparen>s<closeparen><quote><comma>WRITER_DATASET_HINT<comma><quote>Specify<space>a<space>folder<space>for<space>the<space>Esri<space>Shapefile<quote>,WRITER_FORMAT_TYPE,DYNAMIC,WRITER_HAS_DEFLINE_ATTRS,yes,WRITER_USES_DEF,yes" }    WRITER_FEATURE_TYPES { "<dollar><openparen>PARAMETER<closeparen><u91cd><u53e0><u77e2><u91cf><u56fe><u5c42>:Output,ftp_feature_type_name_exp,$(PARAMETER)<u91cd><u53e0><u77e2><u91cf><u56fe><u5c42>,ftp_writer,SHAPEFILE,ftp_geometry,shapefile_first_feature,ftp_dynamic_schema,no,ftp_dynamic_feature_type_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_geometry_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_def_name_type,DYN_SCHEMA_PROP_AUTO,ftp_dynamic_schema_sources,<lt>lt<gt>Unused<lt>gt<gt>,ftp_attribute_source,0,ftp_user_attributes,Q__clipped<comma>varchar<lt>openparen<gt>3<lt>closeparen<gt><comma>Q__area<comma>double,ftp_user_attribute_values,<comma>,ftp_format_parameters,shape_layer_group<comma><comma>shape_dimension<comma>AUTO" }    WRITER_PARAMS { "ADVANCED,,COMPRESSED_FILE,No,DATASET_SPLITTING,Yes,DATETIME_STORAGE,TIMESTAMP_STRING,DESTINATION_DATASETTYPE_VALIDATION,Yes,DIMENSION,AUTO,ENCODING,utf-8,ENFORCE_ATTRIBUTE_LIMIT,No,MEASURES_AS_Z,No,NETWORK_AUTHENTICATION,,PERMIT_NONASCII_FIRST_ATTRNAME,Yes,REQUIRE_FIRST_ATTRNAME_ALPHA,Yes,SPATIAL_INDEXES,NONE,SURFACE_SOLID_STORAGE,PATCH" }    DATASET_ATTR { "_dataset" }    FEATURE_TYPE_LIST_ATTR { "_feature_types" }    TOTAL_FEATURES_WRITTEN_ATTR { "_total_features_written" }    OUTPUT_PORTS { "" }    INPUT Output FEATURE_TYPE AreaCalculator_OUTPUT_1_Z02x17GzxwM=  @SupplyAttributes(ENCODED,fme_template_feature_type,Output)  @FeatureType(ENCODED,@EvaluateExpression(FDIV,STRING_ENCODED,$(PARAMETER$encode)<u91cd><u53e0><u77e2><u91cf><u56fe><u5c42>,FeatureWriter_2)) @CopyAttributes(ENCODED,Q__clipped,_clipped,Q__area,_area)
# -------------------------------------------------------------------------

FACTORY_DEF * RoutingFactory FACTORY_NAME "Destination Feature Type Routing Correlator"   COMMAND_PARM_EVALUATION SINGLE_PASS   INPUT FEATURE_TYPE *   FEATURE_TYPE_ATTRIBUTE __wb_out_feat_type__   OUTPUT ROUTED FEATURE_TYPE *    OUTPUT NOT_ROUTED FEATURE_TYPE __nuke_me__ @Tcl2("FME_StatMessage 818059 [FME_GetAttribute fme_template_feature_type] 818060 818061 fme_warn")
# -------------------------------------------------------------------------

FACTORY_DEF * TeeFactory   FACTORY_NAME "Final Output Nuker"   INPUT FEATURE_TYPE __nuke_me__

